package com.supie.webadmin.app.other.service.impl;

import cn.hutool.json.JSONUtil;
import com.supie.webadmin.app.other.model.BaseFiles;
import com.supie.webadmin.app.other.service.BaseFileChunksService;
import com.supie.webadmin.app.other.service.BaseFilesService;
import com.supie.webadmin.config.FileLocationManagement;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@SpringBootTest
//@MapperScan(value = {"com.supie.webadmin.upms.dao", "com.supie.webadmin.app.*.dao", "com.supie.common.*.dao"})
class BaseFileChunksServiceImplTest {

    // 每个分片的大小，单位为字节（例如 5MB = 5 * 1024 * 1024 字节）
    private static final int CHUNK_SIZE = 5 * 1024 * 1024;

    @Resource
    private FileLocationManagement fileLocationManagement;
    @Resource
    private BaseFilesService baseFilesService;
    @Resource
    private BaseFileChunksService baseFileChunksService;

    @Test
    void uploadFileChunkTest() throws IOException, ExecutionException, InterruptedException {
        Path fileDirectoryPath = Paths.get("TestFile");
        Path filePath = fileDirectoryPath.resolve("TestFile.zip");
        String modelPosition = fileLocationManagement.getModelPosition();
        log.warn("文件路径：" + filePath.toAbsolutePath());
        log.warn("文件上传位置：" + Paths.get(modelPosition).toAbsolutePath());
        // 文件上传判断-数据准备
        BaseFiles baseFiles = new BaseFiles();
        baseFiles.setFileName(filePath.getFileName().toString());
        baseFiles.setFileHash(computeSHA256(filePath));
        long fileSize = Files.size(filePath);
        baseFiles.setFileSize(fileSize);
        // 分片数
        // 计算总分片数量
        int totalChunks = (int) StrictMath.ceil((double) fileSize / CHUNK_SIZE);
        baseFiles.setTotalChunks(totalChunks);
        log.warn("文件上传判断[前]文件信息：" + JSONUtil.toJsonStr(baseFiles));
        baseFiles = baseFilesService.fileUploadJudgment(baseFiles);
        log.warn("文件上传判断[后]文件信息：" + JSONUtil.toJsonStr(baseFiles));
        Long baseFilesId = baseFiles.getId();
        // 打开文件进行切分
        List<CompletableFuture<Map<String, Integer>>> futureList = new LinkedList<>();
        try (RandomAccessFile raf = new RandomAccessFile(filePath.toFile(), "r")) {
            byte[] buffer = new byte[CHUNK_SIZE];
            int chunkNumber = 0;
            List<String> chunkHashList = new LinkedList<>();
            for (int i = 0; i < totalChunks; i++) {
                int bytesRead = raf.read(buffer); // 尝试从文件的当前位置读取最多 buffer.length 个字节到 buffer 数组中

                // 如果读取的字节数小于 CHUNK_SIZE，说明是最后一个分片
                byte[] chunkData;
                if (bytesRead < CHUNK_SIZE) {
                    chunkData = new byte[bytesRead];
                    System.arraycopy(buffer, 0, chunkData, 0, bytesRead);
                } else {
                    chunkData = buffer;
                }

                // 计算分片的 SHA-256 哈希值
                String chunkHash = computeSHA256(chunkData);
                chunkHashList.add(chunkHash);
                // 创建 MockMultipartFile 对象
                MultipartFile fileChunk = new MockMultipartFile("file", "chunk-" + chunkNumber, "application/octet-stream", chunkData);
                // 异步上传，调用 uploadFileChunk 方法上传分片
                final int currentChunkNumber = chunkNumber;
                CompletableFuture<Map<String, Integer>> future = CompletableFuture.supplyAsync(() -> {
                    BaseFiles baseFiles1 = baseFileChunksService.uploadFileChunk(baseFilesId, chunkHash, currentChunkNumber, bytesRead, fileChunk);
                    log.warn("[{}]分片上传结束，文件上传结果（-1：失败，1：正在上传，0：已完成）：{}", chunkHash, baseFiles1.getUploadState());
                    Map<String, Integer> result = new HashMap<>();
                    result.put(chunkHash, baseFiles1.getUploadState());
                    return result;
                });
                futureList.add(future);
                chunkNumber ++;
            }
        }
        // 等待所有分片上传完成
        CompletableFuture<Void> allFuture = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        // 在所有异步任务完成后，处理每个任务的结果
        CompletableFuture<List<Map<String, Integer>>> allResults = allFuture.thenApply(v -> {
            List<Map<String, Integer>> results = new LinkedList<>();
            for (CompletableFuture<Map<String, Integer>> future : futureList) {
                results.add(future.join());  // 使用 join() 获取每个异步任务的结果
            }
            return results;
        });
        // 等待所有任务完成并获取结果
        List<Map<String, Integer>> resultList = allResults.join();  // 阻塞直到所有异步任务完成
        log.warn("====================================================================================================");
        log.warn("分片上传结束，文件上传结果：" + JSONUtil.toJsonStr(resultList));
        log.warn("====================================================================================================");
    }

    /**
     * 计算文件的 SHA-256 哈希值
     * @param filePath 需要计算的文件
     * @return
     * @throws IOException
     */
    private String computeSHA256(Path filePath) throws IOException {
        try (InputStream fis = Files.newInputStream(filePath)) {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[10240];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
            byte[] hashBytes = digest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 计算字节数组的 SHA-256 哈希值
     */
    private static String computeSHA256(byte[] data) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(data);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

}
