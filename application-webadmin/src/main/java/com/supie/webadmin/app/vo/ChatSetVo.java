package com.supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 对话分组表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Schema(description = "对话分组表VO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatSetVo extends BaseVo {

    /**
     * 模型信息ID集合JSON字符串。
     */
    @Schema(description = "模型信息ID集合JSON字符串")
    private String modelInfoIdListJson;

    /**
     * 模型信息ID集合。
     */
    @Schema(description = "模型信息ID集合")
    private List<Long> modelInfoIdList;

    /**
     * 来源类型（1:正常对话；2:API调用；3:分享）
     */
    @Schema(description = "来源类型（1:正常对话；2:API调用；3:分享）")
    private Integer sourceType;

    /**
     * 对话类型（1、正常chat。2、模型对比）
     */
    @Schema(description = "对话类型（1、正常chat。2、模型对比）")
    private Integer chatSetType;

    /**
     * 字符编号。
     */
    @Schema(description = "字符编号")
    private String strId;

    /**
     * 主键id。
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 对话名称。
     */
    @Schema(description = "对话名称")
    private String chatSetName;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;
}
