package com.supie.webadmin.app.llmService.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 服务管理-算力申请与算力关联表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Schema(description = "服务管理-算力申请与算力关联表VO视图对象")
@Data
public class ApplyComputingPowerRelationVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 流程状态((1未提交，2待审批，3通过，4,未通过, 5终止, 6时间耗尽))。
     */
    @Schema(description = "流程状态((1未提交，2待审批，3通过，4,未通过, 5终止, 6时间耗尽))")
    private Integer processState;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 创建者ID。
     */
    @Schema(description = "创建者ID")
    private Long createUserId;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改者ID。
     */
    @Schema(description = "修改者ID")
    private Long updateUserId;

    /**
     * 修改时间。
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 算力申请表id。
     */
    @Schema(description = "算力申请表id")
    private Long computingPowerApplyId;

    /**
     * 算力管理表id。
     */
    @Schema(description = "算力管理表id")
    private Long computingPowerManagementId;

    /**
     * 申请使用开始时间。
     */
    @Schema(description = "申请使用开始时间")
    private Date useStartTime;

    /**
     * 申请使用结束时间。
     */
    @Schema(description = "申请使用结束时间")
    private Date useEndTime;

    /**
     * 申请人id。
     */
    @Schema(description = "申请人id")
    private Long applyUserId;

    /**
     * 使用状态（1未开始，2使用进行中，3使用时长耗尽，4删除, 5终止）。
     */
    @Schema(description = "使用状态（1未开始，2使用进行中，3使用时长耗尽，4删除, 5终止）")
    private Integer useState;
}
