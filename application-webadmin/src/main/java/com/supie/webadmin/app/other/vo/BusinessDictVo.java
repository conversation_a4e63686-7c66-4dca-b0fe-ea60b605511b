package com.supie.webadmin.app.other.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * BusinessDictVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "BusinessDictVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessDictVo extends BaseVo {

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符编号。
     */
    @Schema(description = "字符编号")
    private String strId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 显示顺序。
     */
    @Schema(description = "显示顺序")
    private Integer showOrder;

    /**
     * 父级编号。
     */
    @Schema(description = "父级编号")
    private Long parentId;

    /**
     * 绑定类型。
     */
    @Schema(description = "绑定类型")
    private String bindType;

    /**
     * 名称。
     */
    @Schema(description = "名称")
    private String dictName;

    /**
     * 描述。
     */
    @Schema(description = "描述")
    private String dictDescription;

    /**
     * 颜色数据。
     */
    @Schema(description = "颜色数据")
    private String colorData;

    /**
     * 层级。
     */
    @Schema(description = "层级")
    private Integer dictLevel;

    /**
     * 其他数据。
     */
    @Schema(description = "其他数据")
    private String otherData;
}
