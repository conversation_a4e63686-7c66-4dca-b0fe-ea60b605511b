package com.supie.webadmin.app.data.service.impl;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.webadmin.app.clickhouse.model.DataInsight;
import com.supie.webadmin.app.clickhouse.model.DataRecords;
import com.supie.webadmin.app.clickhouse.model.InsightResults;
import com.supie.webadmin.app.clickhouse.service.DataInsightService;
import com.supie.webadmin.app.clickhouse.service.InsightResultsService;
import com.supie.webadmin.app.data.model.DataSetInsights;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据集处理抽象类
 * <AUTHOR>
 */
public abstract class BaseDatasetInsightProcessor {
    protected Map<Long, String> trainingInMap;
    @Value("${python.dataInsightBaseUrl}")
    private String baseUrl;
    @Value("${python.authorizationKey}")
    private String authorizationKey;
    @Resource
    private DataInsightService dataInsightService;
    @Resource
    private InsightResultsService insightResultsService;

    // 定义一个正则表达式模式，用于匹配特殊字符
    private static final Pattern PORT_PATTERN = Pattern.compile("[!@#$%^&*()_+|~\\-=`{}\\[\\]:\";'<>?,./]");

//    public void DataSetProcessor() {
//        trainingInMap = new LinkedHashMap<>();
//    }

    protected int computeCharacterCount(String text) {
        return text.length();
    }
    /**
     * 调用python获取文本困惑度
     * @param dataMap 待计算的json
     * @param dataInsightList 单条数据列表
     * @param insightResultsList 存放完整json列表
     * @param dataStatistics 统计json
     */
    protected  void computePerplexity(Map<Long,Map<Long, String>> dataMap, List<DataInsight> dataInsightList, List<InsightResults> insightResultsList, Map<String,Map> dataStatistics) {
        try {
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put(String.valueOf(Header.AUTHORIZATION), authorizationKey);
            headers.put(String.valueOf(Header.CONTENT_TYPE), "application/json");
            Map<String, Object> requestBody = new LinkedHashMap<>();
            requestBody.put("columns", dataMap);
//            log.info("requestBody"+ JSONUtil.toJsonStr(requestBody));
            HttpResponse response = HttpRequest.of(baseUrl + "/ngram_dataset_process")
                    .method(Method.POST)
                    .addHeaders(headers)
                    .body(JSONUtil.toJsonStr(requestBody)).execute();
//            log.info("response:"+response);
            if (!response.isOk()) {
                throw new MyRuntimeException("数据处理失败");
            }
            //处理返回内容
            String body = response.body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            Map<String, Object> returnMap = jsonObject.toBean(Map.class);
            Map<String, Object> bodyMap = (Map<String, Object>) returnMap.get("data");
            //将需要处理的dataInsightList提取对象id转为map
            Map<Long, DataInsight> dataIdMap = dataInsightList.stream()
                    .collect(Collectors.toMap(DataInsight::getId, dataInsight -> dataInsight));
//            log.info("insightResultsList"+insightResultsList);
            Map<Long, InsightResults> insightResultsMap = insightResultsList.stream()
                    .collect(Collectors.toMap(InsightResults::getId, insightResults -> insightResults));
            for (Map.Entry<String, Object> entry : bodyMap.entrySet()) {
                Map<String,List<Map<String,Object>>> dataResultMap=new LinkedHashMap<>();
                String dataSetId=entry.getKey();
                Long dataRecordsId= Long.valueOf(dataSetId);
                InsightResults insightResults=insightResultsMap.get(dataRecordsId);
                Object value = entry.getValue();
                Map<String, Object> steamObject = (JSONObject) value;
                for (String key : steamObject.keySet()) {
                    Long id = Long.valueOf(key);
                    BigDecimal perplexity = (BigDecimal) steamObject.get(key);
                    float perplexityFloat = perplexity.floatValue();
                    dataIdMap.get(id).setPerplexity(perplexityFloat);
                    DataInsight dataInsight=dataIdMap.get(id);
                    Map<String,Object> dataJson=new LinkedHashMap<>();
                    dataJson.put("characterNumber", dataInsight.getCharacterNumber());
                    dataJson.put("specialCharacterNumber", dataInsight.getSpecialCharacterNumber());
                    dataJson.put("perplexity", dataInsight.getPerplexity());
                    // 检查dataResultMap是否已包含该键
                    if (dataResultMap.containsKey(dataInsight.getDataKey())) {
                        List<Map<String,Object>> existingValue=dataResultMap.get(dataInsight.getDataKey());
                        existingValue.add(dataJson);
                        dataResultMap.put(dataInsight.getDataKey(), existingValue);
                    } else {
                        // 如果dataResultMap中不包含该键，则直接设置值
                        List<Map<String,Object>> existingValue=new ArrayList<>();
                        existingValue.add(dataJson);
                        dataResultMap.put(dataInsight.getDataKey(), existingValue);
                    }
                    // 更新 dataStatistics
                    String dataKey = dataInsight.getDataKey();
                    String conversationsKey =dataInsight.getConversationsKey();
                    int dataRound=dataInsight.getDataRound();
                    Map<String, Map<String, Object>>dataStatisticsByRoundsMap=dataStatistics.getOrDefault(dataKey,new HashMap<>());
                    Map<String, Object> statisticsMap = dataStatisticsByRoundsMap.getOrDefault(String.valueOf(dataRound), new HashMap<>());
                    if(conversationsKey!=null){
                        Map<String, Object> conversationsMap = (Map<String, Object>) statisticsMap.getOrDefault(conversationsKey, new HashMap<>());
                        //计算极值
                        calculatedExtremum(conversationsMap,dataInsight,perplexityFloat);

                        statisticsMap.put(conversationsKey,conversationsMap);
                    }else{
                        //计算极值
                        calculatedExtremum(statisticsMap,dataInsight,perplexityFloat);
                    }


                    // 更新 dataStatistics
                    dataStatisticsByRoundsMap.put(String.valueOf(dataRound), statisticsMap);
//                    log.info("statisticsMap"+statisticsMap);
                    dataStatistics.put(dataKey,dataStatisticsByRoundsMap);
//                    log.info("dataStatistics："+dataKey+dataStatistics);
                }
                //填入insightResults（完整json）的处理情况
//                log.info("dataResultMap"+dataResultMap);
                insightResults.setDataResult(JSONUtil.toJsonStr(dataResultMap));
            }
            //存入数据
            dataInsightService.saveNewBatch(dataInsightList);
            insightResultsService.saveNewBatch(insightResultsList);
            dataInsightList.clear();
            insightResultsList.clear();
            dataMap.clear();
        } catch (MyRuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 计算字符串中的特殊字符数量
     * 特殊字符包括:!@#$%^&*()_+|~\-=`{}[]:;"";'<>?,./
     *
     * @param text 输入的字符串
     * @return 特殊字符的数量
     */
    protected int computeSpecialCharacterCount(String text) {
        // 定义一个正则表达式模式，用于匹配特殊字符
        // 创建一个匹配器，用于在文本中查找特殊字符
        Matcher matcher = PORT_PATTERN.matcher(text);
        // 初始化特殊字符计数器
        int count = 0;
        // 遍历文本，查找并计数特殊字符
        while (matcher.find()) {
            count++;
        }
        // 返回特殊字符的数量
        return count;
    }
    /**
     * 计算并更新极端值统计信息
     *
     * 该方法主要用于统计和更新给定数据洞察（DataInsight）对象中的极端值信息，
     * 包括字符数量、特殊字符数量和困惑度的分布及其最大最小值
     *
     * @param map 用于存储统计信息的Map对象
     * @param dataInsight 包含文本洞察的数据对象
     * @param perplexityFloat 困惑度值，用于分布统计
     * @return 更新后的包含统计信息的Map对象
     */
    protected Map<String, Object> calculatedExtremum(Map<String, Object>map,DataInsight dataInsight,float perplexityFloat) {
        // 统计 CharacterNumber
        Map<String, Integer> characterCountMap = (Map<String, Integer>) map.getOrDefault("characterNumber", new HashMap<>());
        Integer characterCount = characterCountMap.getOrDefault(String.valueOf(dataInsight.getCharacterNumber()), 0);
        characterCountMap.put(String.valueOf(dataInsight.getCharacterNumber()), characterCount + 1);
        map.put("characterNumber", characterCountMap);
        // 统计 specialCharacterNumber
        Map<String, Integer> specialCharacterCountMap = (Map<String, Integer>) map.getOrDefault("specialCharacterNumber", new HashMap<>());
        Integer specialCharacterCount = specialCharacterCountMap.getOrDefault(String.valueOf(dataInsight.getSpecialCharacterNumber()), 0);
        specialCharacterCountMap.put(String.valueOf(dataInsight.getSpecialCharacterNumber()), specialCharacterCount + 1);
        map.put("specialCharacterNumber", specialCharacterCountMap);

        // 统计 perplexity
        Map<String, Integer> perplexityCountMap = (Map<String, Integer>) map.getOrDefault("perplexity", new HashMap<>());
        // 计算区间
        int interval = 100;
        int lowerBound = (int) (perplexityFloat / interval) * interval;
        int upperBound = lowerBound + interval;
        // 创建区间字符串
        String rangeKey = lowerBound + "~" + upperBound;
        // 获取当前区间的计数
        Integer perplexityCount = perplexityCountMap.getOrDefault(rangeKey, 0);
        perplexityCountMap.put(rangeKey, perplexityCount + 1);
        map.put("perplexity", perplexityCountMap);

        // 更新最大值和最小值
        float currentPerplexity = dataInsight.getPerplexity();
        int currentCharacterNumber =  dataInsight.getCharacterNumber();
        int currentSpecialCharacterNumber = dataInsight.getSpecialCharacterNumber();

        // 更新 perplexity 最大最小
        // 获取 maxPerplexity 的值，并处理类型转换
        Float maxPerplexity;
        if (map.get("maxPerplexity") instanceof BigDecimal) {
            maxPerplexity = ((BigDecimal) map.get("maxPerplexity")).floatValue();
        } else {
            maxPerplexity = (Float) map.getOrDefault("maxPerplexity", currentPerplexity);
        }

// 获取 minPerplexity 的值，并处理类型转换
        Float minPerplexity;
        if (map.get("minPerplexity") instanceof BigDecimal) {
            minPerplexity = ((BigDecimal) map.get("minPerplexity")).floatValue();
        } else {
            minPerplexity = (Float) map.getOrDefault("minPerplexity", currentPerplexity);
        }
        map.put("maxPerplexity", Math.max(maxPerplexity, currentPerplexity));
        map.put("minPerplexity", Math.min(minPerplexity, currentPerplexity));

        // 更新 CharacterNumber 最大最小
        int maxCharacterNumber = (int) map.getOrDefault("maxCharacterNumber", currentCharacterNumber);
        int minCharacterNumber = (int) map.getOrDefault("minCharacterNumber", currentCharacterNumber);
        map.put("maxCharacterNumber", Math.max(maxCharacterNumber, currentCharacterNumber));
        map.put("minCharacterNumber", Math.min(minCharacterNumber, currentCharacterNumber));

        // 更新 specialCharacterNumber 最大最小
        int maxSpecialCharacterNumber = (int) map.getOrDefault("maxSpecialCharacterNumber", currentSpecialCharacterNumber);
        int minSpecialCharacterNumber = (int) map.getOrDefault("minSpecialCharacterNumber", currentSpecialCharacterNumber);
        map.put("maxSpecialCharacterNumber", Math.max(maxSpecialCharacterNumber, currentSpecialCharacterNumber));
        map.put("minSpecialCharacterNumber", Math.min(minSpecialCharacterNumber, currentSpecialCharacterNumber));
//        log.info("map"+map);
        return map;
    }

    public abstract void processData(List<DataRecords> resultList,DataSetInsights dataSetInsights);
}
