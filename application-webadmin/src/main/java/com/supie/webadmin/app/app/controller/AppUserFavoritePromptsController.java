package com.supie.webadmin.app.app.controller;

import com.supie.webadmin.interceptor.PyAuthInterface;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.app.dto.AppUserFavoritePromptsDto;
import com.supie.webadmin.app.app.model.AppUserFavoritePrompts;
import com.supie.webadmin.app.app.service.AppUserFavoritePromptsService;
import com.supie.webadmin.app.app.vo.AppUserFavoritePromptsVo;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 应用管理-prompt用户收藏关联表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "应用管理-prompt用户收藏关联表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/appUserFavoritePrompts")
public class AppUserFavoritePromptsController {

    @Autowired
    private AppUserFavoritePromptsService appUserFavoritePromptsService;

    /**
     * 新增应用管理-prompt用户收藏关联表数据。
     *
     * @param appUserFavoritePromptsDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appUserFavoritePromptsDto.id",
            "appUserFavoritePromptsDto.createTimeStart",
            "appUserFavoritePromptsDto.createTimeEnd",
            "appUserFavoritePromptsDto.updateTimeStart",
            "appUserFavoritePromptsDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody AppUserFavoritePromptsDto appUserFavoritePromptsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appUserFavoritePromptsDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppUserFavoritePrompts appUserFavoritePrompts = MyModelUtil.copyTo(appUserFavoritePromptsDto, AppUserFavoritePrompts.class);
        appUserFavoritePrompts = appUserFavoritePromptsService.saveNew(appUserFavoritePrompts);
        return ResponseResult.success(appUserFavoritePrompts.getId());
    }

    /**
     * 更新应用管理-prompt用户收藏关联表数据。
     *
     * @param appUserFavoritePromptsDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appUserFavoritePromptsDto.createTimeStart",
            "appUserFavoritePromptsDto.createTimeEnd",
            "appUserFavoritePromptsDto.updateTimeStart",
            "appUserFavoritePromptsDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody AppUserFavoritePromptsDto appUserFavoritePromptsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appUserFavoritePromptsDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppUserFavoritePrompts appUserFavoritePrompts = MyModelUtil.copyTo(appUserFavoritePromptsDto, AppUserFavoritePrompts.class);
        AppUserFavoritePrompts originalAppUserFavoritePrompts = appUserFavoritePromptsService.getById(appUserFavoritePrompts.getId());
        if (originalAppUserFavoritePrompts == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appUserFavoritePromptsService.update(appUserFavoritePrompts, originalAppUserFavoritePrompts)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除应用管理-prompt用户收藏关联表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的应用管理-prompt用户收藏关联表列表。
     *
     * @param appUserFavoritePromptsDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<AppUserFavoritePromptsVo>> list(
            @MyRequestBody AppUserFavoritePromptsDto appUserFavoritePromptsDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppUserFavoritePrompts appUserFavoritePromptsFilter = MyModelUtil.copyTo(appUserFavoritePromptsDtoFilter, AppUserFavoritePrompts.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppUserFavoritePrompts.class);
        List<AppUserFavoritePrompts> appUserFavoritePromptsList =
                appUserFavoritePromptsService.getAppUserFavoritePromptsListWithRelation(appUserFavoritePromptsFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(appUserFavoritePromptsList, AppUserFavoritePrompts.INSTANCE));
    }

    /**
     * 查看指定应用管理-prompt用户收藏关联表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<AppUserFavoritePromptsVo> view(@RequestParam Long id) {
        AppUserFavoritePrompts appUserFavoritePrompts = appUserFavoritePromptsService.getByIdWithRelation(id, MyRelationParam.full());
        if (appUserFavoritePrompts == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        AppUserFavoritePromptsVo appUserFavoritePromptsVo = AppUserFavoritePrompts.INSTANCE.fromModel(appUserFavoritePrompts);
        return ResponseResult.success(appUserFavoritePromptsVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        AppUserFavoritePrompts originalAppUserFavoritePrompts = appUserFavoritePromptsService.getById(id);
        if (originalAppUserFavoritePrompts == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appUserFavoritePromptsService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 新增应用管理-prompt用户收藏关联表-----无权限接口方便提供给python。
     *
     * @param appUserFavoritePromptsDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @Operation(summary = "应用管理-prompt用户收藏关联表无权限接口", description = "应用管理-prompt用户收藏关联表无权限接口")
    @PyAuthInterface
    @PostMapping("/noAuthInterfaceAdd")
    public ResponseResult<Long> noAuthInterfaceAdd(@MyRequestBody AppUserFavoritePromptsDto appUserFavoritePromptsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appUserFavoritePromptsDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppUserFavoritePrompts appUserFavoritePrompts = MyModelUtil.copyTo(appUserFavoritePromptsDto, AppUserFavoritePrompts.class);
        Long id = appUserFavoritePromptsService.noAuthInterfaceAdd(appUserFavoritePrompts);
        return ResponseResult.success(id);
    }
}
