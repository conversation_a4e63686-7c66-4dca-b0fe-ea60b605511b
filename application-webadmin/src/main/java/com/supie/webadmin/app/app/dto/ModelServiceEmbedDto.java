package com.supie.webadmin.app.app.dto;

import com.supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 模型嵌入表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ModelServiceEmbedDto对象")
@Data
public class ModelServiceEmbedDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 服务apiId。
     */
    @Schema(description = "服务apiId")
    private Long modelServiceApiId;

    /**
     * 嵌入配置Json。
     */
    @Schema(description = "嵌入配置Json")
    private String embedConfig;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * str_id LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
