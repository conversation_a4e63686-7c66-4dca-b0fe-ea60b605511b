package com.supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.vo.*;
import com.supie.webadmin.app.dto.*;
import com.supie.webadmin.app.model.*;
import com.supie.webadmin.app.service.*;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 密钥表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "密钥表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/secretKey")
public class SecretKeyController {

    @Autowired
    private SecretKeyService secretKeyService;

    /**
     * 新增密钥表数据。
     *
     * @param secretKeyDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "secretKeyDto.id",
            "secretKeyDto.searchString",
            "secretKeyDto.createTimeStart",
            "secretKeyDto.createTimeEnd",
            "secretKeyDto.updateTimeStart",
            "secretKeyDto.updateTimeEnd"})
    ////@SaCheckPermission("secretKey.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SecretKeyDto secretKeyDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(secretKeyDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SecretKey secretKey = MyModelUtil.copyTo(secretKeyDto, SecretKey.class);
        secretKey.setSecretValue(RandomStringUtils.randomAlphanumeric(50));
        secretKey = secretKeyService.saveNew(secretKey);
        return ResponseResult.success(secretKey.getId());
    }

    /**
     * 更新密钥表数据。
     *
     * @param secretKeyDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "secretKeyDto.searchString",
            "secretKeyDto.createTimeStart",
            "secretKeyDto.createTimeEnd",
            "secretKeyDto.updateTimeStart",
            "secretKeyDto.updateTimeEnd"})
    ////@SaCheckPermission("secretKey.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SecretKeyDto secretKeyDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(secretKeyDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SecretKey secretKey = MyModelUtil.copyTo(secretKeyDto, SecretKey.class);
        SecretKey originalSecretKey = secretKeyService.getById(secretKey.getId());
        if (originalSecretKey == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!secretKeyService.update(secretKey, originalSecretKey)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除密钥表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("secretKey.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除密钥表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("secretKey.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的密钥表列表。
     *
     * @param secretKeyDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("secretKey.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SecretKeyVo>> list(
            @MyRequestBody SecretKeyDto secretKeyDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SecretKey secretKeyFilter = MyModelUtil.copyTo(secretKeyDtoFilter, SecretKey.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SecretKey.class);
        List<SecretKey> secretKeyList = secretKeyService.getSecretKeyListWithRelation(secretKeyFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(secretKeyList, SecretKeyVo.class));
    }

    /**
     * 分组列出符合过滤条件的密钥表列表。
     *
     * @param secretKeyDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("secretKey.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SecretKeyVo>> listWithGroup(
            @MyRequestBody SecretKeyDto secretKeyDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SecretKey.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SecretKey.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SecretKey filter = MyModelUtil.copyTo(secretKeyDtoFilter, SecretKey.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SecretKey> resultList = secretKeyService.getGroupedSecretKeyListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SecretKeyVo.class));
    }

    /**
     * 查看指定密钥表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////@SaCheckPermission("secretKey.view")
    @GetMapping("/view")
    public ResponseResult<SecretKeyVo> view(@RequestParam Long id) {
        SecretKey secretKey = secretKeyService.getByIdWithRelation(id, MyRelationParam.full());
        if (secretKey == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SecretKeyVo secretKeyVo = MyModelUtil.copyTo(secretKey, SecretKeyVo.class);
        return ResponseResult.success(secretKeyVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SecretKey originalSecretKey = secretKeyService.getById(id);
        if (originalSecretKey == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!secretKeyService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
