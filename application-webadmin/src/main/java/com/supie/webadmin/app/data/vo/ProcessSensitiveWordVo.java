package com.supie.webadmin.app.data.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据管理-数据处理敏感词表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "数据管理-数据处理敏感词表VO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessSensitiveWordVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 词。
     */
    @Schema(description = "词")
    private String word;

    /**
     * 敏感词库id。
     */
    @Schema(description = "敏感词库id")
    private Long libraryId;
}
