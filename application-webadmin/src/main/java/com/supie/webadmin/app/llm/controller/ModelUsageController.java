package com.supie.webadmin.app.llm.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.llm.dto.ModelUsageDto;
import com.supie.webadmin.app.llm.model.ModelUsage;
import com.supie.webadmin.app.llm.service.ModelUsageService;
import com.supie.webadmin.app.llm.vo.ModelUsageVo;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 模型中心-模型使用场景操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "模型中心-模型使用场景管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/modelUsage")
public class ModelUsageController {

    @Autowired
    private ModelUsageService modelUsageService;

    /**
     * 新增模型中心-模型使用场景数据。
     *
     * @param modelUsageDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {"modelUsageDto.id", "modelUsageDto.searchString"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody ModelUsageDto modelUsageDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(modelUsageDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ModelUsage modelUsage = MyModelUtil.copyTo(modelUsageDto, ModelUsage.class);
        modelUsage = modelUsageService.saveNew(modelUsage);
        return ResponseResult.success(modelUsage.getId());
    }

    /**
     * 更新模型中心-模型使用场景数据。
     *
     * @param modelUsageDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {"modelUsageDto.searchString"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody ModelUsageDto modelUsageDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(modelUsageDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ModelUsage modelUsage = MyModelUtil.copyTo(modelUsageDto, ModelUsage.class);
        ModelUsage originalModelUsage = modelUsageService.getById(modelUsage.getId());
        if (originalModelUsage == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!modelUsageService.update(modelUsage, originalModelUsage)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除模型中心-模型使用场景数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的模型中心-模型使用场景列表。
     *
     * @param modelUsageDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<ModelUsageVo>> list(
            @MyRequestBody ModelUsageDto modelUsageDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        ModelUsage modelUsageFilter = MyModelUtil.copyTo(modelUsageDtoFilter, ModelUsage.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ModelUsage.class);
        List<ModelUsage> modelUsageList = modelUsageService.getModelUsageListWithRelation(modelUsageFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(modelUsageList, ModelUsage.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的模型中心-模型使用场景列表。
     *
     * @param modelUsageDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<ModelUsageVo>> listWithGroup(
            @MyRequestBody ModelUsageDto modelUsageDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ModelUsage.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, ModelUsage.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        ModelUsage filter = MyModelUtil.copyTo(modelUsageDtoFilter, ModelUsage.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<ModelUsage> resultList = modelUsageService.getGroupedModelUsageListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, ModelUsage.INSTANCE));
    }

    /**
     * 查看指定模型中心-模型使用场景对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<ModelUsageVo> view(@RequestParam Long id) {
        ModelUsage modelUsage = modelUsageService.getByIdWithRelation(id, MyRelationParam.full());
        if (modelUsage == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ModelUsageVo modelUsageVo = ModelUsage.INSTANCE.fromModel(modelUsage);
        return ResponseResult.success(modelUsageVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        ModelUsage originalModelUsage = modelUsageService.getById(id);
        if (originalModelUsage == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!modelUsageService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
