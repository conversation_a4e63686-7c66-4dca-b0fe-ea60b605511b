<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.dao.AppBusinessDictMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.model.BusinessDict">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="bind_type" jdbcType="VARCHAR" property="bindType"/>
        <result column="dict_name" jdbcType="VARCHAR" property="dictName"/>
        <result column="dict_description" jdbcType="VARCHAR" property="dictDescription"/>
        <result column="color_data" jdbcType="VARCHAR" property="colorData"/>
        <result column="other_data" jdbcType="VARCHAR" property="otherData"/>
        <result column="dict_level" jdbcType="INTEGER" property="dictLevel"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO li_business_dict
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            show_order,
            parent_id,
            bind_type,
            dict_name,
            dict_description,
            color_data,
            other_data,
            dict_level)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.showOrder},
            #{item.parentId},
            #{item.bindType},
            #{item.dictName},
            #{item.dictDescription},
            #{item.colorData},
            #{item.otherData},
            #{item.dictLevel})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.dao.AppBusinessDictMapper.inputFilterRef"/>
        AND li_business_dict.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="businessDictFilter != null">
            <if test="businessDictFilter.id != null">
                AND li_business_dict.id = #{businessDictFilter.id}
            </if>
            <if test="businessDictFilter.strId != null and businessDictFilter.strId != ''">
                <bind name = "safeBusinessDictStrId" value = "'%' + businessDictFilter.strId + '%'" />
                AND li_business_dict.str_id LIKE #{safeBusinessDictStrId}
            </if>
            <if test="businessDictFilter.updateTimeStart != null and businessDictFilter.updateTimeStart != ''">
                AND li_business_dict.update_time &gt;= #{businessDictFilter.updateTimeStart}
            </if>
            <if test="businessDictFilter.updateTimeEnd != null and businessDictFilter.updateTimeEnd != ''">
                AND li_business_dict.update_time &lt;= #{businessDictFilter.updateTimeEnd}
            </if>
            <if test="businessDictFilter.createTimeStart != null and businessDictFilter.createTimeStart != ''">
                AND li_business_dict.create_time &gt;= #{businessDictFilter.createTimeStart}
            </if>
            <if test="businessDictFilter.createTimeEnd != null and businessDictFilter.createTimeEnd != ''">
                AND li_business_dict.create_time &lt;= #{businessDictFilter.createTimeEnd}
            </if>
            <if test="businessDictFilter.createUserId != null">
                AND li_business_dict.create_user_id = #{businessDictFilter.createUserId}
            </if>
            <if test="businessDictFilter.updateUserId != null">
                AND li_business_dict.update_user_id = #{businessDictFilter.updateUserId}
            </if>
            <if test="businessDictFilter.dataUserId != null">
                AND li_business_dict.data_user_id = #{businessDictFilter.dataUserId}
            </if>
            <if test="businessDictFilter.dataDeptId != null">
                AND li_business_dict.data_dept_id = #{businessDictFilter.dataDeptId}
            </if>
            <if test="businessDictFilter.isDelete != null">
                AND li_business_dict.is_delete = #{businessDictFilter.isDelete}
            </if>
            <if test="businessDictFilter.showOrder != null">
                AND li_business_dict.show_order = #{businessDictFilter.showOrder}
            </if>
            <if test="businessDictFilter.parentId != null">
                AND li_business_dict.parent_id = #{businessDictFilter.parentId}
            </if>
            <if test="businessDictFilter.bindType != null and businessDictFilter.bindType != ''">
                <bind name = "safeBusinessDictBindType" value = "'%' + businessDictFilter.bindType + '%'" />
                AND li_business_dict.bind_type LIKE #{safeBusinessDictBindType}
            </if>
            <if test="businessDictFilter.dictName != null and businessDictFilter.dictName != ''">
                <bind name = "safeBusinessDictDictName" value = "'%' + businessDictFilter.dictName + '%'" />
                AND li_business_dict.dict_name LIKE #{safeBusinessDictDictName}
            </if>
            <if test="businessDictFilter.dictDescription != null and businessDictFilter.dictDescription != ''">
                <bind name = "safeBusinessDictDictDescription" value = "'%' + businessDictFilter.dictDescription + '%'" />
                AND li_business_dict.dict_description LIKE #{safeBusinessDictDictDescription}
            </if>
            <if test="businessDictFilter.colorData != null and businessDictFilter.colorData != ''">
                <bind name = "safeBusinessDictColorData" value = "'%' + businessDictFilter.colorData + '%'" />
                AND li_business_dict.color_data LIKE #{safeBusinessDictColorData}
            </if>
            <if test="businessDictFilter.otherData != null and businessDictFilter.otherData != ''">
                <bind name = "safeBusinessDictOtherData" value = "'%' + businessDictFilter.otherData + '%'" />
                AND li_business_dict.other_data LIKE #{safeBusinessDictOtherData}
            </if>
            <if test="businessDictFilter.dictLevel != null">
                AND li_business_dict.dict_level = #{businessDictFilter.dictLevel}
            </if>
            <if test="businessDictFilter.searchString != null and businessDictFilter.searchString != ''">
                <bind name = "safeBusinessDictSearchString" value = "'%' + businessDictFilter.searchString + '%'" />
                AND IFNULL(li_business_dict.dict_name,'') LIKE #{safeBusinessDictSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedBusinessDictList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.model.BusinessDict">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM li_business_dict
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) li_business_dict
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getBusinessDictList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.model.BusinessDict">
        SELECT * FROM li_business_dict
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
