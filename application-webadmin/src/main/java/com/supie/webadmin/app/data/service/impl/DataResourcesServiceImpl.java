package com.supie.webadmin.app.data.service.impl;

//import cn.easyes.core.conditions.LambdaEsQueryWrapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.minio.util.MinioUpDownloader;
import com.supie.common.minio.wrapper.MinioTemplate;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.app.service.AppKnowledgeBaseBasicInfoService;
import com.supie.webadmin.app.app.service.AppKnowledgeBaseFileRelationService;
import com.supie.webadmin.app.data.dao.DataResourcesMapper;
import com.supie.webadmin.app.data.model.DataResources;
import com.supie.webadmin.app.data.model.MyEntityFile;
import com.supie.webadmin.app.data.service.DataResourcesService;
import com.supie.webadmin.app.dataSync.annotation.DataSync;
import com.supie.webadmin.app.removeFromMinio.RemoveFromMinio;
import com.supie.webadmin.app.util.ToolUtil;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 数据资源基础表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("dataResourcesService")
@MyDataSource(DataSourceType.MAIN)
public class DataResourcesServiceImpl extends BaseService<DataResources, Long> implements DataResourcesService {

    @Resource
    private DataResourcesMapper dataResourcesMapper;
    @Resource
    private IdGeneratorWrapper idGenerator;
//    @Resource
//    private DataResourcesEsMapper dataResourcesEsMapper;
    @Resource
    private ApplicationConfig appConfig;
    @Resource
    private MinioUpDownloader minioUpDownloader;
    @Resource
    private AppKnowledgeBaseFileRelationService appKnowledgeBaseFileRelationService;
    @Resource
    private AppKnowledgeBaseBasicInfoService appKnowledgeBaseBasicInfoService;
    @Resource
    private MinioTemplate minioTemplate;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DataResources> mapper() {
        return dataResourcesMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param dataResources 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "DataResources", method = "add", idKey = "id", parentId = "1000001", nameKey = "fileName")
    public DataResources saveNew(DataResources dataResources) throws IOException {
        int insertResult = dataResourcesMapper.insert(this.buildDefaultValue(dataResources));
        if (insertResult != 1) {
            throw new MyRuntimeException("新增数据失败");
        }
        // TODO 暂时注释ES相关代码
//        MyEntityFile myEntityFile = this.myEntityFile(dataResources);
//        InputStream inputStream = minioUpDownloader.doDownloadFile(appConfig.getUploadFileBaseDir(),
//                DataResources.class.getSimpleName(), myEntityFile.getFieldName(), myEntityFile.getFilename(), myEntityFile.isAsImage());
//        dataResources.setFileContent(TikaUtil.getTikaParsers(inputStream));
//        Integer integer = this.saveAndEsNew(dataResources);
//        if (integer == 0) {
//            throw new MyRuntimeException("新增数据失败");
//        }
        return dataResources;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param dataResourcesList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DataResources> dataResourcesList) {
        if (CollUtil.isNotEmpty(dataResourcesList)) {
            dataResourcesList.forEach(this::buildDefaultValue);
            dataResourcesMapper.insertList(dataResourcesList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param dataResources         更新的对象。
     * @param originalDataResources 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "DataResources", method = "update", idKey = "id", parentId = "1000001", nameKey = "fileName")
    public boolean update(DataResources dataResources, DataResources originalDataResources) {
        MyModelUtil.fillCommonsForUpdate(dataResources, originalDataResources);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        // createUpdateQueryForNullValue 会出现 this.setIdFieldMethod 空指针
//        UpdateWrapper<DataResources> uw = this.createUpdateQueryForNullValue(dataResources, dataResources.getId());
//        return dataResourcesMapper.update(dataResources, uw) == 1;
        return dataResourcesMapper.updateById(dataResources) == 1;
    }
/*
    *//**
     * 删除指定数据,并删除es库里的数据
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     *//*
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        DataResources dataResources = dataResourcesMapper.selectById(id);
        dataResources.setIsAddKnowlege(-1);
        dataResourcesMapper.updateById(dataResources);
        return dataResourcesMapper.deleteById(id) == 1;
    }*/

    /**
     * 删除指定数据,并删除es库里的数据
     *
     * @param originalDataResources 数据对象。。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @DataSync(sourceTableName = "DataResources", method = "delete", idKey = "id", nameKey = "fileName")
    @RemoveFromMinio(sourceTableName = "DataResources",method="delete", nameKey = "fileName")
    public boolean remove(DataResources originalDataResources) {
        Long dataResourcesId = originalDataResources.getId();
        originalDataResources.setIsAddKnowlege(-1);
        dataResourcesMapper.updateById(originalDataResources);
        int deleteById = dataResourcesMapper.deleteById(dataResourcesId);
        if(deleteById == 1){
            // 删除相关的表的信息
            if (!appKnowledgeBaseBasicInfoService.removeByDataResourcesId(dataResourcesId)) {
                throw new MyRuntimeException("数据删除失败！");
            }
            if (!appKnowledgeBaseFileRelationService.removeByDataResourcesId(dataResourcesId)) {
                throw new MyRuntimeException("数据删除失败！");
            }
            // TODO 暂时注释ES相关代码
//            // 删除ES
//            List<DataResourcesEs> dataResourcesEsList = dataResourcesEsMapper.selectList(new LambdaEsQueryWrapper<DataResourcesEs>().eq(DataResourcesEs::getFileId, dataResourcesId));
//            if(dataResourcesEsList != null && !dataResourcesEsList.isEmpty()) {
//                List<String> dataResourcesIdListEs = dataResourcesEsList.stream()
//                        .map(DataResourcesEs::getId)
//                        .collect(Collectors.toList());
//                if (dataResourcesEsMapper.deleteBatchIds(dataResourcesIdListEs) == 0) {
//                    throw new MyRuntimeException("ES数据删除失败!");
//                }
//            }
            return true;
        }
        return false;
    }

    /**
     * 删除指定数据,并删除es库里的数据
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFromEs(Long id) {
//        LambdaEsQueryWrapper<DataResourcesEs> wrapper = new LambdaEsQueryWrapper<>();
//        wrapper.eq(DataResourcesEs::getFileId,id);
//        List<DataResourcesEs> dataResourcesEsList = dataResourcesEsMapper.selectList(wrapper);
//        if(dataResourcesEsList != null && dataResourcesEsList.size() != 0){
//            {
//                List<String> idList = dataResourcesEsList.stream()
//                        .map(DataResourcesEs::getId)
//                        .collect(Collectors.toList());
//                return dataResourcesEsMapper.deleteBatchIds(idList) > 0;
//            }
//        }
        return false;
    }


    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getDataResourcesListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<DataResources> getDataResourcesList(DataResources filter, String orderBy) {
        return dataResourcesMapper.getDataResourcesList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getDataResourcesList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<DataResources> getDataResourcesListWithRelation(DataResources filter, String orderBy) {
        List<DataResources> resultList = dataResourcesMapper.getDataResourcesList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<DataResources> getGroupedDataResourcesListWithRelation(
            DataResources filter, String groupSelect, String groupBy, String orderBy) {
        List<DataResources> resultList =
                dataResourcesMapper.getGroupedDataResourcesList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }



    private DataResources buildDefaultValue(DataResources dataResources) {
        if (dataResources.getId() == null) {
            dataResources.setId(idGenerator.nextLongId());
        }
        dataResources.setIsAddKnowlege(-1);
        MyModelUtil.fillCommonsForInsert(dataResources);
        dataResources.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataResources;
    }

    @Override
    public DataResources addNoAuthInterface(DataResources dataResources) {
        // 因为是无权限接口所以先将用户id和部门id添加到用户里
//        SysUser sysUser = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserType, 0));
//        dataResources.setDataDeptId(sysUser.getDeptId());
//        dataResources.setDataUserId(sysUser.getUserId());
//        dataResources.setCreateUserId(sysUser.getUserId());
        dataResources.setCreateTime(new Date());
        if (dataResources.getId() == null) {
            dataResources.setId(idGenerator.nextLongId());
        }
        dataResources.setIsDelete(GlobalDeletedFlag.NORMAL);
        dataResourcesMapper.insert(this.buildDefaultValue(dataResources));
        return dataResources;
    }

    @Override
    public int updateNoAuthInterface(DataResources dataResources, DataResources originalDataResources) {
        // 填写必要参数
        dataResources.setUpdateTime(new Date());
        return dataResourcesMapper.updateById(dataResources);
    }

    @Override
    public String queryFileAddress(Long id) {
        DataResources dataResources = dataResourcesMapper.selectOne(new LambdaQueryWrapper<DataResources>().eq(DataResources::getId, id));
        if (dataResources == null) {
            throw new RuntimeException("该文件不存在！");
        }
        String fileJson = dataResources.getFileJson();

        JSONObject json = JSONUtil.parseObj(fileJson);
        String fieldName = json.getStr("fieldName");
        String fileName = json.getStr("filename");
        boolean asImage = json.getBool("asImage");
        String uploadPath = ToolUtil.makeFullPath(null, DataResources.class.getSimpleName(), fieldName, asImage);
        return  uploadPath +  "/" + fileName;
    }

    public  List<String> splitString(String input) {
        List<String> parts = new ArrayList<>();
        int length = input.length();
        int chunkSize = 500;
        for (int i = 0; i < length; i += chunkSize) {
            parts.add(input.substring(i, Math.min(length, i + chunkSize)));
        }
        return parts;
    }

    /**
     * 文件信息解析并存入es中
     * @param dataResources
     * @return
     */
    @Override
    public Integer saveAndEsNew(DataResources dataResources) {
//        String cleanedStringQu = dataResources.getFileContent().replaceAll("[\\r\\n\\s]", "");
//        ArrayList<DataResourcesEs> saveDataResourcesES = new ArrayList<>();
//        List<String> strings = splitString(cleanedStringQu);
//        if(strings != null && strings.size() != 0){
//            //把解析出来的文档以段落为基点存储在文件中
//            for (String content : strings) {
//                DataResourcesEs dataResourcesEs = new DataResourcesEs();
//                //把dataResources对象重要属性赋值到dataResourcesEs对象实体中
//                this.copyProperties(dataResourcesEs,dataResources);
//                dataResourcesEs.setFileContent(content);
//                dataResourcesEs.setIsAddKnowlege(1);
//                saveDataResourcesES.add(dataResourcesEs);
//            }
//        }else {
//            DataResourcesEs dataResourcesEs = new DataResourcesEs();
//            BeanUtils.copyProperties(dataResourcesEs,dataResources);
//            saveDataResourcesES.add(dataResourcesEs);
//        }
//        return dataResourcesEsMapper.insertBatch(saveDataResourcesES);
        throw new MyRuntimeException("ES暂不可用！");
    }

//    /**
//     * 封装DataResourcesEs对象的属性值
//     * @param dataResourcesEs
//     * @param dataResources
//     */
//    private void copyProperties( DataResourcesEs dataResourcesEs, DataResources dataResources) {
//        dataResourcesEs.setStrId(dataResources.getStrId());
//        dataResourcesEs.setIsDelete(dataResources.getIsDelete());
//        dataResourcesEs.setDataUserId(dataResources.getDataUserId());
//        dataResourcesEs.setDataDeptId(dataResources.getDataDeptId());
//        dataResourcesEs.setResourcesData(dataResources.getResourcesData());
//        dataResourcesEs.setResourcesName(dataResources.getResourcesName());
//        dataResourcesEs.setIsClean(dataResources.getIsClean());
//        dataResourcesEs.setCleanId(dataResources.getCleanId());
//        dataResourcesEs.setCleaningAfterId(dataResources.getCleaningAfterId());
//        dataResourcesEs.setResourcesType(dataResources.getResourcesType());
//        dataResourcesEs.setBindStrId(dataResources.getBindStrId());
//        dataResourcesEs.setFileName(dataResources.getFileName());
//        dataResourcesEs.setIsAddKnowlege(dataResources.getIsAddKnowlege());
//        dataResourcesEs.setFileType(dataResources.getFileType());
//        dataResourcesEs.setFileType(dataResources.getFileType());
//        dataResourcesEs.setFileFormat(dataResources.getFileFormat());
//        dataResourcesEs.setFileRemark(dataResources.getFileRemark());
//        dataResourcesEs.setAsImage(dataResources.getAsImage());
//        dataResourcesEs.setFileId(dataResources.getId());
//        dataResourcesEs.setFileNameOfIk(dataResources.getFileNameOfIk());
//    }

    /**
     * 解析json字符串，提取出文件信息
     * @param dataResources
     * @return
     */
    @Override
    public MyEntityFile myEntityFile(DataResources dataResources) {
        JSONObject jsonObject = null;
        try {
             jsonObject = JSONUtil.parseObj(dataResources.getFileJson());
            // 打印解析后的实体对象
            //log.debug(myEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject.toBean(MyEntityFile.class);
    }

    @Override
    public DataResources selectById(Long id) {
        return dataResourcesMapper.selectById(id);
    }

    @Override
    public List<DataResources> selectList(LambdaQueryWrapper<DataResources> lambdaQueryWrapper) {
        return dataResourcesMapper.selectList(lambdaQueryWrapper);
    }
}
