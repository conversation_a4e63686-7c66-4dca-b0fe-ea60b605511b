package com.supie.webadmin.app.data.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supie.common.core.annotation.RelationOneToOne;
import com.supie.common.core.base.vo.BaseVo;
import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.data.model.DataResources;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;

/**
 * 数据标注任务表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "DataMarkTaskVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class DataMarkTaskVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 数据资源ID。
     */
    @Schema(description = "数据资源ID")
    private Long dataResourceId;

    /**
     * 数据源数据集ID。
     */
    @Schema(description = "数据源数据集ID")
    private Long sourceDatasetId;

    /**
     * 数据集ID。
     */
    @Schema(description = "数据集ID")
    private Long datasetId;

    /**
     * 标注状态（0：未开始。1：进行中。2：标注完成）。
     */
    @Schema(description = "标注状态（0：未开始。1：进行中。2：标注完成）")
    private Integer markState;

    /**
     * 标注数量。
     */
    @Schema(description = "标注数量")
    private Integer markNum;

    /**
     * 标注类型。
     */
    @Schema(description = "标注类型")
    private String markType;

    /**
     * 模型id
     */
    @Schema(description = "模型id")
    private Long modelId;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String modelType;

    /**
     * 标注配置
     */
    @Schema(description = "标注配置")
    private String markConfig;

    /**
     * 数据资源对象。
     */
    @Schema(description = "数据资源对象")
    private DataResources dataResources;

    /**
     * 数据集对象。
     */
    @Schema(description = "数据集对象")
    private DataDataSet dataSet;


    /**
     * 数据量
     */
    @Schema(description = "数据量")
    private Long dataNumber;
    /**
     * 培训任务id。
     */
    @Schema(description = "培训任务id")
    private Long trainingTaskId;
    /**
     * 课程id。
     */
    @Schema(description = "课程id")
    private Long courseId;
    /**
     * 用户id。
     */
    @Schema(description = "用户id")
    private Long userId;
    /**
     * 课程与功能模块关联关系id。
     */
    @Schema(description = "课程与功能模块关联关系id")
    private Long courseFunctionRelationId;

    /**
     * 标注来源类型（1：data_resource。2：business_file。3：data_set）
     */
    @Schema(description = "标注来源类型（1：data_resource。2：business_file。3：data_set）")
    private Integer markSourceType;

    /**
     *创建用户id字典。
     */
    @Schema(description = "创建用户id字典")
    private Map<String, Object> createUserIdDictMap;

    /**
     * 标注进度
     */
    @Schema(description = "标注进度")
    private Float markProgress;

    @RelationOneToOne(
            masterIdField = "dataDataSetId",
            slaveModelClass = DataDataSet.class,
            slaveIdField = "id"
    )
    @TableField(exist = false)
    private DataDataSet dataDataSet;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;


    /**
     * 是否有数据标注失败日志
     */
    @Schema(description = "是否有数据标注失败日志")
    private Integer isMarkTaskLog;


    /**
     * 标注模型。
     */
    private String modelName;

    /**
     * 数据集名称。
     */
    private String dataSetName;

}
