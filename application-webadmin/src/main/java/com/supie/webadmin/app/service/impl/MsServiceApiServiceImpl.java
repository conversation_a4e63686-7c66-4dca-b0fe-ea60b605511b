package com.supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.webadmin.app.service.*;
import com.supie.webadmin.app.dao.*;
import com.supie.webadmin.app.model.*;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 开发平台模型服务分享表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Slf4j
@Service("msServiceApiService")
public class MsServiceApiServiceImpl extends BaseService<MsServiceApi, Long> implements MsServiceApiService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private MsServiceApiMapper msServiceApiMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<MsServiceApi> mapper() {
        return msServiceApiMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MsServiceApi saveNew(MsServiceApi msServiceApi) {
        msServiceApiMapper.insert(this.buildDefaultValue(msServiceApi));
        return msServiceApi;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<MsServiceApi> msServiceApiList) {
        if (CollUtil.isNotEmpty(msServiceApiList)) {
            msServiceApiList.forEach(this::buildDefaultValue);
            msServiceApiMapper.insertList(msServiceApiList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(MsServiceApi msServiceApi, MsServiceApi originalMsServiceApi) {
        MyModelUtil.fillCommonsForUpdate(msServiceApi, originalMsServiceApi);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<MsServiceApi> uw = this.createUpdateQueryForNullValue(msServiceApi, msServiceApi.getId());
        return msServiceApiMapper.update(msServiceApi, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return msServiceApiMapper.deleteById(id) == 1;
    }

    @Override
    public List<MsServiceApi> getMsServiceApiList(MsServiceApi filter, String orderBy) {
        return msServiceApiMapper.getMsServiceApiList(filter, orderBy);
    }

    @Override
    public List<MsServiceApi> getMsServiceApiListWithRelation(MsServiceApi filter, String orderBy) {
        List<MsServiceApi> resultList = msServiceApiMapper.getMsServiceApiList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public MsServiceApi insertDataFromLmd(Long serviceId, String serviceUrl, String serviceName, Integer serviceType) {
        MsServiceApi msServiceApi = new MsServiceApi();
        msServiceApi.setId(idGenerator.nextLongId());
        msServiceApi.setDataUserId(1891323715682045952L);
        msServiceApi.setDataDeptId(1891323715686240258L);
        msServiceApi.setCreateUserId(1891323715682045952L);
        msServiceApi.setUpdateUserId(1891323715682045952L);
        msServiceApi.setCreateTime(new Date());
        msServiceApi.setUpdateTime(new Date());
        msServiceApi.setServiceId(serviceId);
        msServiceApi.setServiceUrl(serviceUrl);
        msServiceApi.setServiceName(serviceName);
        msServiceApi.setServiceType(serviceType);
        msServiceApi.setIsDelete(GlobalDeletedFlag.NORMAL);
        msServiceApiMapper.insert(msServiceApi);
        return msServiceApi;
    }

    private MsServiceApi buildDefaultValue(MsServiceApi msServiceApi) {
        if (msServiceApi.getId() == null) {
            msServiceApi.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(msServiceApi);
        msServiceApi.setIsDelete(GlobalDeletedFlag.NORMAL);
        return msServiceApi;
    }
}
