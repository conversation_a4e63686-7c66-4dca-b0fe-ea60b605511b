package com.supie.webadmin.app.liteflow.node;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.supie.webadmin.app.liteflow.model.FlowInfo;
import com.supie.webadmin.app.pythonClient.ApiUrlEnums.LiteflowUrlEnums;
import com.supie.webadmin.app.pythonClient.model.PythonResponse;
import com.supie.webadmin.app.pythonClient.service.impl.PythonClientServiceImpl;
import com.supie.webadmin.app.liteflow.model.NodeResult;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@LiteflowComponent(id = "codeNode", name = "Python代码执行组件")
public class PythonCodeNode extends BaseNode {
    private final PythonClientServiceImpl pythonClientService;

    public PythonCodeNode(PythonClientServiceImpl pythonClientService) {
        super();
        this.pythonClientService = pythonClientService;
    }

//    @Value("${python.baseUrl}")
//    private String baseUrl;
//    @Value("${python.authorizationKey}")
//    private String authorizationKey;

    @Override
    public void process() throws Exception {
        String tag = getTag();
        NodeResult nodeResult = nodeResultMap.get(tag);
        JsonNode thisNodeConfigJsonNode = thisNodeConfigJsonNodeMap.get(tag);
        FlowInfo flowInfo = flowInfoMap.get(tag);
        JsonNode pythonCodeJsonNode = thisNodeConfigJsonNode.get("code");
        if (pythonCodeJsonNode == null) {
            throw new RuntimeException("Python代码执行组件[" + flowInfo.getNodeTagToNameMap().get(tag) + "]配置错误，没有配置python代码");
        }
        String pythonCode = pythonCodeJsonNode.textValue();
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        Map<String, Object> pythonParams = new HashMap<>();
        pythonParams.put("python_code", pythonCode);
        Map<String, Object> input_params = new HashMap<>();
        input_params.put("params", nodeResult.getInputParamsMap());
        pythonParams.put("input_params", JSONUtil.toJsonStr(input_params));
//        String url = baseUrl + "/codeExcute/pythonExecute";
        PythonResponse response = pythonClientService.request(LiteflowUrlEnums.PYTHONEXECUTE, headers, null, pythonParams);

//        HttpResponse response = HttpRequest.post(url)
//                .header("Content-Type", "application/json; charset=utf-8") // 设置请求头
//                .header("Authorization", authorizationKey)
//                .body(JSONUtil.toJsonStr(pythonParams)) // 设置请求体为JSON字符串
//                .execute();
        if(response.isSuccess()) {
            Object puthonResult;
            try {
                Map<String, Object> outputMap = (Map<String, Object>) response.getData();
                puthonResult = outputMap.get("output");
            } catch (Exception e) {
                throw new RuntimeException("【" + JSON.toJSONString(response) + "】请求结果异常，异常信息为：" + e);
            }
            // 将结果设置到outputParamsMap中
            ArrayNode outPutParamsJsonNode = (ArrayNode) thisNodeConfigJsonNode.get("outPutParams");
            if (outPutParamsJsonNode != null) {
                for (JsonNode jsonNode : outPutParamsJsonNode) {
                    Map<String, Object> outputParamsMap = new HashMap<>();
                    outputParamsMap.put(jsonNode.get("name").textValue(), puthonResult);
                    nodeResult.setOutputParamsMap(outputParamsMap);
                }
            }
        } else {
            // 错误处理
            throw new RuntimeException("[" + flowInfo.getNodeTagToNameMap().get(tag) +
                    "]节点请求失败，状态：" + response.getErrorCode() + "。" + response.getErrorMessage());
        }
        nodeResultMap.put(tag, nodeResult);
        thisNodeConfigJsonNodeMap.put(tag, thisNodeConfigJsonNode);
        flowInfoMap.put(tag, flowInfo);
    }

}
