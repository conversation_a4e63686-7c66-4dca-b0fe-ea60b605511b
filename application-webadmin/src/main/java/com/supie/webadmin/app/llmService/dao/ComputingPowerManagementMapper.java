package com.supie.webadmin.app.llmService.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.llmService.model.ComputingPowerManagement;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务管理-算力管理数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm
public interface ComputingPowerManagementMapper extends BaseDaoMapper<ComputingPowerManagement> {

    /**
     * 批量插入对象列表。
     *
     * @param computingPowerManagementList 新增对象列表。
     */
    void insertList(List<ComputingPowerManagement> computingPowerManagementList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param computingPowerManagementFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<ComputingPowerManagement> getGroupedComputingPowerManagementList(
            @Param("computingPowerManagementFilter") ComputingPowerManagement computingPowerManagementFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param computingPowerManagementFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<ComputingPowerManagement> getComputingPowerManagementList(
            @Param("computingPowerManagementFilter") ComputingPowerManagement computingPowerManagementFilter, @Param("orderBy") String orderBy);

    int[] updateComputingPowerManagementBatch(List<ComputingPowerManagement> saveComputingPowerManagementList);
}
