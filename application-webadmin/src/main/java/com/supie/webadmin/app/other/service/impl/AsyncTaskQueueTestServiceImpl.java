package com.supie.webadmin.app.other.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.supie.asynctaskqueue.AsyncTaskQueue;
import com.supie.asynctaskqueue.TaskType;
import com.supie.webadmin.app.other.service.AsyncTaskQueueTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service("asyncTaskQueueTestService")
public class AsyncTaskQueueTestServiceImpl implements AsyncTaskQueueTestService {

    @Override
    @AsyncTaskQueue
    public void test(Integer count) {
        Date nowDate = new Date();
        log.warn("test() ===> [{}]-[start]", count);
        ThreadUtil.sleep(10000);
        log.error("test() ===> [{}]-[end]-[{}]", count, new Date().getTime() - nowDate.getTime());
    }

    @Override
    @AsyncTaskQueue(TaskType.TEST)
    public void test2(Integer count) {
        Date nowDate = new Date();
        log.warn("【test】 ===> [{}]-[start]", count);
        ThreadUtil.sleep(5000);
        log.error("【test】 ===> [{}]-[end]-[{}]", count, new Date().getTime() - nowDate.getTime());
    }

}
