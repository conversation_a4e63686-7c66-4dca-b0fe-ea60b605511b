package com.supie.webadmin.app.app.model;

import com.baomidou.mybatisplus.annotation.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.supie.webadmin.app.app.vo.AppModelChatTextBlockAssociationVo;

/**
 * AppModelChatTextBlockAssociation实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_app_model_chat_text_block_association")
public class AppModelChatTextBlockAssociation extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 对话id。
     */
    private Long modelChatId;

    /**
     * 文本块。
     */
    private String textBlock;

    /**
     * 知识库id。
     */
    private Long knowledgeBaseId;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * text_block LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    @Mapper
    public interface AppModelChatTextBlockAssociationModelMapper extends BaseModelMapper<AppModelChatTextBlockAssociationVo, AppModelChatTextBlockAssociation> {
    }
    public static final AppModelChatTextBlockAssociationModelMapper INSTANCE = Mappers.getMapper(AppModelChatTextBlockAssociationModelMapper.class);

}
