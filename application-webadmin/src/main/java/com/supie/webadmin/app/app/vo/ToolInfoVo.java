package com.supie.webadmin.app.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.base.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * ToolInfoVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ToolInfoVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ToolInfoVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 工具名称。
     */
    @Schema(description = "工具名称")
    private String toolName;

    /**
     * 工具描述。
     */
    @Schema(description = "工具描述")
    private String toolDesc;

    /**
     * 工具集id。
     */
    @Schema(description = "工具集id")
    private Long toolSetId;

    /**
     * 函数名。
     */
    @Schema(description = "函数名")
    private String functionName;

    /**
     * 是否外部插件（-1：否，1：是）。
     */
    @Schema(description = "是否外部插件（-1：否，1：是）")
    private Integer isExternalPlugin;

    /**
     * 外接api地址。
     */
    @Schema(description = "外接api地址")
    private String externalApiAddress;

    /**
     * 请求头。
     */
    @Schema(description = "请求头")
    private String requestHeader;

    /**
     * 请求参数。
     */
    @Schema(description = "请求参数")
    private String requestParamsExample;

    /**
     * 请求体示例。
     */
    @Schema(description = "请求体示例")
    private String requestBodyExample;

    /**
     * 请求参数说明。
     */
    @Schema(description = "请求参数说明")
    private String requestParamsDesc;

    /**
     * 请求体参数说明。
     */
    @Schema(description = "请求体参数说明")
    private String requestBodyDesc;

    /**
     * 请求方式。
     */
    @Schema(description = "请求方式")
    private String methodType;

    /**
     * 返回参数示例。
     */
    @Schema(description = "返回参数示例")
    private String returnParamsExample;

    /**
     * 返回参数说明。
     */
    @Schema(description = "返回参数说明")
    private String returnParamsDesc;

    /**
     * 前端配置。
     */
    @Schema(description = "前端配置")
    private String frontConfig;

    /**
     * python示例代码。
     */
    @Schema(description = "python示例代码")
    private String pythonSampleCode;

    /**
     * 请求头参数说明。
     */
    @Schema(description = "请求头参数说明")
    private String requestHeaderDesc;

    /**
     * 工具函数路径 tool_function_path。
     */
    @Schema(description = "工具函数路径")
    private String toolFunctionPath;

}
