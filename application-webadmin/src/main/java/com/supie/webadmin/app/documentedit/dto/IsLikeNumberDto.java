package com.supie.webadmin.app.documentedit.dto;

import com.supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 点赞和点踩统计表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Schema(description = "IsLikeNumberDto对象")
@Data
public class IsLikeNumberDto {

    /**
     * 主键ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "主键ID。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符串ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符串ID。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人ID。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 是否喜欢（1 是；-1 否）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "是否喜欢（1 是；-1 否）。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，是否喜欢（1 是；-1 否）不能为空！")
    private Integer isLike;

    /**
     * 关联数据id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "关联数据id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，关联数据id不能为空！")
    private Long bindId;

    /**
     * 关联类型（表名）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "关联类型（表名）。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，关联类型（表名）不能为空！")
    private String bindType;

    /**
     * 用户id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "用户id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，用户id不能为空！")
    private Long userId;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * dataDeptId 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "dataDeptId 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private Long dataDeptIdStart;

    /**
     * dataDeptId 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "dataDeptId 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private Long dataDeptIdEnd;

    /**
     * str_id LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
