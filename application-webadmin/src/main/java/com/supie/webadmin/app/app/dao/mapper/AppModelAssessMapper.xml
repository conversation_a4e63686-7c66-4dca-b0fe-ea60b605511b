<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.app.dao.AppModelAssessMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.app.model.AppModelAssess">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="assess_name" jdbcType="VARCHAR" property="assessName"/>
        <result column="assess_desc" jdbcType="VARCHAR" property="assessDesc"/>
        <result column="assess_state" jdbcType="VARCHAR" property="assessState"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="assess_pattern" jdbcType="VARCHAR" property="assessPattern"/>
        <result column="data_set_id" jdbcType="BIGINT" property="dataSetId"/>
        <result column="assess_dimension" jdbcType="LONGVARCHAR" property="assessDimension"/>
        <result column="scoring_prompt" jdbcType="LONGVARCHAR" property="scoringPrompt"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_app_model_assess
            (id,
            str_id,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            assess_name,
            assess_desc,
            assess_state,
            error_msg,
            assess_pattern,
            data_set_id,
            assess_dimension,
            scoring_prompt,
            model_deploy_id,
            model_id,
            model_type,
            model_name)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.assessName},
            #{item.assessDesc},
            #{item.assessState},
            #{item.errorMsg},
            #{item.assessPattern},
            #{item.dataSetId},
            #{item.assessDimension},
            #{item.scoringPrompt},
            #{item.modelDeployId},
            #{item.modelId},
            #{item.modelType},
            #{item.modelName})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.app.dao.AppModelAssessMapper.inputFilterRef"/>
        AND lmd_app_model_assess.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="appModelAssessFilter != null">
            <if test="appModelAssessFilter.id != null">
                AND lmd_app_model_assess.id = #{appModelAssessFilter.id}
            </if>
            <if test="appModelAssessFilter.strId != null and appModelAssessFilter.strId != ''">
                AND lmd_app_model_assess.str_id = #{appModelAssessFilter.strId}
            </if>
            <if test="appModelAssessFilter.isDelete != null">
                AND lmd_app_model_assess.is_delete = #{appModelAssessFilter.isDelete}
            </if>
            <if test="appModelAssessFilter.createTimeStart != null and appModelAssessFilter.createTimeStart != ''">
                AND lmd_app_model_assess.create_time &gt;= #{appModelAssessFilter.createTimeStart}
            </if>
            <if test="appModelAssessFilter.createTimeEnd != null and appModelAssessFilter.createTimeEnd != ''">
                AND lmd_app_model_assess.create_time &lt;= #{appModelAssessFilter.createTimeEnd}
            </if>
            <if test="appModelAssessFilter.createUserId != null">
                AND lmd_app_model_assess.create_user_id = #{appModelAssessFilter.createUserId}
            </if>
            <if test="appModelAssessFilter.updateTimeStart != null and appModelAssessFilter.updateTimeStart != ''">
                AND lmd_app_model_assess.update_time &gt;= #{appModelAssessFilter.updateTimeStart}
            </if>
            <if test="appModelAssessFilter.updateTimeEnd != null and appModelAssessFilter.updateTimeEnd != ''">
                AND lmd_app_model_assess.update_time &lt;= #{appModelAssessFilter.updateTimeEnd}
            </if>
            <if test="appModelAssessFilter.updateUserId != null">
                AND lmd_app_model_assess.update_user_id = #{appModelAssessFilter.updateUserId}
            </if>
            <if test="appModelAssessFilter.modelId != null">
                AND lmd_app_model_assess.model_id   = #{appModelAssessFilter.modelId}
            </if>
            <if test="appModelAssessFilter.dataUserId != null">
                AND lmd_app_model_assess.data_user_id = #{appModelAssessFilter.dataUserId}
            </if>
            <if test="appModelAssessFilter.dataDeptId != null">
                AND lmd_app_model_assess.data_dept_id = #{appModelAssessFilter.dataDeptId}
            </if>
            <if test="appModelAssessFilter.modelName != null and appModelAssessFilter.modelName != ''">
                <bind name = "safeAppModelAssessModelName" value = "'%' + appModelAssessFilter.modelName + '%'" />
                AND lmd_app_model_assess.model_name LIKE #{safeAppModelAssessModelName}
            </if>
            <if test="appModelAssessFilter.assessName != null and appModelAssessFilter.assessName != ''">
                <bind name = "safeAppModelAssessAssessName" value = "'%' + appModelAssessFilter.assessName + '%'" />
                AND lmd_app_model_assess.assess_name LIKE #{safeAppModelAssessAssessName}
            </if>
            <if test="appModelAssessFilter.assessDesc != null and appModelAssessFilter.assessDesc != ''">
                <bind name = "safeAppModelAssessAssessDesc" value = "'%' + appModelAssessFilter.assessDesc + '%'" />
                AND lmd_app_model_assess.assess_desc LIKE #{safeAppModelAssessAssessDesc}
            </if>
            <if test="appModelAssessFilter.assessState != null and appModelAssessFilter.assessState != ''">
                AND lmd_app_model_assess.assess_state = #{appModelAssessFilter.assessState}
            </if>
            <if test="appModelAssessFilter.errorMsg != null and appModelAssessFilter.errorMsg != ''">
                <bind name = "safeAppModelAssessErrorMsg" value = "'%' + appModelAssessFilter.errorMsg + '%'" />
                AND lmd_app_model_assess.error_msg LIKE #{safeAppModelAssessErrorMsg}
            </if>
            <if test="appModelAssessFilter.assessPattern != null and appModelAssessFilter.assessPattern != ''">
                AND lmd_app_model_assess.assess_pattern = #{appModelAssessFilter.assessPattern}
            </if>
            <if test="appModelAssessFilter.dataSetId != null">
                AND lmd_app_model_assess.data_set_id = #{appModelAssessFilter.dataSetId}
            </if>
            <if test="appModelAssessFilter.assessDimension != null and appModelAssessFilter.assessDimension != ''">
                <bind name = "safeAppModelAssessAssessDimension" value = "'%' + appModelAssessFilter.assessDimension + '%'" />
                AND lmd_app_model_assess.assess_dimension LIKE #{safeAppModelAssessAssessDimension}
            </if>
            <if test="appModelAssessFilter.scoringPrompt != null and appModelAssessFilter.scoringPrompt != ''">
                <bind name = "safeAppModelAssessScoringPrompt" value = "'%' + appModelAssessFilter.scoringPrompt + '%'" />
                AND lmd_app_model_assess.scoring_prompt LIKE #{safeAppModelAssessScoringPrompt}
            </if>
            <if test="appModelAssessFilter.modelType != null and appModelAssessFilter.modelType != ''">
                <bind name = "safeAppModelAssessModelType" value = "'%' + appModelAssessFilter.modelType + '%'" />
                AND lmd_app_model_assess.model_type LIKE #{safeAppModelAssessModelType}
            </if>
            <if test="appModelAssessFilter.modelDeployId != null">
                AND lmd_app_model_assess.model_deploy_id = #{appModelAssessFilter.modelDeployId}
            </if>
        </if>
    </sql>

    <select id="getGroupedAppModelAssessList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.AppModelAssess">
        SELECT * FROM
            (SELECT
                COUNT(is_delete) is_delete,
                ${groupSelect}
            FROM lmd_app_model_assess
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_app_model_assess
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getAppModelAssessList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.AppModelAssess">
        SELECT DISTINCT lmd_app_model_assess.* FROM lmd_app_model_assess
        LEFT JOIN lmd_app_assess_model_model_relationship ON lmd_app_assess_model_model_relationship.assess_id = lmd_app_model_assess.id
        <where>
            <include refid="filterRef"/>
            <if test="appModelAssessFilter.searchString != null and appModelAssessFilter.searchString != ''">
                <bind name = "safeAppModelAssessSearchString" value = "'%' + appModelAssessFilter.searchString + '%'" />
                AND CONCAT(
                    IFNULL(lmd_app_model_assess.assess_name, ''),
                    IFNULL(lmd_app_model_assess.assess_desc, ''),
                    IFNULL(lmd_app_model_assess.assess_state, ''),
                    IFNULL(lmd_app_model_assess.assess_pattern, ''),
                    IFNULL(lmd_app_model_assess.assess_dimension, ''),
                    IFNULL(lmd_app_model_assess.scoring_prompt, ''),
                    IFNULL(lmd_app_model_assess.model_name, ''),
                    IFNULL(lmd_app_assess_model_model_relationship.model_name, '')
                ) LIKE #{safeAppModelAssessSearchString}
            </if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
