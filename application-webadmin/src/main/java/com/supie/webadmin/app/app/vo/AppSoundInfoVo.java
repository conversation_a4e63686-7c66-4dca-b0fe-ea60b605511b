package com.supie.webadmin.app.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.base.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * AppSoundInfoVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "AppSoundInfoVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppSoundInfoVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 音源文件名称。
     */
    @Schema(description = "音源文件名称")
    private String soundFileName;

    /**
     * 音源文件大小。
     */
    @Schema(description = "音源文件大小")
    private Integer soundFileSize;

    /**
     * 音源文件内容。
     */
    @Schema(description = "音源文件内容")
    private String soundFileContent;

    /**
     * 音源文件语言。
     */
    @Schema(description = "音源文件语言")
    private String soundFileLanguage;
}
