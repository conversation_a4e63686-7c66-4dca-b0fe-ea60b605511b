package com.supie.webadmin.app.llm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.base.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * ModelUsageVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ModelUsageVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelUsageVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 场景名称。
     */
    @Schema(description = "场景名称")
    private String sceneName;

    /**
     * 场景描述。
     */
    @Schema(description = "场景描述")
    private String sceneDesc;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;
}
