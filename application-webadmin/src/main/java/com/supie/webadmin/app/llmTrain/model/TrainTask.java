package com.supie.webadmin.app.llmTrain.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.llm.model.ModelBasic;
import com.supie.webadmin.app.other.model.FileExportRecord;
import com.supie.webadmin.upms.model.SysUser;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.webadmin.app.llmTrain.vo.TrainTaskVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TrainTask实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_train_task")
public class TrainTask extends BaseModel implements Cloneable {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    private String strId;

    /**
     * 任务类型。
     */
    private String trainTaskType;

    /**
     * 任务状态（未开始，基础环境搭建中，训练中，终止，失败，已完成，已发布）。
     */
    private String trainTaskState;

    /**
     * 描述。
     */
    private String trainTaskDescribe;

    /**
     * 训练参数配置。
     */
    private String trainTaskConfigJson;

    /**
     * deepspeed多卡训练配置信息 deepspeed_config
     */
    private String deepspeedConfigJson;

    /**
     * 权重合并配置 weights_merged_config_json
     */
    private String weightsMergedConfigJson;

    /**
     * 通用参数JSON common_parameter_json。
     */
    private String commonParameterJson;

    /**
     * 训练方法。
     */
    private String trainTaskMethod;

    /**
     *mindformers训练yaml配置
     */
    private String yamlConfig;

    /**
     * 实验计划id（字段名称拼写错误！！！experimentPlanId）
     */
    private Long expermentPlanId;

    /**
     * 基础模型id
     */
    private Long baseModelId;

    /**
     * 上一版本的训练任务id
     */
    private Long trainTaskId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型标识符
     */
    private String modelCode;

    /**
     * 版本管理状态：最新的为now（历史的为版本更迭时的当前时间，格式 yyyy-MM-dd HH:mm:ss）
     */
    private String editionStatus;

    /**
     * 显卡数量
     */
    private Integer graphicNumber;

    /**
     * 训练框架（1，torch  2，mindformers）
     */
    private Integer trainFramework;

    /**
     * 模型权重合并id
     */
    private Long mergeId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 创建用户id字典。
     */
    @RelationDict(
            masterIdField = "createUserId",
            slaveModelClass = SysUser.class,
            slaveIdField = "userId",
            slaveNameField = "showName")
    @TableField(exist = false)
    private Map<String, Object> createUserIdDict;


    /**
     * ModelBasic。
     */
    @RelationOneToOne(
            masterIdField = "baseModelId",
            slaveModelClass = ModelBasic.class,
            slaveIdField = "id"
    )
    @TableField(exist = false)
    private ModelBasic modelBasic;
    /**
     *FileExportRecord。
     */
//    @RelationOneToOne(
//            masterIdField = "id",
//            slaveModelClass =  FileExportRecord.class,
//            slaveIdField = "bindId"
//    )
    @TableField(exist = false)
    private  FileExportRecord  FileExportRecord;

    /**
     * DataDataSetList
     */
    @TableField(exist = false)
    private List<DataDataSet> dataDataSetList;

    /**
     * 训练日志信息ID。
     */
    private Long trainInfoId;

    /**
     * 训练开始时间。
     */
    private Date trainStartTime;

    /**
     * 训练结束时间。
     */
    private Date trainEndTime;

    /**
     * 使用时间段
     */
    private String timePeriods;

    /**
     * train_task_name / train_task_type / train_task_state / train_task_describe / train_task_method LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    /**
     * 模型版本
     */
    @TableField(exist = false)
    private String version;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * trainStartTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String trainStartTimeStart;

    /**
     * trainStartTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String trainStartTimeEnd;

    /**
     * trainEndTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String trainEndTimeStart;

    /**
     * trainEndTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String trainEndTimeEnd;

    /**
     * ModelWeightMerge。
     */
    @RelationOneToMany(
            masterIdField = "id",
            slaveModelClass = ModelWeightMerge.class,
            slaveIdField = "trainTaskId"
    )
    @TableField(exist = false)
    private List<ModelWeightMerge> modelWeightMergeList;

    @Override
    public TrainTask clone() {
        try {
            return (TrainTask) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    @Mapper
    public interface TrainTaskModelMapper extends BaseModelMapper<TrainTaskVo, TrainTask> {
    }
    public static final TrainTaskModelMapper INSTANCE = Mappers.getMapper(TrainTaskModelMapper.class);
}
