package com.supie.webadmin.app.llmService.dto;

import com.supie.common.core.validator.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ComputingPowerManagementDto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ComputingPowerManagementDto对象")
@Data
public class ComputingPowerManagementDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 服务器id。
     */
    @Schema(description = "服务器id")
    private Long remoteHostId;

    /**
     * 备注。
     */
    @Schema(description = "备注")
    private String computingPowerRemarks;


    /**
     * updateTime 。
     */
    @Schema(description = "updateTime")
    private Data updateTime;


    /**
     * UUID。
     */
    @Schema(description = "UUID")
    private String uuid;

    /**
     * 许可证状态。
     */
    @Schema(description = "许可证状态")
    private Integer dcgmFiDevVgpuLicenseStatus;

    /**
     * 设备。
     */
    @Schema(description = "设备")
    private String device;


    /**
     * 是否正在训练
     */
    @Schema(description = "是否正在训练")
    private Integer isTrain;

    /**
     * 节点名。
     */
    @Schema(description = "节点名")
    private String modelname;

    /**
     * 主机名。
     */
    @Schema(description = "主机名")
    private String hostname;

    /**
     * 设备ip。
     */
    @Schema(description = "设备ip")
    private String deviceIp;
    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * dcgmFiDevSmClock 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevSmClock 范围过滤起始值(>=)")
    private Integer dcgmFiDevSmClockStart;

    /**
     * dcgmFiDevSmClock 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevSmClock 范围过滤结束值(<=)")
    private Integer dcgmFiDevSmClockEnd;

    /**
     * dcgmFiDevMemClock 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevMemClock 范围过滤起始值(>=)")
    private Integer dcgmFiDevMemClockStart;

    /**
     * dcgmFiDevMemClock 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevMemClock 范围过滤结束值(<=)")
    private Integer dcgmFiDevMemClockEnd;

    /**
     * dcgmFiDevTemp 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevTemp 范围过滤起始值(>=)")
    private Integer dcgmFiDevTempStart;

    /**
     * dcgmFiDevTemp 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevTemp 范围过滤结束值(<=)")
    private Integer dcgmFiDevTempEnd;

    /**
     * dcgmFiDevPowerUsage 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevPowerUsage 范围过滤起始值(>=)")
    private Double dcgmFiDevPowerUsageStart;

    /**
     * dcgmFiDevPowerUsage 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevPowerUsage 范围过滤结束值(<=)")
    private Double dcgmFiDevPowerUsageEnd;

    /**
     * dcgmFiDevPcieReplayCounter 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevPcieReplayCounter 范围过滤起始值(>=)")
    private Integer dcgmFiDevPcieReplayCounterStart;

    /**
     * dcgmFiDevPcieReplayCounter 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevPcieReplayCounter 范围过滤结束值(<=)")
    private Integer dcgmFiDevPcieReplayCounterEnd;

    /**
     * dcgmFiDevUtil 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevUtil 范围过滤起始值(>=)")
    private Double dcgmFiDevUtilStart;

    /**
     * dcgmFiDevUtil 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevUtil 范围过滤结束值(<=)")
    private Double dcgmFiDevUtilEnd;

    /**
     * dcgmFiDevMemCopyUtil 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevMemCopyUtil 范围过滤起始值(>=)")
    private Double dcgmFiDevMemCopyUtilStart;

    /**
     * dcgmFiDevMemCopyUtil 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevMemCopyUtil 范围过滤结束值(<=)")
    private Double dcgmFiDevMemCopyUtilEnd;

    /**
     * dcgmFiDevEncUtil 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevEncUtil 范围过滤起始值(>=)")
    private Double dcgmFiDevEncUtilStart;

    /**
     * dcgmFiDevEncUtil 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevEncUtil 范围过滤结束值(<=)")
    private Double dcgmFiDevEncUtilEnd;

    /**
     * dcgmFiDevDecUtil 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevDecUtil 范围过滤起始值(>=)")
    private Double dcgmFiDevDecUtilStart;

    /**
     * dcgmFiDevDecUtil 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevDecUtil 范围过滤结束值(<=)")
    private Double dcgmFiDevDecUtilEnd;

    /**
     * dcgmFiDevXidErrors 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevXidErrors 范围过滤起始值(>=)")
    private Integer dcgmFiDevXidErrorsStart;

    /**
     * dcgmFiDevXidErrors 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevXidErrors 范围过滤结束值(<=)")
    private Integer dcgmFiDevXidErrorsEnd;

    /**
     * dcgmFiDevFbFree 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevFbFree 范围过滤起始值(>=)")
    private Integer dcgmFiDevFbFreeStart;

    /**
     * dcgmFiDevFbFree 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevFbFree 范围过滤结束值(<=)")
    private Integer dcgmFiDevFbFreeEnd;

    /**
     * dcgmFiDevFbUsed 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevFbUsed 范围过滤起始值(>=)")
    private Integer dcgmFiDevFbUsedStart;

    /**
     * dcgmFiDevFbUsed 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevFbUsed 范围过滤结束值(<=)")
    private Integer dcgmFiDevFbUsedEnd;

    /**
     * dcgmFiDevNvlinkBandwidthTotal 范围过滤起始值(>=)。
     */
    @Schema(description = "dcgmFiDevNvlinkBandwidthTotal 范围过滤起始值(>=)")
    private Integer dcgmFiDevNvlinkBandwidthTotalStart;

    /**
     * dcgmFiDevNvlinkBandwidthTotal 范围过滤结束值(<=)。
     */
    @Schema(description = "dcgmFiDevNvlinkBandwidthTotal 范围过滤结束值(<=)")
    private Integer dcgmFiDevNvlinkBandwidthTotalEnd;

    /**
     * computing_power_remarks LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
