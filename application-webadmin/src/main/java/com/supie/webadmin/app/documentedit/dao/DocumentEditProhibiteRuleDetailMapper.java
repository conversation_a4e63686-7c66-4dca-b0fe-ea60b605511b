package com.supie.webadmin.app.documentedit.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.documentedit.model.DocumentEditProhibiteRuleDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档编辑禁止规则详细信息表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@EnableDataPerm
public interface DocumentEditProhibiteRuleDetailMapper extends BaseDaoMapper<DocumentEditProhibiteRuleDetail> {

    /**
     * 批量插入对象列表。
     *
     * @param documentEditProhibiteRuleDetailList 新增对象列表。
     */
    void insertList(List<DocumentEditProhibiteRuleDetail> documentEditProhibiteRuleDetailList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param documentEditProhibiteRuleDetailFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<DocumentEditProhibiteRuleDetail> getDocumentEditProhibiteRuleDetailList(
            @Param("documentEditProhibiteRuleDetailFilter") DocumentEditProhibiteRuleDetail documentEditProhibiteRuleDetailFilter, @Param("orderBy") String orderBy);
}
