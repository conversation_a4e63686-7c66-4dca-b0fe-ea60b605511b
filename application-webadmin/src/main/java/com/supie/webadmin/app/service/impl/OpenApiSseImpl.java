package com.supie.webadmin.app.service.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.TokenDataUtil;
import com.supie.common.pythonclient.ApiUrlEnums.ChatApiEnums;
import com.supie.common.pythonclient.model.PythonResult;
import com.supie.common.pythonclient.service.PythonClientService;
import com.supie.webadmin.app.model.ModelInfo;
import com.supie.webadmin.app.service.ModelConfigService;
import com.supie.webadmin.app.service.OpenApi;
import com.supie.webadmin.app.service.OpenApiService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service("openApiSseService")
public class OpenApiSseImpl extends OpenApi implements OpenApiService {

    private static final CopyOnWriteArrayList<SseEmitter> EMITTER_LIST = new CopyOnWriteArrayList<>();

    @Autowired
    public OpenApiSseImpl(PythonClientService pythonClientService, ModelConfigService modelConfigService, RedissonClient redissonClient) {
        super(pythonClientService, modelConfigService, redissonClient);
    }

    @Override
    public Object chatV1(ModelInfo modelInfo, JSONArray messages, Integer max_tokens, JSONArray stop, Double temperature, Double top_p, Integer top_k, Double frequency_penalty, Integer n, JSONObject response_format, JSONArray tools) {
        Map<String, Object> requestBody = buildRequestBody(modelInfo, messages, max_tokens, temperature, top_p, n);
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        EMITTER_LIST.add(emitter);
        emitter.onCompletion(() -> EMITTER_LIST.remove(emitter));
        emitter.onTimeout(() -> EMITTER_LIST.remove(emitter));
        emitter.onError(e -> EMITTER_LIST.remove(emitter));
        TokenData tokenData = TokenData.takeFromRequest();
        new Thread(() -> {
            try {
                TokenDataUtil.setTokenDataOfThisThread(tokenData);
                requestChat(requestBody, emitter);
                emitter.send(" [DONE]", MediaType.TEXT_PLAIN);
                emitter.complete();
            } catch (Exception e) {
                log.error("对话失败：{}", e.getMessage(), e);
                emitter.completeWithError(e);
            } finally {
                TokenDataUtil.removeTokenDataOfThisThread();
            }
        }).start();
        return emitter;
    }

    private void requestChat(Map<String, Object> requestBody, SseEmitter emitter) {
        log.error("requestBody ---> {}", JSON.toJSONString(requestBody));
        pythonClientService.sse(ChatApiEnums.BASE_CHAT, null, null, requestBody, msg -> {
            try {
                PythonResult pythonResult = JSON.parseObject(msg, PythonResult.class);
                if (pythonResult.isSuccess()) {
                    try {
                        Map<String, Object> data = (Map<String, Object>) pythonResult.getData();
                        String chunk = data.get("chunk").toString();
                        if (!chunk.isEmpty()) {
                            String inTokens = data.getOrDefault("inputTokens", "").toString();
                            String outTokens = data.getOrDefault("outputTokens", "").toString();
                            if (inTokens.isEmpty() && outTokens.isEmpty()) {
//                                emitter.send(JSON.toJSONString(ResponseResult.success(chunk)));
                                Delta delta = new Delta();
                                delta.setContent(chunk);
                                delta.setRole("assistant");
                                Choice choice = new Choice();
                                choice.setDelta(delta);
                                ChatMessage chatMessage = new ChatMessage();
                                chatMessage.setId(UUID.randomUUID().toString());
                                chatMessage.setChoices(List.of(choice));
                                String jsonString = JSON.toJSONString(chatMessage);
                                log.info("返回的数据 ---> " + jsonString);
                                emitter.send(jsonString, MediaType.TEXT_PLAIN);
                                Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(jsonString);
//                                if (matcher.find()) {
//                                    String extractedData = matcher.group(1);
//                                    emitter.send(" " + extractedData, MediaType.TEXT_PLAIN);
//                                }
                            } else if (!inTokens.isEmpty() && !outTokens.isEmpty()) {
                                //                                inputTokens.set(Integer.valueOf(inTokens));
                                //                                outputTokens.set(Integer.valueOf(outTokens));
                            }
                        }
                    } catch (IOException e) {
                        throw new MyRuntimeException(e);
                    }
                } else {
                    throw new MyRuntimeException(pythonResult.getErrMsgInfo());
                }
            } catch (MyRuntimeException e) {
                emitter.completeWithError(e);
            }
        }, com -> {}, error -> {
            emitter.completeWithError(error);
            throw new MyRuntimeException(error);
        });
    }

}

@Data
class ChatMessage {
    private String id;
    private List<Choice> choices;
    private List<ToolCall> tool_calls;
    private Usage usage;
    private Integer created;
    private String model;
    private String object;
}
@Data
class Usage {
    private Integer prompt_tokens;
    private Integer completion_tokens;
    private Integer total_tokens;
}
@Data
class ToolCall {
    private String id;
    private String type;
    private Function function;
}
@Data
class Function {
    private String name;
    private String arguments;
}
@Data
class Choice {
//    private Message message;
//    private String finish_reason;
    /**
     *     {
     *       "index": 0,
     *       "delta": {
     *         "content": null,
     *         "reasoning_content": "问的是",
     *         "role": "assistant"
     *       },
     *       "finish_reason": null,
     *       "content_filter_results": {
     *         "hate": {
     *           "filtered": false
     *         },
     *         "self_harm": {
     *           "filtered": false
     *         },
     *         "sexual": {
     *           "filtered": false
     *         },
     *         "violence": {
     *           "filtered": false
     *         }
     *       }
     *     }
     */
    private Delta delta;
}
@Data
class Delta {
    private String role = "assistant";
    private String content;
    private String reasoning_content;
}
