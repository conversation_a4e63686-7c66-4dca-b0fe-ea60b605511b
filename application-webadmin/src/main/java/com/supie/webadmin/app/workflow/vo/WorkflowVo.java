package com.supie.webadmin.app.workflow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.base.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * WorkflowVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "WorkflowVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowVo extends BaseVo {

    /**
     * 是公文模板
     */
    @Schema(description = "是公文模板")
    private Integer officialDoc;

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 流程名称。
     */
    @Schema(description = "流程名称")
    private String workflowName;

    /**
     * 流程描述。
     */
    @Schema(description = "流程描述")
    private String workflowDesc;

    /**
     * 是否发布（0：否，1:是）。
     */
    @Schema(description = "是否发布（0：否，1:是）")
    private Integer isPublish;

    /**
     * el规则表达式 el_data。
     */
    @Schema(description = "el规则表达式")
    private String elData;

    /**
     * 工作流配置。
     */
    @Schema(description = "工作流配置")
    private String workflowConfig;

//    /**
//     * 已经发布的el规则表达式。
//     */
//    @Schema(description = "已经发布的el规则表达式")
//    private String elDataWasPublish;
//
//    /**
//     * 已经发布的工作流配置。
//     */
//    @Schema(description = "已经发布的工作流配置")
//    private String workflowConfigWasPublish;

    /**
     * 流程版本。
     */
    @Schema(description = "流程版本")
    private Integer workflowVersion;

    /**
     * 输入参数JSON。
     */
    @Schema(description = "输入参数JSON")
    private String workflowInputParameter;

    /**
     * 输出参数JSON。
     */
    @Schema(description = "输出参数JSON")
    private String workflowOutputParameter;

    /**
     * 已发布的输入参数JSON。
     */
    @Schema(description = "已发布的输入参数JSON")
    private String workflowInputParams;

    /**
     * 已发布的输出参数JSON。
     */
    @Schema(description = "已发布的输出参数JSON")
    private String workflowOutputParams;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     *创建用户id字典。
     */
    @Schema(description = "创建用户id字典")
    private Map<String, Object> createUserIdDictMap;

}
