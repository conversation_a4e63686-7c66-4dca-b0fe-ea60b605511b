package com.supie.webadmin.app.llmService.service;

import cn.hutool.json.JSONArray;
import com.supie.common.core.base.service.IBaseService;
import com.supie.webadmin.app.llmService.model.RemoteHost;

import java.util.List;
import java.util.Map;

/**
 * 服务管理-远程主机配置数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
public interface RemoteHostService extends IBaseService<RemoteHost, Long> {

    /**
     * 保存新增对象。
     *
     * @param remoteHost 新增对象。
     * @return 返回新增对象。
     */
    RemoteHost saveNew(RemoteHost remoteHost);

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param remoteHostList 新增对象列表。
     */
    void saveNewBatch(List<RemoteHost> remoteHostList);

    /**
     * 更新数据对象。
     *
     * @param remoteHost         更新的对象。
     * @param originalRemoteHost 原有数据对象。
     * @return 成功返回true，否则false。
     */
    boolean update(RemoteHost remoteHost, RemoteHost originalRemoteHost);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getRemoteHostListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<RemoteHost> getRemoteHostList(RemoteHost filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getRemoteHostList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<RemoteHost> getRemoteHostListWithRelation(RemoteHost filter, String orderBy,String useEndTimeStart,String useEndTimeEnd);

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    List<RemoteHost> getGroupedRemoteHostListWithRelation(
            RemoteHost filter, String groupSelect, String groupBy, String orderBy);


    /**
     * 测试远程服务器的连接是否正常
     * @param remoteHostFilter 实体对象
     * @return 返回测试信息
     */
    String hostConnection(RemoteHost remoteHostFilter);

    String unencryptedConnection(RemoteHost remoteHostFilter);

    RemoteHost saveMonitor(RemoteHost remoteHost);

    List<RemoteHost> selectAll();

    void downloadChunk(Long remoteHostId,Long modelId,String path) ;

    /**
     * 查询用户已经申请的算力。
     *
     * @param userId 用户id。
     * @return 应答结果对象，包含查询结果集。
     */
    List<RemoteHost> listComputingPowerApply(Long userId);
    List<RemoteHost> listComputingPowerApply();

    /**
     * 获取筛选条件信息
     *
     * @return
     */
    Map<String, Object> getFilterCriteria();

    Map<String, List<Map<String,Long>>>computingPowerMonitoring(Long userId);

    List<Map<String,Object>> taskExecutionStatus(Long userId, Integer type, String cardName);

    List<Map<String,Object>> exclusiveCardAllocationSituation(Long userId, String modelName);

    void deleteTask(Long taskId, Integer type);



    /**
     * 判断设备是否占用了指定的卡号并打印占用信息
     * @param devices 设备的 JSON 数组
     */
    void checkDeviceCardOccupancy(JSONArray devices);


    List<Map<String, Object>> monitoringMetrics(RemoteHost remoteHostFilter,String orderBy);

    Map<String, Object> statisticalIndicators(Long remoteHostId, String tsStart, String tsEnd, String type,Integer interval);

    List<RemoteHost> cardComputingPowerApply(Long remoteHostId);

    Map<String, Object> cardSituation();
}
