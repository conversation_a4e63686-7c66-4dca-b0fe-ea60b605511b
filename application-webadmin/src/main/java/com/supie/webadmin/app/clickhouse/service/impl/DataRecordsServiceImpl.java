package com.supie.webadmin.app.clickhouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.Header;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.upload.BaseUpDownloader;
import com.supie.common.core.upload.UpDownloaderFactory;
import com.supie.common.core.upload.UploadStoreInfo;
import com.supie.common.core.util.JsonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.webadmin.app.clickhouse.dao.DataRecordsMapper;
import com.supie.webadmin.app.clickhouse.model.DataRecords;
import com.supie.webadmin.app.clickhouse.model.DataSetCount;
import com.supie.webadmin.app.clickhouse.service.DataRecordsService;
import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.data.model.TrainDataFile;
import com.supie.webadmin.app.data.service.impl.DataDataSetServiceImpl;
import com.supie.webadmin.app.llmService.model.RemoteHost;
import com.supie.webadmin.app.llmService.service.RemoteHostService;
import com.supie.webadmin.app.llmTrain.model.TrainTask;
import com.supie.webadmin.app.llmTrainImpl.NfsPathMgmt;
import com.supie.webadmin.app.pythonClient.ApiUrlEnums.ClickshouseUrlEnums;
import com.supie.webadmin.app.pythonClient.model.PythonResponse;
import com.supie.webadmin.app.pythonClient.service.PythonClientService;
import com.supie.webadmin.app.util.RemoteSshUtils;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据管理-数据表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("dataRecordsService")
@MyDataSource(DataSourceType.CLICKHOME)
public class DataRecordsServiceImpl extends BaseService<DataRecords, Long> implements DataRecordsService {

    @Resource
    private DataRecordsMapper dataRecordsMapper;
    @Resource
    private NfsPathMgmt nfsPathMgmt;
    @Resource
    private RemoteSshUtils remoteSshUtils;
    @Resource
    private RemoteHostService remoteHostService;
    @Resource
    private PythonClientService pythonClientService;
    @Resource
    private ApplicationConfig appConfig;
    @Resource
    private UpDownloaderFactory upDownloaderFactory;
    @Lazy
    @Autowired
    private DataDataSetServiceImpl dataDataSetService;


    private static final Pattern PORT_PATTERN = Pattern.compile("[!@#$%^&*()_+|~\\-=`{}\\[\\]:\";'<>?,./]");

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DataRecords> mapper() {
        return dataRecordsMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param dataRecords 新增对象。
     * @return 返回新增对象。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataRecords saveNew(DataRecords dataRecords) {
        dataRecordsMapper.insert(this.buildDefaultValue(dataRecords));
        return dataRecords;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param dataRecordsList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DataRecords> dataRecordsList) {
        if (CollUtil.isNotEmpty(dataRecordsList)) {
            dataRecordsList.forEach(this::buildDefaultValue);
            dataRecordsMapper.insertList(dataRecordsList);
        }
    }


    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param dataRecordsList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatchByTaken(List<DataRecords> dataRecordsList) {
        if (CollUtil.isNotEmpty(dataRecordsList)) {
            dataRecordsMapper.insertList(dataRecordsList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param dataRecords         更新的对象。
     * @param originalDataRecords 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(DataRecords dataRecords, DataRecords originalDataRecords) {
        MyModelUtil.fillCommonsForUpdate(dataRecords, originalDataRecords);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<DataRecords> uw = this.createUpdateQueryForNullValue(dataRecords, dataRecords.getId());
        return dataRecordsMapper.update(dataRecords, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
//        return dataRecordsMapper.deleteById(id) == 1;

        DataRecords dataRecords = new DataRecords();
        dataRecords.setId(id);
        return dataRecordsMapper.deleteByDataRecords(dataRecords) == 1;

    }

    @Override
    public DataRecords getDataRecordsById(Long id) {
        return dataRecordsMapper.getDataRecordsById(id);
    }
    @Override
    public List<DataRecords> getDataRecordsListByDataSetId(List<Long> dataSetIdList) {
        return dataRecordsMapper.getDataRecordsListByDataSetId(dataSetIdList);
    }
    /**
     * 根据dataSetId获取指定数据。
     *
     * @param dataSetId 主键Id。
     * @return 成功返回true，否则false。
     */
//    @Override
//    public List<DataRecords> getDataRecordsByDataSetId(Long dataSetId) {
//        return dataRecordsMapper.getDataRecordsListByDataSetId(dataSetId);
//    }

    /**
     * 根据dataSetId删除指定数据。
     *
     * @param dataSetId 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long removeByDataSetId(Long dataSetId) {
        DataRecords dataRecords = new DataRecords();
        dataRecords.setDataSetId(dataSetId);
        return dataRecordsMapper.deleteByDataRecords(dataRecords);
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getDataRecordsListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<DataRecords> getDataRecordsList(DataRecords filter, String orderBy) {
        return dataRecordsMapper.getDataRecordsList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getDataRecordsList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<DataRecords> getDataRecordsListWithRelation(DataRecords filter, String orderBy) {
        List<DataRecords> resultList = dataRecordsMapper.getDataRecordsList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<DataRecords> getGroupedDataRecordsListWithRelation(
            DataRecords filter, String groupSelect, String groupBy, String orderBy) {
        List<DataRecords> resultList =
                dataRecordsMapper.getGroupedDataRecordsList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }


    public String generateDatasetFile(TrainTask trainTask) {
        String commonParameterJson = trainTask.getCommonParameterJson();
        JSONObject json = JSONUtil.parseObj(commonParameterJson);
        JSONArray devices = json.getJSONArray("devices");
        ArrayList<String> ids = new ArrayList<>();
        for (int i = 0; i < devices.size(); i++) {
            JSONObject device = devices.getJSONObject(i);
            String id = device.getStr("id");
            ids.add(id);
        }
        return generateDatasetFile(
                trainTask.getTrainFramework(),
                trainTask.getModelCode(),
                dataDataSetService.getDataDataSetListByTrainTaskId(trainTask.getId()),
                ids);
    }

    @Override
    public List<DataSetCount> groupByDataMarkTaskId() {
        return dataRecordsMapper.groupByDataMarkTaskId();
    }

    @Override
    public List<DataSetCount> groupByDataSetId(Set<Long> dataSetIdSet) {
        return dataRecordsMapper.groupByDataSetId(dataSetIdSet);
    }

    @Override
    public Long deleteByDataRecords(DataRecords dataRecordsFilter) {
        return dataRecordsMapper.deleteByDataRecords(dataRecordsFilter);
    }


    /**
     * 构建数据集到服务器中
     */
    public String generateDatasetFile(Integer type, String dockerName, List<DataDataSet> dataDataSetList, List<String> ids) {
        // 存放的文件路径
        String filePath;
        if (type.equals(1)) {
            filePath = nfsPathMgmt.getDataSetPath(dockerName);
        } else if (type.equals(2)) {
            filePath = nfsPathMgmt.getMindformersDataSetPath(dockerName);
        } else {
            throw new RuntimeException();
        }
        ids.forEach(remoteHostId -> {
            RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
            // docker .env 内的 OPTION 中的 dataset_dir 为 ./source/input/dataSet
            if (!remoteHost.getIsMasterHost().equals(1)) {
                remoteSshUtils.remoteFolderIsExits(Long.valueOf(remoteHostId),remoteHost.getResourceDirectory() + filePath.substring(1));
            } else {
                File directory = new File(filePath);
                if (!directory.exists()) {
                    boolean created = directory.mkdirs();
                    if (created) {
                        log.info("文件夹目录已成功创建！");
                    } else {
                        log.error("无法创建文件夹目录！");
                    }
                } else {
                    log.info("文件夹目录已经存在！");
                }
            }
        });
        String suffix = ".json";
        ArrayList<HashMap<String,Object>> options = new ArrayList<>();
        for (DataDataSet dataDataSet : dataDataSetList) {
            DataRecords dataRecords = new DataRecords();
            dataRecords.setDataSetId(dataDataSet.getId());

            // 构建query查询数据量
            Long count = dataRecordsMapper.selectCount(new LambdaQueryWrapper<DataRecords>()
                    .eq(DataRecords::getDataSetId, dataDataSet.getId()));
            if (count == 0) {
                continue;
            }
            JSONArray jsonArray = new JSONArray();
            int pageSize = 20000;
            int pageCount = (int) StrictMath.ceil((double) count / pageSize);
            Integer dataFormat = dataDataSet.getDataFormat();
            Integer dataType = dataDataSet.getDataType();
            boolean isImageMultimodal = false; // 是否为图片多模态
            for (int i = 1; i <= pageCount; i++) {
//                int currentPageSize =((i == pageCount) ? (int) (count % pageSize) : pageSize + 1);
                int currentPageSize = (i == pageCount && count % pageSize != 0) ? (int) (count % pageSize) : pageSize;
                int pageStart = (i - 1) * pageSize;

                DataRecords dataRecords1 = new DataRecords();
                dataRecords1.setDataSetId(dataDataSet.getId());
                List<DataRecords> recordsList = dataRecordsMapper.getDataRecordsListByPag(dataRecords1,null,pageStart,currentPageSize);
                for (DataRecords dataRecord : recordsList) {
                    String dataContent = dataRecord.getDataContent();
                    // 将 DataRecord 的 dataContent 处理成训练时需要的数据格式
                    JSONObject dataContentJsonOfTran = processDataContentToTrainData(dockerName, dataContent, dataFormat, dataType);
                    if (dataContentJsonOfTran.containsKey("images")) {
                        isImageMultimodal = true;
                    }
                    jsonArray.put(dataContentJsonOfTran);
                }
            }
            Date date = new Date();
            long time = date.getTime();
            String fileName = "dataSet_" + time;
            log.warn("生成训练集文件：{}", fileName);
            ids.forEach(remoteHostId -> {
                RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
                if (!remoteHost.getIsMasterHost().equals(1)) {
                    byte[] bytes = jsonArray.toString().getBytes(StandardCharsets.UTF_8);
                    InputStream is = new ByteArrayInputStream(bytes);
                    log.warn("使用ssh进行文件传输(stream流形式，传输其他文件)");
                    remoteSshUtils.transferFile(Long.valueOf(remoteHostId),remoteHost.getResourceDirectory() + filePath.substring(1) + fileName + suffix, is);
                } else {
                    try (FileWriter writer = new FileWriter(filePath + fileName + suffix)) {
                        writer.write(jsonArray.toString());
                        writer.flush();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            HashMap<String, Object> option = new HashMap<>(3);
            option.put("file_name", fileName);
            if (dataFormat != null) {
                if (dataType.equals(1)){
                    Map<String, String> hashMap = new HashMap<>();
                    hashMap.put("prompt","text");
                    option.put("columns",hashMap);
                }else {
                    if (dataFormat.equals(2)){
                        option.put("formatting","sharegpt");
                    }
                    if (dataType.equals(3)){
                        option.put("ranking",true);
                    }
                    HashMap<String, String> hashMap = new HashMap<>();
                    if (dataFormat.equals(1) && dataType.equals(1)){
                        hashMap.put("text","text");
                    }
                    if (dataFormat.equals(1) && dataType.equals(2)){
                        hashMap.put("prompt","instruction");
                        hashMap.put("query","input");
                        hashMap.put("response","output");
                        hashMap.put("system","system");
                        hashMap.put("history","history");
                    }
                    if (dataFormat.equals(1) && dataType.equals(3)){
                        hashMap.put("prompt","instruction");
                        hashMap.put("input", "input");
                        hashMap.put("chosen","chosen");
                        hashMap.put("rejected","rejected");
                        hashMap.put("history","history");
                    }
                    if (dataFormat.equals(1) && dataType.equals(4)){
                        hashMap.put("prompt","instruction");
                        hashMap.put("input","input");
                        hashMap.put("output","output");
                        hashMap.put("history","history");
                        hashMap.put("kto_tag","tag");
                    }
                    if (dataFormat.equals(2) && dataType.equals(2)){
                        hashMap.put("messages","conversations");
                        hashMap.put("system","system");
                        hashMap.put("tools","tools");
                    }
                    if (dataFormat.equals(2) && dataType.equals(3)){
                        hashMap.put("messages","conversations");
                        hashMap.put("chosen","chosen");
                        hashMap.put("rejected","rejected");
                    }
                    if (dataFormat.equals(2) && dataType.equals(4)){
                        hashMap.put("messages","conversations");
                        hashMap.put("kto_tag","tag");
                    }
                    if (isImageMultimodal) {
                        // 图片多模态训练
                        hashMap.put("messages","conversations");
                        hashMap.put("images","images");
                    }
                    if (!hashMap.isEmpty()){
                        option.put("columns",hashMap);
                    }
                }
            }
            options.add(option);
        }

        String dataSetInfoFilePath = filePath + "dataset_info.json";

        JSONObject newDataObject = new JSONObject();
        File dataSetInfoFile = new File(dataSetInfoFilePath);

        // 检查是否存在 dataSet_info.json 文件
        if (dataSetInfoFile.exists()) {
            // 如果文件存在，则读取现有内容
            try (BufferedReader reader = new BufferedReader(new FileReader(dataSetInfoFile))) {
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line);
                }
                newDataObject = new JSONObject(content.toString());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        // 构造要追加的数据
        StringBuilder returnsTheFileName = new StringBuilder();
        for (int i = 0; i < options.size(); i++) {
            HashMap<String, Object> option = options.get(i);
            String fileName = (String) option.get("file_name");
            returnsTheFileName.append(fileName);
            if (i < options.size() - 1) {
                returnsTheFileName.append(",");
            }
            option.put("file_name",fileName+suffix);
            newDataObject.putOpt(fileName,option);
        }
        // 将更新后的 JSON 数组写回到文件中
        JSONObject finalNewDataObject = newDataObject;
        ids.forEach(remoteHostId -> {
            RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
            if (!remoteHost.getIsMasterHost().equals(1)) {
                byte[] bytes = finalNewDataObject.toString().getBytes(StandardCharsets.UTF_8);
                InputStream is = new ByteArrayInputStream(bytes);
                remoteSshUtils.transferFile(Long.valueOf(remoteHostId),
                        remoteHostService.getById(remoteHostId).getResourceDirectory() + dataSetInfoFilePath.substring(1), is);
            } else {
                try (FileWriter writer = new FileWriter(dataSetInfoFilePath)) {
                    writer.write(finalNewDataObject.toString());
                    writer.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });

        return returnsTheFileName.toString();
    }

    /**
     * 将 DataRecord 的 dataContent 处理成训练时需要的数据格式
     * @param dataContent
     * @param dataFormat 1.Alpaca 2.Sharegpt 3.基准测试集。
     * @param dataType 1: 预训练数据集 2: 微调数据集 3: 偏好数据集 4: KTO 数据集 5:基准测试集。
     * @return
     */
    private @NotNull JSONObject processDataContentToTrainData(String dockerName, String dataContent, Integer dataFormat, Integer dataType) {
        JSONObject jsonObject = new JSONObject(dataContent);
        if (dataFormat != null) {
            if (dataFormat.equals(1) && dataType.equals(2)) {
                if (!jsonObject.containsKey("instruction")) {
                    jsonObject.put("instruction", "");
                }
                if (!jsonObject.containsKey("input")) {
                    jsonObject.put("input", "");
                }
                if (!jsonObject.containsKey("output")) {
                    jsonObject.put("output", "");
                }
                if (!jsonObject.containsKey("system")) {
                    jsonObject.put("system", "");
                }
                if (!jsonObject.containsKey("history")) {
                    jsonObject.put("history", new JSONArray());
                }
            }
            if (dataFormat.equals(1) && dataType.equals(3)) {
                if (!jsonObject.containsKey("instruction")) {
                    jsonObject.put("instruction", "");
                }
                if (!jsonObject.containsKey("input")) {
                    jsonObject.put("input", "");
                }
                if (!jsonObject.containsKey("chosen")) {
                    jsonObject.put("chosen", "");
                }
                if (!jsonObject.containsKey("rejected")) {
                    jsonObject.put("rejected", "");
                }
                if (!jsonObject.containsKey("history")) {
                    jsonObject.put("history", new JSONArray());
                }
            }
            if (dataFormat.equals(1) && dataType.equals(4)) {
                if (!jsonObject.containsKey("instruction")) {
                    jsonObject.put("instruction", "");
                }
                if (!jsonObject.containsKey("input")) {
                    jsonObject.put("input", "");
                }
                if (!jsonObject.containsKey("output")) {
                    jsonObject.put("output", "");
                }
                if (!jsonObject.containsKey("history")) {
                    jsonObject.put("history", new JSONArray());
                }
                if (!jsonObject.containsKey("kto_tag")) {
                    jsonObject.put("tag", "");
                }
            }
            if (dataFormat.equals(2) && dataType.equals(2)) {
                if (!jsonObject.containsKey("conversations")) {
                    jsonObject.put("conversations", "");
                }
                if (!jsonObject.containsKey("system")) {
                    jsonObject.put("system", "");
                }
                if (!jsonObject.containsKey("tools")) {
                    jsonObject.put("tools", "");
                }
            }
            if (dataFormat.equals(2) && dataType.equals(3)) {
                if (!jsonObject.containsKey("conversations")) {
                    jsonObject.put("conversations", "");
                }
                if (!jsonObject.containsKey("chosen")) {
                    jsonObject.put("chosen", "");
                }
                if (!jsonObject.containsKey("rejected")) {
                    jsonObject.put("rejected", "");
                }
            }
            if (dataFormat.equals(2) && dataType.equals(4)) {
                if (!jsonObject.containsKey("conversations")) {
                    jsonObject.put("tag", "");
                }
            }
        }
        // 判断是否为多模态训练数据，是则将文件存储至相应目录
        if (jsonObject.containsKey("images")) {
            List<Map<String, Object>> imageList = JsonUtil.jsonStrToListMap(JSONUtil.toJsonStr(jsonObject.getJSONArray("images")));
            List<String> imagePathList = new ArrayList<>();

            Path dataSetImagesPath = Paths.get(nfsPathMgmt.getDataSetImagesPath(dockerName));
            // 如果路径文件夹不存在则创建
            if (!Files.exists(dataSetImagesPath)) {
                try {
                    Files.createDirectories(dataSetImagesPath);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            String dataSetImagesPathInDocker = nfsPathMgmt.getDataSetImagesPathInDocker();
            int index = 0;
            for (Map<String, Object> image : imageList) {
                index++;
                // 1、获取到图片相关信息
//                Long id = Long.valueOf(image.get("id").toString());
//                String fileName = image.get("fileName").toString();
                String fileJsonStr = image.get("fileJson").toString();
                if (!JsonUtil.isValidJson(fileJsonStr)) {
                    log.warn("训练数据生成错误：{}", JsonUtil.toJsonStr(image));
                    continue;
                }
                Map<String, Object> fileJsonMap = JsonUtil.jsonStrToMap(fileJsonStr);
                String fieldName = fileJsonMap.get("fieldName").toString();
                String filename = fileJsonMap.get("filename").toString();
                Boolean asImage = Boolean.valueOf(fileJsonMap.get("asImage").toString());
                // 2、将图片下载至对应的位置
                UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(TrainDataFile.class, fieldName);
                BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
                try {
                    try (InputStream imageInputStream = upDownloader.downloadInternalInputStream(appConfig.getUploadFileBaseDir(), TrainDataFile.class.getSimpleName(), fieldName, filename, asImage)) {
                        // 将图片 imageInputStream 存储至 dataSetImagesPath 目录下
                        Files.copy(imageInputStream, dataSetImagesPath.resolve(filename), StandardCopyOption.REPLACE_EXISTING);
                    } catch (IOException e) {
                        e.printStackTrace();
                        continue;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
                // 3、训练数据文件指定图片路径等信息
                imagePathList.add(dataSetImagesPathInDocker + filename);
            }
            // 将 jsonObject 中的 images 节点的值替换为 imagePathList（注意修改后的 jsonObject 不可存入替换数据库中的数据，否则其他引用该数据的模型无法获取到相关的图片信息）
            jsonObject.put("images", JSONUtil.parseArray(imagePathList));
        }
        return jsonObject;
    }

    private DataRecords buildDefaultValue(DataRecords dataRecords) {
        MyModelUtil.fillCommonsForInsert(dataRecords);
        dataRecords.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataRecords;
    }

    /**
     * 导出数据集
     *
     * @param dataDataSet
     * @param response
     */
    @Override
    public void exportDataRecords(DataDataSet dataDataSet, HttpServletResponse response) {
        List<DataRecords> dataRecordsList = dataRecordsMapper.selectList(new LambdaQueryWrapper<DataRecords>().eq(DataRecords::getDataSetId, dataDataSet.getId()));
        // 设置响应头信息
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + dataDataSet.getDatasetName() + ".jsonl");
        try {
            // 使用PrintWriter输出流写入数据
            PrintWriter out = response.getWriter();
            try {
                for (DataRecords dataRecords : dataRecordsList) {
                    // 输出每个数据记录的JSON内容，并添加换行符
                    //                out.println(dataRecords.getDataContent());
                    out.println(JSONUtil.toJsonStr(JSONUtil.parse(dataRecords.getDataContent())));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                out.flush();
                out.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据数据集ID获取数据记录列表
     * @param dataSetId
     * @return
     */
    @Override
    public List<DataRecords> getDataRecordsByDataSetId(Long dataSetId) {
        List<DataRecords> dataRecordsList = dataRecordsMapper.selectListByDataSetId(dataSetId);
        if (dataRecordsList == null) {
            log.error("根据数据集ID获取数据记录列表失败，获取内容为空");
            return null;
        }
        return dataRecordsList;
    }

    @Override
    public boolean updateDataRecords(DataRecords dataRecords, DataRecords originalDataRecords) {
        MyModelUtil.fillCommonsForUpdate(dataRecords, originalDataRecords);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        return dataRecordsMapper.updateDataRecords(dataRecords) == 1;
    }

    @Override
    public void saveNewBatch(List<DataRecords> dataRecordsResultList, TokenData tokenData) {
        if (CollUtil.isNotEmpty(dataRecordsResultList)) {
            for (DataRecords dataRecords : dataRecordsResultList) {
                dataRecords = buildDefaultValue(dataRecords, tokenData);
            }
            dataRecordsMapper.insertList(dataRecordsResultList);
        }
    }
    private DataRecords buildDefaultValue(DataRecords dataRecords, TokenData tokenData) {
        MyModelUtil.fillCommonsForInsert(dataRecords, tokenData);
        dataRecords.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataRecords;
    }


    @Override
    public List<DataRecords> dataInsights(DataRecords filter, String orderBy) {
        //获取数据
        List<DataRecords> resultList = dataRecordsMapper.getDataRecordsList(filter, orderBy);
        //定义特殊字符
        List<Map> dataList = new ArrayList<>();
        //遍历列表，获取文本内容
        for (DataRecords dataRecords : resultList) {
            Map<String, Object> map = JSONUtil.toBean(dataRecords.getDataContent(), Map.class);
            Map<Long,Map<String, Object>> dataMap=new HashMap<>();
            dataMap.put(dataRecords.getId(), map);
            dataList.add(dataMap);
            String instruction = (String) map.get("instruction");
            Integer characterNumber = instruction.length();
            Integer specialCharactersNumber  = 0;
            Matcher matcher = PORT_PATTERN.matcher(instruction);
            while (matcher.find()) {
                specialCharactersNumber++;
            }
            //500条调用python接口
            if(dataList.size()==500){
                Map<String, Object> headers = new HashMap<>();
//                headers.put(String.valueOf(Header.AUTHORIZATION), authorizationKey);
                headers.put(String.valueOf(Header.CONTENT_TYPE), "application/json");

                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("data", dataList);

                PythonResponse response = pythonClientService.request(ClickshouseUrlEnums.NGRAM, headers, null, requestBody);
//                HttpResponse response = HttpRequest.of(baseUrl + "/lmd-py-ngram/ngram")
//                        .method(Method.POST)
//                        .addHeaders(headers)
//                        .body(JSONUtil.toJsonStr(requestBody)).execute();
                if (!response.isSuccess()) {
                    throw new MyRuntimeException("数据处理失败：" + response.getErrorCode() + ":" + response.getErrorMessage());
                }
                Map resultMap = (Map) response.getData();
                log.debug(JSONUtil.toJsonStr(resultMap));
            }
            Map<String, Object> headers = new HashMap<>();
//            headers.put(String.valueOf(Header.AUTHORIZATION), authorizationKey);
            headers.put(String.valueOf(Header.CONTENT_TYPE), "application/json");

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("data", dataList);

            PythonResponse response = pythonClientService.request(ClickshouseUrlEnums.NGRAM, headers, null, requestBody);
//            HttpResponse response = HttpRequest.of(baseUrl + "/lmd-py-ngram/ngram")
//                    .method(Method.POST)
//                    .addHeaders(headers)
//                    .body(JSONUtil.toJsonStr(requestBody)).execute();
            if (!response.isSuccess()) {
                throw new MyRuntimeException("数据处理失败：" + response.getErrorCode() + ":" + response.getErrorMessage());
            }
            Map resultMap = (Map) response.getData();
            log.debug(JSONUtil.toJsonStr(resultMap));
            log.debug("instruction:"+map.get("instruction")+"  "+"characterNumber:" +"    "+characterNumber+"    "+"specialCharactersNumber:"+specialCharactersNumber);
            log.debug("input:"+map.get("input"));
            log.debug("output:"+map.get("output"));
            log.debug("---------------------------------------------------------------------------------------------");
        }
        return resultList;
    }

    @Override
    public List<DataRecords> selectList(LambdaQueryWrapper<DataRecords> lambdaQueryWrapper) {
//        return dataRecordsMapper.selectCustomList(lambdaQueryWrapper);
        return super.selectList(lambdaQueryWrapper);
    }

}
