package com.supie.webadmin.app.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.supie.webadmin.app.data.model.DataResources;
import com.supie.webadmin.app.data.service.DataResourcesService;
import com.supie.webadmin.app.util.ModelConfigUtil2;
import com.supie.webadmin.interceptor.PyAuthInterface;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.app.dto.AppKnowledgeBaseFileRelationDto;
import com.supie.webadmin.app.app.model.AppKnowledgeBaseFileRelation;
import com.supie.webadmin.app.app.service.AppKnowledgeBaseFileRelationService;
import com.supie.webadmin.app.app.vo.AppKnowledgeBaseFileRelationVo;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 应用中心-知识库与文件关联表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "应用中心-知识库与文件关联表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/appKnowledgeBaseFileRelation")
public class AppKnowledgeBaseFileRelationController {

    @Autowired
    private AppKnowledgeBaseFileRelationService appKnowledgeBaseFileRelationService;
    @Autowired
    private DataResourcesService dataResourcesService;

    @Operation(summary = "文件总结")
    @PostMapping("/articleSummary")
    public ResponseResult<Void> articleSummary(@MyRequestBody List<Long> baseFileRelationIdList) {
        if (baseFileRelationIdList == null || baseFileRelationIdList.isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        appKnowledgeBaseFileRelationService.articleSummary(baseFileRelationIdList);
        return ResponseResult.success();
    }

    @Operation(summary = "文件萃取")
    @PostMapping("/fileExtraction")
    public ResponseResult<Void> fileExtraction(@MyRequestBody List<Long> baseFileRelationIdList) {
        if (baseFileRelationIdList == null || baseFileRelationIdList.isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        appKnowledgeBaseFileRelationService.fileExtraction(baseFileRelationIdList);
        return ResponseResult.success();
    }

    /**
     * 重新添加向量表。
     *
     * @param id 关联表id。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseFileRelationDto.id",
            "appKnowledgeBaseFileRelationDto.searchString",
            "appKnowledgeBaseFileRelationDto.createTimeStart",
            "appKnowledgeBaseFileRelationDto.createTimeEnd",
            "appKnowledgeBaseFileRelationDto.updateTimeStart",
            "appKnowledgeBaseFileRelationDto.updateTimeEnd",
            "appKnowledgeBaseFileRelationDto.minLengthStart",
            "appKnowledgeBaseFileRelationDto.minLengthEnd",
            "appKnowledgeBaseFileRelationDto.maxLengthStart",
            "appKnowledgeBaseFileRelationDto.maxLengthEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/reAddVector")
    public ResponseResult<Long> reAddVector(@MyRequestBody Long id) {
        if (id == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "缺失必传参数：关联表id");
        }
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.getById(id);
        // 验证父Id的数据合法性
        DataResources dataResources = null;
        if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())) {
            dataResources = dataResourcesService.getById(appKnowledgeBaseFileRelation.getBusinessFileId());
            if (dataResources == null) {
                String errorMessage = "数据验证失败，关联的数据资源表数据并不存在！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        appKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.reAddVector(appKnowledgeBaseFileRelation, dataResources);
        return ResponseResult.success(appKnowledgeBaseFileRelation.getId());
    }

    /**
     * 新增应用中心-知识库与文件关联表数据。
     *
     * @param appKnowledgeBaseFileRelationDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseFileRelationDto.id",
            "appKnowledgeBaseFileRelationDto.searchString",
            "appKnowledgeBaseFileRelationDto.createTimeStart",
            "appKnowledgeBaseFileRelationDto.createTimeEnd",
            "appKnowledgeBaseFileRelationDto.updateTimeStart",
            "appKnowledgeBaseFileRelationDto.updateTimeEnd",
            "appKnowledgeBaseFileRelationDto.minLengthStart",
            "appKnowledgeBaseFileRelationDto.minLengthEnd",
            "appKnowledgeBaseFileRelationDto.maxLengthStart",
            "appKnowledgeBaseFileRelationDto.maxLengthEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody AppKnowledgeBaseFileRelationDto appKnowledgeBaseFileRelationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appKnowledgeBaseFileRelationDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelation = MyModelUtil.copyTo(appKnowledgeBaseFileRelationDto, AppKnowledgeBaseFileRelation.class);
        // 验证父Id的数据合法性
        DataResources dataResources = null;
        if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())) {
            dataResources = dataResourcesService.getById(appKnowledgeBaseFileRelation.getBusinessFileId());
            if (dataResources == null) {
                errorMessage = "数据验证失败，关联的数据资源表数据并不存在！！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        appKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.saveNew(appKnowledgeBaseFileRelation, dataResources);
        return ResponseResult.success(appKnowledgeBaseFileRelation.getId());
    }

    /**
     * 更新应用中心-知识库与文件关联表数据。
     *
     * @param appKnowledgeBaseFileRelationDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseFileRelationDto.searchString",
            "appKnowledgeBaseFileRelationDto.createTimeStart",
            "appKnowledgeBaseFileRelationDto.createTimeEnd",
            "appKnowledgeBaseFileRelationDto.updateTimeStart",
            "appKnowledgeBaseFileRelationDto.updateTimeEnd",
            "appKnowledgeBaseFileRelationDto.minLengthStart",
            "appKnowledgeBaseFileRelationDto.minLengthEnd",
            "appKnowledgeBaseFileRelationDto.maxLengthStart",
            "appKnowledgeBaseFileRelationDto.maxLengthEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody AppKnowledgeBaseFileRelationDto appKnowledgeBaseFileRelationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appKnowledgeBaseFileRelationDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelation = MyModelUtil.copyTo(appKnowledgeBaseFileRelationDto, AppKnowledgeBaseFileRelation.class);
        AppKnowledgeBaseFileRelation originalAppKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.getById(appKnowledgeBaseFileRelation.getId());
        if (originalAppKnowledgeBaseFileRelation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // 验证父Id的数据合法性
        if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())
                && ObjectUtil.notEqual(appKnowledgeBaseFileRelation.getBusinessFileId(), originalAppKnowledgeBaseFileRelation.getBusinessFileId())) {
            if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())) {
                DataResources dataResources = dataResourcesService.getById(appKnowledgeBaseFileRelation.getBusinessFileId());
                if (dataResources == null) {
                    errorMessage = "数据验证失败，关联的父节点并不存在，请刷新后重试！";
                    return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
                }
            }
        }
        if (!appKnowledgeBaseFileRelationService.update(appKnowledgeBaseFileRelation, originalAppKnowledgeBaseFileRelation)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseFileRelationDto.searchString",
            "appKnowledgeBaseFileRelationDto.createTimeStart",
            "appKnowledgeBaseFileRelationDto.createTimeEnd",
            "appKnowledgeBaseFileRelationDto.updateTimeStart",
            "appKnowledgeBaseFileRelationDto.updateTimeEnd",
            "appKnowledgeBaseFileRelationDto.minLengthStart",
            "appKnowledgeBaseFileRelationDto.minLengthEnd",
            "appKnowledgeBaseFileRelationDto.maxLengthStart",
            "appKnowledgeBaseFileRelationDto.maxLengthEnd"})
    @Operation(summary = "无权限更新接口")
    @PyAuthInterface
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/noAuthInterfaceUpdate")
    public ResponseResult<Void> noAuthInterfaceUpdate(@MyRequestBody AppKnowledgeBaseFileRelationDto appKnowledgeBaseFileRelationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appKnowledgeBaseFileRelationDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelation = MyModelUtil.copyTo(appKnowledgeBaseFileRelationDto, AppKnowledgeBaseFileRelation.class);
        if (appKnowledgeBaseFileRelation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // 验证父Id的数据合法性
        if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())) {
            if (MyCommonUtil.isNotBlankOrNull(appKnowledgeBaseFileRelation.getBusinessFileId())) {
                DataResources dataResources = dataResourcesService.getById(appKnowledgeBaseFileRelation.getBusinessFileId());
                if (dataResources == null) {
                    errorMessage = "数据验证失败，关联的父节点并不存在，请刷新后重试！";
                    return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
                }
            }
        }
        if (!appKnowledgeBaseFileRelationService.noAuthInterfaceUpdate(appKnowledgeBaseFileRelation)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除应用中心-知识库与文件关联表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的应用中心-知识库与文件关联表列表。
     *
     * @param appKnowledgeBaseFileRelationDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<AppKnowledgeBaseFileRelationVo>> list(
            @MyRequestBody AppKnowledgeBaseFileRelationDto appKnowledgeBaseFileRelationDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelationFilter = MyModelUtil.copyTo(appKnowledgeBaseFileRelationDtoFilter, AppKnowledgeBaseFileRelation.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppKnowledgeBaseFileRelation.class);
        List<AppKnowledgeBaseFileRelation> appKnowledgeBaseFileRelationList =
                appKnowledgeBaseFileRelationService.getAppKnowledgeBaseFileRelationListWithRelation(appKnowledgeBaseFileRelationFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(appKnowledgeBaseFileRelationList, AppKnowledgeBaseFileRelation.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的应用中心-知识库与文件关联表列表。
     *
     * @param appKnowledgeBaseFileRelationDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<AppKnowledgeBaseFileRelationVo>> listWithGroup(
            @MyRequestBody AppKnowledgeBaseFileRelationDto appKnowledgeBaseFileRelationDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppKnowledgeBaseFileRelation.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, AppKnowledgeBaseFileRelation.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppKnowledgeBaseFileRelation filter = MyModelUtil.copyTo(appKnowledgeBaseFileRelationDtoFilter, AppKnowledgeBaseFileRelation.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<AppKnowledgeBaseFileRelation> resultList = appKnowledgeBaseFileRelationService.getGroupedAppKnowledgeBaseFileRelationListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, AppKnowledgeBaseFileRelation.INSTANCE));
    }

    /**
     * 查看指定应用中心-知识库与文件关联表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<AppKnowledgeBaseFileRelationVo> view(@RequestParam Long id) {
        AppKnowledgeBaseFileRelation appKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.getByIdWithRelation(id, MyRelationParam.full());
        if (appKnowledgeBaseFileRelation == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        AppKnowledgeBaseFileRelationVo appKnowledgeBaseFileRelationVo = AppKnowledgeBaseFileRelation.INSTANCE.fromModel(appKnowledgeBaseFileRelation);
        return ResponseResult.success(appKnowledgeBaseFileRelationVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        AppKnowledgeBaseFileRelation originalAppKnowledgeBaseFileRelation = appKnowledgeBaseFileRelationService.getById(id);
        if (originalAppKnowledgeBaseFileRelation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if ("总结中".equals(originalAppKnowledgeBaseFileRelation.getConclusionState())) {
            errorMessage = "总结中的数据无法删除！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if ("萃取中".equals(originalAppKnowledgeBaseFileRelation.getExtractionState())) {
            errorMessage = "萃取中的数据无法删除！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if ("进行中".equals(originalAppKnowledgeBaseFileRelation.getVectorState())) {
            errorMessage = "向量化中的数据无法删除！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (appKnowledgeBaseFileRelationService.hasChildren(id)) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象存在子对象] ，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.HAS_CHILDREN_DATA, errorMessage);
        }
        if (!appKnowledgeBaseFileRelationService.remove(originalAppKnowledgeBaseFileRelation)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
