<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.data.dao.DataProcessTaskMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.data.model.DataProcessTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="clear_state" jdbcType="VARCHAR" property="clearState"/>
        <result column="clear_type" jdbcType="LONGVARCHAR" property="clearType"/>
        <result column="clear_method" jdbcType="LONGVARCHAR" property="clearMethod"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="handle_task_name" jdbcType="VARCHAR" property="handleTaskName"/>
        <result column="data_id_before" jdbcType="LONGVARCHAR" property="dataIdBefore"/>
        <result column="data_set_id" jdbcType="BIGINT" property="dataSetId"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="current_step" jdbcType="VARCHAR" property="currentStep"/>
        <result column="regular_json_library_id_list" jdbcType="LONGVARCHAR" property="regularJsonLibraryIdList"/>
        <result column="paramter" jdbcType="LONGVARCHAR" property="paramter"/>
        <result column="data_resources_id" jdbcType="BIGINT" property="dataResourcesId"/>
        <result column="sensitive_word_library_id_list" jdbcType="LONGVARCHAR" property="sensitiveWordLibraryIdList"/>
        <result column="technical_word_library_id_list" jdbcType="LONGVARCHAR" property="technicalWordLibraryIdList"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_data_process_task
            (id,
            str_id,
            clear_state,
            clear_type,
            clear_method,
            is_delete,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            handle_task_name,
            data_id_before,
            data_set_id,
            data_type,
            current_step,
            regular_json_library_id_list,
             paramter,
             technical_word_library_id_list,
             sensitive_word_library_id_list)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.clearState},
            #{item.clearType},
            #{item.clearMethod},
            #{item.isDelete},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.handleTaskName},
            #{item.dataIdBefore},
            #{item.dataSetId},
            #{item.dataType},
            #{item.currentStep},
            #{item.regularJsonLibraryIdList},
             #{item.paramter},
             #{item.technicalWordLibraryIdList},
             #{item.sensitiveWordLibraryIdList})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.data.dao.DataProcessTaskMapper.inputFilterRef"/>
        AND lmd_data_process_task.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="dataProcessTaskFilter != null">
            <if test="dataProcessTaskFilter.id != null">
                AND lmd_data_process_task.id = #{dataProcessTaskFilter.id}
            </if>
            <if test="dataProcessTaskFilter.strId != null and dataProcessTaskFilter.strId != ''">
                AND lmd_data_process_task.str_id = #{dataProcessTaskFilter.strId}
            </if>
            <if test="dataProcessTaskFilter.clearState != null and dataProcessTaskFilter.clearState != ''">
                <bind name="safeDataProcessTaskClearState" value="'%' + dataProcessTaskFilter.clearState + '%'"/>
                AND lmd_data_process_task.clear_state LIKE #{safeDataProcessTaskClearState}
            </if>
            <if test="dataProcessTaskFilter.clearType != null and dataProcessTaskFilter.clearType != ''">
                <bind name="safeDataProcessTaskClearType" value="'%' + dataProcessTaskFilter.clearType + '%'"/>
                AND lmd_data_process_task.clear_type LIKE #{safeDataProcessTaskClearType}
            </if>
            <if test="dataProcessTaskFilter.clearMethod != null and dataProcessTaskFilter.clearMethod != ''">
                <bind name="safeDataProcessTaskClearMethod" value="'%' + dataProcessTaskFilter.clearMethod + '%'"/>
                AND lmd_data_process_task.clear_method LIKE #{safeDataProcessTaskClearMethod}
            </if>
            <if test="dataProcessTaskFilter.createUserId != null">
                AND lmd_data_process_task.create_user_id = #{dataProcessTaskFilter.createUserId}
            </if>
            <if test="dataProcessTaskFilter.createTimeStart != null and dataProcessTaskFilter.createTimeStart != ''">
                AND lmd_data_process_task.create_time &gt;= #{dataProcessTaskFilter.createTimeStart}
            </if>
            <if test="dataProcessTaskFilter.createTimeEnd != null and dataProcessTaskFilter.createTimeEnd != ''">
                AND lmd_data_process_task.create_time &lt;= #{dataProcessTaskFilter.createTimeEnd}
            </if>
            <if test="dataProcessTaskFilter.updateUserId != null">
                AND lmd_data_process_task.update_user_id = #{dataProcessTaskFilter.updateUserId}
            </if>
            <if test="dataProcessTaskFilter.updateTimeStart != null and dataProcessTaskFilter.updateTimeStart != ''">
                AND lmd_data_process_task.update_time &gt;= #{dataProcessTaskFilter.updateTimeStart}
            </if>
            <if test="dataProcessTaskFilter.updateTimeEnd != null and dataProcessTaskFilter.updateTimeEnd != ''">
                AND lmd_data_process_task.update_time &lt;= #{dataProcessTaskFilter.updateTimeEnd}
            </if>
            <if test="dataProcessTaskFilter.dataUserId != null">
                AND lmd_data_process_task.data_user_id = #{dataProcessTaskFilter.dataUserId}
            </if>
            <if test="dataProcessTaskFilter.dataDeptId != null">
                AND lmd_data_process_task.data_dept_id = #{dataProcessTaskFilter.dataDeptId}
            </if>
            <if test="dataProcessTaskFilter.handleTaskName != null and dataProcessTaskFilter.handleTaskName != ''">
                <bind name="safeDataProcessTaskHandleTaskName"
                      value="'%' + dataProcessTaskFilter.handleTaskName + '%'"/>
                AND lmd_data_process_task.handle_task_name LIKE #{safeDataProcessTaskHandleTaskName}
            </if>
            <if test="dataProcessTaskFilter.dataIdBefore != null">
                AND lmd_data_process_task.data_id_before = #{dataProcessTaskFilter.dataIdBefore}
            </if>
            <if test="dataProcessTaskFilter.dataSetId != null">
                AND lmd_data_process_task.data_set_id = #{dataProcessTaskFilter.dataSetId}
            </if>
            <if test="dataProcessTaskFilter.dataType != null">
                AND lmd_data_process_task.data_type = #{dataProcessTaskFilter.dataType}
            </if>
            <if test="dataProcessTaskFilter.currentStep != null and dataProcessTaskFilter.currentStep != ''">
                <bind name="safeDataProcessTaskCurrentStep" value="'%' + dataProcessTaskFilter.currentStep + '%'"/>
                AND lmd_data_process_task.current_step LIKE #{safeDataProcessTaskCurrentStep}
            </if>
            <if test="dataProcessTaskFilter.regularJsonLibraryIdList != null and dataProcessTaskFilter.regularJsonLibraryIdList != ''">
                <bind name="safeDataProcessTaskRegularJsonLibraryIdList"
                      value="'%' + dataProcessTaskFilter.regularJsonLibraryIdList + '%'"/>
                AND lmd_data_process_task.regular_json_library_id_list LIKE #{safeDataProcessTaskRegularJsonLibraryIdList}
            </if>
            <if test="dataProcessTaskFilter.paramter != null and dataProcessTaskFilter.paramter != ''">
                <bind name="safeDataProcessTaskParamter" value="'%' + dataProcessTaskFilter.paramter + '%'"/>
                AND lmd_data_process_task.paramter LIKE #{safeDataProcessTaskParamter}
            </if>
            <if test="dataProcessTaskFilter.technicalWordLibraryIdList != null and dataProcessTaskFilter.technicalWordLibraryIdList != ''">
                <bind name="safeDataProcessTaskTechnicalWordLibraryIdList"
                      value="'%' + dataProcessTaskFilter.technicalWordLibraryIdList + '%'"/>
                AND lmd_data_process_task.technical_word_library_id_list LIKE #{safeDataProcessTaskTechnicalWordLibraryIdList}
            </if>
            <if test="dataProcessTaskFilter.sensitiveWordLibraryIdList != null and dataProcessTaskFilter.sensitiveWordLibraryIdList != ''">
                <bind name="safeDataProcessTaskSensitiveWordLibraryIdList"
                      value="'%' + dataProcessTaskFilter.sensitiveWordLibraryIdList + '%'"/>
                AND lmd_data_process_task.sensitive_word_library_id_list LIKE #{safeDataProcessTaskSensitiveWordLibraryIdList}
            </if>
        </if>
    </sql>

    <select id="getDataProcessTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataProcessTask">
        SELECT * FROM lmd_data_process_task
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
