package com.supie.webadmin.app.servicesApi.service;

import com.alibaba.fastjson.JSONArray;
import com.supie.common.core.object.ResponseResult;
import com.supie.webadmin.app.workflow.model.WorkflowLog;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ServiceApiService {
    Object modelChat(
            String model,
            JSONArray messages,
            Boolean store,
            String reasoning_effort,
            Object metadata,
            Integer frequency_penalty,
            Map logit_bias,
            Boolean logprobs,
            Integer top_logprobs,
            Integer max_tokens,
            Integer max_completion_tokens,
            Integer n,
            JSONArray modalities,
            Object prediction,
            Object audio,
            Long presence_penalty,
            Object response_format,
            Integer seed,
            String service_tier,
            String stop,
            Boolean stream,
            Object stream_options,
            String temperature,
            Double top_p,
            JSONArray tools,
            String tool_choice,
            Boolean parallel_tool_calls,
            String user,
            String function_call,
            JSONArray functions,
            String serviceKey) throws IOException;

    Object webSearchChat(
            String model,
            JSONArray messages,
            Boolean store,
            String reasoning_effort,
            Object metadata,
            Integer frequency_penalty,
            Map logit_bias,
            Boolean logprobs,
            Integer top_logprobs,
            Integer max_tokens,
            Integer max_completion_tokens,
            Integer n,
            JSONArray modalities,
            Object prediction,
            Object audio,
            Long presence_penalty,
            Object response_format,
            Integer seed,
            String service_tier,
            String stop,
            Boolean stream,
            Object stream_options,
            String temperature,
            Double top_p,
            JSONArray tools,
            String tool_choice,
            Boolean parallel_tool_calls,
            String user,
            String function_call,
            JSONArray functions,
            String serviceKey) throws IOException;

    Object appAssistant(String model, Boolean stream, JSONArray messages, String serviceKey) throws IOException;

    Object knowledgeAssistant(String model, Boolean stream, JSONArray messages, String serviceKey) throws IOException;

    ResponseResult<String> workflow(MultipartHttpServletRequest request);

    void fileDownload(Long fileId, HttpServletResponse response) throws IOException;

    String publicServiceToLi(Long serviceId, String serviceUrl, String serviceName, Integer serviceType);

    SseEmitter docProcess(String serviceName, String serviceKey, String url, String dataMap) throws IOException;

    WorkflowLog runWorkFlowV2(String serviceName, String serviceKey, MultipartHttpServletRequest request) throws IOException;

    List<Map<String, Object>> documentProofread(String serviceName, String serviceKey, String argumentJsonStr) throws IOException;

    Map<String,Object> soundReproduction(String text);
}

