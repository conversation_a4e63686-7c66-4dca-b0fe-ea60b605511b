<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.dao.PaperReviewMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.model.PaperReview">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="business_file_id" jdbcType="BIGINT" property="businessFileId"/>
        <result column="business_file_str_id" jdbcType="VARCHAR" property="businessFileStrId"/>
        <result column="review_title" jdbcType="VARCHAR" property="reviewTitle"/>
        <result column="review_content" jdbcType="LONGVARCHAR" property="reviewContent"/>
        <result column="review_status" jdbcType="TINYINT" property="reviewStatus"/>
        <result column="error_message" jdbcType="LONGVARCHAR" property="errorMessage"/>
        <result column="processing_time" jdbcType="INTEGER" property="processingTime"/>
        <result column="api_call_id" jdbcType="VARCHAR" property="apiCallId"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO li_paper_review
            (id,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            str_id,
            model_name,
            business_file_id,
            business_file_str_id,
            review_title,
            review_content,
            review_status,
            error_message,
            processing_time,
            api_call_id,
            model_id)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.strId},
            #{item.modelName},
            #{item.businessFileId},
            #{item.businessFileStrId},
            #{item.reviewTitle},
            #{item.reviewContent},
            #{item.reviewStatus},
            #{item.errorMessage},
            #{item.processingTime},
            #{item.apiCallId},
            #{item.modelId})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.dao.PaperReviewMapper.inputFilterRef"/>
        AND li_paper_review.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="paperReviewFilter != null">
            <if test="paperReviewFilter.id != null">
                AND li_paper_review.id = #{paperReviewFilter.id}
            </if>
            <if test="paperReviewFilter.createTimeStart != null and paperReviewFilter.createTimeStart != ''">
                AND li_paper_review.create_time &gt;= #{paperReviewFilter.createTimeStart}
            </if>
            <if test="paperReviewFilter.createTimeEnd != null and paperReviewFilter.createTimeEnd != ''">
                AND li_paper_review.create_time &lt;= #{paperReviewFilter.createTimeEnd}
            </if>
            <if test="paperReviewFilter.createUserId != null">
                AND li_paper_review.create_user_id = #{paperReviewFilter.createUserId}
            </if>
            <if test="paperReviewFilter.updateTimeStart != null and paperReviewFilter.updateTimeStart != ''">
                AND li_paper_review.update_time &gt;= #{paperReviewFilter.updateTimeStart}
            </if>
            <if test="paperReviewFilter.updateTimeEnd != null and paperReviewFilter.updateTimeEnd != ''">
                AND li_paper_review.update_time &lt;= #{paperReviewFilter.updateTimeEnd}
            </if>
            <if test="paperReviewFilter.updateUserId != null">
                AND li_paper_review.update_user_id = #{paperReviewFilter.updateUserId}
            </if>
            <if test="paperReviewFilter.dataUserId != null">
                AND li_paper_review.data_user_id = #{paperReviewFilter.dataUserId}
            </if>
            <if test="paperReviewFilter.dataDeptId != null">
                AND li_paper_review.data_dept_id = #{paperReviewFilter.dataDeptId}
            </if>
            <if test="paperReviewFilter.strId != null and paperReviewFilter.strId != ''">
                <bind name = "safePaperReviewStrId" value = "'%' + paperReviewFilter.strId + '%'" />
                AND li_paper_review.str_id LIKE #{safePaperReviewStrId}
            </if>
            <if test="paperReviewFilter.modelName != null and paperReviewFilter.modelName != ''">
                <bind name = "safePaperReviewModelName" value = "'%' + paperReviewFilter.modelName + '%'" />
                AND li_paper_review.model_name LIKE #{safePaperReviewModelName}
            </if>
            <if test="paperReviewFilter.businessFileId != null">
                AND li_paper_review.business_file_id = #{paperReviewFilter.businessFileId}
            </if>
            <if test="paperReviewFilter.modelId != null">
                AND li_paper_review.model_id = #{paperReviewFilter.modelId}
            </if>
            <if test="paperReviewFilter.businessFileStrId != null and paperReviewFilter.businessFileStrId != ''">
                <bind name = "safePaperReviewBusinessFileStrId" value = "'%' + paperReviewFilter.businessFileStrId + '%'" />
                AND li_paper_review.business_file_str_id LIKE #{safePaperReviewBusinessFileStrId}
            </if>
            <if test="paperReviewFilter.reviewTitle != null and paperReviewFilter.reviewTitle != ''">
                <bind name = "safePaperReviewReviewTitle" value = "'%' + paperReviewFilter.reviewTitle + '%'" />
                AND li_paper_review.review_title LIKE #{safePaperReviewReviewTitle}
            </if>
            <if test="paperReviewFilter.reviewContent != null and paperReviewFilter.reviewContent != ''">
                <bind name = "safePaperReviewReviewContent" value = "'%' + paperReviewFilter.reviewContent + '%'" />
                AND li_paper_review.review_content LIKE #{safePaperReviewReviewContent}
            </if>
            <if test="paperReviewFilter.reviewStatus != null">
                AND li_paper_review.review_status = #{paperReviewFilter.reviewStatus}
            </if>
            <if test="paperReviewFilter.errorMessage != null and paperReviewFilter.errorMessage != ''">
                <bind name = "safePaperReviewErrorMessage" value = "'%' + paperReviewFilter.errorMessage + '%'" />
                AND li_paper_review.error_message LIKE #{safePaperReviewErrorMessage}
            </if>
            <if test="paperReviewFilter.processingTimeStart != null">
                AND li_paper_review.processing_time &gt;= #{paperReviewFilter.processingTimeStart}
            </if>
            <if test="paperReviewFilter.processingTimeEnd != null">
                AND li_paper_review.processing_time &lt;= #{paperReviewFilter.processingTimeEnd}
            </if>
            <if test="paperReviewFilter.apiCallId != null and paperReviewFilter.apiCallId != ''">
                <bind name = "safePaperReviewApiCallId" value = "'%' + paperReviewFilter.apiCallId + '%'" />
                AND li_paper_review.api_call_id LIKE #{safePaperReviewApiCallId}
            </if>
            <if test="paperReviewFilter.searchString != null and paperReviewFilter.searchString != ''">
                <bind name = "safePaperReviewSearchString" value = "'%' + paperReviewFilter.searchString + '%'" />
                AND CONCAT(IFNULL(li_paper_review.model_name,''), IFNULL(li_paper_review.review_title,''), IFNULL(li_paper_review.review_content,''), IFNULL(li_paper_review.error_message,'')) LIKE #{safePaperReviewSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedPaperReviewList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.model.PaperReview">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM li_paper_review
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) li_paper_review
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getPaperReviewList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.model.PaperReview">
        SELECT * FROM li_paper_review
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
