package com.supie.webadmin.app.data.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.data.model.ProcessTechnicalWordLibray;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 数据管理-数据处理专业词库表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm
public interface ProcessTechnicalWordLibrayMapper extends BaseDaoMapper<ProcessTechnicalWordLibray> {

    /**
     * 批量插入对象列表。
     *
     * @param processTechnicalWordLibrayList 新增对象列表。
     */
    void insertList(List<ProcessTechnicalWordLibray> processTechnicalWordLibrayList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param processTechnicalWordLibrayFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<ProcessTechnicalWordLibray> getProcessTechnicalWordLibrayList(
            @Param("processTechnicalWordLibrayFilter") ProcessTechnicalWordLibray processTechnicalWordLibrayFilter, @Param("orderBy") String orderBy);
}
