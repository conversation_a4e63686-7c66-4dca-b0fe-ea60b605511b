package com.supie.webadmin.app.pythonClient.ApiUrlEnums;

import com.supie.webadmin.app.pythonClient.BaseApiUrlEnums;
import com.supie.webadmin.app.pythonClient.ServiceTypeEnums;
import lombok.Getter;
import org.springframework.http.HttpMethod;

/**
 * Python知识库服务接口
 */
public enum ClickshouseUrlEnums implements BaseApiUrlEnums {

    NGRAM("/lmd-py-ngram/ngram", "N-gram 分析接口"),
    ;

    ClickshouseUrlEnums(String url, String description) {
        this.serviceTypeEnums = ServiceTypeEnums.BASE;
        this.isSse = false;
        this.httpMethod = HttpMethod.POST;
        this.url = url;
        this.description = description;
    }

    ClickshouseUrlEnums(Boolean isSse, String url, String description) {
        this.serviceTypeEnums = ServiceTypeEnums.BASE;
        this.isSse = isSse;
        this.httpMethod = HttpMethod.POST;
        this.url = url;
        this.description = description;
    }

    ClickshouseUrlEnums(ServiceTypeEnums serviceTypeEnums, String url, String description) {
        this.serviceTypeEnums = serviceTypeEnums;
        this.isSse = false;
        this.httpMethod = HttpMethod.POST;
        this.url = url;
        this.description = description;
    }

    ClickshouseUrlEnums(ServiceTypeEnums serviceTypeEnums, Boolean isSse, String url, String description) {
        this.serviceTypeEnums = serviceTypeEnums;
        this.isSse = isSse;
        this.httpMethod = HttpMethod.POST;
        this.url = url;
        this.description = description;
    }

    ClickshouseUrlEnums(HttpMethod httpMethod, String url, String description) {
        this.serviceTypeEnums = ServiceTypeEnums.BASE;
        this.isSse = false;
        this.httpMethod = httpMethod;
        this.url = url;
        this.description = description;
    }

    ClickshouseUrlEnums(Boolean isSse, HttpMethod httpMethod, String url, String description) {
        this.serviceTypeEnums = ServiceTypeEnums.BASE;
        this.isSse = isSse;
        this.httpMethod = httpMethod;
        this.url = url;
        this.description = description;
    }

    /**
     * Python服务类型
     */
    @Getter
    private final ServiceTypeEnums serviceTypeEnums;
    /**
     * 是否为SSE
     */
    private final Boolean isSse;
    /**
     * Http请求方式
     */
    @Getter
    private final HttpMethod httpMethod;
    /**
     * 请求地址
     */
    @Getter
    private final String url;
    /**
     * 请求描述
     */
    @Getter
    private final String description;

    /**
     * @return 是否为SSE
     */
    @Override
    public Boolean isSse() {
        return isSse;
    }

}
