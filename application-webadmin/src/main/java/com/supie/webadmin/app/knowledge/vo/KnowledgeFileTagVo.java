package com.supie.webadmin.app.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 知识库-知识库文件_标签多对多关联表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Schema(description = "知识库-知识库文件_标签多对多关联表VO视图对象")
@Data
public class KnowledgeFileTagVo {

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 知识库文件ID。
     */
    @Schema(description = "知识库文件ID")
    private Long knowledgeFileId;

    /**
     * 标签ID。
     */
    @Schema(description = "标签ID")
    private Long tagId;
}
