package com.supie.webadmin.app.clickhouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.clickhouse.dao.DataInsightMapper;
import com.supie.webadmin.app.clickhouse.model.DataInsight;
import com.supie.webadmin.app.clickhouse.model.InsightResults;
import com.supie.webadmin.app.clickhouse.service.DataInsightService;
import com.supie.webadmin.app.clickhouse.service.InsightResultsService;
import com.supie.webadmin.app.data.dao.DataInsightImportTaskMapper;
import com.supie.webadmin.app.data.dao.DataSetInsightsMapper;
import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.data.model.DataInsightImportTask;
import com.supie.webadmin.app.data.model.DataSetInsights;
import com.supie.webadmin.app.data.service.impl.DataInsightImportTaskServiceImpl;
import com.supie.webadmin.app.data.service.impl.DataSetInsightsServiceImpl;
import com.supie.webadmin.pythonClient.service.impl.PythonClientServiceImpl;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据管理-数据表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("dataInsightService")
@MyDataSource(DataSourceType.CLICKHOME)
public class DataInsightServiceImpl extends BaseService<DataInsight, Long> implements DataInsightService {
    @Autowired
    private DataInsightMapper dataInsightMapper;
    @Value("${python.dataInsightBaseUrl}")
    private String baseUrl;
    @Value("${python.authorizationKey}")
    private String authorizationKey;
    @Autowired
    private DataSetInsightsMapper dataSetInsightsMapper;
    @Autowired
    private InsightResultsService insightResultsService;
    @Autowired
    private DataSetInsightsServiceImpl dataSetInsightsService;
    @Autowired
    private PythonClientServiceImpl pythonClientService;

    @Resource
    private DataInsightImportTaskServiceImpl dataInsightImportTaskServiceImpl;

    @Resource
    private IdGeneratorWrapper idGenerator;
    @Resource
    private  DataInsightImportTaskMapper dataInsightImportTaskMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DataInsight> mapper() {
        return dataInsightMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param dataInsight 新增对象。
     * @return 返回新增对象。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataInsight saveNew(DataInsight dataInsight) {
        dataInsightMapper.insert(this.buildDefaultValue(dataInsight));
        DataInsight originalDataInsight = MyModelUtil.copyTo(dataInsight, DataInsight.class);
        updateInsightResults(dataInsight, originalDataInsight);
        return dataInsight;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param dataInsightList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DataInsight> dataInsightList) {
        if (CollUtil.isNotEmpty(dataInsightList)) {
            dataInsightMapper.insertList(dataInsightList);
        }
    }


    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param dataInsightList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatchByTaken(List<DataInsight> dataInsightList) {

        if (CollUtil.isNotEmpty(dataInsightList)) {
            dataInsightMapper.insertList(dataInsightList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param dataInsight         更新的对象。
     * @param originalDataInsight 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Override
    public boolean update(DataInsight dataInsight, DataInsight originalDataInsight) {
        //同步更新InsightResults以及DataSetInsights
        dataInsight = updateInsightResults(dataInsight, originalDataInsight);
        MyModelUtil.fillCommonsForUpdate(dataInsight, originalDataInsight);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<DataInsight> uw = this.createUpdateQueryForNullValue(dataInsight, dataInsight.getId());

        return dataInsightMapper.updateDataInsight(dataInsight) == 1;
    }

    /**
     * 更新InsightResults对象及整体统计情况。
     *
     * @param dataInsight         更新的对象。
     * @param originalDataInsight 原有数据对象。
     * @return 成功返回true，否则false。
     */
    public DataInsight updateInsightResults(DataInsight dataInsight, DataInsight originalDataInsight) {
        //更新文本内容的困惑度，字符数等
        //定义特殊字符
        Pattern character = Pattern.compile("[!@#$%^&*()_+|~\\-=`{}\\[\\]:\";'<>?,./]");
        String text = dataInsight.getDataContent();
        //字符数
        Integer characterNumber = text.length();
        //特殊字符数
        Integer specialCharacterNumber = 0;
        Matcher matcher = character.matcher(text);
        while (matcher.find()) {
            specialCharacterNumber++;
        }
        //调用python端获取困惑度
        //组成所传参数
        Map<String, Map<Long, String>> dataMap = new HashMap<>();
        Map<Long, String> dataMapKey = new HashMap<>();
        dataMapKey.put(dataInsight.getId(), text);
        dataMap.put("content", dataMapKey);
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put(String.valueOf(Header.AUTHORIZATION), authorizationKey);
        headers.put(String.valueOf(Header.CONTENT_TYPE), "application/json");

        Map<String, Object> requestBody = new LinkedHashMap<>();
        requestBody.put("columns", dataMap);
        log.debug("requestBody:" + JSONUtil.toJsonStr(requestBody));
        HttpResponse response = HttpRequest.of(baseUrl + "/ngram_dataset_process")
                .method(Method.POST)
                .addHeaders(headers)
                .body(JSONUtil.toJsonStr(requestBody)).execute();
        log.debug("response:" + response);
        if (!response.isOk()) {
            pythonClientService.saveFailureModelToken("/ngram_dataset_process", requestBody);
            throw new MyRuntimeException("更新失败");
        }
        //解析返回结果
        Map resultMap = JSONUtil.toBean(response.body(), Map.class);
        Map responseMap = (Map) resultMap.get("data");
        if (responseMap != null && resultMap.get("success").equals("false")) {
            pythonClientService.saveFailureModelToken("/ngram_dataset_process", requestBody);
        }
        log.debug("responseMap:" + JSONUtil.toJsonStr(responseMap));
        Map resultDataMap = (Map) responseMap.get("content");
        BigDecimal resultPerplexity = (BigDecimal) resultDataMap.get(String.valueOf(dataInsight.getId()));
        //设置新的洞察情况
        float dataPerplexity = resultPerplexity.floatValue();
        dataInsight.setPerplexity(dataPerplexity);
        dataInsight.setCharacterNumber(characterNumber);
        dataInsight.setSpecialCharacterNumber(specialCharacterNumber);
        //更新数据
        if (dataInsightMapper.updateDataInsight(dataInsight) != 1) {
            throw new MyRuntimeException("更新失败！");
        }
        //获取单条数据的全部情况
        DataInsight filter = new DataInsight();
        filter.setDataSetInsightsId(originalDataInsight.getDataSetInsightsId());
        filter.setDataKey(originalDataInsight.getDataKey());
        filter.setDataRound(originalDataInsight.getDataRound());
        filter.setConversationsKey(originalDataInsight.getConversationsKey());
        List<DataInsight> dataInsightList = dataInsightMapper.getDataInsightList(filter, null);

        //计算数据洞察整体统计情况
        DataSetInsights dataSetInsights = computeStatistics(dataInsight, originalDataInsight, dataInsightList, characterNumber, specialCharacterNumber, resultDataMap);
        //计算更新后的json内容
        InsightResults insightResults = calculatedInsightResults(dataInsight, originalDataInsight, characterNumber, specialCharacterNumber, dataPerplexity);
        //更新
        insightResultsService.updateInsightResultsById(insightResults);
        dataSetInsightsService.updateDataSetInsights(dataSetInsights);
        return dataInsight;
    }

    /**
     * 计算更新后的整体统计情况。
     *
     * @param dataInsight         更新的对象。
     * @param originalDataInsight 原有数据对象。
     * @return 成功返回true，否则false。
     */
    private DataSetInsights computeStatistics(DataInsight dataInsight, DataInsight originalDataInsight, List<DataInsight> dataInsightList, Integer characterNumber, Integer specialCharacterNumber, Map resultDataMap) {
        DataSetInsights dataSetInsights = dataSetInsightsService.getDataSetInsightsById(originalDataInsight.getDataSetInsightsId());
        InsightResults insightResults = insightResultsService.getInsightResultsById(originalDataInsight.getInsightResultsId());
        //原统计情况
        String dataStatistics = dataSetInsights.getDataStatistics();

        Map<String, Object> dataStatisticsMap = JSONUtil.toBean(JSONUtil.toJsonStr(dataStatistics), Map.class);
        Map<String, Integer> characterNumberMap;
        Map<String, Integer> specialCharacterNumberMap;
        Map<String, Integer> perplexityMap;
        //获取轮数统计情况
        Map<String, Object> sessionRoundMap = (Map) dataStatisticsMap.get("sessionRound");
        //获取统计情况
        Map<String, Object> contentMap = (Map) dataStatisticsMap.get(originalDataInsight.getDataKey());
        Map<String, Object> statisticsMap = (Map<String, Object>) contentMap.getOrDefault(String.valueOf(originalDataInsight.getDataRound()), new HashMap<>());
        if (originalDataInsight.getDataFormat() == 2 || "history".equals(originalDataInsight.getDataKey())) {
            //统计轮次下的会话情况
            if ("historyInput".equals(originalDataInsight.getConversationsKey()) || "human".equals(originalDataInsight.getConversationsKey())) {
                //原轮次统计个数减一，新统计情况加一
                Integer turn = (Integer) sessionRoundMap.get(String.valueOf(insightResults.getSessionRound()));
                if (turn != null) {
                    sessionRoundMap.put(String.valueOf(insightResults.getSessionRound()), turn - 1);
                }
                Integer newTurn = (Integer) sessionRoundMap.getOrDefault(String.valueOf(insightResults.getSessionRound() + 1), 0);
                sessionRoundMap.put(String.valueOf(insightResults.getSessionRound() + 1), newTurn + 1);
                //修改json的轮次字段
                insightResults.setSessionRound(insightResults.getSessionRound() + 1);
                insightResultsService.updateInsightResultsById(insightResults);

            }
            Map<String, Object> conversationsMap = (Map<String, Object>) statisticsMap.getOrDefault(originalDataInsight.getConversationsKey(), new HashMap<>());
            characterNumberMap = (Map) conversationsMap.get("characterNumber");
            specialCharacterNumberMap = (Map) conversationsMap.get("specialCharacterNumber");
            perplexityMap = (Map<String, Integer>) conversationsMap.get("perplexity");
            //计算极值
            calculatedExtremum(conversationsMap, dataInsightList);
        } else {
            characterNumberMap = (Map) statisticsMap.get("characterNumber");
            specialCharacterNumberMap = (Map) statisticsMap.get("specialCharacterNumber");
            perplexityMap = (Map<String, Integer>) statisticsMap.get("perplexity");
            //计算极值
            calculatedExtremum(statisticsMap, dataInsightList);
        }
        //将原来的统计个数减一，并插入新的统计情况
        String characterNumberString = String.valueOf(originalDataInsight.getCharacterNumber());
        //判断是否为空
        if (characterNumberString != null && !characterNumberString.isEmpty() && !characterNumberString.equals("null")) {
            int originalCharacterNumber = characterNumberMap.get(characterNumberString);
            // 更新 characterNumberMap
            int updatedCharacterNumber = originalCharacterNumber - 1;
            characterNumberMap.put(String.valueOf(originalDataInsight.getCharacterNumber()), updatedCharacterNumber);
            if (updatedCharacterNumber == 0) {
                characterNumberMap.remove(String.valueOf(originalDataInsight.getCharacterNumber()));
            }
        }
        String specialCharacterNumberString = String.valueOf(originalDataInsight.getSpecialCharacterNumber());
        if (specialCharacterNumberString != null && !specialCharacterNumberString.isEmpty() && !specialCharacterNumberString.equals("null")) {
            int originalSpecialCharacterNumber = specialCharacterNumberMap.get(specialCharacterNumberString);
            // 更新 specialCharacterNumberMap
            int updatedSpecialCharacterNumber = originalSpecialCharacterNumber - 1;
            specialCharacterNumberMap.put(String.valueOf(originalDataInsight.getSpecialCharacterNumber()), updatedSpecialCharacterNumber);
            if (updatedSpecialCharacterNumber == 0) {
                specialCharacterNumberMap.remove(String.valueOf(originalDataInsight.getSpecialCharacterNumber()));
            }
        }
        int interval = 100;
        if (originalDataInsight.getPerplexity() != null && !originalDataInsight.getPerplexity().equals("null")) {
            // 计算区间
            int lowerBound = (int) (originalDataInsight.getPerplexity() / interval) * interval;
            int upperBound = lowerBound + interval;
            // 创建区间字符串
            String rangeKey = lowerBound + "~" + upperBound;

            int originalPerplexity = perplexityMap.get(rangeKey);
            //原统计情况减一

// 更新 perplexityMap
            int updatedPerplexity = originalPerplexity - 1;
            perplexityMap.put(rangeKey, updatedPerplexity);
            if (updatedPerplexity == 0) {
                perplexityMap.remove(rangeKey);
            }
        }
        //存入新的统计情况
        if (characterNumberMap.containsKey(String.valueOf(characterNumber))) {
            characterNumberMap.put(String.valueOf(characterNumber), characterNumberMap.get(String.valueOf(characterNumber)) + 1);
        } else {
            characterNumberMap.put(String.valueOf(characterNumber), 1);
        }
        //特殊字符数
        if (specialCharacterNumberMap.containsKey(String.valueOf(specialCharacterNumber))) {
            specialCharacterNumberMap.put(String.valueOf(specialCharacterNumber), specialCharacterNumberMap.get(String.valueOf(specialCharacterNumber)) + 1);
        } else {
            specialCharacterNumberMap.put(String.valueOf(specialCharacterNumber), 1);
        }
        //计算新困惑度所在区间
        BigDecimal bigDecimal = (BigDecimal) resultDataMap.get(String.valueOf(dataInsight.getId()));
        float perplexity = bigDecimal.floatValue();
        int perplexityLowerBound = (int) (perplexity / interval) * interval;
        int perplexityUpperBound = perplexityLowerBound + interval;
        // 创建区间字符串
        String perplexityRangeKey = perplexityLowerBound + "~" + perplexityUpperBound;
        //困惑度
        if (perplexityMap.containsKey(perplexityRangeKey)) {
            perplexityMap.put(perplexityRangeKey, perplexityMap.get(perplexityRangeKey) + 1);
        } else {
            perplexityMap.put(perplexityRangeKey, 1);
        }
        //更新统计情况
        dataSetInsights.setDataStatistics(JSONUtil.toJsonStr(dataStatisticsMap));
        return dataSetInsights;
    }

    /**
     * 计算最大最小值。
     *
     * @param map             。
     * @param dataInsightList 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Override
    public Map<String, Object> calculatedExtremum(Map<String, Object> map, List<DataInsight> dataInsightList) {
        if (dataInsightList.isEmpty()) {
            map.remove("minCharacterNumber");
            map.remove("maxCharacterNumber");
            map.remove("minSpecialCharacterNumber");
            map.remove("maxSpecialCharacterNumber");
            map.remove("minPerplexity");
            map.remove("maxPerplexity");
            map.remove("sessionRound");
            return map;
        }
        // 判断最大最小值
        int minCharacterNumber = dataInsightList.stream()
                .map(DataInsight::getCharacterNumber)
                .min(Integer::compareTo)
                .orElse(0);
        int maxCharacterNumber = dataInsightList.stream()
                .map(DataInsight::getCharacterNumber)
                .max(Integer::compareTo)
                .orElse(0);
        int minSpecialCharacterNumber = dataInsightList.stream()
                .map(DataInsight::getSpecialCharacterNumber)
                .min(Integer::compareTo)
                .orElse(0);
        int maxSpecialCharacterNumber = dataInsightList.stream()
                .map(DataInsight::getSpecialCharacterNumber)
                .max(Integer::compareTo)
                .orElse(0);

        BigDecimal bigDecimalMinPerplexity = BigDecimal.valueOf(dataInsightList.stream()
                .map(DataInsight::getPerplexity)
                .min(Float::compareTo)
                .orElse(0.0f));
        float minPerplexity = bigDecimalMinPerplexity.floatValue();

        BigDecimal bigDecimalMaxPerplexity = BigDecimal.valueOf(dataInsightList.stream()
                .map(DataInsight::getPerplexity)
                .max(Float::compareTo)
                .orElse(0.0f));
        float maxPerplexity = bigDecimalMaxPerplexity.floatValue();
        //更新极值
        map.put("minCharacterNumber", minCharacterNumber);
        map.put("maxCharacterNumber", maxCharacterNumber);
        map.put("minSpecialCharacterNumber", minSpecialCharacterNumber);
        map.put("maxSpecialCharacterNumber", maxSpecialCharacterNumber);
        map.put("minPerplexity", minPerplexity);
        map.put("maxPerplexity", maxPerplexity);
        return map;
    }

    /**
     * 计算更新后的json内容。
     *
     * @param dataInsight         更新的对象。
     * @param originalDataInsight 原有数据对象。
     * @return 成功返回true，否则false。
     */
    private InsightResults calculatedInsightResults(DataInsight dataInsight, DataInsight originalDataInsight, Integer characterNumber, Integer specialCharacterNumber, float dataPerplexity) {
        //更新数据集json
        InsightResults newInsightResults = new InsightResults();
        InsightResults insightResults = insightResultsService.getInsightResultsById(originalDataInsight.getInsightResultsId());
        Map<String, Object> dataContentMap = JSONUtil.toBean(insightResults.getDataContent(), Map.class);
        Map<String, Object> dataResultMap = JSONUtil.toBean(insightResults.getDataResult(), Map.class);

        //修改json
        //分类型进行判断
        if (originalDataInsight.getDataFormat() == 2) {
            Object dataContentMapList = dataContentMap.get(originalDataInsight.getDataKey());
            List<Map<String, Object>> dataResultMapList = (List<Map<String, Object>>) dataResultMap.get(originalDataInsight.getDataKey());
            int turn = 0;
            int resultTurn = 0;
            //更新json内容
            if (dataContentMapList instanceof JSONArray) {
                JSONArray steamArray = (JSONArray) dataContentMapList;
                // 判断是否需要添加新轮次
                int turnLength = (int) steamArray.size() / 2;
                if (turnLength < originalDataInsight.getDataRound()) {
                    if (steamArray.size() % 2 == 1) {
                        Map<String, Object> stringObjectMap = new HashMap<>();
                        stringObjectMap.put("from", dataInsight.getConversationsKey());
                        stringObjectMap.put("value", dataInsight.getDataContent());
                        steamArray.add(stringObjectMap);
                    } else {
                        Map<String, Object> stringObjectMap = new HashMap<>();
                        stringObjectMap.put("from", dataInsight.getConversationsKey());
                        stringObjectMap.put("value", dataInsight.getDataContent());
                        steamArray.add(stringObjectMap);
                    }

                } else {
                    for (int i = 0; i < steamArray.size(); i++) {
                        Map<String, Object> stringObjectMap = (Map<String, Object>) steamArray.get(i);
                        if ("human".equals(stringObjectMap.get("from"))) {
                            turn++;
                        }
                        if (originalDataInsight.getDataRound() == turn) {
                            if (originalDataInsight.getConversationsKey() != null && originalDataInsight.getConversationsKey().equals(stringObjectMap.get("from"))) {
                                stringObjectMap.put("value", dataInsight.getDataContent());
                                resultTurn = i + 1;
                            }
                        }
                    }
                }

                dataContentMap.put(originalDataInsight.getDataKey(), dataContentMapList);
                newInsightResults.setDataContent(JSONUtil.toJsonStr(dataContentMap));
            } else {
                Map steamObject = (JSONObject) dataContentMapList;
                steamObject.put("value", dataInsight.getDataContent());
            }

            //更新统计的json
            for (int i = 0; i < dataResultMapList.size(); i++) {
                if ((i + 1) == resultTurn) {
                    Map<String, Object> inMap = dataResultMapList.get(i);
                    inMap.put("characterNumber", characterNumber);
                    inMap.put("specialCharacterNumber", specialCharacterNumber);
                    inMap.put("perplexity", dataPerplexity);

                    insightResults.setDataResult(JSONUtil.toJsonStr(inMap));
                }
            }
            dataResultMap.put(String.valueOf(originalDataInsight.getDataKey()), dataResultMapList);
            newInsightResults.setDataResult(JSONUtil.toJsonStr(dataResultMap));
            newInsightResults.setId(insightResults.getId());
        } else if (originalDataInsight.getDataFormat() == 1) {
            //历史对话单独处理
            if ("history".equals(originalDataInsight.getDataKey())) {
                Object dataContentMapList = dataContentMap.get("history");
                List<Map<String, Object>> dataResultMapList = (List<Map<String, Object>>) dataResultMap.getOrDefault(originalDataInsight.getDataKey(), new ArrayList<>());
                //更新json内容
                if (dataContentMapList instanceof JSONArray) {
                    JSONArray steamArray = (JSONArray) dataContentMapList;
                    int turn = originalDataInsight.getDataRound() - 1;
                    //判断无该轮次则新增轮次
                    if (!(turn < steamArray.size())) {
                        JSONArray fourthEntry = new JSONArray();
                        //根据添加的key判断是加输入还是输出
                        if ("historyInput".equals(originalDataInsight.getConversationsKey())) {
                            fourthEntry.put(0, dataInsight.getDataContent());
                        } else if ("historyOutput".equals(originalDataInsight.getConversationsKey())) {
                            fourthEntry.put(1, dataInsight.getDataContent());
                        }
                        steamArray.put(originalDataInsight.getDataRound() - 1, fourthEntry);
//                        steamArray.put(originalDataInsight.getDataRound()-1, dataInsight.getDataContent());
                    } else {
                        JSONArray fourthEntry = (JSONArray) steamArray.get(originalDataInsight.getDataRound() - 1);
                        //判断无该轮次则新增
                        if ("historyInput".equals(originalDataInsight.getConversationsKey())) {
                            fourthEntry.put(0, dataInsight.getDataContent());
                        } else if ("historyOutput".equals(originalDataInsight.getConversationsKey())) {
                            fourthEntry.put(1, dataInsight.getDataContent());
                        }
                        steamArray.put(originalDataInsight.getDataRound() - 1, fourthEntry);
                    }
                    dataContentMap.put("history", dataContentMapList);
                    newInsightResults.setDataContent(JSONUtil.toJsonStr(dataContentMap));
                } else {
                    JSONArray steamObject = (JSONArray) dataContentMapList;
                    if ("historyInput".equals(originalDataInsight.getDataKey())) {
                        steamObject.put(0, dataInsight.getDataContent());
                    } else if ("historyOutput".equals(originalDataInsight.getDataKey())) {
                        steamObject.put(1, dataInsight.getDataContent());
                    }
                }

                //更新统计的json
                for (int i = 1; i <= dataResultMapList.size(); i++) {
                    if (i == originalDataInsight.getDataRound()) {
                        Map<String, Object> inMap = dataResultMapList.get(i - 1);
                        inMap.put("characterNumber", characterNumber);
                        inMap.put("specialCharacterNumber", specialCharacterNumber);
                        inMap.put("perplexity", dataPerplexity);

//                        insightResults.setDataResult(JSONUtil.toJsonStr(inMap));
                    }
                }
//                dataContentMap.put(originalDataInsight.getConversationsKey(), dataInsight.getDataContent());
                dataResultMap.put(String.valueOf(originalDataInsight.getDataKey()), dataResultMapList);
                newInsightResults.setDataResult(JSONUtil.toJsonStr(dataResultMap));
                newInsightResults.setDataContent(JSONUtil.toJsonStr(dataContentMap));
                newInsightResults.setId(insightResults.getId());
            } else {
                List<Map<String, Object>> dataResultMapList = (List<Map<String, Object>>) dataResultMap.get(originalDataInsight.getDataKey());
                //更新统计的json
                for (int i = 1; i <= dataResultMapList.size(); i++) {
                    Map<String, Object> inMap = dataResultMapList.get(i - 1);
                    inMap.put("characterNumber", characterNumber);
                    inMap.put("specialCharacterNumber", specialCharacterNumber);
                    inMap.put("perplexity", dataPerplexity);
//                        insightResults.setDataResult(JSONUtil.toJsonStr(inMap));
                }

                dataContentMap.put(originalDataInsight.getDataKey(), dataInsight.getDataContent());
                newInsightResults.setDataResult(JSONUtil.toJsonStr(dataResultMap));
                newInsightResults.setDataContent(JSONUtil.toJsonStr(dataContentMap));
                newInsightResults.setId(insightResults.getId());
            }
        }
        return newInsightResults;
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getDataRecordsList)，以便获取更好的查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<DataInsight> getDataInsightListWithRelation(DataInsight filter, String orderBy) {
        List<DataInsight> resultList = dataInsightMapper.getDataInsightList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<InsightResults> getDataInsightListWithInsightResults(DataInsight filter, String orderBy) {
        List<InsightResults> resultList = dataInsightMapper.getDataInsightListAndInsightResults(filter, orderBy);

        return resultList;
    }

    @Override
    public void updatePerplexity(Float perplexity, Long id) {
        dataInsightMapper.updatePerplexity(perplexity, id);
    }

    /**
     * 根据id获取对应数据集
     *
     * @param id 主键id。
     * @return 查询结果集。
     */
    @Override
    public DataInsight getDataInsightById(Long id) {
        return dataInsightMapper.getDataInsightById(id);
    }

    /**
     * 根据insightResultsId获取对应数据集
     *
     * @param insightResultsId 主键id。
     * @return 查询结果集。
     */
    @Override
    public List<DataInsight> getDataInsightByInsightResultsId(Long insightResultsId) {
        return dataInsightMapper.getDataInsightByInsightResultsId(insightResultsId);
    }

    /**
     * 根据InsightResultsIdList获取对应数据集
     *
     * @param insightResultsIdList 主键id。
     * @return 查询结果集。
     */
    @Override
    public List<DataInsight> getDataInsightByInsightResultsIdList(List<Long> insightResultsIdList, String orderBy) {
        return dataInsightMapper.getDataInsightByInsightResultsIdList(insightResultsIdList, orderBy);
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        DataInsight dataInsight = dataInsightMapper.getDataInsightById(id);

        return dataInsightMapper.deleteByDataInsight(dataInsight) == 1;
        //获取单条数据的全部情况
//        DataInsight filter=new DataInsight();
//        filter.setDataSetInsightsId(dataInsight.getDataSetInsightsId());
//        List<DataInsight> dataInsightList=dataInsightMapper.getDataInsightList(filter, null);

    }

    /**
     * 根据insightResultsId删除数据。
     *
     * @param insightResultsId 数据集json。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByInsightResultsId(Long insightResultsId) {
        dataInsightMapper.deleteByInsightResultsId(insightResultsId);

    }

    /**
     * 根据DataSetInsightsId删除数据。
     *
     * @param dataSetInsightsId 数据集json。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByDataSetInsightsId(Long dataSetInsightsId) {
        dataInsightMapper.deleteByDataSetInsightsId(dataSetInsightsId);

    }

    /*
    * 数据洞察导入数据集任务表 信息存储
    *  返回数据洞察id
    * */
    @Override
    public Long saveBatchDataInsight(DataDataSet dataSet, Long dataInsightId) {
            DataInsightImportTask data= new DataInsightImportTask();
            data.setDataInsightId(idGenerator.nextLongId()).setIsDelete(GlobalDeletedFlag.NORMAL)
                    .setCreateTime(new Date()).setUpdateTime(new Date())
                    .setCreateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                    .setUpdateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                    .setDataDeptId(Objects.requireNonNull(TokenData.takeFromRequest()).getDeptId())
                    .setDataInsightId(dataInsightId).setDataSetId(dataSet.getId())
                    .setImportState(1).setImportNum(0);
        dataInsightImportTaskServiceImpl.save(data);
        return data.getId();
    }

    private DataInsight buildDefaultValue(DataInsight dataInsight) {
        MyModelUtil.fillCommonsForInsert(dataInsight);
        dataInsight.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataInsight;
    }


}