package com.supie.webadmin.app.llmDeploy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.validator.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * ModelDeployTaskDto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ModelDeployTaskDto对象")
@Data
public class ModelDeployTaskDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 芯片(显卡)类型。
     */
    @Schema(description = "芯片(显卡)类型")
    private String chipCardType;

    /**
     * 模型名称。
     */
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 模型类型（1：我的模型。2：预置模型。）。
     */
    @Schema(description = "模型类型（1：我的模型。2：预置模型。）")
    private Integer modelType;

    /**
     * 模型ID。
     */
    @Schema(description = "模型ID")
    private Long modelId;

    /**
     * 部署的显卡ids
     */
    @Schema(description = "部署的显卡ids")
    private String commonParameterJson;

    /**
     * 部署地址（1：AICC。2：本地部署）。
     */
    @Schema(description = "部署地址（1：AICC。2：本地部署）")
    private Integer deploySiet;

    /**
     * AICC部署地址。
     */
    @Schema(description = "AICC部署地址")
    private String aiccSiet;

    /**
     * 镜像名称。
     */
    @Schema(description = "镜像名称")
    private String mirrorName;

    /**
     * 部署方式（1：FastChat。2：VLLM。3：HuggingFace）。
     */
    @Schema(description = "部署方式（1：FastChat。2：VLLM。3：HuggingFace）")
    private Integer deployType;

    /**
     * 是否兼容OpenAI-SDK（1：兼容。-1：不兼容）。
     */
    @Schema(description = "是否兼容OpenAI-SDK（1：兼容。-1：不兼容）")
    private Integer openAiSdk;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * model_name / mirror_name LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;

    /**
     * dockerID
     */
    @Schema(description = "dockerId")
    private String dockerId;

    /**
     * docker连接信息ID
     */
    @Schema(description = "docker连接信息ID")
    private Long dockerConnectId;

    /**
     * 部署状态（1：未开始 2，部署中，3，已完成， 4，异常）
     */
    @Schema(description = "部署状态（1：未开始 2，部署中，3，已完成， 4，异常）")
    private Integer deployStatus;

    /**
     * 访问地址
     */
    @Schema(description = "访问地址")
    private String accessUrl;

    /**
     * 服务器IP
     */
    @Schema(description = "服务器IP")
    private String serverHost;

    /**
     * 服务器端口
     */
    @Schema(description = "Fastchat openai url port")
    private Integer apiServerPort;

    /**
     * 服务器端口
     */
    @Schema(description = "Fastchat controller port")
    private Integer controllerPort;

    /**
     * 密钥
     */
    @Schema(description = "密钥")
    private String encryptionKey;

    /**
     * 部署模型依赖相关ID
     */
    @Schema(description = "部署模型依赖相关ID")
    private Long relyId;

    /**
     * 远程主机ID
     */
    @Schema(description = "远程主机ID")
    private Long remoteHostId;

    /**
     * 部署日志(部分截取)
     */
    @Schema(description = "部署日志(部分截取)")
    private String deployLogs;

    /**
     * 模型权重
     */
    @Schema(description = "模型权重")
    private String checkPoint;

    /**
     * 模型权重合并任务ID
     */
    @Schema(description = "模型权重合并任务ID")
    private Long mergeId;

    /**
     * 使用时间段
     */
    @Schema(description = "使用时间段")
    private String timePeriods;

    /**
     * 最大序列长度,请求输入长度
     */
    @Schema(description = "最大序列长度,请求输入长度")
    private Integer maxSeqLen;

    /**
     * 模型全局最大输出长度
     */
    @Schema(description = "模型全局最大输出长度")
    private Integer maxIterTimes;
    /**
     * 培训任务id。
     */
    @Schema(description = "培训任务id")
    private Long trainingTaskId;
    /**
     * 课程id。
     */
    @Schema(description = "课程id")
    private Long courseId;
    /**
     * 用户id。
     */
    @Schema(description = "用户id")
    private Long userId;
    /**
     * 课程与功能模块关联关系id。
     */
    @Schema(description = "课程与功能模块关联关系id")
    private Long courseFunctionRelationId;
}
