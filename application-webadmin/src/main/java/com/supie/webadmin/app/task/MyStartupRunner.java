package com.supie.webadmin.app.task;

import com.supie.webadmin.app.app.service.LocalInfoService;
import com.supie.webadmin.app.llmService.model.ComputingPowerApply;
import com.supie.webadmin.app.llmService.service.ComputingPowerApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyStartupRunner implements ApplicationRunner {

    @Autowired
    private LocalInfoService localInfoService;

    @Autowired
    private ComputingPowerApplyService computingPowerApplyService;

    @Autowired
    private CustomTaskScheduler customTaskScheduler;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 初始化本地信息文件
        localInfoService.init();

//        // 在这里编写你希望在项目启动时执行的逻辑
//        List<ComputingPowerApply> computingPowerApplyList = computingPowerApplyService.selectStaticAllWithThree();
//        if(computingPowerApplyList != null && computingPowerApplyList.size() != 0){
//            for (ComputingPowerApply computingPowerApply : computingPowerApplyList) {
//                customTaskScheduler.scheduleTaskAtSpecificTime(computingPowerApply);
//            }
//        }
    }

}
