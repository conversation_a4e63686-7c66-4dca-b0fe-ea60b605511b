package com.supie.webadmin.app.documentedit.service;

import com.supie.common.core.base.service.IBaseService;
import com.supie.webadmin.app.documentedit.model.DocumentEditProhibiteWord;
import com.supie.webadmin.app.documentedit.model.DocumentEditProhibiteWordLib;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文档编辑禁止词句表数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
public interface DocumentEditProhibiteWordService extends IBaseService<DocumentEditProhibiteWord, Long> {

    /**
     * 保存新增对象。
     *
     * @param documentEditProhibiteWord 新增对象。
     * @return 返回新增对象。
     */
    DocumentEditProhibiteWord saveNew(DocumentEditProhibiteWord documentEditProhibiteWord);

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param documentEditProhibiteWordList 新增对象列表。
     */
    void saveNewBatch(List<DocumentEditProhibiteWord> documentEditProhibiteWordList);

    /**
     * 更新数据对象。
     *
     * @param documentEditProhibiteWord         更新的对象。
     * @param originalDocumentEditProhibiteWord 原有数据对象。
     * @return 成功返回true，否则false。
     */
    boolean update(DocumentEditProhibiteWord documentEditProhibiteWord, DocumentEditProhibiteWord originalDocumentEditProhibiteWord);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getDocumentEditProhibiteWordListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<DocumentEditProhibiteWord> getDocumentEditProhibiteWordList(DocumentEditProhibiteWord filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getDocumentEditProhibiteWordList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<DocumentEditProhibiteWord> getDocumentEditProhibiteWordListWithRelation(DocumentEditProhibiteWord filter, String orderBy);

    /**
     * 判断指定对象是否包含下级对象。
     *
     * @param id 主键Id。
     * @return 存在返回true，否则false。
     */
    boolean hasChildren(Long id);

    List<DocumentEditProhibiteWord> getProhibiteWords(DocumentEditProhibiteWord documentEditProhibiteWord, List<Long> prohibiteDetailIdList);

    /**
     * 从文件导入违禁词数据
     * @param file 文件
     * @param prohibiteWordLib 违禁词库信息
     */
    void importProhibiteWord(MultipartFile file, DocumentEditProhibiteWordLib prohibiteWordLib) throws IOException;

}
