<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.app.dao.AppPromptInfoManageMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.app.model.AppPromptInfoManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="prompt_class" jdbcType="VARCHAR" property="promptClass"/>
        <result column="prompt_title" jdbcType="VARCHAR" property="promptTitle"/>
        <result column="prompt_desc" jdbcType="LONGVARCHAR" property="promptDesc"/>
        <result column="prompt_type" jdbcType="INTEGER" property="promptType"/>
        <result column="prompt_content" jdbcType="LONGVARCHAR" property="promptContent"/>
        <result column="prompt_param" jdbcType="VARCHAR" property="promptParam"/>
        <result column="param_identify" jdbcType="VARCHAR" property="paramIdentify"/>
        <result column="model_deploy_id" jdbcType="BIGINT" property="modelDeployId"/>
        <result column="template_type" jdbcType="VARCHAR" property="templateType"/>
        <result column="prompt_state" jdbcType="VARCHAR" property="promptState"/>
        <result column="is_optimize" jdbcType="INTEGER" property="isOptimize"/>
        <result column="optimize_before_id" jdbcType="BIGINT" property="optimizeBeforeId"/>
        <result column="iteration_num" jdbcType="VARCHAR" property="iterationNum"/>
        <result column="optimize_version" jdbcType="INTEGER" property="optimizeVersion"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_app_prompt_info_manage
            (id,
            str_id,
            is_deleted,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            prompt_class,
            prompt_title,
            prompt_desc,
            prompt_type,
            prompt_content,
            prompt_param,
            param_identify,
            model_deploy_id,
            template_type,
            prompt_state,
            is_optimize,
            optimize_before_id,
            iteration_num,
            optimize_version)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDeleted},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.promptClass},
            #{item.promptTitle},
            #{item.promptDesc},
            #{item.promptType},
            #{item.promptContent},
            #{item.promptParam},
            #{item.paramIdentify},
            #{item.modelDeployId},
            #{item.templateType},
            #{item.promptState},
            #{item.isOptimize},
            #{item.optimizeBeforeId},
            #{item.iterationNum},
            #{item.optimizeVersion})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.app.dao.AppPromptInfoManageMapper.inputFilterRef"/>
        AND lmd_app_prompt_info_manage.is_deleted = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="appPromptInfoManageFilter != null">
            <if test="appPromptInfoManageFilter.id != null">
                AND lmd_app_prompt_info_manage.id = #{appPromptInfoManageFilter.id}
            </if>
            <if test="appPromptInfoManageFilter.strId != null and appPromptInfoManageFilter.strId != ''">
                AND lmd_app_prompt_info_manage.str_id = #{appPromptInfoManageFilter.strId}
            </if>
            <if test="appPromptInfoManageFilter.createUserId != null">
                AND lmd_app_prompt_info_manage.create_user_id = #{appPromptInfoManageFilter.createUserId}
            </if>
            <if test="appPromptInfoManageFilter.createTimeStart != null and appPromptInfoManageFilter.createTimeStart != ''">
                AND lmd_app_prompt_info_manage.create_time &gt;= #{appPromptInfoManageFilter.createTimeStart}
            </if>
            <if test="appPromptInfoManageFilter.createTimeEnd != null and appPromptInfoManageFilter.createTimeEnd != ''">
                AND lmd_app_prompt_info_manage.create_time &lt;= #{appPromptInfoManageFilter.createTimeEnd}
            </if>
            <if test="appPromptInfoManageFilter.updateUserId != null">
                AND lmd_app_prompt_info_manage.update_user_id = #{appPromptInfoManageFilter.updateUserId}
            </if>
            <if test="appPromptInfoManageFilter.updateTimeStart != null and appPromptInfoManageFilter.updateTimeStart != ''">
                AND lmd_app_prompt_info_manage.update_time &gt;= #{appPromptInfoManageFilter.updateTimeStart}
            </if>
            <if test="appPromptInfoManageFilter.updateTimeEnd != null and appPromptInfoManageFilter.updateTimeEnd != ''">
                AND lmd_app_prompt_info_manage.update_time &lt;= #{appPromptInfoManageFilter.updateTimeEnd}
            </if>
            <if test="appPromptInfoManageFilter.dataUserId != null">
                AND lmd_app_prompt_info_manage.data_user_id = #{appPromptInfoManageFilter.dataUserId}
            </if>
            <if test="appPromptInfoManageFilter.dataDeptId != null">
                AND lmd_app_prompt_info_manage.data_dept_id = #{appPromptInfoManageFilter.dataDeptId}
            </if>
            <if test="appPromptInfoManageFilter.promptClass != null and appPromptInfoManageFilter.promptClass != ''">
                <bind name = "safeAppPromptInfoManagePromptClass" value = "'%' + appPromptInfoManageFilter.promptClass + '%'" />
                AND lmd_app_prompt_info_manage.prompt_class LIKE #{safeAppPromptInfoManagePromptClass}
            </if>
            <if test="appPromptInfoManageFilter.promptTitle != null and appPromptInfoManageFilter.promptTitle != ''">
                <bind name = "safeAppPromptInfoManagePromptTitle" value = "'%' + appPromptInfoManageFilter.promptTitle + '%'" />
                AND lmd_app_prompt_info_manage.prompt_title LIKE #{safeAppPromptInfoManagePromptTitle}
            </if>
            <if test="appPromptInfoManageFilter.promptDesc != null and appPromptInfoManageFilter.promptDesc != ''">
                <bind name = "safeAppPromptInfoManagePromptDesc" value = "'%' + appPromptInfoManageFilter.promptDesc + '%'" />
                AND lmd_app_prompt_info_manage.prompt_desc LIKE #{safeAppPromptInfoManagePromptDesc}
            </if>
            <if test="appPromptInfoManageFilter.promptType != null">
                AND lmd_app_prompt_info_manage.prompt_type = #{appPromptInfoManageFilter.promptType}
            </if>
            <if test="appPromptInfoManageFilter.promptContent != null and appPromptInfoManageFilter.promptContent != ''">
                <bind name = "safeAppPromptInfoManagePromptContent" value = "'%' + appPromptInfoManageFilter.promptContent + '%'" />
                AND lmd_app_prompt_info_manage.prompt_content LIKE #{safeAppPromptInfoManagePromptContent}
            </if>
            <if test="appPromptInfoManageFilter.promptParam != null and appPromptInfoManageFilter.promptParam != ''">
                AND lmd_app_prompt_info_manage.prompt_param = #{appPromptInfoManageFilter.promptParam}
            </if>
            <if test="appPromptInfoManageFilter.paramIdentify != null and appPromptInfoManageFilter.paramIdentify != ''">
                AND lmd_app_prompt_info_manage.param_identify = #{appPromptInfoManageFilter.paramIdentify}
            </if>
            <if test="appPromptInfoManageFilter.modelDeployId != null">
                AND lmd_app_prompt_info_manage.model_deploy_id = #{appPromptInfoManageFilter.modelDeployId}
            </if>
            <if test="appPromptInfoManageFilter.templateType != null and appPromptInfoManageFilter.templateType != ''">
                AND lmd_app_prompt_info_manage.template_type = #{appPromptInfoManageFilter.templateType}
            </if>
            <if test="appPromptInfoManageFilter.promptState != null and appPromptInfoManageFilter.promptState != ''">
                AND lmd_app_prompt_info_manage.prompt_state = #{appPromptInfoManageFilter.promptState}
            </if>
            <if test="appPromptInfoManageFilter.isOptimize != null">
                AND lmd_app_prompt_info_manage.is_optimize = #{appPromptInfoManageFilter.isOptimize}
            </if>
            <if test="appPromptInfoManageFilter.optimizeBeforeId != null">
                AND lmd_app_prompt_info_manage.optimize_before_id = #{appPromptInfoManageFilter.optimizeBeforeId}
            </if>
            <if test="appPromptInfoManageFilter.iterationNum != null and appPromptInfoManageFilter.iterationNum != ''">
                AND lmd_app_prompt_info_manage.iteration_num = #{appPromptInfoManageFilter.iterationNum}
            </if>
            <if test="appPromptInfoManageFilter.optimizeVersion != null">
                AND lmd_app_prompt_info_manage.optimize_version = #{appPromptInfoManageFilter.optimizeVersion}
            </if>
            <if test="appPromptInfoManageFilter.searchString != null and appPromptInfoManageFilter.searchString != ''">
                <bind name = "safeAppPromptInfoManageSearchString" value = "'%' + appPromptInfoManageFilter.searchString + '%'" />
                AND CONCAT(
                    IFNULL(lmd_app_prompt_info_manage.prompt_class, ''),
                    IFNULL(lmd_app_prompt_info_manage.prompt_title, ''),
                    IFNULL(lmd_app_prompt_info_manage.prompt_desc, ''),
                    IFNULL(lmd_app_prompt_info_manage.prompt_content, '')
                ) LIKE #{safeAppPromptInfoManageSearchString}
            </if>

            <if test="appPromptInfoManageFilter.idList != null and appPromptInfoManageFilter.idList.size() > 0">
                <foreach collection="appPromptInfoManageFilter.idList" item="item"
                         open="AND lmd_app_prompt_info_manage.id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="getGroupedAppPromptInfoManageList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.AppPromptInfoManage">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_app_prompt_info_manage
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_app_prompt_info_manage
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getAppPromptInfoManageList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.AppPromptInfoManage">
        SELECT * FROM lmd_app_prompt_info_manage
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
