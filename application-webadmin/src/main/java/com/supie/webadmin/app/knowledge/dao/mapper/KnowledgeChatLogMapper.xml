<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.knowledge.dao.KnowledgeChatLogMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.knowledge.model.KnowledgeChatLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="chat_set_id" jdbcType="BIGINT" property="chatSetId"/>
        <result column="chat_role" jdbcType="INTEGER" property="chatRole"/>
        <result column="chat_content" jdbcType="LONGVARCHAR" property="chatContent"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_knowledge_chat_log
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            chat_set_id,
            chat_role,
            chat_content)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.chatSetId},
            #{item.chatRole},
            #{item.chatContent})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.knowledge.dao.KnowledgeChatLogMapper.inputFilterRef"/>
        AND lmd_knowledge_chat_log.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="knowledgeChatLogFilter != null">
            <if test="knowledgeChatLogFilter.id != null">
                AND lmd_knowledge_chat_log.id = #{knowledgeChatLogFilter.id}
            </if>
            <if test="knowledgeChatLogFilter.strId != null and knowledgeChatLogFilter.strId != ''">
                AND lmd_knowledge_chat_log.str_id = #{knowledgeChatLogFilter.strId}
            </if>
            <if test="knowledgeChatLogFilter.updateTimeStart != null and knowledgeChatLogFilter.updateTimeStart != ''">
                AND lmd_knowledge_chat_log.update_time &gt;= #{knowledgeChatLogFilter.updateTimeStart}
            </if>
            <if test="knowledgeChatLogFilter.updateTimeEnd != null and knowledgeChatLogFilter.updateTimeEnd != ''">
                AND lmd_knowledge_chat_log.update_time &lt;= #{knowledgeChatLogFilter.updateTimeEnd}
            </if>
            <if test="knowledgeChatLogFilter.createTimeStart != null and knowledgeChatLogFilter.createTimeStart != ''">
                AND lmd_knowledge_chat_log.create_time &gt;= #{knowledgeChatLogFilter.createTimeStart}
            </if>
            <if test="knowledgeChatLogFilter.createTimeEnd != null and knowledgeChatLogFilter.createTimeEnd != ''">
                AND lmd_knowledge_chat_log.create_time &lt;= #{knowledgeChatLogFilter.createTimeEnd}
            </if>
            <if test="knowledgeChatLogFilter.createUserId != null">
                AND lmd_knowledge_chat_log.create_user_id = #{knowledgeChatLogFilter.createUserId}
            </if>
            <if test="knowledgeChatLogFilter.updateUserId != null">
                AND lmd_knowledge_chat_log.update_user_id = #{knowledgeChatLogFilter.updateUserId}
            </if>
            <if test="knowledgeChatLogFilter.dataUserId != null">
                AND lmd_knowledge_chat_log.data_user_id = #{knowledgeChatLogFilter.dataUserId}
            </if>
            <if test="knowledgeChatLogFilter.dataDeptId != null">
                AND lmd_knowledge_chat_log.data_dept_id = #{knowledgeChatLogFilter.dataDeptId}
            </if>
            <if test="knowledgeChatLogFilter.isDelete != null">
                AND lmd_knowledge_chat_log.is_delete = #{knowledgeChatLogFilter.isDelete}
            </if>
            <if test="knowledgeChatLogFilter.chatSetId != null">
                AND lmd_knowledge_chat_log.chat_set_id = #{knowledgeChatLogFilter.chatSetId}
            </if>
            <if test="knowledgeChatLogFilter.chatRole != null">
                AND lmd_knowledge_chat_log.chat_role = #{knowledgeChatLogFilter.chatRole}
            </if>
            <if test="knowledgeChatLogFilter.chatContent != null and knowledgeChatLogFilter.chatContent != ''">
                <bind name = "safeKnowledgeChatLogChatContent" value = "'%' + knowledgeChatLogFilter.chatContent + '%'" />
                AND lmd_knowledge_chat_log.chat_content LIKE #{safeKnowledgeChatLogChatContent}
            </if>
            <if test="knowledgeChatLogFilter.searchString != null and knowledgeChatLogFilter.searchString != ''">
                <bind name = "safeKnowledgeChatLogSearchString" value = "'%' + knowledgeChatLogFilter.searchString + '%'" />
                AND IFNULL(lmd_knowledge_chat_log.chat_content,'') LIKE #{safeKnowledgeChatLogSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedKnowledgeChatLogList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.knowledge.model.KnowledgeChatLog">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_knowledge_chat_log
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_knowledge_chat_log
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getKnowledgeChatLogList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.knowledge.model.KnowledgeChatLog">
        SELECT * FROM lmd_knowledge_chat_log
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
