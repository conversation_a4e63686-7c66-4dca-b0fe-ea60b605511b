package com.supie.webadmin.app.other.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.ResponseResult;
import com.supie.common.core.object.TokenData;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.app.dao.AppAssistantBasicMapper;
import com.supie.webadmin.app.app.dao.AppAssistantWorkflowMapper;
import com.supie.webadmin.app.app.dao.UserPluginConfigMapper;
import com.supie.webadmin.app.app.model.AppAssistantBasic;
import com.supie.webadmin.app.app.model.UserPluginConfig;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.llmService.service.ModelServiceRelationService;
import com.supie.webadmin.app.other.service.AdapterInterfaceService;
import com.supie.webadmin.app.other.service.ExternalModelConfigService;
import com.supie.webadmin.app.pythonClient.service.impl.PythonClientServiceImpl;
import com.supie.webadmin.app.workflow.model.Workflow;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

/**
 * 转接python接口服务
 */
@Tag(name = "其他-转接python接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/adapterInterface")
public class AdapterInterfaceContoller {

    @Value("${python.baseUrl}")
    private String baseUrl;
    @Value("${python.authorizationKey}")
    private String authorizationKey;
    private final CopyOnWriteArrayList<SseEmitter> emitters = new CopyOnWriteArrayList<>();

    @Resource
    private ModelDeployTaskService modelDeployTaskService;
    @Resource
    private AppAssistantWorkflowMapper appAssistantWorkflowMapper;
    @Resource
    private AppAssistantBasicMapper appAssistantBasicMapper;
    @Resource
    private UserPluginConfigMapper userPluginConfigMapper;
    @Resource
    private ModelServiceRelationService modelServiceRelationService;
    @Resource
    private AdapterInterfaceService adapterInterfaceService;
    @Autowired
    private PythonClientServiceImpl pythonClientService;
    @Autowired
    private ExternalModelConfigService externalModelConfigService;

    @OperationLog(type = SysOperationLogType.ADAPTER)
    @PostMapping("/general")
    @Operation(summary = "通用接口，用于发送post到python端")
    @SaIgnore
    public Map<String, Object> general(@MyRequestBody String url, @MyRequestBody String parameter) {
        if (StrUtil.isNotBlank(parameter) && !"llmApp/gpuInfo".equals(url)) {
            parameter = JSONObject.parseObject(parameter).toJSONString();
            Map<String, Object> dataMap = JSONUtil.toBean(parameter, Map.class);
            setLoginUserId(dataMap);
            dataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(dataMap);
            setModelConfigData(dataMap);
            parameter = JSONUtil.toJsonStr(dataMap);
        }
        //工具集管理测试接口判断名称中是否含有空格或-
        if("llmApp/app/tool_debug".equals(url)){
            JSONObject jsonObject = JSONObject.parseObject(parameter);
            if(jsonObject.getString("api_name").contains(" ") || jsonObject.getString("api_name").contains("-")){
                throw new InvalidParameterException("工具名称中不能包含空格或 -");
            }
        }
        url = baseUrl + "/" + url;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(mediaType, parameter);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", authorizationKey)
                .post(requestBody)
                .build();

        // 在构造器中创建客户端，并设置超时
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(3000, TimeUnit.SECONDS)  // 读取超时设置为30秒
                .writeTimeout(3000, TimeUnit.SECONDS) // 写入超时设置为30秒
                .connectTimeout(3000, TimeUnit.SECONDS) // 连接超时设置为30秒
                .build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (jsonObject.get("success") != null && Boolean.FALSE.equals(jsonObject.get("success"))) {
                    pythonClientService.saveFailureModelToken(url, JSON.parseObject(parameter).toJavaObject(Map.class));
                }
                return jsonObject.getInnerMap();
            } else {
                String errorMsg = "Python端请求失败[" + url + "]，请求参数为：" + parameter + "。失败信息：[" + response.message() + "] --> " + responseBody;
                log.error(errorMsg);
                throw (RuntimeException) new RuntimeException(errorMsg).initCause(null);
            }
        } catch (IOException e) {
            pythonClientService.saveFailureModelToken(url, JSON.parseObject(parameter).toJavaObject(Map.class));
            String errorMsg = "Python端请求失败[" + url + "]，请求参数为：" + parameter + "。失败信息：" + e.getMessage();
            log.error(errorMsg, e);
            throw (RuntimeException) new RuntimeException(errorMsg, e).initCause(null);
        }
    }

    /**
     * 对话接口，接收JSON数据并通过SSE (Server-Sent Events) 流传输数据。
     *
     * @param dataMap 接收的JSON数据
     * @return 返回SseEmitter，用于持续发送数据
     */
    @OperationLog(type = SysOperationLogType.ADAPTER)
    @PostMapping("/chat")
    @Operation(summary = "对话接口")
    @SaIgnore
    public SseEmitter receiveAndStreamData(@MyRequestBody Map<String, Object> dataMap, @MyRequestBody String url) throws IOException {
        // 创建一个新的SseEmitter实例，超时时间设置为无限
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        // 将emitter添加到emitters列表中进行管理
        this.emitters.add(emitter);
        setLoginUserId(dataMap);
        setModelConfigData(dataMap);
        dataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(dataMap);
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        // 使用ObjectMapper将Map转换为JSON字符串
        String jsonStr = objectMapper.writeValueAsString(dataMap);
       // 定义完成、超时和错误时移除emitter的逻辑
        emitter.onCompletion(() -> this.emitters.remove(emitter));
        emitter.onTimeout(() -> this.emitters.remove(emitter));
        emitter.onError(e -> this.emitters.remove(emitter));
        // 启动一个新线程以避免阻塞当前线程，连接到SSE URL并流式传输数据
        String finalJson = jsonStr;
        Map<String, Object> finalDataMap = dataMap;
        new Thread(() -> {
            try {
                connectToSseAndStreamData(emitter, baseUrl+"/" + url, finalJson);
                // 完成传输并关闭连接
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
                pythonClientService.saveFailureModelToken(url, finalDataMap);
                String errorMsg = "Python端请求失败[" + baseUrl +"/" + url + "]，请求参数为：" + finalJson + "。失败信息：" + e.getMessage();
                log.error(errorMsg, e);
                throw new RuntimeException(errorMsg, e);
            }
        }).start();
        return emitter;
    }

    /**
     * 应用聊天接口
     */
    @Operation(summary = "应用助手-应用聊天")
    @PostMapping("/llmChat")
    @OperationLog(type = SysOperationLogType.ADAPTER)
    public SseEmitter llmChat(@MyRequestBody Map<String, Object> dataMap) throws IOException {
        try {
            setLoginUserId(dataMap);
            setModelConfigData(dataMap);
            dataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(dataMap);
            dataMap = setLlmChatRequestParametersData(dataMap);
        } catch (Exception e) {
            log.error("llmChat error：{}", e.getMessage(), e);
            ResponseResult.output(500, ResponseResult.error(e.getMessage()));
            return null;
        }
        // 创建一个新的SseEmitter实例，超时时间设置为无限
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        // 将emitter添加到emitters列表中进行管理
        this.emitters.add(emitter);
        // 定义完成、超时和错误时移除emitter的逻辑
        emitter.onCompletion(() -> this.emitters.remove(emitter));
        emitter.onTimeout(() -> this.emitters.remove(emitter));
        emitter.onError(e -> this.emitters.remove(emitter));
        // 启动一个新线程以避免阻塞当前线程，连接到SSE URL并流式传输数据
        String jsonStr = JSONUtil.toJsonStr(dataMap);
        Map<String, Object> finalDataMap = dataMap;
        new Thread(() -> {
            try {
                connectToSseAndStreamData(emitter, baseUrl + "/llmApp/chat", jsonStr);
            } catch (Exception e) {
                try {
                    emitter.send(JSON.toJSONString(ResponseResult.error(ErrorCodeEnum.NO_ERROR, e.getMessage())));
                } catch (IOException ex) {
                    log.error("无法向 SseEmitter 发送错误消息：{}", ex.getMessage(), ex);
                }
                pythonClientService.saveFailureModelToken("/llmApp/chat", finalDataMap);
                String errorMsg = "Python端请求失败[" + baseUrl + "/llmApp/chat]，请求参数为：" + jsonStr + "。失败信息：" + e.getMessage();
                log.error(errorMsg, e);
            } finally {
                emitter.complete();
            }
        }).start();
        return emitter;
    }

    /**
     * 设置登录用户ID
     * @param dataMap
     */
    private Map<String, Object> setLoginUserId(Map<String, Object> dataMap) {
        dataMap.put("login_user_id", TokenData.takeFromRequest().getUserId());
        return dataMap;
    }

    @NotNull
    public Map<String, Object> setLlmChatRequestParametersData(Map<String, Object> dataMap) {
        if (!dataMap.containsKey("app_id")) {
            throw (RuntimeException) new RuntimeException("缺少必要的 app_id 参数！").initCause(null);
        }
        Long appAssistantBasicId = Long.valueOf(String.valueOf(dataMap.get("app_id")));
        // 设置 plugin_id_list 参数
        List<UserPluginConfig> userPluginConfigs = userPluginConfigMapper.selectList(new LambdaQueryWrapper<UserPluginConfig>()
                .eq(UserPluginConfig::getAppId, appAssistantBasicId));
        List<String> userPluginConfigIdList = new ArrayList<>();
        for (UserPluginConfig userPluginConfig : userPluginConfigs) {
            userPluginConfigIdList.add(String.valueOf(userPluginConfig.getId()));
        }
        dataMap.put("plugin_id_list", userPluginConfigIdList);
        // 设置 llm_app_name、llm_app_description 参数
        AppAssistantBasic appAssistantBasic = appAssistantBasicMapper.selectById(appAssistantBasicId);
        if (appAssistantBasic != null) {
            dataMap.put("llm_app_name", appAssistantBasic.getAppName());
            dataMap.put("llm_app_description", appAssistantBasic.getAppDesc());
        } else {
            dataMap.put("llm_app_name", "");
            dataMap.put("llm_app_description", "");
        }
        // dataMap 设置 workflow_config_list 节点的参数
        List<Workflow> workflowList = appAssistantWorkflowMapper.getWorkflowListByAppAssistantBasicId(appAssistantBasicId);
        List<Map<String, Object>> workflowConfigList = new ArrayList<>();
        for (Workflow workflow : workflowList) {
            Map<String, Object> workflowConfigMap = new HashMap<>();
            workflowConfigMap.put("id", workflow.getId());
            String workflowInputParams = workflow.getWorkflowInputParams();
            if (StrUtil.isNotBlank(workflowInputParams)) {
                workflowConfigMap.put("input_params", JSONUtil.toList(workflowInputParams, Map.class));
            } else {
                workflowConfigMap.put("input_params", new ArrayList<>());
            }
            String workflowOutputParams = workflow.getWorkflowOutputParams();
            if (StrUtil.isNotBlank(workflowOutputParams)) {
                workflowConfigMap.put("output_params", JSONUtil.toList(workflowOutputParams, Map.class));
            } else {
                workflowConfigMap.put("output_params", new ArrayList<>());
            }
            workflowConfigMap.put("desc", workflow.getWorkflowDesc());
            workflowConfigList.add(workflowConfigMap);
        }
        dataMap.put("workflow_config_list", workflowConfigList);
        return dataMap;
    }

    /**
     * 设置部署的模型的配置信息
     * @param dataMap
     * @param modelDeployTaskId
     * @param modelDeployTask
     */
    private static void setModelDeployConfig(Map<String, Object> dataMap, Long modelDeployTaskId, ModelDeployTask modelDeployTask) {
        if (modelDeployTask == null) {
            throw (RuntimeException) new RuntimeException("模型部署任务[" + modelDeployTaskId + "]不存在！").initCause(null);
        }
        Map<String, Object> modelConfig;
        if (dataMap.containsKey("model_config")) {
            modelConfig = (Map<String, Object>) dataMap.get("model_config");
        } else {
            modelConfig = new HashMap<>();
        }
        modelConfig.put("model_type", "ChatOpenAI");
        modelConfig.put("model_name", modelDeployTask.getModelName());
        // 设置模型地址及其密钥
        StringBuilder baseModelUrl = new StringBuilder("http://");
        if (null != modelDeployTask.getServerHost()) {
            baseModelUrl.append(modelDeployTask.getServerHost());
        }
        if (null != modelDeployTask.getApiServerPort()) {
            baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
        }
        baseModelUrl.append("/v1");
        modelConfig.put("api_base", baseModelUrl.toString());
        modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        modelConfig.put("modelId", modelDeployTaskId);
        modelConfig.put("temperature",0.7);
        dataMap.put("model_config", modelConfig);
    }

    /**
     * 设置模型的相关信息
     */
    @NotNull
    private Map<String, Object> setModelConfigData(Map<String, Object> dataMap) {
        // 前端客户自定义的模型配置
        Map<String, Object> customizeModelConfig = dataMap.containsKey("modelConfig") ? (Map<String, Object>) dataMap.get("modelConfig") : new HashMap<>();
        if (dataMap.containsKey("modelType") && dataMap.containsKey("modelId")) {
            String modelIdStr = dataMap.get("modelId").toString();
            if (modelIdStr.isEmpty()) {
//                throw new InvalidParameterException("模型ID不能为空！");
                throw new MyRuntimeException("请先配置需要使用的模型！");
            }
            Long modelId = Long.valueOf(modelIdStr);
            // 设置模型的配置信息
            String modelType = dataMap.get("modelType").toString();
            if ("deploy".equals(modelType)) {
                ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
                if (modelDeployTask == null) {
//                    throw new InvalidParameterException("模型[" + modelId + "]不存在！");
                    throw new MyRuntimeException("配置的模型已被删除，请重新配置！");
                }
                setModelDeployConfig(dataMap, modelId, modelDeployTask);
            } else if ("public".equals(modelType)) {
                Map<String, Object> modelConfig = externalModelConfigService.getModelConfigById(modelId, null);
                if (modelConfig == null) {
                    throw new InvalidParameterException("模型[" + modelId + "]不存在！");
                }
                Map<String, Object> dataModelConfig = (Map<String, Object>) dataMap.getOrDefault("model_config", new HashMap<>());
                if (dataModelConfig != null) {
                    modelConfig.putAll(dataModelConfig);
                }
                dataMap.put("model_config", modelConfig);
            } else {
                throw new MyRuntimeException("配置的模型已被删除，请重新配置！");
            }
        }
        if (dataMap.containsKey("app_id")) {
            Long appAssistantBasicId = Long.valueOf(String.valueOf(dataMap.get("app_id")));
            AppAssistantBasic appAssistantBasic = appAssistantBasicMapper.selectById(appAssistantBasicId);
            if (appAssistantBasic != null) {
                dataMap = setModelConfigOtherConfigData(dataMap, appAssistantBasic);
            }
        }
        if (dataMap.containsKey("model_config")) {
            Map<String, Object> newModelConfig = (Map<String, Object>) dataMap.get("model_config");
            newModelConfig.putAll(customizeModelConfig);
        }
        return dataMap;
    }

    /**
     * 设置模型配置的其他配置信息
     * @param dataMap
     * @return
     */
    private Map<String, Object> setModelConfigOtherConfigData(Map<String, Object> dataMap, AppAssistantBasic appAssistantBasic) {
        if (!dataMap.containsKey("model_config")) {
            return dataMap;
        }
        Map<String, Object> modelConfig = (Map<String, Object>) dataMap.get("model_config");
        // 设置模型温度
        if (appAssistantBasic.getTemperature() != null) {
            modelConfig.put("temperature", appAssistantBasic.getTemperature());
        }
        // 设置停止标识
        if (appAssistantBasic.getStopSign() != null) {
            modelConfig.put("stop_sequences", appAssistantBasic.getStopSign());
        }
        dataMap.put("model_config", modelConfig);
        return dataMap;
    }

    /**
     * 建立HTTP连接，向指定的URL发送JSON数据，并将响应流式传输回客户端。
     *
     * @param emitter 用于发送数据的SseEmitter
     * @param sseUrl 要连接的SSE URL
     * @param jsonPayload 要发送的JSON负载
     */
    private void connectToSseAndStreamData(SseEmitter emitter, String sseUrl, String jsonPayload) {
        try {
            URL url = new URL(sseUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置HTTP请求方法为POST
            connection.setRequestMethod("POST");
            // 允许输出，因为这是一个POST请求
            connection.setDoOutput(true);
            // 设置请求头，发送和接收JSON数据
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setRequestProperty("Authorization", authorizationKey);
            // 发送JSON负载
            try (var outputStream = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                outputStream.write(input, 0, input.length);
            }
            // 读取响应数据并通过SseEmitter发送
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.isEmpty()) {
                        // 如果字符串以 "data:" 开头，则移除该前缀
                        if (line.startsWith("data:")) {
                            line = line.substring(5).trim();
                        }
                        // 将每行数据转换为JSON对象，然后转换为Map并发送
                        emitter.send(line);
                    }
                }
            } catch (Exception e) {
                // 发生连接错误
                log.error("连接SSE失败！{}", e.getMessage(), e);
                throw new MyRuntimeException(e);
            } finally {
                connection.disconnect();
            }
        } catch (Exception e) {
            throw new MyRuntimeException("服务器繁忙，请稍后再试。");
        }
    }

    /**
     * 对话接口V2，接收JSON数据并通过SSE (Server-Sent Events) 流传输数据。
     *
     * @param dataMap 接收的JSON数据
     * @return 返回SseEmitter，用于持续发送数据
     */
    @OperationLog(type = SysOperationLogType.ADAPTER)
    @PostMapping("/chatV2")
    @Operation(summary = "对话接口V2")
    @SaIgnore
    public SseEmitter receiveAndStreamDataV2(@MyRequestBody Map<String, Object> dataMap, @MyRequestBody String url) throws IOException {
        // 创建一个新的SseEmitter实例，超时时间设置为无限
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        // 将emitter添加到emitters列表中进行管理
        this.emitters.add(emitter);
        setLoginUserId(dataMap);
        setModelConfigData(dataMap);
        dataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(dataMap);
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        // 使用ObjectMapper将Map转换为JSON字符串
        String jsonStr = objectMapper.writeValueAsString(dataMap);
        // 定义完成、超时和错误时移除emitter的逻辑
        emitter.onCompletion(() -> this.emitters.remove(emitter));
        emitter.onTimeout(() -> this.emitters.remove(emitter));
        emitter.onError(e -> this.emitters.remove(emitter));
        // 启动一个新线程以避免阻塞当前线程，连接到SSE URL并流式传输数据
        String finalJson = jsonStr;
        Map<String, Object> finalDataMap = dataMap;
        new Thread(() -> {
            try {
                connectToSseAndStreamData(emitter, baseUrl+"/" + url, finalJson);
            } catch (Exception e) {
                try {
                    //记录错误信息到数据库
                    pythonClientService.saveFailureModelToken(url, finalDataMap);
                    log.error("对话接口异常：{}", e.getMessage(), e);
                    emitter.send(JSON.toJSONString(ResponseResult.error(e.getMessage())), org.springframework.http.MediaType.TEXT_PLAIN);
                } catch (IOException ex) {
                    throw new MyRuntimeException(ex);
                }
            }finally {
                try {
                    emitter.send(" [DONE]", org.springframework.http.MediaType.TEXT_PLAIN);
                } catch (IOException e) {
                    throw new MyRuntimeException(e);
                }
                // 完成传输并关闭连接
                emitter.complete();
            }
        }).start();
        return emitter;
    }

}
