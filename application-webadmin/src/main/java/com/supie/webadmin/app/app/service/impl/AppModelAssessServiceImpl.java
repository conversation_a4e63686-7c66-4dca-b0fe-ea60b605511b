package com.supie.webadmin.app.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.webadmin.app.other.service.ExternalModelConfigService;
import com.supie.webadmin.app.pythonClient.ApiUrlEnums.AppUrlEnums;
import com.supie.webadmin.app.pythonClient.model.PythonResponse;
import com.supie.webadmin.app.pythonClient.service.impl.PythonClientServiceImpl;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.app.dao.AppAiAssessInfoMapper;
import com.supie.webadmin.app.app.dao.AppAssessModelModelRelationshipMapper;
import com.supie.webadmin.app.app.dao.AppManualAssessInfoMapper;
import com.supie.webadmin.app.app.dao.AppModelAssessMapper;
import com.supie.webadmin.app.app.model.*;
import com.supie.webadmin.app.app.service.AppAiAssessInfoService;
import com.supie.webadmin.app.app.service.AppAssessModelModelRelationshipService;
import com.supie.webadmin.app.app.service.AppManualAssessInfoService;
import com.supie.webadmin.app.app.service.AppModelAssessService;
import com.supie.webadmin.app.clickhouse.model.DataRecords;
import com.supie.webadmin.app.llmDeploy.dao.ModelDeployTaskMapper;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmService.service.ModelServiceRelationService;
import com.supie.webadmin.app.myException.ThrowUpwardsException;
import com.supie.webadmin.app.other.service.AdapterInterfaceService;
import com.supie.webadmin.app.util.HttpUtil;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 应用中心-模型评估表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("appModelAssessService")
@MyDataSource(DataSourceType.MAIN)
public class AppModelAssessServiceImpl extends BaseService<AppModelAssess, Long> implements AppModelAssessService {



    @Autowired
    private ExternalModelConfigService externalModelConfigService;
//    @Value("${python.baseUrl}")
//    private String baseUrl;
    @Autowired
    private AppModelAssessMapper appModelAssessMapper;
    @Autowired
    private AppAssessModelModelRelationshipService appAssessModelModelRelationshipService;
    @Autowired
    private AppAiAssessInfoService appAiAssessInfoService;
    @Autowired
    private AppManualAssessInfoService appManualAssessInfoService;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Resource
    private AppAssessModelModelRelationshipMapper appAssessModelModelRelationshipMapper;
    @Resource
    private AppAiAssessInfoMapper appAiAssessInfoMapper;
    @Resource
    private AppManualAssessInfoMapper appManualAssessInfoMapper;
    @Resource
    private ModelDeployTaskMapper modelDeployTaskMapper;


//    @Resource
//    private ModelDeployTaskService modelDeployTaskService;

    @Resource
    private ModelServiceRelationService modelServiceRelationService;

    @Resource
    private HttpUtil httpUtil;
    @Autowired
    private PythonClientServiceImpl pythonClientService;
    @Autowired
    private AdapterInterfaceService adapterInterfaceService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<AppModelAssess> mapper() {
        return appModelAssessMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param appModelAssess 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AppModelAssess saveNew(AppModelAssess appModelAssess) {
        appModelAssessMapper.insert(this.buildDefaultValue(appModelAssess));
        return appModelAssess;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param appModelAssessList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<AppModelAssess> appModelAssessList) {
        if (CollUtil.isNotEmpty(appModelAssessList)) {
            appModelAssessList.forEach(this::buildDefaultValue);
            appModelAssessMapper.insertList(appModelAssessList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param appModelAssess         更新的对象。
     * @param originalAppModelAssess 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(AppModelAssess appModelAssess, AppModelAssess originalAppModelAssess) {
        MyModelUtil.fillCommonsForUpdate(appModelAssess, originalAppModelAssess);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<AppModelAssess> uw = this.createUpdateQueryForNullValue(appModelAssess, appModelAssess.getId());
        return appModelAssessMapper.update(appModelAssess, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        if (appModelAssessMapper.deleteById(id) == 0) {
            return false;
        }
        appAssessModelModelRelationshipService.removeByAssessId(id);
        appAiAssessInfoService.removeByModelAssessId(id);
        appManualAssessInfoService.removeByModelAssessId(id);
        return true;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getAppModelAssessListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<AppModelAssess> getAppModelAssessList(AppModelAssess filter, String orderBy) {
        return appModelAssessMapper.getAppModelAssessList(filter, orderBy);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AppModelAssess saveNewWithRelation(AppModelAssess appModelAssess, JSONObject relationData) {
        appModelAssess.setAssessState("正在统计");
        this.saveNew(appModelAssess);
        this.saveOrUpdateRelationData(appModelAssess, relationData);

        return appModelAssess;
    }

    private void saveOrUpdateRelationData(AppModelAssess appModelAssess, JSONObject relationData) {
        List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList =
                relationData.getObject("appAssessModelModelRelationshipList", new TypeReference<List<AppAssessModelModelRelationship>>() {});
        // 对于一对多更新，分为以下三步：
        // 1. 在关联从表中，删除掉与主表字段关联，但是又没有出现在本地更新中的数据。我们将这些数据视为需要删除的数据。
        // 2. 在本次更新数据列表中，如果从表的对象没有主键Id，我们视为新数据，可以批量插入。
        // 3. 在本次更新数据列表中，如果从表的对象存在主键Id，我们视为已有数据，逐条更新。
        if (appAssessModelModelRelationshipList != null) {
            appAssessModelModelRelationshipService.updateBatchByAssessId(appModelAssess.getId(), appAssessModelModelRelationshipList);
        }
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getAppModelAssessList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<AppModelAssess> getAppModelAssessListWithRelation(AppModelAssess filter, String orderBy) {
        List<AppModelAssess> resultList = appModelAssessMapper.getAppModelAssessList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.full(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<AppModelAssess> getGroupedAppModelAssessListWithRelation(
            AppModelAssess filter, String groupSelect, String groupBy, String orderBy) {
        List<AppModelAssess> resultList =
                appModelAssessMapper.getGroupedAppModelAssessList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    @Async
    public void startTheAssessment(AppModelAssess appModelAssess,List<DataRecords> dataRecordsList) {
        Long modelAssessId = appModelAssess.getId();
        appModelAssessMapper.update(new LambdaUpdateWrapper<AppModelAssess>()
                .eq(AppModelAssess::getId, modelAssessId)
                .set(AppModelAssess::getAssessState,"进行中"));
        String ssessState = "失败";
        String errorMessage = "NO-MESSAGE";
        try {
            if ("人工打分".equals(appModelAssess.getAssessPattern())) {
                this.manualScoring(dataRecordsList,appModelAssess);
                ssessState = "待评估";
            }
            if ("自动裁判员打分".equals(appModelAssess.getAssessPattern())) {
                this.automaticRefereeScoring(dataRecordsList,appModelAssess);
                ssessState = "已完成";
            }
        } catch (ThrowUpwardsException | IOException e) {
            log.error(e.getMessage(), e);
            errorMessage = e.getMessage();
        }
        appModelAssessMapper.update(new LambdaUpdateWrapper<AppModelAssess>()
                .eq(AppModelAssess::getId, modelAssessId)
                .set(AppModelAssess::getAssessState, ssessState)
                .set(AppModelAssess::getErrorMsg, errorMessage));
    }

    @Override
    public Map<String,Object> manualStatistics(Long id) {
        HashMap<String, Object> objectHashMap = new HashMap<>();
        List<AppAssessModelModelRelationship> appAssessModelModelRelationships = appAssessModelModelRelationshipMapper.selectList(new LambdaQueryWrapper<AppAssessModelModelRelationship>()
                .eq(AppAssessModelModelRelationship::getAssessId, id));
        for (AppAssessModelModelRelationship appAssessModelModelRelationship:appAssessModelModelRelationships) {
            Long modelDeployId = appAssessModelModelRelationship.getModelDeployId();
            ModelDeployTask modelDeployTask = modelDeployTaskMapper.selectOne(new LambdaQueryWrapper<ModelDeployTask>()
                    .eq(ModelDeployTask::getId, modelDeployId));
            if (null == modelDeployTask) {
                log.warn("人工评估统计接口：模型[{}]已被删除！", modelDeployId);
                continue;
            }
            String deployTaskModelName = modelDeployTask.getModelName();
            List<AppManualAssessInfo> appManualAssessInfoList = appManualAssessInfoMapper.selectList(new LambdaQueryWrapper<AppManualAssessInfo>()
                    .eq(AppManualAssessInfo::getModelAssessId, id)
                    .eq(AppManualAssessInfo::getModelDeployId, modelDeployId));
            // 用于存储维度和累计分数的 Map
            HashMap<String, Integer> scoreSummary = new HashMap<>();
            // 用于存储每个维度出现的次数
            HashMap<String, Integer> dimensionCount = new HashMap<>();
            // 用于存储每个分数出现的次数
            // 初始化分数频率，确保0、1、2都有初始值
            HashMap<Object, Integer> scoreFrequency = new HashMap<>();
            scoreFrequency.put("0", 0);
            scoreFrequency.put("1", 0);
            scoreFrequency.put("2", 0);
            scoreFrequency.put("无效", 0);
            // 处理每个 AppManualAssessInfo 对象
            for (AppManualAssessInfo appManualAssessInfo : appManualAssessInfoList) {
                String manualAssessmentContent = appManualAssessInfo.getManualAssessmentContent();
                if (manualAssessmentContent != null) {
                    JSONArray jsonArray = JSON.parseArray(manualAssessmentContent);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        String dimension = jsonObject.getString("dimension");
                        if (jsonObject.getString("score") == null) {
                            scoreFrequency.merge("无效", 1, Integer::sum);
                        } else {
                            int score = jsonObject.getInteger("score");
                            scoreSummary.merge(dimension, score, Integer::sum);
                            scoreFrequency.merge(String.valueOf(score), 1, Integer::sum);
                            dimensionCount.merge(dimension, 1, Integer::sum);
                        }
                    }
                }
            }
            // 计算每个维度的平均分数
            List<Map<String, Object>> averageScores = scoreSummary.entrySet().stream().map(entry -> {
                Map<String, Object> map = new HashMap<>();
                String dimension = entry.getKey();
                int totalScore = entry.getValue();
                int count = dimensionCount.get(dimension);
                double average = (double) totalScore / count;
                map.put(dimension, average);
//                map.put("averageScore", average);
//                map.put("count", count);
                return map;
            }).collect(Collectors.toList());
            // 计算总的平均分数
            int totalScore = scoreSummary.values().stream().mapToInt(Integer::intValue).sum();
            int totalCount = appManualAssessInfoList.size() * dimensionCount.size();
            double overallAverageScore = totalCount != 0 ? (double) totalScore / totalCount : 0;
            // 打印结果
            log.debug("每个维度的平均分数和数量：");
            averageScores.forEach(System.out::println);
            Map<String, Object> map = new HashMap<>();
            map.put("平均值",overallAverageScore);
            averageScores.add(map);
            log.debug("总的平均分数：" + overallAverageScore);
            log.debug("分数频率统计：");
            scoreFrequency.forEach((score, frequency) -> log.debug("分数 " + score + " 出现次数：" + frequency));
            HashMap<String, Object> hashMap = new HashMap<>();
            // 每个维度的平均分数和数量
            hashMap.put("avgScoreAndGuantity",averageScores);
//            // 总的平均分数
//            hashMap.put("totalAvgScore", overallAverageScore);
            // 分数频率统计
            hashMap.put("fractionalFrequencyStatistics", scoreFrequency);
            objectHashMap.put(deployTaskModelName, hashMap);
        }
        return objectHashMap;
    }

    @Override
    public List<Map<String, Object>> newManualStatistics(Long id) {
        List<Map<String, Object>> calculateList = new ArrayList<>();
        Map<Object, Object> objectHashMap = new HashMap<>();
        List<String> dimensionList = new ArrayList<>();
        List<AppAssessModelModelRelationship> appAssessModelModelRelationships = appAssessModelModelRelationshipMapper.selectList(new LambdaQueryWrapper<AppAssessModelModelRelationship>()
                .eq(AppAssessModelModelRelationship::getAssessId, id));
        //取出主表分组信息
        AppModelAssess appModelAssess = appModelAssessMapper.selectById(id);
        List<String> group = getGroup(appModelAssess.getAssessDimension());
        for (AppAssessModelModelRelationship appAssessModelModelRelationship : appAssessModelModelRelationships) {
            List<AppManualAssessInfo> manualAssessInfoList = appManualAssessInfoMapper.selectList(new LambdaQueryWrapper<AppManualAssessInfo>().eq(AppManualAssessInfo::getModelAssessId, appAssessModelModelRelationship.getAssessId()).
                    eq(AppManualAssessInfo::getModelId, appAssessModelModelRelationship.getModelId()));

            // 用于存储维度和累计分数的 Map
            HashMap<String, Integer> scoreSummary = new HashMap<>();
            // 用于存储每个维度出现的次数
            HashMap<String, Integer> dimensionCount = new HashMap<>();
            // 用于存储每个分数出现的次数
            // 初始化分数频率，确保0、1、2都有初始值
            HashMap<Object, Integer> scoreFrequency = new HashMap<>();
            //用于存储平均数维度分数
            Map<String, Object> averageMap = new HashMap<>();
            //计算分数出现个数统计值
            Map<String, Object> resultMap = new HashMap<>();
            //存储计算的各维度结果
            Map<String, Object> saveMap = new HashMap<>();

            //每个维度类别集合,用于初始化赋值
            getDimensionName(dimensionList, manualAssessInfoList);

            //初始化各个维度的值
            initialization(dimensionList, dimensionCount, averageMap, resultMap,scoreFrequency);

            // 使用流操作和map函数提取出所有对象的manualAssessmentContent属性
            List<InformationEntity> informationList = getInformationEntities(manualAssessInfoList);

            //统计每个维度，每个分值出现的次数
            extracted(resultMap, informationList);

            //处理每个 AppManualAssessInfo 对象
            handleAppManualAssessInfo(manualAssessInfoList, scoreSummary, dimensionCount, scoreFrequency);

            //统计未被评估内容总和
            noStatisticsAvailable(group, manualAssessInfoList, scoreFrequency);

            // 计算每个指标的平均值
            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                Map<String, Long> data = (Map<String, Long>) entry.getValue();
                int sum = 0;
                int total = 0;
                for (Map.Entry<String, Long> subEntry : data.entrySet()) {
                    try {
                        int key = Integer.parseInt(subEntry.getKey());
                        sum += key * subEntry.getValue();
                        total += subEntry.getValue();
                    } catch (NumberFormatException e) {
                        // 处理无法解析为整数的情况
                        continue;
                    }
                }
                if (total != 0) {
                    double average = (double) sum / total;
                    averageMap.put(entry.getKey(), Double.parseDouble(String.format("%.2f", average)));
                }
            }

            // 计算总均值
            double totalSum = 0;
            int totalCount = 0;
            for (Map.Entry<String, Object> entry : averageMap.entrySet()) {
                if (entry.getValue() instanceof Double) {
                    totalSum += (Double) entry.getValue();
                    totalCount++;
                }
            }
            double totalAverage = totalSum / totalCount;
            // 保留两位小数
            totalAverage = Double.parseDouble(String.format("%.2f", totalAverage));

            // 将总均值存入map
            averageMap.put("totalAverage", totalAverage);
            saveMap.put("scoresCount",scoreFrequency);
            saveMap.put("modelId",appAssessModelModelRelationship.getModelId());
            saveMap.put("modelName",appAssessModelModelRelationship.getModelName());
            saveMap.put("commentStatistics",resultMap);
            saveMap.put("dimensionCount",dimensionCount);
            saveMap.put("average",averageMap);
            calculateList.add(saveMap);
        }
        return calculateList;
    }

    /**
     * 统计未被评估内容总和
     * @param group
     * @param manualAssessInfoList
     * @param scoreFrequency
     */
    private static void noStatisticsAvailable(List<String> group, List<AppManualAssessInfo> manualAssessInfoList, HashMap<Object, Integer> scoreFrequency) {
        int invalidCount = 0;
        for (String dimension : group) {
            boolean exists = false;
            for (AppManualAssessInfo assessment : manualAssessInfoList) {
                if (assessment.getManualAssessmentContent().equals(dimension)) {
                    exists = true;
                    break;
                }
            }
            if (!exists) {
                invalidCount++;
            }
        }
        scoreFrequency.merge("无效", invalidCount, Integer::sum);
    }

    /**
     * 处理每个 AppManualAssessInfo 对象
     * @param manualAssessInfoList
     * @param scoreSummary
     * @param dimensionCount
     * @param scoreFrequency
     */
    private static void handleAppManualAssessInfo(List<AppManualAssessInfo> manualAssessInfoList, HashMap<String, Integer> scoreSummary, HashMap<String, Integer> dimensionCount, HashMap<Object, Integer> scoreFrequency) {

        for (AppManualAssessInfo appManualAssessInfo : manualAssessInfoList) {
            String manualAssessmentContent = appManualAssessInfo.getManualAssessmentContent();
            if (manualAssessmentContent != null) {
                JSONArray jsonArray = JSON.parseArray(manualAssessmentContent);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String dimension = jsonObject.getString("dimension");
                    if (jsonObject.getString("score") == null) {
                        scoreFrequency.merge("无效", 1, Integer::sum);
                    } else {
                        int score = jsonObject.getInteger("score");
                        scoreSummary.merge(dimension, score, Integer::sum);
                        scoreFrequency.merge(String.valueOf(score), 1, Integer::sum);
                        dimensionCount.merge(dimension, 1, Integer::sum);
                    }
                }
            }
        }
    }

    /**
     * 统计每个维度，每个分值出现的次数
     * @param resultMap
     * @param informationList
     */
    private static void extracted(Map<String, Object> resultMap, List<InformationEntity> informationList) {
        for (InformationEntity entity : informationList) {
            String dimension = entity.getDimension();
            String score = entity.getScore();

            if (!resultMap.containsKey(dimension)) {
                resultMap.put(dimension, new HashMap<Integer, Long>());
            }
            Map<String, Long> scoreCountMap = (Map<String, Long>) resultMap.get(dimension);
            if (score==null){
                scoreCountMap.put("无效", scoreCountMap.getOrDefault(score, 0L) + 1);

            }else {
                scoreCountMap.put(score, scoreCountMap.getOrDefault(score, 0L) + 1);

            }
        }
    }

    /**
     *  使用流操作和map函数提取出所有对象的manualAssessmentContent属性
     * @param manualAssessInfoList
     * @return
     */
    @NotNull
    private static List<InformationEntity> getInformationEntities(List<AppManualAssessInfo> manualAssessInfoList) {
        List<String> jsonStrings = manualAssessInfoList.stream()
                .map(obj -> obj.getManualAssessmentContent())
                .collect(Collectors.toList());
        List<InformationEntity> informationList = new ArrayList<>();
        for (String jsonStr : jsonStrings) {
            cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(jsonStr);

            for (Object obj : jsonArray) {
                cn.hutool.json.JSONObject jsonObj = (cn.hutool.json.JSONObject) obj;
                InformationEntity entity = new InformationEntity();
                entity.setDimension(jsonObj.getStr("dimension"));
                entity.setDesc(jsonObj.getInt("desc"));
                entity.setScore(jsonObj.getStr("score"));
                informationList.add(entity);
            }
        }
        return informationList;
    }

    /**
     * 每个维度类别集合,用于初始化赋值
     * @param dimensionList
     * @param manualAssessInfoList
     */
    private static void getDimensionName(List<String> dimensionList, List<AppManualAssessInfo> manualAssessInfoList) {
        for (AppManualAssessInfo manualAssessInfo : manualAssessInfoList) {
            cn.hutool.json.JSONArray jsonArray =  JSONUtil.parseArray(manualAssessInfo.getManualAssessmentContent());
            for (int i = 0; i < jsonArray.size(); i++) {
                cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                String dimension = jsonObject.getStr("dimension");
                if (!dimensionList.contains(dimension)) {
                    dimensionList.add(dimension);
                }
            }
        }
    }

    /**
     * 初始化各维度值
     * @param dimensionList
     * @param dimensionCount
     * @param averageMap
     * @param resultMap
     * @param scoreFrequency
     */
    private static void initialization(List<String> dimensionList, HashMap<String, Integer> dimensionCount, Map<String, Object> averageMap, Map<String, Object> resultMap,HashMap<Object, Integer> scoreFrequency) {
        scoreFrequency.put("0", 0);
        scoreFrequency.put("1", 0);
        scoreFrequency.put("2", 0);
        scoreFrequency.put("无效", 0);

        for (String dimension : dimensionList) {
            //初始化维度信息，赋默认值
            dimensionCount.put(dimension,0);
            //给每个维度出现的次数算每个得分出现几次赋默认值
            Map<String, Object> dimensionMap = new HashMap<>();
            Map<String, Object> originalMap = (Map<String, Object>) resultMap.get(dimension);

            if (originalMap != null) {
                dimensionMap.putAll(originalMap);
            } else {
                dimensionMap.put("0", 0L);
                dimensionMap.put("1", 0L);
                dimensionMap.put("2", 0L);
                dimensionMap.put("无效", 0L);
            }
            //封装
            resultMap.put(dimension, dimensionMap);
            averageMap.put(dimension,0.0);
        }
    }


    /**
     * 获评取评分维度分组信息
     * @param json
     * @return
     */
    public List<String> getGroup(String json) {
        List<String> assessDimension = new ArrayList<>();
        assessDimension = new ArrayList<>();
        cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray(json);
        for (int i = 0; i < jsonArray.size(); i++) {
            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
            String dimension = jsonObject.getStr("dimension");
            assessDimension.add(dimension);
        }
        return assessDimension;
    }

    /**
     * 初始化人工评估
     * @param dataRecordsList  数据
     * @param appModelAssess  评估任务
     */
//    @Transactional(rollbackFor = Exception.class)
//    public void manualScoring(List<DataRecords> dataRecordsList,AppModelAssess appModelAssess) throws ThrowUpwardsException {
//        Long createUserId = appModelAssess.getCreateUserId();
//        Long modelAssessId = appModelAssess.getId();
//        int dataRecordsSize = dataRecordsList.size();
//        // 查询有几个模型需要评估
//        List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList = appAssessModelModelRelationshipMapper.selectList(new LambdaQueryWrapper<AppAssessModelModelRelationship>()
//                .eq(AppAssessModelModelRelationship::getAssessId, modelAssessId));
//        for (AppAssessModelModelRelationship appAssessModelModelRelationship:appAssessModelModelRelationshipList) {
//
//            int frequency = 0;
//            int current=0;
//            List<AppManualAssessInfo> appManualAssessInfoList = new ArrayList<>();
//            // 获取待评估模型信息
//            Long evaluatedModelId = appAssessModelModelRelationship.getModelId();
//            String evaluatedModelType = appAssessModelModelRelationship.getModelType();
//            String modelName = appAssessModelModelRelationship.getModelName();
//
//            // 构建待评估模型map
//            Map<String, Object> evaluatedModelMap = new HashMap<>();
//            evaluatedModelMap.put("modelType",evaluatedModelType);
//            evaluatedModelMap.put("modelId",evaluatedModelId);
//
//            Map<String, Object> evaluatedParameterMap = setModelConfigData(evaluatedModelMap);
//
//
//            for (DataRecords dataRecords : dataRecordsList) {
//                AppManualAssessInfo appManualAssessInfo = new AppManualAssessInfo();
//                String dataContent = dataRecords.getDataContent();
//                JSONObject jsonObject = JSON.parseObject(dataContent);
//                // 解析数据集其中的问题instruction和回答output
//                String instruction = jsonObject.getString("instruction");
//                //异常结束
//
//
//                // 调用部署模型的openAl接口，接收返回值---调用python接口---评估模型
//                evaluatedParameterMap.put("prompt_str",instruction);
//                String url = baseUrl+"/llmApp/promptProject/baseChat";
//                String modelResult = httpUtil.post(url, evaluatedParameterMap);
//
//                String output = jsonObject.getString("output");
//
//                // 解析模型返回接送的值
//                String content = JSONUtil.parseObj(modelResult).getStr("data");
//                appManualAssessInfo.setPromptProblem(instruction);
//                appManualAssessInfo.setModelResult(content);
//                appManualAssessInfo.setExpectedAnswer(output);
//                appManualAssessInfo.setId(idGenerator.nextLongId());
//                appManualAssessInfo.setCreateTime(new Date());
//                appManualAssessInfo.setCreateUserId(createUserId);
//                appManualAssessInfo.setIsAssessed(-1);
//                appManualAssessInfo.setIsDeleted(1);
//                appManualAssessInfo.setModelAssessId(modelAssessId);
//                appManualAssessInfo.setModelId(evaluatedModelId);
//                appManualAssessInfo.setAssessModelName(modelName);
//                appManualAssessInfo.setDataDeptId(1787740798154969088L);
//                appManualAssessInfoList.add(appManualAssessInfo);
//                frequency++;
//                current++;
//                log.info("人工总条数为:" + dataRecordsSize + "当前处理条数为：" + current);
//                if (frequency >= 10) {
//                    appManualAssessInfoMapper.insertList(appManualAssessInfoList);
//                    frequency = 0;
//                    appManualAssessInfoList.clear();
//                }
//
//            }
//            appManualAssessInfoMapper.insertList(appManualAssessInfoList);
//        }
//    }
    @Transactional(rollbackFor = Exception.class)
    public void manualScoring(List<DataRecords> dataRecordsList, AppModelAssess appModelAssess) throws ThrowUpwardsException {
        Long createUserId = appModelAssess.getCreateUserId();
        Long modelAssessId = appModelAssess.getId();
        int dataRecordsSize = dataRecordsList.size();
        // 查询有几个模型需要评估
        List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList = appAssessModelModelRelationshipMapper.selectList(new LambdaQueryWrapper<AppAssessModelModelRelationship>()
                .eq(AppAssessModelModelRelationship::getAssessId, modelAssessId));
        for (AppAssessModelModelRelationship appAssessModelModelRelationship : appAssessModelModelRelationshipList) {

            int frequency = 0;
            int current = 0;
            List<AppManualAssessInfo> appManualAssessInfoList = new ArrayList<>();
            // 获取待评估模型信息
            Long evaluatedModelId = appAssessModelModelRelationship.getModelId();
            String evaluatedModelType = appAssessModelModelRelationship.getModelType();
            String modelName = appAssessModelModelRelationship.getModelName();

            // 构建待评估模型map
            Map<String, Object> evaluatedModelMap = new HashMap<>();
            evaluatedModelMap.put("modelType", evaluatedModelType);
            evaluatedModelMap.put("modelId", evaluatedModelId);

            Map<String, Object> evaluatedParameterMap = setModelConfigData(evaluatedModelMap);


            for (DataRecords dataRecords : dataRecordsList) {
                AppManualAssessInfo appManualAssessInfo = new AppManualAssessInfo();
                String dataContent = dataRecords.getDataContent();
                JSONObject jsonObject;
                try {
                    jsonObject = JSON.parseObject(dataContent);
                    // 解析数据集其中的问题instruction和回答output
                    String instruction = jsonObject.getString("instruction");
                    //连接不上/*************:32099导致异常结束


                    // 调用部署模型的openAl接口，接收返回值---调用python接口---评估模型
                    evaluatedParameterMap.put("prompt_str", instruction);
                    Map<String, Object> requestDataMap = new HashMap<>();
                    requestDataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(requestDataMap);
                    PythonResponse request = pythonClientService.request(AppUrlEnums.BASE_CHAT, null, evaluatedParameterMap, requestDataMap);
                    String modelResult = new ObjectMapper().writeValueAsString(request);
//                  String url = baseUrl + "/llmApp/promptProject/baseChat";
//                  String modelResult = httpUtil.post(url, evaluatedParameterMap);
//
                    String output = jsonObject.getString("output");

                    // 解析模型返回接送的值
                    String content = JSONUtil.parseObj(modelResult).getStr("data");
                    appManualAssessInfo.setPromptProblem(instruction);
                    appManualAssessInfo.setModelResult(content);
                    appManualAssessInfo.setExpectedAnswer(output);
                    appManualAssessInfo.setId(idGenerator.nextLongId());
                    appManualAssessInfo.setCreateTime(new Date());
                    appManualAssessInfo.setCreateUserId(createUserId);
                    appManualAssessInfo.setIsAssessed(-1);
                    appManualAssessInfo.setIsDeleted(1);
                    appManualAssessInfo.setModelAssessId(modelAssessId);
                    appManualAssessInfo.setModelId(evaluatedModelId);
                    appManualAssessInfo.setAssessModelName(modelName);
                    appManualAssessInfo.setDataDeptId(1787740798154969088L);
                    appManualAssessInfoList.add(appManualAssessInfo);
                    frequency++;
                    current++;
                    log.info("人工总条数为：" + dataRecordsSize + ", 当前处理条数为：" + current);
                    if (frequency >= 10) {
                        appManualAssessInfoMapper.insertList(appManualAssessInfoList);
                        frequency = 0;
                        appManualAssessInfoList.clear();
                    }
                } catch (Exception e) {
                    log.error("JSON 解析异常: {}", e.getMessage(), e);
                    appModelAssess.setAssessState("连接异常");
                    appModelAssessMapper.updateById(appModelAssess);
                    continue;
                }

            }
            appManualAssessInfoMapper.insertList(appManualAssessInfoList);
        }
    }

    /**
     * 解析模型返回中的对话内容
     * @param modelResult   模型返回结果
     * @return
     */
    public String analysisModelResultJson (String modelResult) {
        // 解析裁判的返回数据，提取出需要的值
        JSONObject openAlResultJSONOb = JSON.parseObject(modelResult);
        JSONArray choicesArray = openAlResultJSONOb.getJSONArray("choices");
        JSONObject firstChoice = choicesArray.getJSONObject(0);
        JSONObject message = firstChoice.getJSONObject("message");
        return message.getString("content");
    }


    /**
     * Al评估R
     * @param dataRecordsList 数据
     * @param appModelAssess  评估任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void automaticRefereeScoring(List<DataRecords> dataRecordsList,AppModelAssess appModelAssess) throws ThrowUpwardsException, IOException {
        Long createUserId = appModelAssess.getCreateUserId();
        Long modelAssessId = appModelAssess.getId();
        int dataRecordsSize = dataRecordsList.size();
        String scoringPrompt = appModelAssess.getScoringPrompt();
   //     String refereeModel = appModelAssess.getRefereeModel();
        JSONObject scoringPromptJson = JSON.parseObject(scoringPrompt);
        // 解析问题
        String prompt = scoringPromptJson.getString("prompt");
        String maxScore = scoringPromptJson.getString("max_score");
        String metric = scoringPromptJson.getString("metric");
        String steps = scoringPromptJson.getString("steps");
        String replace = prompt.replace("{max_score}", maxScore)
                .replace("{metric}", metric).replace("{steps}", steps);
        // 获取裁判员模型和裁判员类型
        Long modelId = appModelAssess.getModelId();
        String modelType = appModelAssess.getModelType();
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("modelType",modelType);
        modelMap.put("modelId",modelId);


        // 查询有几个模型需要评估
        List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList = appAssessModelModelRelationshipMapper.selectList(new LambdaQueryWrapper<AppAssessModelModelRelationship>()
                .eq(AppAssessModelModelRelationship::getAssessId, modelAssessId));
        for (AppAssessModelModelRelationship appAssessModelModelRelationship:appAssessModelModelRelationshipList) {
            // 获取待评估模型信息
            Long evaluatedModelId = appAssessModelModelRelationship.getModelId();
            String evaluatedModelType = appAssessModelModelRelationship.getModelType();
            String modelName = appAssessModelModelRelationship.getModelName();

            // 构建待评估模型map
            Map<String, Object> evaluatedModelMap = new HashMap<>();
            evaluatedModelMap.put("modelType",evaluatedModelType);
            evaluatedModelMap.put("modelId",evaluatedModelId);

            Map<String, Object> evaluatedParameterMap = setModelConfigData(evaluatedModelMap);


            // 记录处理第几条
            int frequency = 0;
            int current=0;

            List<AppAiAssessInfo> appAiAssessInfoList = new ArrayList<>();
            for (DataRecords dataRecords : dataRecordsList) {
                AppAiAssessInfo appAiAssessInfo = new AppAiAssessInfo();
                // 将dataRecords中的DataContent的json字符串解析
                String dataContent = dataRecords.getDataContent();
                JSONObject jsonObject = JSON.parseObject(dataContent);
                // 解析问题
                String instruction = jsonObject.getString("instruction"); // 调用评估模型时传入
                String output = jsonObject.getString("output");
                // 调用部署模型的openAl接口，接收返回值---调用python接口---评估模型
                evaluatedParameterMap.put("prompt_str",instruction);

                Map<String, Object> requestDataMap = new HashMap<>();
                requestDataMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(requestDataMap);
                PythonResponse request = pythonClientService.request(AppUrlEnums.BASE_CHAT, null, evaluatedParameterMap, requestDataMap);
                String evaluatedPost = new ObjectMapper().writeValueAsString(request);

//              String url = baseUrl+"/llmApp/promptProject/baseChat";
//              String evaluatedPost = httpUtil.post(url, evaluatedParameterMap);

                JSONObject parseObject = JSON.parseObject(evaluatedPost);
                String modelResultJson = parseObject.getString("data");

                //根据模型返回值来搭建提示工程
                String promptPrompt = replace.replace("{src}", instruction)
                        .replace("{tgt}", output)
                        .replace("{prediction}", modelResultJson);

                // 接受到对模型提问的返回值
                // 去调用裁判来进行评分
                evaluatedModelMap.put("prompt_str",promptPrompt);

                PythonResponse response = pythonClientService.request(AppUrlEnums.BASE_CHAT, null, evaluatedParameterMap, requestDataMap);
                String trialPost = new ObjectMapper().writeValueAsString(response);

//              String trialPost = httpUtil.post(url, evaluatedModelMap);

                JSONObject trialObject = JSON.parseObject(trialPost);
                String trialResultJson = trialObject.getString("data");



                // 提取评分和评分原因
                String[] parts = trialResultJson.split("\\n", 2);
                String score = parts[0].substring(parts[0].indexOf(":") + 1).trim(); // "评分: 9"
                String reason = parts.length > 1 ? parts[1].substring(parts[1].indexOf(":") + 1).trim() : ""; // "原因: ..."
                // 构建appAiAssessInfo实体将
                // 判断score是否为数字避免出错
                if (score.matches("\\d+")) {
                    appAiAssessInfo.setRefereeModelScore(Integer.valueOf(score));
                    appAiAssessInfo.setRefereeModelScoringReason(reason);
                } else {
                    appAiAssessInfo.setRefereeModelScoringReason(trialResultJson);
                }
                appAiAssessInfo.setPromptProblem(instruction);
                appAiAssessInfo.setModelResult(modelResultJson);
                appAiAssessInfo.setExpectedAnswer(output);
                appAiAssessInfo.setId(idGenerator.nextLongId());
                appAiAssessInfo.setCreateTime(new Date());
                appAiAssessInfo.setCreateUserId(createUserId);
                appAiAssessInfo.setIsDeleted(1);
                appAiAssessInfo.setModelAssessId(modelAssessId);
                appAiAssessInfo.setModelId(evaluatedModelId);
                appAiAssessInfo.setAssessModelName(modelName);
                appAiAssessInfo.setIsAssessed(1);
                appAiAssessInfo.setDataDeptId(1787740798154969088L);
                appAiAssessInfoList.add(appAiAssessInfo);
                appAiAssessInfo.setJudgeResult(trialResultJson);
                frequency++;
                current++;
                log.info("Ai总条数为:" + dataRecordsSize + "当前处理条数为：" + current);
                // 判断是否有10条数据
                if (frequency >= 10) {
                    appAiAssessInfoMapper.insertList(appAiAssessInfoList);
                    frequency=0;
                    appAiAssessInfoList.clear();
                }
            }
            if (!appAiAssessInfoList.isEmpty()) {
                appAiAssessInfoMapper.insertList(appAiAssessInfoList);
            }
        }
    }





    private String invokeOpenAl(String url, String apiKey, String modelName, String instruction, Long id) throws ThrowUpwardsException {
        instruction = instruction.replace("\"", "\\\"").replace("\n", "\\n");
        // 创建请求体 JSON 字符串
        String jsonBody = "{"
                + "\"model\": \"" + modelName + "\","
                + "\"messages\": ["
                + "  {"
                + "    \"role\": \"user\","
                + "    \"content\": \"" + instruction + "\""
                + "  }"
                + "]"
                + "}";
        RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json; charset=utf-8"));
        // 创建 HTTP 客户端和请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + apiKey)
                .post(body)
                .build();
        // 在构造器中创建客户端，并设置超时
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(300, TimeUnit.SECONDS)   // 设置合理的超时时间
                .writeTimeout(300, TimeUnit.SECONDS)
                .connectTimeout(300, TimeUnit.SECONDS)
                .build();
        // 发送请求并处理响应
        Response response = null;
        try {
            response = client.newCall(request).execute();
            boolean successful;
            try {
                successful = response.isSuccessful();
            } catch (Exception e) {
                throw new ThrowUpwardsException(e);
            }
            if (!successful) {
                // 如果请求失败需要去处理任务状态
                throw new ThrowUpwardsException("\"Error: " + response.code() + " - " + response.message());
            }
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
            throw new ThrowUpwardsException("Error: 请求模型接口出错请重新尝试！" + e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    private AppModelAssess buildDefaultValue(AppModelAssess appModelAssess) {
        if (appModelAssess.getId() == null) {
            appModelAssess.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(appModelAssess);
        appModelAssess.setIsDelete(GlobalDeletedFlag.NORMAL);
        return appModelAssess;
    }


    /**
     * 设置模型的相关信息
     */
    @NotNull
    public Map<String, Object> setModelConfigData(Map<String, Object> dataMap) {
        if (!dataMap.containsKey("modelType")) {
            log.warn("缺少必要的参数[modelType]!");
            return dataMap;
        }
        if (!dataMap.containsKey("modelId")) {
            log.warn("缺少必要的参数[modelId]!");
            return dataMap;
        }
        String modelIdStr = dataMap.get("modelId").toString();
        if (modelIdStr.isEmpty()) {
            throw new InvalidParameterException("模型ID不能为空！");
        }
        Long modelId = Long.valueOf(modelIdStr);
        String modelType = dataMap.get("modelType").toString();
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskMapper.selectById(modelId);
            setModelDeployConfig(dataMap, modelId, modelDeployTask);
        } else if ("public".equals(modelType)) {
            Map<String, Object> modelConfig = externalModelConfigService.getModelConfigById(modelId, null);
            dataMap.put("model_config", modelConfig);
        } else {
            throw new InvalidParameterException("模型类型[" + modelType + "]错误！");
        }
        return dataMap;
    }

    /**
     * 设置部署的模型的配置信息
     * @param dataMap
     * @param modelDeployTaskId
     * @param modelDeployTask
     */
    private static void setModelDeployConfig(Map<String, Object> dataMap, Long modelDeployTaskId, ModelDeployTask modelDeployTask) {
        if (modelDeployTask == null) {
            throw (RuntimeException) new RuntimeException("模型部署任务[" + modelDeployTaskId + "]不存在！").initCause(null);
        }
        Map<String, Object> modelConfig;
        if (dataMap.containsKey("model_config")) {
            modelConfig = (Map<String, Object>) dataMap.get("model_config");
        } else {
            modelConfig = new HashMap<>();
        }
        modelConfig.put("model_type", "ChatOpenAI");
        modelConfig.put("model_name", modelDeployTask.getModelName());
        // 设置模型地址及其密钥
        StringBuilder baseModelUrl = new StringBuilder("http://");
        if (null != modelDeployTask.getServerHost()) {
            baseModelUrl.append(modelDeployTask.getServerHost());
        }
        if (null != modelDeployTask.getApiServerPort()) {
            baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
        }
        baseModelUrl.append("/v1");
        modelConfig.put("api_base", baseModelUrl.toString());
        modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        dataMap.put("model_config", modelConfig);
    }
}
