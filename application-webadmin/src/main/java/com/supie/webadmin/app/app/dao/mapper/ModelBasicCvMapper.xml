<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.app.dao.ModelBasicCvMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.app.model.ModelBasicCv">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="model_size" jdbcType="VARCHAR" property="modelSize"/>
        <result column="model_desc" jdbcType="VARCHAR" property="modelDesc"/>
        <result column="model_type" jdbcType="INTEGER" property="modelType"/>
        <result column="model_format" jdbcType="INTEGER" property="modelFormat"/>
        <result column="is_download" jdbcType="INTEGER" property="isDownload"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_model_basic_cv
            (id,
            str_id,
            is_delete,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            model_name,
            model_size,
            model_desc,
            model_type,
            model_format,
            is_download)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.modelName},
            #{item.modelSize},
            #{item.modelDesc},
            #{item.modelType},
            #{item.modelFormat},
            #{item.isDownload})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.app.dao.ModelBasicCvMapper.inputFilterRef"/>
        AND lmd_model_basic_cv.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="modelBasicCvFilter != null">
            <if test="modelBasicCvFilter.id != null">
                AND lmd_model_basic_cv.id = #{modelBasicCvFilter.id}
            </if>
            <if test="modelBasicCvFilter.strId != null and modelBasicCvFilter.strId != ''">
                AND lmd_model_basic_cv.str_id = #{modelBasicCvFilter.strId}
            </if>
            <if test="modelBasicCvFilter.createUserId != null">
                AND lmd_model_basic_cv.create_user_id = #{modelBasicCvFilter.createUserId}
            </if>
            <if test="modelBasicCvFilter.createTimeStart != null and modelBasicCvFilter.createTimeStart != ''">
                AND lmd_model_basic_cv.create_time &gt;= #{modelBasicCvFilter.createTimeStart}
            </if>
            <if test="modelBasicCvFilter.createTimeEnd != null and modelBasicCvFilter.createTimeEnd != ''">
                AND lmd_model_basic_cv.create_time &lt;= #{modelBasicCvFilter.createTimeEnd}
            </if>
            <if test="modelBasicCvFilter.updateUserId != null">
                AND lmd_model_basic_cv.update_user_id = #{modelBasicCvFilter.updateUserId}
            </if>
            <if test="modelBasicCvFilter.updateTimeStart != null and modelBasicCvFilter.updateTimeStart != ''">
                AND lmd_model_basic_cv.update_time &gt;= #{modelBasicCvFilter.updateTimeStart}
            </if>
            <if test="modelBasicCvFilter.updateTimeEnd != null and modelBasicCvFilter.updateTimeEnd != ''">
                AND lmd_model_basic_cv.update_time &lt;= #{modelBasicCvFilter.updateTimeEnd}
            </if>
            <if test="modelBasicCvFilter.dataUserId != null">
                AND lmd_model_basic_cv.data_user_id = #{modelBasicCvFilter.dataUserId}
            </if>
            <if test="modelBasicCvFilter.dataDeptId != null">
                AND lmd_model_basic_cv.data_dept_id = #{modelBasicCvFilter.dataDeptId}
            </if>
            <if test="modelBasicCvFilter.modelName != null and modelBasicCvFilter.modelName != ''">
                <bind name = "safeModelBasicCvModelName" value = "'%' + modelBasicCvFilter.modelName + '%'" />
                AND lmd_model_basic_cv.model_name LIKE #{safeModelBasicCvModelName}
            </if>
            <if test="modelBasicCvFilter.modelSize != null and modelBasicCvFilter.modelSize != ''">
                AND lmd_model_basic_cv.model_size = #{modelBasicCvFilter.modelSize}
            </if>
            <if test="modelBasicCvFilter.modelDesc != null and modelBasicCvFilter.modelDesc != ''">
                <bind name = "safeModelBasicCvModelDesc" value = "'%' + modelBasicCvFilter.modelDesc + '%'" />
                AND lmd_model_basic_cv.model_desc LIKE #{safeModelBasicCvModelDesc}
            </if>
            <if test="modelBasicCvFilter.modelType != null">
                AND lmd_model_basic_cv.model_type = #{modelBasicCvFilter.modelType}
            </if>
            <if test="modelBasicCvFilter.modelFormat != null">
                AND lmd_model_basic_cv.model_format = #{modelBasicCvFilter.modelFormat}
            </if>
            <if test="modelBasicCvFilter.isDownload != null">
                AND lmd_model_basic_cv.is_download = #{modelBasicCvFilter.isDownload}
            </if>
            <if test="modelBasicCvFilter.searchString != null and modelBasicCvFilter.searchString != ''">
                <bind name = "safeModelBasicCvSearchString" value = "'%' + modelBasicCvFilter.searchString + '%'" />
                AND IFNULL(lmd_model_basic_cv.model_name,'') LIKE #{safeModelBasicCvSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedModelBasicCvList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.ModelBasicCv">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_model_basic_cv
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_model_basic_cv
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getModelBasicCvList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.app.model.ModelBasicCv">
        SELECT * FROM lmd_model_basic_cv
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
