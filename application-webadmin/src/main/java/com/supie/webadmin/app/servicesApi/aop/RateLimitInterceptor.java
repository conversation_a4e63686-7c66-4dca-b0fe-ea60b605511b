package com.supie.webadmin.app.servicesApi.aop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.ResponseResult;
import com.supie.webadmin.app.app.model.ModelService;
import com.supie.webadmin.app.app.model.ModelServiceApi;
import com.supie.webadmin.app.app.service.ModelServiceApiService;
import com.supie.webadmin.app.app.service.ModelServiceService;
import com.supie.webadmin.app.servicesApi.annotation.ModelServiceApiRateLimit;
import com.supie.webadmin.app.servicesApi.constant.ServiceConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RateLimitInterceptor {

    private final ModelServiceApiService modelServiceApiService;

    private final ModelServiceService modelServiceService;

    private final RedissonClient redissonClient;

    /**
     * 执行拦截
     *
     * @param joinPoint                切入点
     * @param modelServiceApiRateLimit 限流注解注解
     */
    @Around("@annotation(modelServiceApiRateLimit)")
    public Object doInterceptor(ProceedingJoinPoint joinPoint, ModelServiceApiRateLimit modelServiceApiRateLimit) throws Throwable {
        // 获取 模型服务API对象
        ModelServiceApi modelServiceApi = getModelServiceApi(joinPoint, modelServiceApiRateLimit);
        if (modelServiceApi == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }

        // IP白名单
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String remoteAddr = getActualIp(request);

        List<String> ipWhiteList = parseJsonStringToList(modelServiceApi.getIpWhiteList());
        if (ipWhiteList != null && !ipWhiteList.isEmpty()) {
            if (!ipWhiteListRelease(ipWhiteList, remoteAddr)) {
                // 无法匹配白名单 禁止放行
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("当前IP无法访问该模型服务"));
                return null;
            }
        }

        long currentTimeMillis = System.currentTimeMillis();
        long endTimeMillis = modelServiceApi.getEndTime().getTime();
        // 计算剩余存活时间（毫秒）
        long ttlMillis = endTimeMillis - currentTimeMillis;
        if (ttlMillis < 0) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("当前服务已经过了有效期，请联系管理员！"));
            return null;
        }

        // 获取 并发数
        Integer semaphoreNumber = modelServiceApi.getSemaphoreNumber();

        // 无限制 直接放行
        if (semaphoreNumber < 0) {
            return joinPoint.proceed();
        }
        /*
        使用信号量方式进行限流
         */
//        // 构造 Redis key
//        String semaphoreKey = ServiceConstant.SEMAPHORE_NUMBER_KEY + modelServiceApi.getServiceName();
//        RSemaphore semaphore = redissonClient.getSemaphore(semaphoreKey);
//
//        // 获取读写锁，并使用读锁保护读操作
//        RReadWriteLock rwLock = redissonClient.getReadWriteLock(ServiceConstant.READ_WRITE_LOCK_KEY + modelServiceApi.getServiceName());
//        rwLock.readLock().lock();
//        boolean acquireFlag = false;
//        // 使用信号量进行限流
//        try {
//            // 判断 Redis 中已经存在对应模型的信号量
//            if (semaphoreNumber > 0 && !semaphore.isExists()) {
//                // 没有则创建
//                semaphore.trySetPermits(semaphoreNumber);
//                semaphore.expire(ttlMillis, TimeUnit.MILLISECONDS);
//            }
//            if (semaphoreNumber > 0) {
//                acquireFlag = semaphore.tryAcquire();
//                if (!acquireFlag) {
//                    log.error("当前模型并发请求数已达上限！请稍后再试");
//                    throw new MyRuntimeException("当前模型并发请求数已达上限！请稍后再试");
//                }
//            }
//
//            return joinPoint.proceed();
//        } catch (Exception e) {
//            String interfaceUrl = request.getRequestURI();
//            log.error("================ 接口 {} 限流异常 ================", interfaceUrl);
//            log.error(e.toString());
//            log.error("模型调用接口限流异常 异常信息：{}", e.getMessage());
//            throw new MyRuntimeException(e.getMessage());
//        } finally {
//            // 释放信号量资源
//            if (acquireFlag) {
//                semaphore.release();
//            }
//
//            // 释放读锁资源
//            rwLock.readLock().unlock();
//        }

        /*
          使用原子计数方式进行限流
         */
        // 并发量 的 redis键
        String semaphoreKey = ServiceConstant.SEMAPHORE_NUMBER_KEY + modelServiceApi.getServiceId() + ":" + modelServiceApi.getServiceName();
        // 获取对应的原子计数器
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(semaphoreKey);
        // 获得许可
        boolean acquireFlag = true;
        // 执行原本操作 分为流式与非流式
        Boolean stream = (Boolean) getRequestParamByName(joinPoint, "stream");
        Object result = null;
        try {
            // 原子计数器 +1 并获取计算过的值
            if (rAtomicLong.incrementAndGet() > semaphoreNumber) {
                // 没有获取许可 回滚 并 抛出异常
                acquireFlag = false;
                rAtomicLong.decrementAndGet();
                log.error("当前模型并发请求数已达上限！请稍后再试");
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("当前模型并发请求数已达上限！请稍后再试"));
                return null;
            }
            // 执行原方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            // 记录异常信息
            String interfaceUrl = request.getRequestURI();
            log.error("================ 接口 {} 限流异常 ================", interfaceUrl);
            log.error(e.toString());
            log.error("模型调用接口限流异常 异常信息：{}", e.getMessage(), e);
            ResponseResult.output(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, ResponseResult.error(e.getMessage()));
            return null;
        } finally {
            // 执行任务结束后归还许可
            if (acquireFlag) {
                if (Boolean.TRUE.equals(stream)) {
                    // 流式处理：等Sse处理结束
                    // 如果是SseEmitter，设置完成回调
                    if (result instanceof SseEmitter) {
                        SseEmitter emitter = (SseEmitter) result;

                        /*
                        流式接口，在前端终端时，会触发SseEmitter的两个回调任务
                        onCompletion 和 onError
                        所以要确保并发量扣减只执行一次
                         */

                        // 使用原子标志确保只减一次
                        AtomicBoolean decremented = new AtomicBoolean(false);
                        // 创建一个通用的减少计数的方法
                        Runnable decrementOnce = () -> {
                            if (decremented.compareAndSet(false, true)) {
                                rAtomicLong.decrementAndGet();
                            }
                        };

                        // 流式响应完成回调
                        emitter.onCompletion(() -> {
                            log.debug("模型服务 {} 流式接口执行成功", modelServiceApi.getServiceName());
                            decrementOnce.run();
                        });
                        // 流式响应异常回调
                        emitter.onError(throwable -> {
                            log.error("流式响应异常：{}", throwable.getMessage());
                            decrementOnce.run();
                        });
                        // 设置超时回调
                        emitter.onTimeout(() -> {
                            log.debug("模型服务 {} 流式接口执行超时", modelServiceApi.getServiceName());
                            decrementOnce.run();
                        });
                    }
                } else {
                    rAtomicLong.decrementAndGet();
                }
            }
        }

    }

    /**
     * 从连接点获取指定参数值
     *
     * @param joinPoint 连接点
     * @param paramName 参数名称
     * @return 参数值，如果未找到返回null
     */
    private Object getRequestParamByName(ProceedingJoinPoint joinPoint, String paramName) {
        // 获取方法的参数名数组
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = signature.getParameterNames();
        // 获取参数值数组
        Object[] args = joinPoint.getArgs();

        // 遍历参数名数组
        for (int i = 0; i < parameterNames.length; i++) {
            if (paramName.equals(parameterNames[i])) {
                return args[i];
            }
        }
        return null;
    }

    /**
     * 获取模型服务对象
     */
    private ModelServiceApi getModelServiceApi(ProceedingJoinPoint joinPoint, ModelServiceApiRateLimit modelServiceApiRateLimit) {
        ModelServiceApi modelServiceApi;
        if (modelServiceApiRateLimit.serviceType().equals("workFlow")) {
            // 直接通过索引0获取唯一的参数（MultipartHttpServletRequest）
            MultipartHttpServletRequest request = (MultipartHttpServletRequest) joinPoint.getArgs()[0];

            // 规定死了，使用 serviceName ！！！
            String serviceNameStr = request.getParameter("serviceName");// 规定死了，使用 serviceName ！！！
            modelServiceApi = modelServiceApiService.getByName(serviceNameStr);
        } else {
            String model = (String) getRequestParamByName(joinPoint, "model");
            String serviceKey = (String) getRequestParamByName(joinPoint, "serviceKey");
//            String serviceKey = request.getHeader("Authorization");
            ModelService modelService = modelServiceService.selectOne(new LambdaQueryWrapper<ModelService>().eq(ModelService::getServiceKey, serviceKey));
            modelServiceApi = modelServiceApiService.selectOne(new LambdaQueryWrapper<ModelServiceApi>().eq(ModelServiceApi::getServiceName, model).eq(ModelServiceApi::getServiceId, modelService.getId()));

//            modelServiceApi = modelServiceApiService.getByName(model);
        }
        return modelServiceApi;
    }

    /**
     * 根据白名单是否放行
     *
     * @param ipWhiteList IP白名单
     * @param ip          调用的IP地址
     * @return 是否放行
     */
    private Boolean ipWhiteListRelease(List<String> ipWhiteList, String ip) {
        if (ipWhiteList == null || ipWhiteList.isEmpty()) {
            return true;
        }

        for (String entity : ipWhiteList) {
            String pattern = entity.replace(".", "\\.").replace("*", "\\d+");
            if (ip.matches(pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取请求真实 IP
     *
     * @param request 请求
     * @return 真实 IP 地址
     */
    private static String getActualIp(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }

    /**
     * 将 JSON 格式的字符串转换为 List<String>
     *
     * @param jsonStr JSON 格式字符串
     * @return List<String>
     * @throws Exception 转换异常
     */
    private List<String> parseJsonStringToList(String jsonStr) throws Exception {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return new ArrayList<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(jsonStr, new TypeReference<List<String>>() {
        });
    }
}
