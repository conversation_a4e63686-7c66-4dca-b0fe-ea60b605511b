package com.supie.webadmin.app.app.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supie.common.core.annotation.DeptFilterColumn;
import com.supie.common.core.annotation.UserFilterColumn;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.common.core.base.model.BaseModel;
import com.supie.webadmin.app.app.vo.AppAssessModelModelRelationshipVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AppAssessModelModelRelationship实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_app_assess_model_model_relationship")
public class AppAssessModelModelRelationship extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 模型评估任务ID。
     */
    private Long assessId;

    /**
     * 部署模型ID。
     */
    private Long modelDeployId;

    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 模型类型
     */
    private String modelType;



    /**
     * 待评估模型名称
     */
    private String modelName;



    @Mapper
    public interface AppAssessModelModelRelationshipModelMapper extends BaseModelMapper<AppAssessModelModelRelationshipVo, AppAssessModelModelRelationship> {
    }
    public static final AppAssessModelModelRelationshipModelMapper INSTANCE = Mappers.getMapper(AppAssessModelModelRelationshipModelMapper.class);
}
