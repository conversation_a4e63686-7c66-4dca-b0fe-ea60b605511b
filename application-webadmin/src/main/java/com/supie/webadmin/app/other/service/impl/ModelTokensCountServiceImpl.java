package com.supie.webadmin.app.other.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.other.dao.ModelTokensCountMapper;
import com.supie.webadmin.app.other.model.ModelTokensCount;
import com.supie.webadmin.app.other.service.ModelTokensCountService;
import com.supie.webadmin.app.other.vo.ModelTokensCountVo;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Tokens 统计数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Slf4j
@MyDataSource(DataSourceType.CLICKHOME)
@Service("modelTokensCountService")
public class ModelTokensCountServiceImpl extends BaseService<ModelTokensCount, Long> implements ModelTokensCountService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private ModelTokensCountMapper modelTokensCountMapper;
    @Autowired
    private ModelDeployTaskService modelDeployTaskService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<ModelTokensCount> mapper() {
        return modelTokensCountMapper;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public ModelTokensCount saveNew(ModelTokensCount modelTokensCount) {
        this.buildDefaultValue(modelTokensCount);
        modelTokensCountMapper.insert(modelTokensCount);
        return modelTokensCount;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<ModelTokensCount> modelTokensCountList) {
        if (CollUtil.isNotEmpty(modelTokensCountList)) {
            modelTokensCountList.forEach(this::buildDefaultValue);
            modelTokensCountMapper.insertList(modelTokensCountList);
        }
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ModelTokensCount modelTokensCount, ModelTokensCount originalModelTokensCount) {
        modelTokensCount.setCreateUserId(originalModelTokensCount.getCreateUserId());
        modelTokensCount.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        modelTokensCount.setCreateTime(originalModelTokensCount.getCreateTime());
        modelTokensCount.setUpdateTime(new Date());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<ModelTokensCount> uw = this.createUpdateQueryForNullValue(modelTokensCount, modelTokensCount.getId());
        return modelTokensCountMapper.update(modelTokensCount, uw) == 1;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return modelTokensCountMapper.deleteById(id) == 1;
    }

    @Override
    public List<ModelTokensCount> getModelTokensCountList(ModelTokensCount filter, String orderBy) {
        return modelTokensCountMapper.getModelTokensCountList(filter, orderBy);
    }

    @Override
    public List<ModelTokensCount> getModelTokensCountListWithRelation(ModelTokensCount filter, String orderBy) {
        List<ModelTokensCount> resultList = modelTokensCountMapper.getModelTokensCountList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        resultList.sort(Comparator.comparing(ModelTokensCount::getCreateTime));
        return resultList;
    }

    @Override
    public List<ModelTokensCount> getGroupedModelTokensCountListWithRelation(
            ModelTokensCount filter, String groupSelect, String groupBy, String orderBy) {
        List<ModelTokensCount> resultList =
                modelTokensCountMapper.getGroupedModelTokensCountList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取指定时间段内的统计数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public ModelTokensCountVo getVolumeCountsBetweenDates(Date startTime, Date endTime, boolean isAll) {
        // 数据库里可以查到调用总量，失败次数，输入tokens数，输出tokens数, 总tokens数
        ModelTokensCount modelTokensCount = modelTokensCountMapper.getVolumeCountsBetweenDates(startTime, endTime, isAll);
        ModelTokensCountVo modelTokensCountVo = MyModelUtil.copyTo(modelTokensCount, ModelTokensCountVo.class);
        // 自己计算出成功调用总量
        modelTokensCountVo.setSuccessCallVolume(modelTokensCountVo.getTotalCallVolume() - modelTokensCountVo.getFailureCallVolume());
        return modelTokensCountVo;
    }

    @Override
    public List<ModelTokensCountVo> getConsumedTokensByModelName(Date startTime, Date endTime) {
        return modelTokensCountMapper.getConsumedTokensByModelName(startTime, endTime);
    }

    @Override
    public List<ModelTokensCount> callStatistics(List<String> deployModelName, List<Long> modelId, String orderByField, String sortingMethod, boolean isAll) {
        List<ModelTokensCount> resultList = modelTokensCountMapper.callStatistics(deployModelName, modelId, orderByField, sortingMethod, isAll);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);

        // 替换模型名称为任务名(字段为mirror_name)
        Map<String, ModelDeployTask> map = modelDeployTaskService.getModelNameToModelDeployTask();
        for (ModelTokensCount modelTokensCount : resultList) {
            String tempModelName = modelTokensCount.getModelName();
            if (map.containsKey(tempModelName)) {
                modelTokensCount.setModelName(map.get(tempModelName).getMirrorName());
            }
        }
        return resultList;
    }

    @Override
    public List<ModelTokensCount> getCallStatisticsByModelName(Long modelId, String createTimeStart, String createTimeEnd) {
        ModelTokensCount modelTokensCountFilter = new ModelTokensCount();
        modelTokensCountFilter.setModelId(modelId);
        modelTokensCountFilter.setCreateTimeStart(createTimeStart);
        modelTokensCountFilter.setCreateTimeEnd(createTimeEnd);
        List<ModelTokensCount> resultList = modelTokensCountMapper.getModelTokensCountList(modelTokensCountFilter, "create_time");
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);

        // 替换模型名称为任务名(字段为mirror_name)
        Map<String, ModelDeployTask> map = modelDeployTaskService.getModelNameToModelDeployTask();
        for (ModelTokensCount modelTokensCount : resultList) {
            String tempModelName = modelTokensCount.getModelName();
            if (map.containsKey(tempModelName)) {
                modelTokensCount.setModelName(map.get(tempModelName).getMirrorName());
            }
        }
        return resultList;
    }

    @Override
    public void removeByModelName(String modelName){
        modelTokensCountMapper.removeByModelName(modelName);
    }

    @Override
    public void removeByModelId(Long modelId){
        modelTokensCountMapper.removeByModelId(modelId);
    }

    private ModelTokensCount buildDefaultValue(ModelTokensCount modelTokensCount) {
        if (modelTokensCount.getId() == null) {
            modelTokensCount.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null) {
            modelTokensCount.setCreateUserId(tokenData.getUserId());
            modelTokensCount.setUpdateUserId(tokenData.getUserId());
        }
        Date now = new Date();
        modelTokensCount.setCreateTime(now);
        modelTokensCount.setUpdateTime(now);
        modelTokensCount.setIsDelete(GlobalDeletedFlag.NORMAL);
        MyModelUtil.setDefaultValue(modelTokensCount, "tokensCount", 0L);
        return modelTokensCount;
    }
}
