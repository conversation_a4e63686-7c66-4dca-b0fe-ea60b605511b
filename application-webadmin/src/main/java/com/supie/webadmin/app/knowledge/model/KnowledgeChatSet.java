package com.supie.webadmin.app.knowledge.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 知识库-对话集表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Data
@TableName(value = "lmd_knowledge_chat_set")
public class KnowledgeChatSet {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 字符id。
     */
    @TableField(exist = false)
    private String strIdNotLike;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 所属知识助手ID。
     */
    @TableField(value = "agent_id")
    private Long agentId;

    /**
     * 对话集名称。
     */
    @TableField(value = "chat_set_name")
    private String chatSetName;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * chat_set_name LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
