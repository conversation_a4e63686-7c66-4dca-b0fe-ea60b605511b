package com.supie.webadmin.app.app.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.app.dto.AppSoundAssistantRelationDto;
import com.supie.webadmin.app.app.model.AppSoundAssistantRelation;
import com.supie.webadmin.app.app.service.AppSoundAssistantRelationService;
import com.supie.webadmin.app.app.vo.AppSoundAssistantRelationVo;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 应用中心-音源文件信息与应用助手一对多关系表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "应用中心-音源文件信息与应用助手一对多关系表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/appSoundAssistantRelation")
public class AppSoundAssistantRelationController {

    @Autowired
    private AppSoundAssistantRelationService appSoundAssistantRelationService;

    /**
     * 新增应用中心-音源文件信息与应用助手一对多关系表数据。
     *
     * @param appSoundAssistantRelationDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appSoundAssistantRelationDto.id",
            "appSoundAssistantRelationDto.createTimeStart",
            "appSoundAssistantRelationDto.createTimeEnd",
            "appSoundAssistantRelationDto.updateTimeStart",
            "appSoundAssistantRelationDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody AppSoundAssistantRelationDto appSoundAssistantRelationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appSoundAssistantRelationDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppSoundAssistantRelation appSoundAssistantRelation = MyModelUtil.copyTo(appSoundAssistantRelationDto, AppSoundAssistantRelation.class);
        appSoundAssistantRelation = appSoundAssistantRelationService.saveNew(appSoundAssistantRelation);
        return ResponseResult.success(appSoundAssistantRelation.getId());
    }

    /**
     * 更新应用中心-音源文件信息与应用助手一对多关系表数据。
     *
     * @param appSoundAssistantRelationDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appSoundAssistantRelationDto.createTimeStart",
            "appSoundAssistantRelationDto.createTimeEnd",
            "appSoundAssistantRelationDto.updateTimeStart",
            "appSoundAssistantRelationDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody AppSoundAssistantRelationDto appSoundAssistantRelationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appSoundAssistantRelationDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppSoundAssistantRelation appSoundAssistantRelation = MyModelUtil.copyTo(appSoundAssistantRelationDto, AppSoundAssistantRelation.class);
        AppSoundAssistantRelation originalAppSoundAssistantRelation = appSoundAssistantRelationService.getById(appSoundAssistantRelation.getId());
        if (originalAppSoundAssistantRelation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appSoundAssistantRelationService.update(appSoundAssistantRelation, originalAppSoundAssistantRelation)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除应用中心-音源文件信息与应用助手一对多关系表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的应用中心-音源文件信息与应用助手一对多关系表列表。
     *
     * @param appSoundAssistantRelationDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<AppSoundAssistantRelationVo>> list(
            @MyRequestBody AppSoundAssistantRelationDto appSoundAssistantRelationDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppSoundAssistantRelation appSoundAssistantRelationFilter = MyModelUtil.copyTo(appSoundAssistantRelationDtoFilter, AppSoundAssistantRelation.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppSoundAssistantRelation.class);
        List<AppSoundAssistantRelation> appSoundAssistantRelationList =
                appSoundAssistantRelationService.getAppSoundAssistantRelationListWithRelation(appSoundAssistantRelationFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(appSoundAssistantRelationList, AppSoundAssistantRelation.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的应用中心-音源文件信息与应用助手一对多关系表列表。
     *
     * @param appSoundAssistantRelationDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<AppSoundAssistantRelationVo>> listWithGroup(
            @MyRequestBody AppSoundAssistantRelationDto appSoundAssistantRelationDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppSoundAssistantRelation.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, AppSoundAssistantRelation.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppSoundAssistantRelation filter = MyModelUtil.copyTo(appSoundAssistantRelationDtoFilter, AppSoundAssistantRelation.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<AppSoundAssistantRelation> resultList = appSoundAssistantRelationService.getGroupedAppSoundAssistantRelationListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, AppSoundAssistantRelation.INSTANCE));
    }

    /**
     * 查看指定应用中心-音源文件信息与应用助手一对多关系表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<AppSoundAssistantRelationVo> view(@RequestParam Long id) {
        AppSoundAssistantRelation appSoundAssistantRelation = appSoundAssistantRelationService.getByIdWithRelation(id, MyRelationParam.full());
        if (appSoundAssistantRelation == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        AppSoundAssistantRelationVo appSoundAssistantRelationVo = AppSoundAssistantRelation.INSTANCE.fromModel(appSoundAssistantRelation);
        return ResponseResult.success(appSoundAssistantRelationVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        AppSoundAssistantRelation originalAppSoundAssistantRelation = appSoundAssistantRelationService.getById(id);
        if (originalAppSoundAssistantRelation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appSoundAssistantRelationService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
