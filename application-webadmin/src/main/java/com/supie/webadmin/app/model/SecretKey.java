package com.supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 密钥表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "li_secret_key")
public class SecretKey extends BaseModel {
    /**
     * 字符编号。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 密钥表主键id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 密钥值。
     */
    @TableField(value = "secret_value")
    private String secretValue;

    /**
     * 描述。
     */
    @TableField(value = "secret_desc")
    private String secretDesc;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * secret_desc LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
