package com.supie.webadmin.app.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.validator.UpdateGroup;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * ModelChatDto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ModelChatDto对象")
@Data
public class ModelChatDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 对话集ID。
     */
    @Schema(description = "对话集ID")
    private Long chatSetId;

    /**
     * 消息。
     */
    @Schema(description = "消息")
    private String message;

    /**
     * 消息类型。
     */
    @Schema(description = "消息类型")
    private String messageType;

    /**
     * 评价（赞1，踩-1，默认0）。
     */
    @Schema(description = "评价（赞1，踩-1，默认0）")
    private Integer feedback;

    /**
     * 父级节点。
     */
    @Schema(description = "父级节点")
    private Long parentNode;

    /**
     * 版本。
     */
    @Schema(description = "版本")
    private Integer chatVersion;

    /**
     * 关联数据
     */
    @Schema(description = "关联数据")
    private String relationData;

    /**
     * 调试数据
     */
    @Schema(description = "调试数据")
    private String debugData;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * message / message_type LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;

}
