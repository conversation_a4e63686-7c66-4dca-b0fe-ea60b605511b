package com.supie.webadmin.app.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.llmService.dao.ModelServiceRelationMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.InvalidParameterException;
import java.util.HashMap;
import java.util.Map;

@Component
public class ModelConfigUtil {

    private static ModelDeployTaskService modelDeployTaskService;
    private static ModelServiceRelationMapper modelServiceRelationMapper;

    @Resource
    public void setModelDeployTaskService(ModelDeployTaskService modelDeployTaskService) {
        ModelConfigUtil.modelDeployTaskService = modelDeployTaskService;
    }

    @Resource
    public void setModelServiceRelationMapper(ModelServiceRelationMapper modelServiceRelationMapper) {
        ModelConfigUtil.modelServiceRelationMapper = modelServiceRelationMapper;
    }

    /**
     * 根据模型ID和类型设置模型的配置信息
     *
     * @param modelType         模型类型(deploy、public)
     * @param modelId           模型ID
     * @param modelServiceApiId 服务APIID
     * @return model_config：{}
     */
    public static Map<String, Object> getModelConfig(String modelType, Long modelId, Long modelServiceApiId) {
        // 校验模型ID
        if (modelId == null) {
            throw new InvalidParameterException("模型ID不能为空！");
        }
        // 这里只根据 id 和 type进行查询，不使用model_config
        HashMap<String, Object> dataMap = new HashMap<>();
        // 设置模型的配置信息
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            setModelDeployConfig(dataMap, modelId, modelDeployTask, modelServiceApiId);
        } else if ("public".equals(modelType)) {
            Map<String, Object> publicLlmByModelServiceRelationId = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelationId == null) {
                throw new InvalidParameterException("模型[" + modelId + "]不存在！");
            }
            setPublicModelConfig(dataMap, publicLlmByModelServiceRelationId, modelServiceApiId);
        } else {
            throw new InvalidParameterException("模型类型[" + modelType + "]错误！");
        }
        return (Map<String, Object>) dataMap.get("model_config");
    }

    /**
     * 根据模型ID和类型设置模型的配置信息
     *
     * @param modelType         模型类型(deploy、public)
     * @param modelId           模型ID
     * @param modelServiceApiId 服务APIID
     * @return {"model_config":{}}
     */
    public static Map<String, Object> setModelConfigDataByIdAndType(String modelType,
                                                                    Long modelId, Long modelServiceApiId) {
        // 校验模型ID
        if (modelId == null) {
            throw new InvalidParameterException("模型ID不能为空！");
        }
        // 这里只根据 id 和 type进行查询，不使用model_config
        HashMap<String, Object> dataMap = new HashMap<>();
        // 设置模型的配置信息
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            setModelDeployConfig(dataMap, modelId, modelDeployTask, modelServiceApiId);
        } else if ("public".equals(modelType)) {
            Map<String, Object> publicLlmByModelServiceRelationId = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelationId == null) {
                throw new InvalidParameterException("模型[" + modelId + "]不存在！");
            }
            setPublicModelConfig(dataMap, publicLlmByModelServiceRelationId, modelServiceApiId);
        } else {
            throw new InvalidParameterException("模型类型[" + modelType + "]错误！");
        }
        return dataMap;
    }

    /**
     * 设置部署的模型的配置信息
     *
     * @param dataMap
     * @param modelDeployTaskId
     * @param modelDeployTask
     */
    private static void setModelDeployConfig(Map<String, Object> dataMap, Long modelDeployTaskId, ModelDeployTask modelDeployTask, Long modelServiceApiId) {
        if (modelDeployTask == null) {
            throw (RuntimeException) new RuntimeException("模型部署任务[" + modelDeployTaskId + "]不存在！").initCause(null);
        }
        //判断部署模型是否运行中
        if(modelDeployTask.getDeployStatus() != 6){
            throw (RuntimeException) new RuntimeException("模型部署任务[" + modelDeployTaskId + "]未运行！").initCause(null);
        }
        Map<String, Object> modelConfig;
        if (dataMap.containsKey("model_config")) {
            modelConfig = (Map<String, Object>) dataMap.get("model_config");
        } else {
            modelConfig = new HashMap<>();
        }
        modelConfig.put("model_type", "ChatOpenAI");
        modelConfig.put("model_name", modelDeployTask.getModelName());
        // 设置模型地址及其密钥
        StringBuilder baseModelUrl = new StringBuilder("http://");
        if (null != modelDeployTask.getServerHost()) {
            baseModelUrl.append(modelDeployTask.getServerHost());
        }
        if (null != modelDeployTask.getApiServerPort()) {
            baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
        }
        baseModelUrl.append("/v1");
        modelConfig.put("api_base", baseModelUrl.toString());
        modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        modelConfig.put("modelId", modelDeployTaskId);
        if (modelServiceApiId != null) {
            modelConfig.put("modelServiceApiId", modelServiceApiId);
        }
        modelConfig.put("temperature", 0.7);
//        modelConfig.put("top_p",0.7);
//        modelConfig.put("n",1);
//        modelConfig.put("request_timeout",60);
        dataMap.put("model_config", modelConfig);
    }

    private static void setPublicModelConfig(Map<String, Object> dataMap, Map<String, Object> publicLlmByModelServiceRelation, Long modelServiceApiId) {
        String serviceConfiguration = publicLlmByModelServiceRelation.get("serviceConfiguration").toString();
        if (StrUtil.isNotBlank(serviceConfiguration) && JSONUtil.isTypeJSON(serviceConfiguration)) {
            Map serviceConfigurationMap = JSONUtil.toBean(serviceConfiguration, Map.class);
            Map<String, Object> modelConfig;
            if (dataMap.containsKey("model_config")) {
                modelConfig = (Map<String, Object>) dataMap.get("model_config");
            } else {
                modelConfig = new HashMap<>();
            }
            modelConfig.put("model_type", publicLlmByModelServiceRelation.get("serviceProvider"));
            modelConfig.put("model_name", publicLlmByModelServiceRelation.get("modelName"));
            modelConfig.put("modelId", publicLlmByModelServiceRelation.get("id"));
            if (modelServiceApiId != null) {
                modelConfig.put("modelServiceApiId", modelServiceApiId);
            }
            modelConfig.put("top_p", 0.7);
            modelConfig.put("n", 1);
            modelConfig.put("request_timeout", 60);
            serviceConfigurationMap.forEach((key, value) -> {
                modelConfig.put((String) key, value);
            });
            dataMap.put("model_config", modelConfig);
        }
    }
}
