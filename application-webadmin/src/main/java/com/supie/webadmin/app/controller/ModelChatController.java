package com.supie.webadmin.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.supie.common.core.annotation.DisableDataFilter;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.ResponseResult;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.TokenDataUtil;
import com.supie.webadmin.app.dao.UserAccountMapper;
import com.supie.webadmin.app.model.BusinessDict;
import com.supie.webadmin.app.model.ModelInfo;
import com.supie.webadmin.app.model.ModelShare;
import com.supie.webadmin.app.model.UserAccount;
import com.supie.webadmin.app.service.AppBusinessDictService;
import com.supie.webadmin.app.service.ModelChatService;
import com.supie.webadmin.app.service.ModelInfoService;
import com.supie.webadmin.app.service.impl.ModelShareServiceImpl;
import com.supie.webadmin.interceptor.OpenApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

@Tag(name = "模型对话接口组")
@Slf4j
@RequiredArgsConstructor

@Controller(value = "appModelChatController")
@ResponseBody
@RequestMapping("/admin/app/modelChat")
public class ModelChatController {

    private static final  CopyOnWriteArrayList<SseEmitter> EMITTER_LIST = new CopyOnWriteArrayList<>();

    private final ModelChatService modelChatService;
    private final ModelInfoService modelInfoService;
    private final ModelShareServiceImpl modelShareService;
    @Resource
    private UserAccountMapper userAccountMapper;
    @Resource(name = "appBusinessDictService")
    private AppBusinessDictService businessDictServiceImpl;


    /*
    * 添加模型校验模型可用性
    *
    * */
    @Parameters({
            @Parameter(name = "model_name",description = "模型名称",example = "DeepSeek",required = true),
            @Parameter(name = "api_base",description = "模型调用地址",example = "http://************:8081/v1",required = true),
            @Parameter(name = "api_key",description = "密钥",example = "SNION8lnnin",required = true),
            @Parameter(name = "model_type",description = "模型类型",example = "ChatOpenAPI",required = true),
    })
    @Operation(summary = "modelChatCheck",description = "添加模型校验模的可用性")
    @PostMapping("/modelChatCheck")
    public ResponseResult<Boolean> modelChatCheck(@MyRequestBody Map<String,Object>  modelConfig){
       return  ResponseResult.success(modelChatService.modelChatCheck(modelConfig));
    }

    /**
     * 联网搜索
     * @param modelInfoId
     * @param chatSetStrId
     * @param inputContent
     * @param systemPrompt
     * @param modelConfig
     * @return
     * @throws IOException
     */
    @OpenApi
    @DisableDataFilter
    @Operation(summary = "networkSearch", description = "联网搜索")
    @Parameters({
            @Parameter(name = "modelInfoId" ,description = "模型ID"),
            @Parameter(name = "chatSetStrId",description = "对话集ID(对话分组)"),
            @Parameter(name = "inputContext",description = "输入内容"),
            @Parameter(name="systemPrompt",description = "系统提示词"),
            @Parameter(name = "modelConfig",description = "模型配置"),
    })
    @PostMapping("/networkSearch")
    public SseEmitter networkSearch(@MyRequestBody(required = true) Long modelInfoId, @MyRequestBody List<Long> modelInfoIdList, @MyRequestBody String chatSetStrId,
                                    @MyRequestBody String inputContent, @MyRequestBody String systemPrompt,
                                    @MyRequestBody Map<String, Object> modelConfig, @MyRequestBody Integer chatSetType) throws IOException {
        UserAccount userAccount = userAccountMapper.selectOne(new LambdaQueryWrapper<UserAccount>().
                eq(UserAccount::getSysUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId()));
        List<BusinessDict> searchKeyList = businessDictServiceImpl.selectList(new LambdaQueryWrapper<BusinessDict>().eq(BusinessDict::getBindType, "联网秘钥"));
        if (searchKeyList.isEmpty()) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("网络搜索暂不可用，请联系管理员添加相关Key！"));
            return null;
        }
        if (chatSetType == null || (chatSetType != 1 && chatSetType != 2)) {
            chatSetType = 1;
        }
        ModelInfo modelInfo = null;
        try {
            if (chatSetStrId == null || chatSetStrId.isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("对话集ID不能为空"));
                return null;
            }
            if (inputContent == null || inputContent.isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("用户输入的内容不能为空"));
                return null;
            }
            if (modelInfoId == null) {
                String tokenValue = StpUtil.getTokenValue();
                List<ModelShare> modelShareList = modelShareService.selectList(new LambdaQueryWrapper<ModelShare>().eq(ModelShare::getSecretKey, tokenValue));
                if (modelShareList == null || modelShareList.isEmpty()) {
                    ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型ID不能为空"));
                    return null;
                }
                modelInfoId = modelShareList.get(0).getModelInfoId();
            }
            modelInfo = modelInfoService.getById(modelInfoId);
            //当前模型为空
            if (modelInfo == null) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型不存在，请选择其他模型！"));
                return null;
            }
            if (modelInfo.getModelStatus() == -1) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型已下线，请选择其他模型！"));
                return null;
            }
            //调用列表
            if (modelInfo.getModelConfigList().isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("该模型无法使用，请选择其他模型！"));
                return null;
            }
            if((Objects.isNull(userAccount) || userAccount.getBalance()<0) && modelInfo.getIsFree() != 1){
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("账户余额不足！"));
                return null;
            }
        } catch (Exception e) {
            log.error("联网搜索失败：{}", e.getMessage(), e);
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_ACCESS_FAILED));
            return null;
        }
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        EMITTER_LIST.add(emitter);
        emitter.onCompletion(() -> EMITTER_LIST.remove(emitter));
        emitter.onTimeout(() -> EMITTER_LIST.remove(emitter));
        emitter.onError(e -> EMITTER_LIST.remove(emitter));
        ModelInfo finalModelInfo = modelInfo;
        TokenData tokenData = TokenData.takeFromRequest();
        Integer finalChatSetType = chatSetType;
        new Thread(() -> {
            try {
                TokenDataUtil.setTokenDataOfThisThread(tokenData);
                modelChatService.networkSearch(
                        emitter, finalModelInfo, chatSetStrId, inputContent, systemPrompt,
                        modelConfig == null ? new HashMap<>() : modelConfig, finalChatSetType, modelInfoIdList, searchKeyList);
            } catch (Exception e) {
                log.error("对话失败：{}", JSON.toJSONString(ResponseResult.error(e.getMessage())));
              /*  try {
                    emitter.send(JSON.toJSONString(ResponseResult.error(e.getMessage())));
                } catch (IOException ex) {
                }*/
            } finally {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("SSE关闭失败：" + e.getMessage(), e);
                }
                CHATSETSTRID.remove(chatSetStrId);
                TokenDataUtil.removeTokenDataOfThisThread();
            }
        }).start();
//        executor.submit(() -> {
//        });
        return emitter;
    }

    /**
     * 普通对话接口
     *
     * @param chatSetStrId 对话集ID
     * @param inputContent 用户输入的内容
     * @return SseEmitter
     */
    @DisableDataFilter
    @OpenApi
    @Operation(summary = "普通对话接口")
    @PostMapping("/chat")
    public SseEmitter chat(
            @MyRequestBody(required = true) Long modelInfoId, @MyRequestBody List<Long> modelInfoIdList, @MyRequestBody String chatSetStrId,
            @MyRequestBody String inputContent, @MyRequestBody String systemPrompt,
            @MyRequestBody List<Long> fileIdList, @MyRequestBody Map<String, Object> modelConfig, @MyRequestBody Integer chatSetType) throws IOException {
        ModelInfo modelInfo = null;
        try {
            UserAccount userAccount = userAccountMapper.selectOne(new LambdaQueryWrapper<UserAccount>().
                    eq(UserAccount::getSysUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId()));
            if (chatSetType == null || (chatSetType != 1 && chatSetType != 2)) {
                chatSetType = 1;
            }
            if (chatSetStrId == null || chatSetStrId.isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("对话集ID不能为空"));
                return null;
            }
            if (inputContent == null || inputContent.isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("用户输入的内容不能为空"));
                return null;
            }
            if (modelInfoId == null) {
                String tokenValue = StpUtil.getTokenValue();
                List<ModelShare> modelShareList = modelShareService.selectList(new LambdaQueryWrapper<ModelShare>().eq(ModelShare::getSecretKey, tokenValue));
                if (modelShareList == null || modelShareList.isEmpty()) {
                    ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型ID不能为空"));
                    return null;
                }
                modelInfoId = modelShareList.get(0).getModelInfoId();
            }
            modelInfo = modelInfoService.selectModelInfoById(modelInfoId);
            if (modelInfo == null) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型不存在，请选择其他模型！"));
                return null;
            }
            if (modelInfo.getModelStatus() == -1) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("模型已下线，请选择其他模型！"));
                return null;
            }
            if (modelInfo.getModelConfigList().isEmpty()) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("该模型无法使用，请选择其他模型！"));
                return null;
            }
            if((Objects.isNull(userAccount) || userAccount.getBalance()<0) && modelInfo.getIsFree() != 1){
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("账户余额不足！"));
                return null;
            }
        } catch (Exception e) {
            log.error("对话失败：{}", e.getMessage(), e);
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_ACCESS_FAILED));
            return null;
        }
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        EMITTER_LIST.add(emitter);
        emitter.onCompletion(() -> EMITTER_LIST.remove(emitter));
        emitter.onTimeout(() -> EMITTER_LIST.remove(emitter));
        emitter.onError(e -> EMITTER_LIST.remove(emitter));
        ModelInfo finalModelInfo = modelInfo;
        TokenData tokenData = TokenData.takeFromRequest();
        Integer finalChatSetType = chatSetType;
        new Thread(() -> {
            try {
                TokenDataUtil.setTokenDataOfThisThread(tokenData);
                modelChatService.chat(
                        emitter, finalModelInfo, chatSetStrId, inputContent, systemPrompt,
                        fileIdList == null ? new ArrayList<>() : fileIdList,
                        modelConfig == null ? new HashMap<>() : modelConfig, finalChatSetType, modelInfoIdList);
            } catch (Exception e) {
                // TDO 失败信息不返回
                log.error("对话失败：{}", JSON.toJSONString(ResponseResult.error(e.getMessage())));
               /* try {
                    emitter.send("OK");
                } catch (IOException ex) {
                    throw new MyRuntimeException("服务繁忙");
                }*/
            } finally {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("SSE关闭失败：" + e.getMessage(), e);
                }
                CHATSETSTRID.remove(chatSetStrId);
                TokenDataUtil.removeTokenDataOfThisThread();
            }
        }).start();
        return emitter;
    }
    public static final Set<String> CHATSETSTRID = new HashSet<>();
}
