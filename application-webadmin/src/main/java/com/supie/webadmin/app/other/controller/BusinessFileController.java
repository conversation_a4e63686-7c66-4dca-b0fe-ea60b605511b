package com.supie.webadmin.app.other.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.*;
import com.supie.common.core.upload.BaseUpDownloader;
import com.supie.common.core.upload.UpDownloaderFactory;
import com.supie.common.core.upload.UploadResponseInfo;
import com.supie.common.core.upload.UploadStoreInfo;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.upload.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.common.redis.cache.SessionCacheHelper;
import com.supie.webadmin.app.other.dto.BusinessFileDto;
import com.supie.webadmin.app.other.model.BusinessFile;
import com.supie.webadmin.app.other.service.BusinessFileService;
import com.supie.webadmin.app.other.vo.BusinessFileVo;
import com.supie.webadmin.app.util.ToolUtil;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.interceptor.PyAuthInterface;
import com.supie.webadmin.upms.dao.SysUserMapper;
import com.supie.webadmin.upms.model.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务附件表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "其他-业务附件表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/businessFile")
public class BusinessFileController {

    @Autowired
    private BusinessFileService businessFileService;
    @Autowired
    private ApplicationConfig appConfig;
    @Autowired
    private SessionCacheHelper cacheHelper;
    @Autowired
    private UpDownloaderFactory upDownloaderFactory;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 新增业务附件表数据。
     *
     * @param businessFileDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "businessFileDto.id",
            "businessFileDto.searchString",
            "businessFileDto.updateTimeStart",
            "businessFileDto.updateTimeEnd",
            "businessFileDto.createTimeStart",
            "businessFileDto.createTimeEnd",
            "businessFileDto.fileSizeStart",
            "businessFileDto.fileSizeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody BusinessFileDto businessFileDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessFileDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessFile businessFile = MyModelUtil.copyTo(businessFileDto, BusinessFile.class);
        businessFile = businessFileService.saveNew(businessFile);
        return ResponseResult.success(businessFile.getId());
    }

    /**
     * 更新业务附件表数据。
     *
     * @param businessFileDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "businessFileDto.searchString",
            "businessFileDto.updateTimeStart",
            "businessFileDto.updateTimeEnd",
            "businessFileDto.createTimeStart",
            "businessFileDto.createTimeEnd",
            "businessFileDto.fileSizeStart",
            "businessFileDto.fileSizeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody BusinessFileDto businessFileDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessFileDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessFile businessFile = MyModelUtil.copyTo(businessFileDto, BusinessFile.class);
        BusinessFile originalBusinessFile = businessFileService.getById(businessFile.getId());
        if (originalBusinessFile == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!businessFileService.update(businessFile, originalBusinessFile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除业务附件表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的业务附件表列表。
     *
     * @param businessFileDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<BusinessFileVo>> list(
            @MyRequestBody BusinessFileDto businessFileDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        BusinessFile businessFileFilter = MyModelUtil.copyTo(businessFileDtoFilter, BusinessFile.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessFile.class);
        List<BusinessFile> businessFileList =
                businessFileService.getBusinessFileListWithRelation(businessFileFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(businessFileList, BusinessFile.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的业务附件表列表。
     *
     * @param businessFileDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<BusinessFileVo>> listWithGroup(
            @MyRequestBody BusinessFileDto businessFileDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessFile.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, BusinessFile.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        BusinessFile filter = MyModelUtil.copyTo(businessFileDtoFilter, BusinessFile.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<BusinessFile> resultList = businessFileService.getGroupedBusinessFileListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, BusinessFile.INSTANCE));
    }

    /**
     * 查看指定业务附件表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<BusinessFileVo> view(@RequestParam Long id) {
        BusinessFile businessFile = businessFileService.getByIdWithRelation(id, MyRelationParam.full());
        if (businessFile == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        BusinessFileVo businessFileVo = BusinessFile.INSTANCE.fromModel(businessFile);
        return ResponseResult.success(businessFileVo);
    }


    /**
     * 查看指定业务附件表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/queryPath")
    @PyAuthInterface
    @Operation(summary = "附件表查询文件路径----无权限接口")
    public ResponseResult<String> queryPath(@RequestParam Long id) {
        BusinessFile businessFile = businessFileService.getByIdWithRelation(id, MyRelationParam.full());
        if (businessFile == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        String fileJson = businessFile.getFileJson();
        JSONObject json = JSONUtil.parseObj(fileJson);
        String fieldName = json.getStr("fieldName");
        String fileName = json.getStr("filename");
        boolean asImage = json.getBool("asImage");
        String uploadPath = ToolUtil.makeFullPath(null, BusinessFile.class.getSimpleName(), fieldName, asImage);

        return ResponseResult.success(uploadPath +  "/" + fileName) ;
    }

    /**
     * 附件文件下载。
     * 这里将图片和其他类型的附件文件放到不同的父目录下，主要为了便于今后图片文件的迁移。
     *
     * @param id 附件所在记录的主键Id。
     * @param fieldName 附件所属的字段名。
     * @param filename  文件名。如果没有提供该参数，就从当前记录的指定字段中读取。
     * @param asImage   下载文件是否为图片。
     * @param response  Http 应答对象。
     */
    @OperationLog(type = SysOperationLogType.DOWNLOAD, saveResponse = false)
    @GetMapping("/download")
    public void download(
            @RequestParam(required = false) Long id,
            @RequestParam String fieldName,
            @RequestParam String filename,
            @RequestParam Boolean asImage,
            HttpServletResponse response) {
        if (MyCommonUtil.existBlankArgument(fieldName, filename, asImage)) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        // 使用try来捕获异常，是为了保证一旦出现异常可以返回500的错误状态，便于调试。
        // 否则有可能给前端返回的是200的错误码。
        try {
            // 如果请求参数中没有包含主键Id，就判断该文件是否为当前session上传的。
            if (id == null) {
                if (!cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            } else {
                BusinessFile businessFile = businessFileService.getById(id);
                if (businessFile == null) {
                    ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                    return;
                }
                String fieldJsonData = (String) ReflectUtil.getFieldValue(businessFile, fieldName);
                if (fieldJsonData == null && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                if (!UpDownloaderUtil.containFile(fieldJsonData, filename)
                        && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }
            UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
            if (!storeInfo.isSupportUpload()) {
                ResponseResult.output(HttpServletResponse.SC_NOT_IMPLEMENTED,
                        ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
                return;
            }
            BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
            upDownloader.doDownload(appConfig.getUploadFileBaseDir(),
                    BusinessFile.class.getSimpleName(), fieldName, filename, asImage, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 附件文件下载。
     * 这里将图片和其他类型的附件文件放到不同的父目录下，主要为了便于今后图片文件的迁移。
     *
     * @param id 附件所在记录的主键Id。
     */
    @SaIgnore
    @OperationLog(type = SysOperationLogType.DOWNLOAD, saveResponse = false)
    @GetMapping("/downloadImg")
    public void downloadImg(Long id,
            HttpServletResponse response) {
        // 使用try来捕获异常，是为了保证一旦出现异常可以返回500的错误状态，便于调试。
        // 否则有可能给前端返回的是200的错误码。
        try {
            if (id == null) {
                ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            BusinessFile businessFile = businessFileService.getById(id);
            if (businessFile == null) {
                ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, "fileJson");
            if (!storeInfo.isSupportUpload()) {
                ResponseResult.output(HttpServletResponse.SC_NOT_IMPLEMENTED,
                        ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
                return;
            }
            com.alibaba.fastjson.JSONObject fileJson = JSON.parseObject(businessFile.getFileJson());
            if (!fileJson.getBoolean("asImage")) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD, "不支持下载非图片文件！"));
                return;
            }
            BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
            upDownloader.doDownload(appConfig.getUploadFileBaseDir(),
                    BusinessFile.class.getSimpleName(), "fileJson", fileJson.getString("filename"), fileJson.getBoolean("asImage"), response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 文件上传操作。
     *
     * @param fieldName  上传文件名。
     * @param asImage    是否作为图片上传。如果是图片，今后下载的时候无需权限验证。否则就是附件上传，下载时需要权限验证。
     * @param uploadFile 上传文件对象。
     */
    @OperationLog(type = SysOperationLogType.UPLOAD, saveResponse = false)
    @PostMapping("/upload")
    public void upload(
            @RequestParam String fieldName,
            @RequestParam Boolean asImage,
            @RequestParam("uploadFile") MultipartFile uploadFile) throws IOException {
        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
        // 这里就会判断参数中指定的字段，是否支持上传操作。
        if (!storeInfo.isSupportUpload()) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
            return;
        }
        // 根据字段注解中的存储类型，通过工厂方法获取匹配的上传下载实现类，从而解耦。
        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), fieldName, asImage, uploadFile);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.UPLOAD_FAILED, responseInfo.getErrorMessage()));
            return;
        }
        cacheHelper.putSessionUploadFile(responseInfo.getFilename());
        ResponseResult.output(ResponseResult.success(responseInfo));
    }


    /**
     * 无权限文件上传操作。
     *
     * @param fieldName  上传文件名。
     * @param asImage    是否作为图片上传。如果是图片，今后下载的时候无需权限验证。否则就是附件上传，下载时需要权限验证。
     * @param uploadFile 上传文件对象。
     */
    @OperationLog(type = SysOperationLogType.UPLOAD, saveResponse = false)
    @PostMapping("/noAuthInterfaceUpload")
    @PyAuthInterface
    @Operation(summary = "无权限上传文件")
    public ResponseResult<Void> noAuthInterfaceUpload(
            @RequestParam String fieldName,
            @RequestParam Boolean asImage,
            @RequestParam String bindStrId,
            @RequestParam("uploadFile") MultipartFile uploadFile) throws IOException {
        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
        // 这里就会判断参数中指定的字段，是否支持上传操作。
        if (!storeInfo.isSupportUpload()) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
            return null;
        }
        // 根据字段注解中的存储类型，通过工厂方法获取匹配的上传下载实现类，从而解耦。
        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), fieldName, asImage, uploadFile);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.UPLOAD_FAILED, responseInfo.getErrorMessage()));
            return null;
        }
        BusinessFile businessFile = new BusinessFile();
        String filename = uploadFile.getOriginalFilename();
        assert filename != null;
        String substring = filename.substring(filename.lastIndexOf(".") + 1);
        businessFile.setStrId("");
        businessFile.setAsImage(String.valueOf(asImage));
        businessFile.setFileFormat(substring);
        businessFile.setFileName(uploadFile.getOriginalFilename());
        JSONObject json = new JSONObject();
        json.set("fieldName",fieldName);
        json.set("filename",responseInfo.getFilename());
        json.set("asImage",String.valueOf(asImage));
        businessFile.setFileJson(json.toString());
        businessFile.setFileSize(uploadFile.getSize());
        businessFile.setBindStrId(bindStrId);
        SysUser sysUser = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserType, 0));
        businessFile.setCreateUserId(sysUser.getUserId());
        businessFile.setCreateTime(new Date());
        businessFile.setUpdateUserId(sysUser.getUserId());
        businessFile.setUpdateTime(new Date());
        businessFile.setDataUserId(0L);
        businessFile.setDataDeptId(0L);
        businessFile.setIsDelete(1);
        businessFileService.noAuthSaveNew(businessFile);
        return ResponseResult.success();
    }

    /**
     * 文件上传操作。
     *
     * @param fieldName  上传文件名。
     * @param asImage    是否作为图片上传。如果是图片，今后下载的时候无需权限验证。否则就是附件上传，下载时需要权限验证。
     * @param uploadFile 上传文件对象。
     */
    @OperationLog(type = SysOperationLogType.UPLOAD, saveResponse = false)
    @PostMapping("/fileUpload")
    public ResponseResult<BusinessFileVo> fileUpload(
            @RequestParam String fieldName,
            @RequestParam(required = false) String bindStrId,
            @RequestParam(required = false) String bindType,
            @RequestParam Boolean asImage,
            @RequestParam("uploadFile") MultipartFile uploadFile) throws IOException {
        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
        // 这里就会判断参数中指定的字段，是否支持上传操作。
        if (!storeInfo.isSupportUpload()) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
            throw new MyRuntimeException("上传失败！");
        }
        // 根据字段注解中的存储类型，通过工厂方法获取匹配的上传下载实现类，从而解耦。
        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), fieldName, asImage, uploadFile);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.UPLOAD_FAILED, responseInfo.getErrorMessage()));
            throw new MyRuntimeException("上传失败！");
        }
        cacheHelper.putSessionUploadFile(responseInfo.getFilename());
        String filename = responseInfo.getFilename();
        assert filename != null;
        BusinessFile businessFile = new BusinessFile();
        String originalFilename = uploadFile.getOriginalFilename();
        assert originalFilename != null;
        String substring = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        businessFile.setStrId("");
        businessFile.setAsImage(String.valueOf(asImage));
        businessFile.setFileFormat(substring);
        businessFile.setFileName(originalFilename);
        Map<String, Object> fileJson = new HashMap<>();
        // {"fieldName":"fileJson","filename":"68a619518c9047d992c244053b1a55fe.png","asImage":true}
        fileJson.put("fieldName",fieldName);
        fileJson.put("filename", filename);
        fileJson.put("asImage",String.valueOf(asImage));
        businessFile.setFileJson(JSON.toJSONString(fileJson));
        businessFile.setFileSize(uploadFile.getSize());
        if (bindStrId != null && bindType != null && !bindStrId.isEmpty() && !bindType.isEmpty()) {
            businessFile.setBindStrId(bindStrId);
            businessFile.setBindType(bindType);
        }
        businessFileService.saveNew(businessFile);
        BusinessFileVo businessFileVo = BusinessFile.INSTANCE.fromModel(businessFile);
        businessFileService.fileContentParse(businessFile);
        return ResponseResult.success(businessFileVo);
    }



    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        BusinessFile originalBusinessFile = businessFileService.getById(id);
        if (originalBusinessFile == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!businessFileService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // TODO 同步删除对象存储中的文件
        return ResponseResult.success();
    }

    /**
     * 前端无权限附件文件下载。
     * 这里将图片和其他类型的附件文件放到不同的父目录下，主要为了便于今后图片文件的迁移。
     *
     * @param id 附件所在记录的主键Id。
     * @param fieldName 附件所属的字段名。
     * @param filename  文件名。如果没有提供该参数，就从当前记录的指定字段中读取。
     * @param asImage   下载文件是否为图片。
     * @param response  Http 应答对象。
     */
    @OperationLog(type = SysOperationLogType.DOWNLOAD, saveResponse = false)
    @SaIgnore
    @GetMapping("/noPermissionDownload")
    public void noPermissionDownload(
            @RequestParam(required = false) Long id,
            @RequestParam String fieldName,
            @RequestParam String filename,
            @RequestParam Boolean asImage,
            HttpServletResponse response) {
        if (MyCommonUtil.existBlankArgument(fieldName, filename, asImage)) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        // 使用try来捕获异常，是为了保证一旦出现异常可以返回500的错误状态，便于调试。
        // 否则有可能给前端返回的是200的错误码。
        try {
            // 如果请求参数中没有包含主键Id，就判断该文件是否为当前session上传的。
            if (id == null) {
                if (!cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            } else {
                BusinessFile businessFile = businessFileService.getById(id);
                if (businessFile == null) {
                    ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                    return;
                }
                String fieldJsonData = (String) ReflectUtil.getFieldValue(businessFile, fieldName);
                if (fieldJsonData == null && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                if (!UpDownloaderUtil.containFile(fieldJsonData, filename)
                        && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }
            UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
            if (!storeInfo.isSupportUpload()) {
                ResponseResult.output(HttpServletResponse.SC_NOT_IMPLEMENTED,
                        ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
                return;
            }
            BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
            upDownloader.doDownload(appConfig.getUploadFileBaseDir(),
                    BusinessFile.class.getSimpleName(), fieldName, filename, asImage, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error(e.getMessage(), e);
        }
    }
}
