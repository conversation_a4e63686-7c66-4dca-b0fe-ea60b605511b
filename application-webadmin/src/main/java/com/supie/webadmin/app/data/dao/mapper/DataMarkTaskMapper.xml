<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.data.dao.DataMarkTaskMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.data.model.DataMarkTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="data_resource_id" jdbcType="BIGINT" property="dataResourceId"/>
        <result column="dataSet_id" jdbcType="BIGINT" property="datasetId"/>
        <result column="mark_state" jdbcType="INTEGER" property="markState"/>
        <result column="mark_num" jdbcType="INTEGER" property="markNum"/>
        <result column="mark_type" jdbcType="VARCHAR" property="markType"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="mark_config" jdbcType="VARCHAR" property="markConfig"/>
        <result column="mark_source_type" jdbcType="INTEGER" property="markSourceType"/>
        <result column="mark_progress" jdbcType="VARCHAR" property="markProgress"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="is_mark_task_log" jdbcType="INTEGER" property="isMarkTaskLog"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="data_set_name" jdbcType="VARCHAR" property="dataSetName"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_data_mark_task
            (id,
            str_id,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            data_resource_id,
            dataSet_id,
            mark_state,
            mark_num,
            mark_type,
            model_id,
            model_type,
            mark_config ,
            mark_source_type,
            mark_progress,
            end_time,
            is_mark_task_log,
            model_name,
            data_set_name)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.dataResourceId},
            #{item.datasetId},
            #{item.markState},
            #{item.markNum},
            #{item.markType},
            #{item.modelId},
            #{item.modelType},
            #{item.markConfig},
            #{item.trainingTaskId},
            #{item.courseId},
            #{item.userId},
            #{item.courseFunctionRelationId})
            #{item.markConfig},
            #{item.markSourceType},
            #{item.markProgress},
            #{item.endTime},
            #{item.isMarkTaskLog},
            #{item.modelName},
            #{item.dataSetName})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.data.dao.DataMarkTaskMapper.inputFilterRef"/>
        AND lmd_data_mark_task.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="dataMarkTaskFilter != null">
            <if test="dataMarkTaskFilter.id != null">
                AND lmd_data_mark_task.id = #{dataMarkTaskFilter.id}
            </if>
            <if test="dataMarkTaskFilter.strId != null and dataMarkTaskFilter.strId != ''">
                AND lmd_data_mark_task.str_id = #{dataMarkTaskFilter.strId}
            </if>
            <if test="dataMarkTaskFilter.createTimeStart != null and dataMarkTaskFilter.createTimeStart != ''">
                AND lmd_data_mark_task.create_time &gt;= #{dataMarkTaskFilter.createTimeStart}
            </if>
            <if test="dataMarkTaskFilter.createTimeEnd != null and dataMarkTaskFilter.createTimeEnd != ''">
                AND lmd_data_mark_task.create_time &lt;= #{dataMarkTaskFilter.createTimeEnd}
            </if>
            <if test="dataMarkTaskFilter.createUserId != null">
                AND lmd_data_mark_task.create_user_id = #{dataMarkTaskFilter.createUserId}
            </if>
            <if test="dataMarkTaskFilter.updateTimeStart != null and dataMarkTaskFilter.updateTimeStart != ''">
                AND lmd_data_mark_task.update_time &gt;= #{dataMarkTaskFilter.updateTimeStart}
            </if>
            <if test="dataMarkTaskFilter.updateTimeEnd != null and dataMarkTaskFilter.updateTimeEnd != ''">
                AND lmd_data_mark_task.update_time &lt;= #{dataMarkTaskFilter.updateTimeEnd}
            </if>
            <if test="dataMarkTaskFilter.updateUserId != null">
                AND lmd_data_mark_task.update_user_id = #{dataMarkTaskFilter.updateUserId}
            </if>
            <if test="dataMarkTaskFilter.modelId != null">
                AND lmd_data_mark_task.model_id = #{dataMarkTaskFilter.modelId}
            </if>
            <if test="dataMarkTaskFilter.markSourceType != null">
                AND lmd_data_mark_task.mark_source_type = #{dataMarkTaskFilter.markSourceType}
            </if>
            <if test="dataMarkTaskFilter.dataUserId != null">
                AND lmd_data_mark_task.data_user_id = #{dataMarkTaskFilter.dataUserId}
            </if>
            <if test="dataMarkTaskFilter.dataDeptId != null">
                AND lmd_data_mark_task.data_dept_id = #{dataMarkTaskFilter.dataDeptId}
            </if>
            <if test="dataMarkTaskFilter.dataResourceId != null">
                AND lmd_data_mark_task.data_resource_id = #{dataMarkTaskFilter.dataResourceId}
            </if>
            <if test="dataMarkTaskFilter.datasetId != null">
                AND lmd_data_mark_task.dataSet_id = #{dataMarkTaskFilter.datasetId}
            </if>
            <if test="dataMarkTaskFilter.markState != null">
                AND lmd_data_mark_task.mark_state = #{dataMarkTaskFilter.markState}
            </if>
            <if test="dataMarkTaskFilter.markNum != null">
                AND lmd_data_mark_task.mark_num = #{dataMarkTaskFilter.markNum}
            </if>
            <if test="dataMarkTaskFilter.markType != null and dataMarkTaskFilter.markType != ''">
                <bind name = "safeDataMarkTaskMarkType" value = "'%' + dataMarkTaskFilter.markType + '%'" />
                AND lmd_data_mark_task.mark_type LIKE #{safeDataMarkTaskMarkType}
            </if>
            <if test="dataMarkTaskFilter.modelType  != null and dataMarkTaskFilter.modelType != ''">
                <bind name = "safeDataMarkTaskModelType" value = "'%' + dataMarkTaskFilter.modelType + '%'" />
                AND IFNULL(lmd_data_mark_task.model_type,'') LIKE #{safeDataMarkTaskModelType}
            </if>
            <if test="dataMarkTaskFilter.markProgress != null">
                AND lmd_data_mark_task.mark_progress = #{dataMarkTaskFilter.markProgress}
            </if>
            <if test="dataMarkTaskFilter.isMarkTaskLog != null">
                AND lmd_data_mark_task.is_mark_task_log = #{dataMarkTaskFilter.isMarkTaskLog}
            </if>
            <if test="dataMarkTaskFilter.endTimeStart != null and dataMarkTaskFilter.endTimeStart != ''">
                AND lmd_data_mark_task.end_time &gt;= #{dataMarkTaskFilter.endTimeStart}
            </if>
            <if test="dataMarkTaskFilter.endTimeEnd != null and dataMarkTaskFilter.endTimeEnd != ''">
                AND lmd_data_mark_task.endTime &lt;= #{dataMarkTaskFilter.endTimeEnd}
            </if>
            <if test="dataMarkTaskFilter.modelName != null and dataMarkTaskFilter.modelName != ''">
                <bind name = "safeDataMarkTaskModelName" value = "'%' + dataMarkTaskFilter.modelName + '%'" />
                AND lmd_data_mark_task.model_name LIKE #{safeDataMarkTaskModelName}
            </if>
            <if test="dataMarkTaskFilter.dataSetName != null and dataMarkTaskFilter.dataSetName != ''">
                <bind name = "safeDataMarkTaskDataSetName" value = "'%' + dataMarkTaskFilter.dataSetName + '%'" />
                AND lmd_data_mark_task.data_set_name LIKE #{safeDataMarkTaskDataSetName}
            </if>
            <if test="dataMarkTaskFilter.markConfig  != null and dataMarkTaskFilter.markConfig != ''">
                <bind name = "safeDataMarkTaskMarkConfig" value = "'%' + dataMarkTaskFilter.markConfig + '%'" />
                AND IFNULL(lmd_data_mark_task.mark_config,'') LIKE #{safeDataMarkTaskMarkConfig}
            </if>
            <if test="dataMarkTaskFilter.searchString != null and dataMarkTaskFilter.searchString != ''">
                <bind name = "safeDataMarkTaskFilterSearchString" value = "'%' + dataMarkTaskFilter.searchString + '%'" />
                AND CONCAT(IFNULL(lmd_data_distillation_task.model_name,''), IFNULL(lmd_data_distillation_task.data_set_name,'') LIKE #{safeDataMarkTaskFilterSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDataMarkTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataMarkTask">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_data_mark_task
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_data_mark_task
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDataMarkTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataMarkTask">
        SELECT lmd_data_mark_task.* FROM lmd_data_mark_task
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNewDataMarkTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataMarkTask">
        SELECT lmd_data_mark_task.* FROM lmd_data_mark_task
        LEFT JOIN lmd_data_dataset ON lmd_data_mark_task.dataSet_id = lmd_data_dataset.id
        <where>
            <include refid="filterRef"/>
            <if test="dataMarkTaskFilter.searchString != null  and dataMarkTaskFilter.searchString != ''">
                <bind name = "safeDataMarkTaskSearchString" value = "'%' + dataMarkTaskFilter.searchString + '%'" />
                AND lmd_data_dataset.dataset_name LIKE #{safeDataMarkTaskSearchString}
            </if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
