package com.supie.webadmin.app.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.webadmin.app.data.vo.DataQualityAssessmentRecordVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * DataQualityAssessmentRecord实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_data_quality_assessment_record")
public class DataQualityAssessmentRecord extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 评价数据id。
     */
    private Long evaluationDataId;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 评估任务id。
     */
    private Long assessTaskId;

    /**
     * 评估文本。
     */
    private String assessText;

    /**
     * 文本词数目。
     */
    private Integer textWordCount;

    /**
     * 文本特殊字符率。
     */
    private Double textSpecialCharRate;

    /**
     * 文本违禁词比例。
     */
    private Double textProhibitedWordRate;

    /**
     * 文本困惑度。
     */
    private Double textPerplexity;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * textWordCount 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Integer textWordCountStart;

    /**
     * textWordCount 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Integer textWordCountEnd;

    /**
     * textSpecialCharRate 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Double textSpecialCharRateStart;

    /**
     * textSpecialCharRate 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Double textSpecialCharRateEnd;

    /**
     * textProhibitedWordRate 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Double textProhibitedWordRateStart;

    /**
     * textProhibitedWordRate 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Double textProhibitedWordRateEnd;

    /**
     * textPerplexity 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Double textPerplexityStart;

    /**
     * textPerplexity 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Double textPerplexityEnd;

    /**
     * assess_text LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    @Mapper
    public interface DataQualityAssessmentRecordModelMapper extends BaseModelMapper<DataQualityAssessmentRecordVo, DataQualityAssessmentRecord> {
    }
    public static final DataQualityAssessmentRecordModelMapper INSTANCE = Mappers.getMapper(DataQualityAssessmentRecordModelMapper.class);
}
