package com.supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.controller.ModelChatController;
import com.supie.webadmin.app.dao.ChatSetMapper;
import com.supie.webadmin.app.model.ChatRecords;
import com.supie.webadmin.app.model.ChatSet;
import com.supie.webadmin.app.service.ChatRecordsService;
import com.supie.webadmin.app.service.ChatSetService;
import com.supie.webadmin.config.DataSourceType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对话分组表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */

@Slf4j
@Service("chatSetService")
@MyDataSource(DataSourceType.CLICK_HOUSE)
public class ChatSetServiceImpl extends BaseService<ChatSet, Long> implements ChatSetService {

    @Autowired
    private IdGeneratorWrapper idGenerator;

    @Resource(name = "chatSetMapper")
    private ChatSetMapper chatSetMapper;
    @Autowired
    private ChatRecordsService chatRecordsService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<ChatSet> mapper() {
        return chatSetMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ChatSet saveNew(ChatSet chatSet) {
        chatSetMapper.insert(this.buildDefaultValue(chatSet));
        return chatSet;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<ChatSet> chatSetList) {
        if (CollUtil.isNotEmpty(chatSetList)) {
            chatSetList.forEach(this::buildDefaultValue);
            chatSetMapper.insertList(chatSetList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ChatSet chatSet, ChatSet originalChatSet) {
        MyModelUtil.fillCommonsForUpdate(chatSet, originalChatSet);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<ChatSet> uw = this.createUpdateQueryForNullValue(chatSet, chatSet.getId());
        return chatSetMapper.update(chatSet, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return chatSetMapper.deleteById(id) == 1;
    }

    @Override
    public List<ChatSet> getChatSetList(ChatSet filter, String orderBy) {
        return chatSetMapper.getChatSetList(filter, orderBy);
    }

    @Override
    public List<ChatSet> getChatSetListWithRelation(ChatSet filter, String orderBy) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData.getSourceType() == 2) {
            filter.setSourceType(tokenData.getSourceType());
            filter.setBindId(tokenData.getSecretKeyId());
        }
        if (tokenData.getSourceType() == 3) {
            filter.setSourceType(tokenData.getSourceType());
            filter.setBindId(tokenData.getModelShareId());
        }
        List<ChatSet> resultList = chatSetMapper.getChatSetList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<ChatSet> getGroupedChatSetListWithRelation(
            ChatSet filter, String groupSelect, String groupBy, String orderBy) {
        List<ChatSet> resultList =
                chatSetMapper.getGroupedChatSetList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private ChatSet buildDefaultValue(ChatSet chatSet) {
        if (chatSet.getId() == null) {
            chatSet.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(chatSet);
        chatSet.setIsDelete(GlobalDeletedFlag.NORMAL);
        return chatSet;
    }

    /**
     * 根据用户输入的内容生成对话分组名称
     * @param userInputContent 用户输入的内容
     * @return 对话分组名称
     */
    private String getChatSetNameByUserInput(String userInputContent) {
        if (userInputContent == null || userInputContent.isEmpty()) {
            return DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        }
        if (userInputContent.length() > 12) {
            return userInputContent.substring(0, 12);
        }
        return userInputContent;
    }

    public static final Set<String> CHAT_SET_STR_ID = new HashSet<>();

    /**
     * 获取聊天记录
     * <p>用户的最新一条提问不拼接在聊天记录中</p>
     * @param chatSetStrId     chatSetStrId
     * @param userInputContent 用户输入的内容（用于生成ChatSetName）
     * @return List<Map < String, Object>> 无则返回空集合
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW) // New transaction for B
    @Override
    public List<Map<String, Object>> getHistoryChatRecords(String chatSetStrId, String userInputContent, Integer chatSetType, List<Long> modelInfoIdList, String system) {
        ChatSet chatSet = chatSetMapper.selectOne(new LambdaQueryWrapper<ChatSet>().eq(ChatSet::getStrId, chatSetStrId));
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (system != null && !system.isEmpty()) {
            resultList.add(Map.of("role", "system", "message", system));
        }
        if (chatSet == null) {
            chatSet = new ChatSet();
            chatSet.setChatSetType(chatSetType);
            chatSet.setSourceType(TokenData.takeFromRequest().getSourceType());
            chatSet.setBindId(TokenData.takeFromRequest().getModelShareId());
            chatSet.setStrId(chatSetStrId);
            chatSet.setChatSetName(getChatSetNameByUserInput(userInputContent));
            chatSet.setModelInfoIdListJson(JSON.toJSONString(modelInfoIdList));
            if (!ModelChatController.CHATSETSTRID.contains(chatSetStrId)) {
                ModelChatController.CHATSETSTRID.add(chatSetStrId);
                chatSetMapper.insert(this.buildDefaultValue(chatSet));
            }
            return resultList;
        }
        List<ChatRecords> chatRecordsList = chatRecordsService.selectList(
                new LambdaQueryWrapper<ChatRecords>().eq(ChatRecords::getChatSetStrId, chatSetStrId).orderByAsc(ChatRecords::getCreateTime));
        for (ChatRecords chatRecords : chatRecordsList) {
            if (chatRecords.getChatContent() == null || chatRecords.getChatContent().isEmpty()) {
                continue;
            }
            try {
                Long.valueOf(chatRecords.getChatContent());
            } catch (NumberFormatException e) {
                String role = chatRecords.getChatRole() == 1 ? "user" : "assistant";
                resultList.add(Map.of("role", role, "message", chatRecords.getChatContent()));
            }
        }
        return resultList;
    }

}
