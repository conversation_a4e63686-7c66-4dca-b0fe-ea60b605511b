package com.supie.webadmin.app.llmDeploy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.GlobalThreadLocal;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.app.dao.AppAssistantBasicMapper;
import com.supie.webadmin.app.app.dao.ModelServiceApiMapper;
import com.supie.webadmin.app.app.model.AppAssistantBasic;
import com.supie.webadmin.app.app.model.ModelServiceApi;
import com.supie.webadmin.app.llm.model.ModelBasic;
import com.supie.webadmin.app.llm.service.ModelBasicService;
import com.supie.webadmin.app.llmDeploy.dao.ModelDeployTaskMapper;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.llmService.model.RemoteHost;
import com.supie.webadmin.app.llmService.service.RemoteHostService;
import com.supie.webadmin.app.llmTrain.dao.ModelWeightMergeMapper;
import com.supie.webadmin.app.llmTrain.dao.TrainTaskMapper;
import com.supie.webadmin.app.llmTrain.model.ModelWeightMerge;
import com.supie.webadmin.app.llmTrain.model.TrainTask;
import com.supie.webadmin.app.llmTrainImpl.NfsPathMgmt;
import com.supie.webadmin.app.other.service.ModelTokensCountService;
import com.supie.webadmin.app.util.DockerJavaUtil;
import com.supie.webadmin.app.util.RemoteSshUtils;
import com.supie.webadmin.app.util.SysUtil;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermission;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.supie.webadmin.app.util.TaskConstant.*;

/**
 * 部署管理-模型部署表数据操作服务类。
 */
@Slf4j
@Service("modelDeployTaskService")
@MyDataSource(DataSourceType.MAIN)
public class ModelDeployTaskServiceImpl extends BaseService<ModelDeployTask, Long> implements ModelDeployTaskService {

    @Value("${fileLocationManagement.modelPosition}")
    private String modelPosition;
    @Value("${fileLocationManagement.jarPosition}")
    private String jarPosition;
    @Resource
    private ModelDeployTaskMapper modelDeployTaskMapper;
    @Resource
    private IdGeneratorWrapper idGenerator;
    @Resource
    private NfsPathMgmt nfsPathMgmt;
    @Resource
    private TrainTaskMapper trainTaskMapper;
    @Resource
    private ModelWeightMergeMapper modelWeightMergeMapper;
    @Resource
    private RemoteSshUtils remoteSshUtils;
    @Resource
    private RemoteHostService remoteHostService;
    @Resource
    private ModelBasicService modelBasicService;
    @Resource
    private ModelServiceApiMapper modelServiceApiMapper;
    @Resource
    private AppAssistantBasicMapper appAssistantBasicMapper;
    @Resource
    private DockerJavaUtil dockerJavaUtil;
    @Resource
    private SysUtil sysUtil;
    @Resource
    private ModelTokensCountService modelTokensCountService;
    private static final Random random = new Random();
    @Value("${imageConfig.ascendLlama}")
    private String ascendLlama;
    @Value("${imageConfig.nvidiaLlama}")
    private String nvidiaLlama;
    @Value("${imageConfig.nvidiaVllm}")
    private String nvidiaVllm;
    @Value("${imageConfig.ascendVllm}")
    private String ascendVllm;
    @Value("${imageConfig.mindIe300iDuo}")
    private String mindIe300iDuo;
    @Value("${imageConfig.mindIe910a}")
    private String mindIe910a;
    @Value("${imageConfig.mindIe910b}")
    private String mindIe910b;
    @Value("${imageConfig.mindIet71}")
    private String mindIet71;


    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<ModelDeployTask> mapper() {
        return modelDeployTaskMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param modelDeployTask 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ModelDeployTask saveNew(ModelDeployTask modelDeployTask) {
        modelDeployTaskMapper.insert(this.buildDefaultValue(modelDeployTask));
        return modelDeployTask;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param modelDeployTaskList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<ModelDeployTask> modelDeployTaskList) {
        if (CollUtil.isNotEmpty(modelDeployTaskList)) {
            modelDeployTaskList.forEach(this::buildDefaultValue);
            modelDeployTaskMapper.insertList(modelDeployTaskList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param modelDeployTask         更新的对象。
     * @param originalModelDeployTask 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ModelDeployTask modelDeployTask, ModelDeployTask originalModelDeployTask) {
        MyModelUtil.fillCommonsForUpdate(modelDeployTask, originalModelDeployTask);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<ModelDeployTask> uw = this.createUpdateQueryForNullValue(modelDeployTask, modelDeployTask.getId());
        return modelDeployTaskMapper.update(modelDeployTask, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return modelDeployTaskMapper.deleteById(id) == 1;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getModelDeployTaskListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<ModelDeployTask> getModelDeployTaskList(ModelDeployTask filter, String orderBy) {
        return modelDeployTaskMapper.getModelDeployTaskList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getModelDeployTaskList)，以便获取更好查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<ModelDeployTask> getModelDeployTaskListWithRelation(ModelDeployTask filter, String orderBy) {
        List<ModelDeployTask> resultList = modelDeployTaskMapper.getModelDeployTaskList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        for (ModelDeployTask deployTask : resultList) {
            Integer modelType = deployTask.getModelType();
            if (modelType == 1) {
                TrainTask trainTask = trainTaskMapper.selectTrainTask(deployTask.getModelId());
                if (trainTask == null) {
                    deployTask.setBaseModelName("未知！");
                    deployTask.setInferenceModel(-1);
                    continue;
                }
                ModelBasic modelBasic = modelBasicService.getById(trainTask.getBaseModelId());
                if (modelBasic != null){
                    deployTask.setBaseModelName(trainTask.getModelName());
                    deployTask.setInferenceModel(modelBasic.getInferenceModel());
                }
            } else if (modelType == 2) {
                //取消数据权限
                boolean originFlag = GlobalThreadLocal.setDataFilter(false);
                ModelBasic modelBasic = modelBasicService.getById(deployTask.getModelId());
                if (modelBasic != null){
                    deployTask.setBaseModelName(modelBasic.getModelName());
                    deployTask.setInferenceModel(modelBasic.getInferenceModel());
                }
                GlobalThreadLocal.setDataFilter(originFlag);
            }
        }
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<ModelDeployTask> getGroupedModelDeployTaskListWithRelation(
            ModelDeployTask filter, String groupSelect, String groupBy, String orderBy) {
        List<ModelDeployTask> resultList =
                modelDeployTaskMapper.getGroupedModelDeployTaskList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private ModelDeployTask buildDefaultValue(ModelDeployTask modelDeployTask) {
        if (modelDeployTask.getId() == null) {
            modelDeployTask.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(modelDeployTask);
        modelDeployTask.setIsDelete(GlobalDeletedFlag.NORMAL);
        return modelDeployTask;
    }

    @Override
    public void deploy(ModelDeployTask modelDeployTask) {
        initTimePeriods(modelDeployTask.getId());
        JSONObject jsonObject = JSONUtil.parseObj(modelDeployTask.getCommonParameterJson());
        // 获取devices数组
        JSONArray devices = jsonObject.getJSONArray(DEVICES);
        JSONArray deviceIds = devices.getJSONObject(0).getJSONArray(DEVICE_IDS);
        List<String> ids = new ArrayList<>();
        for (int j = 0; j < deviceIds.size(); j++) {
            ids.add(deviceIds.getStr(j));
        }
        Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
        RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
        HashMap<String, Object> deployTaskMap = new HashMap<>();
        int port = sysUtil.getPort(remoteHost.getHostIp());
        String rsa = generateRsa();
        deployTaskMap.put(API_SERVER_PORT, port);
        deployTaskMap.put(ENCRYPTION_KEY, rsa);
        deployTaskMap.put(TASK_NAME, modelDeployTask.getModelName());
        deployTaskMap.put(REMOTE_HOST_ID, remoteHostId);
        Integer deployType = modelDeployTask.getDeployType();
        String taskName = modelDeployTask.getModelName();
        String deployTaskPath = nfsPathMgmt.getDeployTaskPath(taskName);
        try {
            // 创建部署任务
            if (nfsPathMgmt.createDir(deployTaskPath).equals(false)) {
                throw new MyRuntimeException("模型部署目录创建失败!");
            }
            if (modelDeployTask.getModelType().equals(2)) {
                // 部署基础模型
                ModelBasic modelBasic = modelBasicService.getById(modelDeployTask.getModelId());
                String baseModelPath = modelPosition + modelBasic.getModelName();
                File file = new File(baseModelPath);
                // 检查文件夹是否存在
                if (!file.exists()) {
                    throw new MyRuntimeException("该模型暂未下载，请联系管理员");
                }
                // 确认基础模型存在，开始执行部署任务
                deployTaskMap.put(TASK_NAME, taskName);
                deployTaskMap.put(TASK_PATH, deployTaskPath);
                deployTaskMap.put(BASE_MODEL_PATH, baseModelPath);
            } else {
                TrainTask trainTask = trainTaskMapper.selectById(modelDeployTask.getModelId());
                String modelCode = trainTask.getModelCode();
                boolean flag = nfsPathMgmt.isFull(trainTask.getTrainTaskMethod());
                String modelPath;
                if (modelDeployTask.getModelType().equals(1)) {
                    ModelWeightMerge modelWeightMerge = modelWeightMergeMapper.selectById(modelDeployTask.getMergeId());
                    String mergeCode = modelWeightMerge.getMergeCode();
                    //部署我的模型
                    if (flag) {
                        //全参
                        modelPath = nfsPathMgmt.getCheckpointPath(modelCode);
                    } else {
                        modelPath = nfsPathMgmt.getMergeOutputPath(mergeCode);
                    }
                    //检查模型是否已存在
                    File folder = new File(modelPath);
                    if (!folder.exists() || !folder.isDirectory()) {
                        throw new MyRuntimeException("该模型不存在!");
                    }
                    modelPath = jarPosition + modelPath.substring(2);
                    //构建.env脚本
                    deployTaskMap.put(TASK_NAME, taskName);
                    deployTaskMap.put(TASK_PATH, deployTaskPath);
                    deployTaskMap.put(BASE_MODEL_PATH, modelPath);
                }
                if (modelDeployTask.getModelType().equals(3)) {
                    if (flag) {
                        //全参
                        modelPath = nfsPathMgmt.getCheckpointPath(modelCode);
                        deployTaskMap.put(IS_LORA, "false");
                    } else {
                        if (trainTask.getMergeId() != null) {
                            ModelWeightMerge modelWeightMerge = modelWeightMergeMapper.selectById(trainTask.getMergeId());
                            modelPath = jarPosition + nfsPathMgmt.getMergeOutputPath(modelWeightMerge.getMergeCode()).substring(2);
                        } else {
                            ModelBasic modelBasic = modelBasicService.getById(trainTask.getBaseModelId());
                            modelPath = modelPosition + modelBasic.getModelName();
                        }
                        String checkPointPath = jarPosition + nfsPathMgmt.getCheckPoint(modelCode, modelDeployTask.getCheckPoint()).substring(2);
                        deployTaskMap.put(WEIGHT_VALUE, checkPointPath);
                        deployTaskMap.put(IS_LORA, "true");
                    }
                    deployTaskMap.put(TASK_NAME, taskName);
                    deployTaskMap.put(TASK_PATH, deployTaskPath);
                    deployTaskMap.put(BASE_MODEL_PATH, modelPath);
                }
            }
            String composeType = null;
            // fastChat
            if (deployType.equals(1)) {
                creatFastchatEnv(deployTaskMap, ids);
                if (remoteHost.getHostType().equals(1)) {
                    composeType = nfsPathMgmt.getFastChatPath();
                } else {
                    composeType = nfsPathMgmt.getFastChatDeployAscend();
                }
            }
            // vllm
            if (deployType.equals(2)) {
                if (remoteHost.getHostType().equals(1)) {
                    creatNvidiaVllmEnv(deployTaskMap, ids);
                    composeType = nfsPathMgmt.getVllmDeployPath();
                } else {
                    creatAscendVllmEnv(deployTaskMap, ids);
                    composeType = nfsPathMgmt.getAscendVllmPath();
                }
            }
            // LlamaFactory
            if (deployType.equals(4)) {
                Integer hostType = remoteHost.getHostType();
                if (hostType.equals(1)) {
                    composeType = nfsPathMgmt.getLlmDeployPath();
                } else {
                    if (remoteHost.getAscendType().equals(1)) {
                        composeType = nfsPathMgmt.getLlmDeployAscend910B();
                    } else {
                        composeType = nfsPathMgmt.getLlmDeployAscend();
                    }
                }
                creatLlamaFactoryEnv(deployTaskMap, ids, modelDeployTask, hostType);
            }
            // 上传compose.yaml
            nfsPathMgmt.uploadComposeYaml(composeType, deployTaskPath);
            // 执行docker compose
            remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, nfsPathMgmt.getModelDeployComposeYamlPath(taskName), nfsPathMgmt.getEndWithUp());
            String containerId = dockerJavaUtil.getContainerId(taskName, remoteHostId);
            if (containerId == null) {
                throw new MyRuntimeException();
            }
            // 更新模型部署表数据
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .set(ModelDeployTask::getDockerId, containerId)
                    .set(ModelDeployTask::getServerHost, remoteHost.getHostIp())
                    .set(ModelDeployTask::getApiServerPort, port)
                    .set(ModelDeployTask::getEncryptionKey, rsa)
                    .eq(ModelDeployTask::getId, modelDeployTask.getId()));
            //检测是否部署成功
            checkDeployState(containerId, remoteHostId, modelDeployTask.getId(), 0);
        } catch (MyRuntimeException | IOException e) {
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .eq(ModelDeployTask::getId, modelDeployTask.getId())
                    .set(ModelDeployTask::getDeployStatus, 4)
                    .set(ModelDeployTask::getDeployLogs, e.getMessage()));
            e.printStackTrace();
        }
    }

    private String generateRsa() {
        //生成密钥
        SecureRandom salt = new SecureRandom();
        StringBuilder sb = new StringBuilder(48);
        for (int i = 0; i < 48; i++) {
            int randomIndex = salt.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        return sb.toString();
    }


    //获取部署日志，判断部署是否成功
    public void checkDeployState(String containerId, Long remoteHostId, Long deployTaskId, int checkTime) {
        log.info("****开始检测****");
        String logs;
        int attempts = 0;
        int maxAttempts = 0;
        if (checkTime == 0) {
            maxAttempts = 600;
        } else if (checkTime == 1) {
            maxAttempts = 18000;
        }
        boolean isDeploySuccess = false;
        while (attempts <= maxAttempts && !isDeploySuccess) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread was interrupted", e);
                break;
            }
            logs = dockerJavaUtil.getContainerLog(containerId, null, remoteHostId);
            if (logs.contains("Uvicorn running") || logs.contains("Daemon start success") || logs.contains("Application startup complete")) {
                update(new LambdaUpdateWrapper<ModelDeployTask>().eq(ModelDeployTask::getDockerId, containerId).set(ModelDeployTask::getDeployStatus, 6));
                updateTime(deployTaskId);
                isDeploySuccess = true;
                log.info("****检测完成,模型部署成功****");
            } else if (logs.contains("Traceback (most recent call last)") || logs.contains("Failed to init endpoint")) {
                update(new LambdaUpdateWrapper<ModelDeployTask>().eq(ModelDeployTask::getDockerId, containerId).set(ModelDeployTask::getDeployStatus, 4));
                isDeploySuccess = true;
                log.info("****检测完成,模型部署失败****");
            } else {
                attempts++;
            }
        }
        if (!isDeploySuccess) {
            update(new LambdaUpdateWrapper<ModelDeployTask>().eq(ModelDeployTask::getDockerId, containerId).set(ModelDeployTask::getDeployStatus, 4));
            dockerJavaUtil.stopContainer(containerId, remoteHostId);
        }
    }

    /**
     * 删除模型
     */
    @Override
    public void removeModel(ModelDeployTask modelDeployTask) {
        try {
            //清理模型
            Integer status = modelDeployTask.getDeployStatus();
            if (status.equals(6)) {
                // 结算时间
                updateTime(modelDeployTask.getId());
            }
            String taskName = modelDeployTask.getModelName();
            String deployTaskPath = nfsPathMgmt.getDeployTaskPath(taskName);
            String commonParameterJson = modelDeployTask.getCommonParameterJson();
            JSONObject json = JSONUtil.parseObj(commonParameterJson);
            JSONArray devices = json.getJSONArray(DEVICES);
            List<Long> remoteHostIds = new ArrayList<>();
            for (int i = 0; i < devices.size(); i++) {
                JSONObject device = devices.getJSONObject(i);
                String id = device.getStr("id");
                remoteHostIds.add(Long.valueOf(id));
            }
            String masterId = json.getStr("mainDeviceId");
            if (masterId != null && !masterId.isEmpty()) {
                remoteHostIds.forEach(id -> {
                    String containerId = dockerJavaUtil.getContainerId(taskName, id);
                    if (containerId != null) {
                        remoteSshUtils.checkRemoteHostAndExecution(id, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
                    }
                });
            } else {
                Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
                String containerId = dockerJavaUtil.getContainerId(taskName, remoteHostId);
                if (containerId != null) {
                    remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
                }
            }
            String command = "rm -rf " + deployTaskPath;
            remoteSshUtils.localExecution(command);
            modelDeployTaskMapper.deleteById(modelDeployTask.getId());
            modelTokensCountService.removeByModelName(taskName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 停止模型，释放资源
     */
    @Override
    public void stopModel(ModelDeployTask modelDeployTask) {
        //结算时间
        updateTime(modelDeployTask.getId());
        //部署服务的路径
        String taskName = modelDeployTask.getModelName();
        String deployTaskPath = nfsPathMgmt.getDeployTaskPath(taskName);
        //暂停模型
        String commonParameterJson = modelDeployTask.getCommonParameterJson();
        JSONObject json = JSONUtil.parseObj(commonParameterJson);
        JSONArray devices = json.getJSONArray(DEVICES);
        List<Long> remoteHostIds = new ArrayList<>();
        for (int i = 0; i < devices.size(); i++) {
            JSONObject device = devices.getJSONObject(i);
            String id = device.getStr("id");
            remoteHostIds.add(Long.valueOf(id));
        }
        String masterId = json.getStr("mainDeviceId");
        if (!masterId.isEmpty()) {
            remoteHostIds.forEach(id -> remoteSshUtils.checkRemoteHostAndExecution(id, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown()));
        } else {
            Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
            remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
        }
        //更新状态
        this.update(new LambdaUpdateWrapper<ModelDeployTask>().eq(ModelDeployTask::getId, modelDeployTask.getId()).set(ModelDeployTask::getDeployStatus, 7));
    }

    /**
     * 重新启动模型
     */
    @Override
    public void startModel(ModelDeployTask modelDeployTask) {
        Integer status = modelDeployTask.getDeployStatus();
        if (!status.equals(7) && !status.equals(4)) {
            throw new MyRuntimeException();
        }
        //启动容器
        String commonParameterJson = modelDeployTask.getCommonParameterJson();
        JSONObject json = JSONUtil.parseObj(commonParameterJson);
        JSONArray devices = json.getJSONArray(DEVICES);
        Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
        String taskName = modelDeployTask.getModelName();
        remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, nfsPathMgmt.getModelDeployComposeYamlPath(taskName), nfsPathMgmt.getEndWithUp());
        String containerId = dockerJavaUtil.getContainerId(taskName, remoteHostId);
        modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>().set(ModelDeployTask::getDockerId, containerId).eq(ModelDeployTask::getId, modelDeployTask.getId()));
        checkDeployState(containerId, remoteHostId, modelDeployTask.getId(), 0);
    }

    @Override
    public String modelDeployLogs(ModelDeployTask modelDeployTask) {
        String containerLog;
        String commonParameterJson = modelDeployTask.getCommonParameterJson();
        JSONObject deployJson = JSONUtil.parseObj(commonParameterJson);
        JSONArray devices = deployJson.getJSONArray(DEVICES);
        String remoteHostId = devices.getJSONObject(0).getStr("id");
        String containerName = modelDeployTask.getModelName();
        String containerId = dockerJavaUtil.getContainerId(containerName, Long.valueOf(remoteHostId));
        if (containerId != null) {
            containerLog = dockerJavaUtil.getContainerLog(containerId, null, Long.valueOf(remoteHostId));
            return containerLog;
        } else {
            containerLog = modelDeployTask.getDeployLogs();
            if (StringUtils.isNotBlank(containerLog)) {
                return containerLog;
            } else {
                return "部署失败，容器未启动";
            }
        }
    }

    @Override
    public List<Object> getRelyList(Long modelId) {
        List<ModelServiceApi> deployList = modelServiceApiMapper.selectList(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceTypeId, modelId)
                .eq(ModelServiceApi::getServiceType, 1));
        List<AppAssistantBasic> appAssistantBasicList = appAssistantBasicMapper.selectList(new LambdaQueryWrapper<AppAssistantBasic>()
                .eq(AppAssistantBasic::getModelId, modelId)
                .eq(AppAssistantBasic::getModelType, "deploy"));
        List<ModelServiceApi> knowledgeAssistantList = modelServiceApiMapper.selectList(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getModelId, modelId)
                .eq(ModelServiceApi::getServiceType, 3));
        ArrayList<Object> list = new ArrayList<>();
        list.add(deployList);
        list.add(appAssistantBasicList);
        list.add(knowledgeAssistantList);
        return list;
    }

    private void creatFastchatEnv(Map<String, Object> hashMap, List<String> deviceIds) {
        StringBuilder envScriptBuilder = new StringBuilder();
        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_PATH=").append(hashMap.get(BASE_MODEL_PATH)).append(System.lineSeparator());
        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_NAMES=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        int port = (int) hashMap.get(API_SERVER_PORT);
        envScriptBuilder.append("FASTCHAT_API_SERVER_PORT=").append(port).append(System.lineSeparator());
        envScriptBuilder.append("FASTCHAT_CONTROLLER_PORT=").append(port + 1).append(System.lineSeparator());
        if (deviceIds != null && !deviceIds.isEmpty()) {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("\"").append(String.join(",", deviceIds)).append("\"").append(System.lineSeparator());
            envScriptBuilder.append("NUM_GPUS=").append(deviceIds.size()).append(System.lineSeparator());
        } else {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("0").append(System.lineSeparator());
            envScriptBuilder.append("NUM_GPUS=").append("1").append(System.lineSeparator());
        }
        envScriptBuilder.append("OPENAI_API_KEY=").append("\"").append("sk-").append(hashMap.get(ENCRYPTION_KEY)).append("\"").append(System.lineSeparator());
        assert deviceIds != null;
        int memory = deviceIds.size() * 32;
        envScriptBuilder.append("MAX_GPU_MEMORY=").append(memory).append("GiB");
        String path = hashMap.get(TASK_PATH) + ENV_PATH;
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder.toString());
    }

    private void creatNvidiaVllmEnv(Map<String, Object> hashMap, List<String> deviceIds) {
        String path = hashMap.get(TASK_PATH) + ENV_PATH;
        StringBuilder envScriptBuilder = new StringBuilder();
        envScriptBuilder.append(MODEL_PATH).append(hashMap.get(BASE_MODEL_PATH)).append(System.lineSeparator());
        envScriptBuilder.append(CONTAINER).append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        envScriptBuilder.append(IMAGE_NAME).append(nvidiaVllm).append(System.lineSeparator());
        envScriptBuilder.append("MODEL_NAME=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        envScriptBuilder.append("PORT=").append(hashMap.get(API_SERVER_PORT)).append(System.lineSeparator());
        if (deviceIds != null && !deviceIds.isEmpty()) {
            envScriptBuilder.append("CUDA_VISIBLE_DEVICES=").append("\"").append(String.join(",", deviceIds)).append("\"").append(System.lineSeparator());
            envScriptBuilder.append("WORLD_SIZE=").append(deviceIds.size()).append(System.lineSeparator());
        } else {
            envScriptBuilder.append("CUDA_VISIBLE_DEVICES=").append("0").append(System.lineSeparator());
            envScriptBuilder.append("WORLD_SIZE=").append(1).append(System.lineSeparator());
        }
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder.toString());
    }

    private void creatAscendVllmEnv(Map<String, Object> hashMap, List<String> deviceIds) {
        String path = hashMap.get(TASK_PATH) + ENV_PATH;
        String envScriptBuilder =
                CONTAINER + hashMap.get(TASK_NAME) + System.lineSeparator() +
                        "MODEL_NAME=" + hashMap.get(TASK_NAME) + System.lineSeparator() +
                        IMAGE_NAME + ascendVllm + System.lineSeparator() +
                        MODEL_PATH + hashMap.get(BASE_MODEL_PATH) + System.lineSeparator() +
                        "API_SERVER_PORT=" + hashMap.get(API_SERVER_PORT) + System.lineSeparator() +
                        "DEVICES_NUMS_IDS=" + String.join(",", deviceIds) + System.lineSeparator() +
                        "WORLD_SIZE=" + deviceIds.size();
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder);
    }

    private void creatLlamaFactoryEnv(Map<String, Object> deployTaskMap, List<String> ids, ModelDeployTask modelDeployTask, int hostType) {
        String path = deployTaskMap.get(TASK_PATH) + ENV_PATH;
        StringBuilder envScriptBuilder = new StringBuilder();
        String template;
        Integer modelType = modelDeployTask.getModelType();
        if (modelType.equals(2)) {
            ModelBasic modelBasic = modelBasicService.getById(modelDeployTask.getModelId());
            template = modelBasic.getModelTrainTemplate();
            if (template == null) {
                template = getTemplate(modelBasic.getModelName());
            }
        } else {
            TrainTask trainTask = trainTaskMapper.selectOne(new LambdaQueryWrapper<TrainTask>().eq(TrainTask::getId, modelDeployTask.getModelId()));
            JSONObject configJson = JSONUtil.parseObj(trainTask.getTrainTaskConfigJson());
            template = configJson.getStr("template");
        }
        envScriptBuilder.append(MODEL_PATH).append(deployTaskMap.get(BASE_MODEL_PATH)).append(System.lineSeparator());
        if ("true".equals(deployTaskMap.get(IS_LORA))) {
            envScriptBuilder.append(CHECK_POINT).append(deployTaskMap.get(WEIGHT_VALUE)).append(System.lineSeparator());
        } else {
            envScriptBuilder.append(CHECK_POINT).append("./tmp").append(System.lineSeparator());
        }
        envScriptBuilder.append(CONTAINER).append(deployTaskMap.get(TASK_NAME)).append(System.lineSeparator());
        envScriptBuilder.append(LOCAL_IP).append(System.lineSeparator());
        envScriptBuilder.append(LMD_PORT).append(8000).append(System.lineSeparator());
        envScriptBuilder.append("API_SERVER_PORT=").append(deployTaskMap.get(API_SERVER_PORT)).append(System.lineSeparator());
        envScriptBuilder.append(RESTART).append(System.lineSeparator());
        envScriptBuilder.append(DEVICES_NUMS_IDS).append("\"").append(String.join(",", ids)).append("\"").append(System.lineSeparator());
        envScriptBuilder.append("operationType=previewModel").append(System.lineSeparator());
        if (hostType == 1) {
            envScriptBuilder.append(IMAGE_NAME).append(nvidiaLlama).append(System.lineSeparator());
        } else {
            envScriptBuilder.append(IMAGE_NAME).append(ascendLlama).append(System.lineSeparator());
        }
        envScriptBuilder.append(OPTIONS);
        envScriptBuilder.append(" --do_sample False");
        envScriptBuilder.append(" --trust_remote_code True");
        envScriptBuilder.append(" --template ").append(template);
        envScriptBuilder.append(" --model_name_or_path ./model");
        if ("true".equals(deployTaskMap.get(IS_LORA)) && modelType.equals(3)) {
            envScriptBuilder.append(" --adapter_name_or_path  ./checkpoint");
            envScriptBuilder.append(" --finetuning_type lora");
        }
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder.toString());
    }

    private String getTemplate(String modelName) {
        String[][] templates = {
                {"DeepSeek-R1", "deepseek3"},
                {"deepseek-R1", "deepseek3"},
                {"DeepSeek-V3", "deepseek3"},
                {"DeepSeek-V2.5", "deepseek3"},
                {"Llama-3", "llama3"},
                {"Llama3", "llama3"},
                {"llama-3", "llama3"},
                {"Llama-2", "llama2"},
                {"Llama2", "llama2"},
                {"llama-2", "llama2"},
                {"Qwen3", "qwen3"},
                {"Qwen2-VL", "qwen2_vl"},
                {"Qwen1.5", "qwen"},
                {"Qwen", "qwen"},
                {"qwen", "qwen"},
                {"DeepSeek", "deepseek"},
                {"glm-4", "glm4"},
                {"Baichuan2", "baichuan2"},
                {"bloom", "default"},
                {"chatglm-3", "chatglm3"},
                {"chatglm3", "chatglm3"},
                {"ChatGLM3", "chatglm3"},
                {"GLM", "chatglm3"},
                {"Command-R", "cohere"},
                {"Falcon", "falcon"},
                {"Gemma", "gemma"},
                {"gemma", "gemma"},
                {"InternLM2", "intern2"},
                {"Llama", "default"},
                {"Mistral", "mistral"},
                {"Mixtral", "mistral"},
                {"OLMo", "olmo"},
                {"Phi-", "default"},
                {"StarCoder2", "default"},
                {"XVERSE", "xverse"},
                {"Yi", "yi"},
                {"Yuan", "yuan"},
                {"other", "other"}
        };
        for (String[] template : templates) {
            if (modelName.contains(template[0])) {
                return template[1];
            }
        }
        return DEFAULT;
    }

    @Override
    public void mindIeDeploy(ModelDeployTask modelDeployTask) {
        initTimePeriods(modelDeployTask.getId());
        try {
            JSONObject jsonObject = JSONUtil.parseObj(modelDeployTask.getCommonParameterJson());
            // 获取devices数组
            JSONArray devices = jsonObject.getJSONArray("devices");
            // 使用显卡号
            JSONArray deviceIds = devices.getJSONObject(0).getJSONArray("device_ids");
            int[] ids = new int[deviceIds.size()];
            for (int i = 0; i < deviceIds.size(); i++) {
                ids[i] = deviceIds.getInt(i);
            }
            Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
            RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
            String hostIp = remoteHost.getHostIp();
            int port;
            do {
                port = 30000 + random.nextInt(1001);
            } while (!isPortAvailable(hostIp, port));
            int metricsPort;
            do {
                metricsPort = 31000 + random.nextInt(1001);
            } while (!isPortAvailable(hostIp, metricsPort));
            //.env
            StringBuilder envScriptBuilder = new StringBuilder();
            Integer modelType = modelDeployTask.getModelType();
            String mindIdDeployTaskName = modelDeployTask.getModelName();
            String mindIdDeployTaskPath = nfsPathMgmt.getDeployTaskPath(mindIdDeployTaskName);
            String mindIdDeployTaskConfPath = mindIdDeployTaskPath + "conf";
            if (nfsPathMgmt.createDir(mindIdDeployTaskConfPath).equals(false)) {
                throw new MyRuntimeException("目录创建失败!");
            }
            Integer ascendType = remoteHost.getAscendType();
            String containerName = modelDeployTask.getModelName();
            String modelPath;
            if (modelType.equals(1)) {
                // 我的模型
                TrainTask trainTask = trainTaskMapper.selectById(modelDeployTask.getModelId());
                ModelWeightMerge modelWeightMerge = modelWeightMergeMapper.selectById(modelDeployTask.getMergeId());
                String mergeCode = modelWeightMerge.getMergeCode();
                String taskName = trainTask.getModelCode();
                boolean flag = nfsPathMgmt.isFull(trainTask.getTrainTaskMethod());
                if (flag) {
                    String checkPoint = modelWeightMerge.getCheckPoint();
                    modelPath = nfsPathMgmt.getCheckPoint(taskName, checkPoint);
                } else {
                    modelPath = nfsPathMgmt.getMergeOutputPath(mergeCode);
                }
                modelPath = jarPosition + modelPath.substring(2);
                envScriptBuilder.append(CONTAINER).append(containerName).append(System.lineSeparator());
                envScriptBuilder.append(MODEL_PATH).append(modelPath).append(System.lineSeparator());
                // 训练好的模型使用新版mindie部署存在bug(使用基础模型的tokenizer.json进行替换)
                ModelBasic modelBasic = modelBasicService.getById(trainTask.getBaseModelId());
                String baseModelPath = nfsPathMgmt.getBaseModelPath(modelBasic.getModelName());
                tokenizerSet(baseModelPath, modelPath);
            } else {
                // 基础模型
                ModelBasic modelBasic = modelBasicService.getById(modelDeployTask.getModelId());
                modelPath = nfsPathMgmt.getBaseModelPath(modelBasic.getModelName());
                envScriptBuilder.append(MODEL_PATH).append(modelPath).append(System.lineSeparator());
                envScriptBuilder.append(CONTAINER).append(containerName).append(System.lineSeparator());
            }
            // 设置模型的config.json文件权限为640
            String modelConfigPath = modelPath + File.separator + "config.json";
            File modelConfigFile = new File(modelConfigPath);
            Path filePath = Paths.get(modelConfigFile.getPath());
            Set<PosixFilePermission> permissions = new HashSet<>();
            permissions.add(PosixFilePermission.OWNER_READ);
            permissions.add(PosixFilePermission.OWNER_WRITE);
            permissions.add(PosixFilePermission.GROUP_READ);
            Files.setPosixFilePermissions(filePath, permissions);

            String mindIePathComposeYamlPath = nfsPathMgmt.getMindIePath();
            if (ascendType == 0) {
                changeTorchDtype(modelConfigPath, "float16");
                mindIePathComposeYamlPath += "/910a";
                envScriptBuilder.append(IMAGE_NAME).append(mindIe910a).append(System.lineSeparator());
            } else if (ascendType == 1) {
                changeTorchDtype(modelConfigPath, "bfloat16");
                mindIePathComposeYamlPath += "/910b";
                envScriptBuilder.append(IMAGE_NAME).append(mindIe910b).append(System.lineSeparator());
            } else if (ascendType == 2) {
                changeTorchDtype(modelConfigPath, "float16");
                mindIePathComposeYamlPath += "/300iDuo";
                envScriptBuilder.append(IMAGE_NAME).append(mindIe300iDuo).append(System.lineSeparator());
            }
            //compose
            nfsPathMgmt.uploadComposeYaml(mindIePathComposeYamlPath, mindIdDeployTaskPath);

            // 设置环境变量
            String path = mindIdDeployTaskPath + ENV_PATH;
            File file = new File(path);
            FileWriter writer = new FileWriter(file);
            writer.write(envScriptBuilder.toString());

            //conf/config
            Integer maxSeqLen = modelDeployTask.getMaxSeqLen();
            if (maxSeqLen == null) {
                maxSeqLen = 8192;
            }
            Integer maxIterTimes = modelDeployTask.getMaxIterTimes();
            if (maxIterTimes == null) {
                maxIterTimes = 8192;
            }
            nfsPathMgmt.mindIeConfig(mindIdDeployTaskName, ids, mindIdDeployTaskConfPath, hostIp, port, metricsPort, ascendType, maxSeqLen, maxIterTimes);
            String mindIdDeployTaskCompose = mindIdDeployTaskPath + "compose.yaml";
            remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, mindIdDeployTaskCompose, nfsPathMgmt.getEndWithUp());
            String containerId = dockerJavaUtil.getContainerId(containerName, remoteHostId);
            if (containerId == null) {
                throw new MyRuntimeException();
            }
            //更新模型部署表数据
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .set(ModelDeployTask::getDockerId, containerId)
                    .set(ModelDeployTask::getServerHost, hostIp)
                    .set(ModelDeployTask::getApiServerPort, port)
                    .eq(ModelDeployTask::getId, modelDeployTask.getId()));
            //检测是否部署成功
            checkDeployState(containerId, remoteHostId, modelDeployTask.getId(), 0);
        } catch (IOException | MyRuntimeException e) {
            log.error(e.getMessage(), e);
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .eq(ModelDeployTask::getId, modelDeployTask.getId())
                    .set(ModelDeployTask::getDeployStatus, 4)
                    .set(ModelDeployTask::getDeployLogs, e.getMessage()));
        }
    }

    /**
     * 修改模型精度(torch_dtype)
     */
    private void changeTorchDtype(String modelConfigPath, String torchDtype) throws IOException {
        String modelConfigString = new String(Files.readAllBytes(Paths.get(modelConfigPath)));
        JSONObject configJson = JSONUtil.parseObj(modelConfigString);
        String originalTorchDtype = configJson.getStr("torch_dtype");
        if (originalTorchDtype != null && !torchDtype.equals(originalTorchDtype)) {
            configJson.putOpt("torch_dtype", torchDtype);
            FileWriter writer = new FileWriter(new File(modelConfigPath));
            writer.write(configJson.toString());
        }
    }


    /**
     * 设置tokenizer.json
     */
    private void tokenizerSet(String baseModelPath, String newModelPath) throws IOException {
        Path source = Paths.get(baseModelPath, "tokenizer.json");
        Path target = Paths.get(newModelPath, "tokenizer.json");
        try {
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw new IOException();
        }
    }

    @Override
    public void mindIeT71Deploy(ModelDeployTask modelDeployTask) {
        initTimePeriods(modelDeployTask.getId());
        try {
            JSONObject jsonObject = JSONUtil.parseObj(modelDeployTask.getCommonParameterJson());
            // 获取devices数组
            JSONArray devicesConfig = jsonObject.getJSONArray("devices");
            List<Long> remoteHostIds = new ArrayList<>();
            for (int i = 0; i < devicesConfig.size(); i++) {
                JSONObject device = devicesConfig.getJSONObject(i);
                String id = device.getStr("id");
                remoteHostIds.add(Long.valueOf(id));
            }
            Long masterId = Long.valueOf(jsonObject.getStr("mainDeviceId"));
            RemoteHost remoteHost = remoteHostService.getById(masterId);
            String hostIp = remoteHost.getHostIp();
            int port = sysUtil.getPort(hostIp);
            // .env
            StringBuilder envScriptBuilder = new StringBuilder();
            String mindIdDeployTaskName = modelDeployTask.getModelName();
            String mindIdDeployTaskPath = nfsPathMgmt.getDeployTaskPath(mindIdDeployTaskName);
            String mindIdDeployTaskConfPath = mindIdDeployTaskPath + "conf";
            if (nfsPathMgmt.createDir(mindIdDeployTaskConfPath).equals(false)) {
                throw new MyRuntimeException("目录创建失败!");
            }
            String containerName = modelDeployTask.getModelName();
            // 基础模型
            ModelBasic modelBasic = modelBasicService.getById(modelDeployTask.getModelId());
            String modelPath = nfsPathMgmt.getBaseModelPath(modelBasic.getModelName());
            envScriptBuilder.append(MODEL_PATH).append(modelPath).append(System.lineSeparator());
            envScriptBuilder.append(CONTAINER).append(containerName).append(System.lineSeparator());
            envScriptBuilder.append("WORLD_SIZE=").append(devicesConfig.size() * 8).append(System.lineSeparator());
            envScriptBuilder.append(IMAGE_NAME).append(mindIet71).append(System.lineSeparator());

            // 设置模型的config.json文件权限为640
            File modelConfigFile = new File(modelPath + File.separator + "config.json");
            Path filePath = Paths.get(modelConfigFile.getPath());
            Set<PosixFilePermission> permissions = new HashSet<>();
            permissions.add(PosixFilePermission.OWNER_READ);
            permissions.add(PosixFilePermission.OWNER_WRITE);
            permissions.add(PosixFilePermission.GROUP_READ);
            Files.setPosixFilePermissions(filePath, permissions);

            // conf/config
            Integer maxSeqLen = modelDeployTask.getMaxSeqLen();
            if (maxSeqLen == null) {
                maxSeqLen = 8192;
            }
            Integer maxIterTimes = modelDeployTask.getMaxIterTimes();
            if (maxIterTimes == null) {
                maxIterTimes = 8192;
            }
            nfsPathMgmt.mindIeT71Config(mindIdDeployTaskName, mindIdDeployTaskConfPath, hostIp, port, maxSeqLen, maxIterTimes);

            // compose
            String mindIePathComposeYamlPath = nfsPathMgmt.getMindIePath() + "/T71";
            nfsPathMgmt.uploadComposeYaml(mindIePathComposeYamlPath, mindIdDeployTaskPath);

            // rank_table_file.json
            JSONObject rankTableFile = new JSONObject();
            rankTableFile.putOpt("server_count", String.valueOf(devicesConfig.size()));
            rankTableFile.putOpt("status", "completed");
            rankTableFile.putOpt("version", "1.0");
            JSONArray serverList = new JSONArray();
            for (int i = 0; i < remoteHostIds.size(); i++) {
                JSONObject serverNode = new JSONObject();
                Long id = remoteHostIds.get(i);
                JSONArray deviceArray = new JSONArray();
                for (int deviceId = 0; deviceId < 8; deviceId++) {
                    JSONObject deviceJson = new JSONObject();
                    String command = "hccn_tool -i " + deviceId + " -ip -g";
                    String deviceIp = remoteSshUtils.executeCommand(id, command);
                    if (deviceIp.contains("failed")) {
                        throw new MyRuntimeException("hccl配置错误,请检查服务器的卡间通信是否正确");
                    } else if (deviceIp.contains("ipaddr")) {
                        deviceIp = deviceIp.split(System.lineSeparator())[0].split(":")[1];
                    }
                    deviceJson.putOpt("device_id", String.valueOf(deviceId));
                    deviceJson.putOpt("device_ip", deviceIp);
                    deviceJson.putOpt("rank_id", String.valueOf(i * 8 + deviceId));
                    deviceArray.put(deviceJson);
                }
                serverNode.putOpt("device", deviceArray);
                RemoteHost node = remoteHostService.getById(id);
                serverNode.putOpt("server_id", node.getHostIp());
                serverNode.putOpt("container_ip", node.getHostIp());
                serverList.put(serverNode);
            }
            rankTableFile.putOpt("server_list", serverList);
            String rankTableFilePath = mindIdDeployTaskPath + "rank_table_file.json";

            File rankTableFileFile = new File(rankTableFilePath);
            FileWriter rankTableFileWriter = new FileWriter(rankTableFileFile);
            rankTableFileWriter.write(rankTableFile.toString());

            File rankTable = new File(rankTableFilePath);
            Path rankTablePath = Paths.get(rankTable.getPath());
            Files.setPosixFilePermissions(rankTablePath, permissions);

            for (Long remoteHostId : remoteHostIds) {
                RemoteHost node = remoteHostService.getById(remoteHostId);
                creatDistributedOptions(mindIdDeployTaskPath + ENV_PATH, envScriptBuilder, node.getHostIp());
                String mindIdDeployTaskCompose = mindIdDeployTaskPath + "compose.yaml";
                remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, mindIdDeployTaskCompose, nfsPathMgmt.getEndWithUp());
            }
            String containerId = dockerJavaUtil.getContainerId(containerName, masterId);
            if (containerId == null) {
                throw new MyRuntimeException();
            }
            // 更新模型部署表数据
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .set(ModelDeployTask::getDockerId, containerId)
                    .set(ModelDeployTask::getServerHost, hostIp)
                    .set(ModelDeployTask::getApiServerPort, port)
                    .eq(ModelDeployTask::getId, modelDeployTask.getId()));
            // 检测是否部署成功
            checkDeployState(containerId, masterId, modelDeployTask.getId(), 1);
        } catch (IOException | MyRuntimeException e) {
            log.error(e.getMessage(), e);
            modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                    .eq(ModelDeployTask::getId, modelDeployTask.getId())
                    .set(ModelDeployTask::getDeployStatus, 4)
                    .set(ModelDeployTask::getDeployLogs, e.getMessage()));
        }
    }

    private boolean isPortAvailable(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), 2000);
            // 占用
            return false;
        } catch (IOException e) {
            // 未占用
            return true;
        }
    }

    /**
     * 设置各节点的环境变量
     */
    private void creatDistributedOptions(String envPath, StringBuilder envScriptBuilder, String hostIp) {
        File envFile = new File(envPath);
        FileWriter envWriter = new FileWriter(envFile);
        envWriter.write(envScriptBuilder.toString() + "HOST_IP=" + hostIp + System.lineSeparator());
    }

    void initTimePeriods(Long modelDeployTaskId) {
        JSONObject timePeriods = new JSONObject();
        timePeriods.putOpt("time_periods", new JSONArray());
        modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                .set(ModelDeployTask::getTimePeriods, timePeriods.toString())
                .eq(ModelDeployTask::getId, modelDeployTaskId));
    }

    void updateTime(Long modelDeployId) {
        ModelDeployTask modelDeployTask = modelDeployTaskMapper.selectById(modelDeployId);
        String timePeriods = modelDeployTask.getTimePeriods();
        if (timePeriods == null) {
            return;
        }
        JSONObject timePeriodsJson = JSONUtil.parseObj(timePeriods);
        JSONArray jsonArray = timePeriodsJson.getJSONArray("time_periods");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = formatter.format(new Date());
        if (jsonArray.isEmpty()) {
            JSONObject startTime = new JSONObject();
            startTime.putOpt("start_time", formattedDateTime);
            jsonArray.add(startTime);
        } else {
            JSONObject lastJson = jsonArray.getJSONObject(jsonArray.size() - 1);
            if (!lastJson.containsKey("end_time")) {
                lastJson.putOpt("end_time", formattedDateTime);
                JSONObject json = JSONUtil.parseObj(modelDeployTask.getCommonParameterJson());
                JSONArray devicesConfig = json.getJSONArray(DEVICES);
                JSONArray deviceIds = devicesConfig.getJSONObject(0).getJSONArray(DEVICE_IDS);
                List<String> ids = new ArrayList<>();
                for (int j = 0; j < deviceIds.size(); j++) {
                    ids.add(deviceIds.getStr(j));
                }
                lastJson.putOpt("device_ids", String.join(",", ids));
            } else {
                JSONObject startTime = new JSONObject();
                startTime.putOpt("start_time", formattedDateTime);
                jsonArray.add(startTime);
            }
        }
        modelDeployTaskMapper.update(new LambdaUpdateWrapper<ModelDeployTask>()
                .set(ModelDeployTask::getTimePeriods, timePeriodsJson.toString())
                .eq(ModelDeployTask::getId, modelDeployTask.getId()));
    }

    @Override
    public Map<String, ModelDeployTask> getModelNameToModelDeployTask() {
        List<ModelDeployTask> modelDeployTaskList = modelDeployTaskMapper.selectListToCallStatistics();
        int batchSize = modelDeployTaskList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(modelDeployTaskList, MyRelationParam.normal(), batchSize);
        return modelDeployTaskList.stream()
                .collect(Collectors.toMap(ModelDeployTask::getModelName, modelDeployTask -> modelDeployTask));
    }
}
