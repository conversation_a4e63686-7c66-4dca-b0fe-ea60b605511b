package com.supie.webadmin.app.app.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资源管理-显卡资源任务排名与算力资源关联表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "GraphicTaskScheduleComputePowerManageVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class GraphicTaskScheduleComputePowerManageVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 显卡资源任务调度id。
     */
    @Schema(description = "显卡资源任务调度id")
    private Long graphicTaskScheduleId;

    /**
     * 算力资源管理id。
     */
    @Schema(description = "算力资源管理id")
    private Long computePowerManageId;
}
