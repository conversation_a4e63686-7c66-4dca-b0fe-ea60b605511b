package com.supie.webadmin.app.app.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.webadmin.app.llmTrain.model.ModelWeightMerge;
import com.supie.webadmin.upms.model.SysUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 模型服务表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_model_service")
public class ModelService extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 服务名称。
     */
    private String serviceName;

    /**
     * 服务描述。
     */
    private String serviceDesc;

    /**
     * 服务key。
     */
    private String serviceKey;

    public String getNoBearerServiceKey() {
        if (serviceKey != null && serviceKey.startsWith("Bearer ")) {
            serviceKey = serviceKey.substring("Bearer ".length());
        }
        return serviceKey;
    }

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * service_name / service_desc LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    @RelationDict(
            masterIdField = "createUserId",
            slaveModelClass = SysUser.class,
            slaveIdField = "userId",
            slaveNameField = "showName")
    @TableField(exist = false)
    private Map<String, Object> createUserIdDictMap;


    /**
     * ModelServiceApi。
     */
    @RelationOneToMany(
            masterIdField = "id",
            slaveModelClass = ModelServiceApi.class,
            slaveIdField = "serviceId"
    )
    @TableField(exist = false)
    private List<ModelServiceApi> modelServiceApiList;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
