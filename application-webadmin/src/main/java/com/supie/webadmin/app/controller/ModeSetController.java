package com.supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.core.annotation.DisableDataFilter;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.vo.*;
import com.supie.webadmin.app.dto.*;
import com.supie.webadmin.app.model.*;
import com.supie.webadmin.app.service.*;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 模型厂商集合表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "模型厂商集合表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/modeSet")
public class ModeSetController {

    @Autowired
    private ModeSetService modeSetService;

    /**
     * 新增模型厂商集合表数据。
     *
     * @param modeSetDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "modeSetDto.id",
            "modeSetDto.searchString",
            "modeSetDto.createTimeStart",
            "modeSetDto.createTimeEnd",
            "modeSetDto.updateTimeStart",
            "modeSetDto.updateTimeEnd"})
    ////@SaCheckPermission("modeSet.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody ModeSetDto modeSetDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(modeSetDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ModeSet modeSet = MyModelUtil.copyTo(modeSetDto, ModeSet.class);
        modeSet = modeSetService.saveNew(modeSet);
        return ResponseResult.success(modeSet.getId());
    }

    /**
     * 更新模型厂商集合表数据。
     *
     * @param modeSetDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "modeSetDto.searchString",
            "modeSetDto.createTimeStart",
            "modeSetDto.createTimeEnd",
            "modeSetDto.updateTimeStart",
            "modeSetDto.updateTimeEnd"})
    ////@SaCheckPermission("modeSet.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody ModeSetDto modeSetDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(modeSetDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ModeSet modeSet = MyModelUtil.copyTo(modeSetDto, ModeSet.class);
        ModeSet originalModeSet = modeSetService.getById(modeSet.getId());
        if (originalModeSet == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!modeSetService.update(modeSet, originalModeSet)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除模型厂商集合表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("modeSet.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除模型厂商集合表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("modeSet.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的模型厂商集合表列表。
     *
     * @param modeSetDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @DisableDataFilter
    ////@SaCheckPermission("modeSet.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ModeSetVo>> list(
            @MyRequestBody ModeSetDto modeSetDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ModeSet modeSetFilter = MyModelUtil.copyTo(modeSetDtoFilter, ModeSet.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ModeSet.class);
        List<ModeSet> modeSetList = modeSetService.getModeSetListWithRelation(modeSetFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(modeSetList, ModeSetVo.class));
    }

    /**
     * 分组列出符合过滤条件的模型厂商集合表列表。
     *
     * @param modeSetDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("modeSet.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<ModeSetVo>> listWithGroup(
            @MyRequestBody ModeSetDto modeSetDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ModeSet.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, ModeSet.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ModeSet filter = MyModelUtil.copyTo(modeSetDtoFilter, ModeSet.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<ModeSet> resultList = modeSetService.getGroupedModeSetListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, ModeSetVo.class));
    }

    /**
     * 查看指定模型厂商集合表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////@SaCheckPermission("modeSet.view")
    @GetMapping("/view")
    public ResponseResult<ModeSetVo> view(@RequestParam Long id) {
        ModeSet modeSet = modeSetService.getByIdWithRelation(id, MyRelationParam.full());
        if (modeSet == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ModeSetVo modeSetVo = MyModelUtil.copyTo(modeSet, ModeSetVo.class);
        return ResponseResult.success(modeSetVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        ModeSet originalModeSet = modeSetService.getById(id);
        if (originalModeSet == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!modeSetService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
