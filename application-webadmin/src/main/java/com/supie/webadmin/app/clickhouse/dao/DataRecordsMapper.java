package com.supie.webadmin.app.clickhouse.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.clickhouse.model.DataRecords;
import com.supie.webadmin.app.clickhouse.model.DataSetCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 数据管理-数据表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm(excluseMethodName = {
        "getGroupedDataRecordsList",
        "deleteByDataRecords",
        "groupByDataSetId",
        "getDataRecordsList",
        "getDataRecordsById",
        "groupByDataMarkTaskId",
        "getDataRecordsListByPag",
        "updateDataRecords",
        "insertList",
        "getDataRecordsListByDataSetId",
        "selectList",
        "selectListByDataSetId",
        "selectCustomList"

})
public interface DataRecordsMapper extends BaseDaoMapper<DataRecords> {

    /**
     * 批量插入对象列表。
     *
     * @param dataRecordsList 新增对象列表。
     */
    void insertList(List<DataRecords> dataRecordsList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param dataRecordsFilter 主表过滤对象。
     * @param groupSelect       分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy           分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy           排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<DataRecords> getGroupedDataRecordsList(
            @Param("dataRecordsFilter") DataRecords dataRecordsFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);


    /**
     * 获取过滤后的对象列表。
     *
     * @param dataRecordsFilter 主表过滤对象。
     * @param orderBy           排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<DataRecords> getDataRecordsList(
            @Param("dataRecordsFilter") DataRecords dataRecordsFilter, @Param("orderBy") String orderBy);


    /**
     * 获取过滤后的对象列表。
     *
     * @param dataRecordsFilter 主表过滤对象。
     * @param orderBy           排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<DataRecords> getDataRecordsListByPag(
            @Param("dataRecordsFilter") DataRecords dataRecordsFilter, @Param("orderBy") String orderBy,
            @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);


    List<DataSetCount> groupByDataMarkTaskId();

    List<DataSetCount> groupByDataSetId(@Param("dataSetIdSet") Set<Long> dataSetIdSet);

    Long deleteByDataRecords(@Param("dataRecordsFilter") DataRecords dataRecordsFilter);

    int updateDataRecords(@Param("dataRecordsFilter") DataRecords dataRecords);

    DataRecords getDataRecordsById(@Param("id") Long id);

    List<DataRecords> getDataRecordsListByDataSetId(
            @Param("dataSetIdList") List<Long> dataSetIdList);

    List<DataRecords> selectListByDataSetId(Long dataSetId);

    List<DataRecords> selectCustomList(@Param("ew") LambdaQueryWrapper<DataRecords> lambdaQueryWrapper);

}
