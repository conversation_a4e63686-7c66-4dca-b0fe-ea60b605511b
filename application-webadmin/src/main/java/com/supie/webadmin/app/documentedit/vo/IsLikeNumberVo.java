package com.supie.webadmin.app.documentedit.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 点赞和点踩统计表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Schema(description = "IsLikeNumberVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class IsLikeNumberVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 是否喜欢（1 是；-1 否）。
     */
    @Schema(description = "是否喜欢（1 是；-1 否）")
    private Integer isLike;

    /**
     * 关联数据id。
     */
    @Schema(description = "关联数据id")
    private Long bindId;

    /**
     * 关联类型（表名）。
     */
    @Schema(description = "关联类型（表名）")
    private String bindType;

    /**
     * 用户id。
     */
    @Schema(description = "用户id")
    private Long userId;
}
