package com.supie.webadmin.app.data.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.validator.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * DataDataSetDto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "DataDataSetDto对象")
@Data
public class DataDataSetDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 数据集组id。
     */
    @Schema(description = "数据集组id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long indexId;

    /**
     * 名称。
     */
    @Schema(description = "名称")
    private String datasetName;

    /**
     * 数据条数。
     */
    @Schema(description = "数据条数")
    private Long dataNumber;

    /**
     * mark配置。
     */
    @Schema(description = "mark配置")
    private String markConfig;

    /**
     * 数据集标注状态（1：未开始；2：标注中；3：已完成）。
     */
    @Schema(description = "数据集标注状态（1：未开始；2：标注中；3：已完成）")
    private Integer datasetState;

    /**
     * 数据资源id。
     */
    @Schema(description = "数据资源id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long dataResourcesId;


    /**
     * 1.Alpaca  2.Sharegpt 3.基准测试集。
     */
    @Schema(description = "1.Alpaca  2.Sharegpt 3.基准测试集")
    private Integer dataFormat;


    /**
     * 1: 预训练数据集 2: 微调数据集 3: 偏好数据集 4: KTO 数据集  5:基准测试集。
     */
    @Schema(description = "1: 预训练数据集 2: 微调数据集 3: 偏好数据集 4: KTO 数据集  5:基准测试集。")
    private Integer dataType;


    /**
     * 1.文本 2.图片 3.音频 4. 视频
     */
    @Schema(description = " 1.文本 2.图片 3.音频 4. 视频")
    private Integer multimodalType;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * dataset_name LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
