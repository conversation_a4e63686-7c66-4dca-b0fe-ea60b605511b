package com.supie.webadmin.app.documentedit.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文档编写素材表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Schema(description = "DocumentEditMaterialVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentEditMaterialVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 素材标题。
     */
    @Schema(description = "素材标题")
    private String materialTitle;

    /**
     * 标签。
     */
    @Schema(description = "标签")
    private String materialTags;

    /**
     * 输出内容。
     */
    @Schema(description = "输出内容")
    private String materialContent;
}
