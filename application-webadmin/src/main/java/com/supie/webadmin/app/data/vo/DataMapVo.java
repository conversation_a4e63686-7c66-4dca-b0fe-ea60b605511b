package com.supie.webadmin.app.data.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据管理-数据映射表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "DataMapVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class DataMapVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 索引id。
     */
    @Schema(description = "索引id")
    private Integer mapId;

    /**
     * 标签集合。
     */
    @Schema(description = "标签集合")
    private String tagsStr;

    /**
     * 父级id。
     */
    @Schema(description = "父级id")
    private Long parentId;

    /**
     * 表名称。
     */
    @Schema(description = "表名称")
    private String dataTableName;

    /**
     * 关联数据id。
     */
    @Schema(description = "关联数据id")
    private Long dataTableId;

    /**
     * 映射类型(1，数据表；2，目录)。
     */
    @Schema(description = "映射类型(1，数据表；2，目录)")
    private Integer mapType;

    /**
     * 映射名称。
     */
    @Schema(description = "映射名称")
    private String mapName;

    /**
     * 模态类型(0，其他；1，文本；2，图片；3，音频；4，视频；)。
     */
    @Schema(description = "模态类型(0，其他；1，文本；2，图片；3，音频；4，视频；)")
    private Integer modalType;

    /**
     * 映射名称key。
     */
    @Schema(description = "映射名称key")
    private String mapNameKey;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer showOrder;

    /**
     * 数据json
     */
    @Schema(description = "数据json")
    private String dataJson;
}
