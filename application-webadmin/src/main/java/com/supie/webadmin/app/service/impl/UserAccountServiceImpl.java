package com.supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.webadmin.app.service.*;
import com.supie.webadmin.app.dao.*;
import com.supie.webadmin.app.model.*;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户账户表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Slf4j
@MyDataSource(DataSourceType.MAIN)
@Service("userAccountService")
public class UserAccountServiceImpl extends BaseService<UserAccount, Long> implements UserAccountService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private UserAccountMapper userAccountMapper;
    @Autowired
    private PointsChangeHistoryServiceImpl pointsChangeHistoryService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<UserAccount> mapper() {
        return userAccountMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserAccount saveNew(UserAccount userAccount) {
        userAccountMapper.insert(this.buildDefaultValue(userAccount));
        return userAccount;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<UserAccount> userAccountList) {
        if (CollUtil.isNotEmpty(userAccountList)) {
            userAccountList.forEach(this::buildDefaultValue);
            userAccountMapper.insertList(userAccountList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(UserAccount userAccount, UserAccount originalUserAccount) {
        MyModelUtil.fillCommonsForUpdate(userAccount, originalUserAccount);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<UserAccount> uw = this.createUpdateQueryForNullValue(userAccount, userAccount.getId());
        return userAccountMapper.update(userAccount, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return userAccountMapper.deleteById(id) == 1;
    }

    @Override
    public List<UserAccount> getUserAccountList(UserAccount filter, String orderBy) {
        return userAccountMapper.getUserAccountList(filter, orderBy);
    }

    @Override
    public List<UserAccount> getUserAccountListWithRelation(UserAccount filter, String orderBy) {
        List<UserAccount> resultList = userAccountMapper.getUserAccountList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<UserAccount> getGroupedUserAccountListWithRelation(
            UserAccount filter, String groupSelect, String groupBy, String orderBy) {
        List<UserAccount> resultList =
                userAccountMapper.getGroupedUserAccountList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private UserAccount buildDefaultValue(UserAccount userAccount) {
        if (userAccount.getId() == null) {
            userAccount.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(userAccount);
        userAccount.setIsDelete(GlobalDeletedFlag.NORMAL);
        MyModelUtil.setDefaultValue(userAccount, "balance", 0);
        return userAccount;
    }

    @Override
    public void updateUserBalance(UserAccount userAccount, Integer balance) {
        PointsChangeHistory pointsChangeHistory = new PointsChangeHistory();
        pointsChangeHistory.setChangeType(1);
        pointsChangeHistory.setUserAccountId(userAccount.getId());
        pointsChangeHistory.setPointsCurrent(userAccount.getBalance());
        pointsChangeHistory.setPointsAfter(balance);
        pointsChangeHistory.setPointsBefore(userAccount.getBalance());
        pointsChangeHistory.setRechargeNumber(balance);
        pointsChangeHistoryService.saveNew(pointsChangeHistory);
        userAccountMapper.update(new LambdaUpdateWrapper<UserAccount>().eq(UserAccount::getId, userAccount.getId())
                .set(UserAccount::getBalance, balance));
    }

}
