package com.supie.webadmin.app.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.app.dao.AppAiAssessInfoMapper;
import com.supie.webadmin.app.app.dao.AppAssessModelModelRelationshipMapper;
import com.supie.webadmin.app.app.dao.AppModelAssessMapper;
import com.supie.webadmin.app.app.model.AppAiAssessInfo;
import com.supie.webadmin.app.app.model.AppAssessModelModelRelationship;
import com.supie.webadmin.app.app.model.AppModelAssess;
import com.supie.webadmin.app.app.service.AppAiAssessInfoService;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;

/**
 * 应用中心-Al评估信息表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("appAiAssessInfoService")
@MyDataSource(DataSourceType.MAIN)
public class AppAiAssessInfoServiceImpl extends BaseService<AppAiAssessInfo, Long> implements AppAiAssessInfoService {

    @Autowired
    private AppAiAssessInfoMapper appAiAssessInfoMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Resource
    private AppModelAssessMapper appModelAssessMapper;
    @Resource
    private AppAssessModelModelRelationshipMapper appAssessModelModelRelationshipMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<AppAiAssessInfo> mapper() {
        return appAiAssessInfoMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param appAiAssessInfo 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AppAiAssessInfo saveNew(AppAiAssessInfo appAiAssessInfo) {
        appAiAssessInfoMapper.insert(this.buildDefaultValue(appAiAssessInfo));
        return appAiAssessInfo;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param appAiAssessInfoList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<AppAiAssessInfo> appAiAssessInfoList) {
        if (CollUtil.isNotEmpty(appAiAssessInfoList)) {
            appAiAssessInfoList.forEach(this::buildDefaultValue);
            appAiAssessInfoMapper.insertList(appAiAssessInfoList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param appAiAssessInfo         更新的对象。
     * @param originalAppAiAssessInfo 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(AppAiAssessInfo appAiAssessInfo, AppAiAssessInfo originalAppAiAssessInfo) {
        MyModelUtil.fillCommonsForUpdate(appAiAssessInfo, originalAppAiAssessInfo);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<AppAiAssessInfo> uw = this.createUpdateQueryForNullValue(appAiAssessInfo, appAiAssessInfo.getId());
        return appAiAssessInfoMapper.update(appAiAssessInfo, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return appAiAssessInfoMapper.deleteById(id) == 1;
    }



    /**
     * 当前服务的支持表为从表，根据主表的关联Id，删除一对多的从表数据。
     *
     * @param modelAssessId 从表关联字段。
     * @return 删除数量。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int removeByModelAssessId(Long modelAssessId) {
        AppAiAssessInfo deletedObject = new AppAiAssessInfo();
        deletedObject.setModelAssessId(modelAssessId);
        return appAiAssessInfoMapper.delete(new QueryWrapper<>(deletedObject));
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getAppAiAssessInfoListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<AppAiAssessInfo> getAppAiAssessInfoList(AppAiAssessInfo filter, String orderBy) {
        return appAiAssessInfoMapper.getAppAiAssessInfoList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getAppAiAssessInfoList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<AppAiAssessInfo> getAppAiAssessInfoListWithRelation(AppAiAssessInfo filter, String orderBy) {
        List<AppAiAssessInfo> resultList = appAiAssessInfoMapper.getAppAiAssessInfoList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<AppAiAssessInfo> getGroupedAppAiAssessInfoListWithRelation(
            AppAiAssessInfo filter, String groupSelect, String groupBy, String orderBy) {
        List<AppAiAssessInfo> resultList =
                appAiAssessInfoMapper.getGroupedAppAiAssessInfoList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private AppAiAssessInfo buildDefaultValue(AppAiAssessInfo appAiAssessInfo) {
        if (appAiAssessInfo.getId() == null) {
            appAiAssessInfo.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(appAiAssessInfo);
        appAiAssessInfo.setIsDeleted(GlobalDeletedFlag.NORMAL);
        return appAiAssessInfo;
    }

    /**
     * AI统计接口
     *
     * @param modelAssessId 评估任务ID
     * @return {
     * "fractionalFrequencyStatistics" : 裁判员模型打分统计({"分数":"出现次数"}),
     * "avgScoreAndGuantity" : 自动裁判员打分指标({"standardDeviation":"标准差", "average":"平均值", "median":"中位数"})
     * }
     */
    @Override
    public Map<String, Object> aiStatisticsOld(Long modelAssessId) {
        AppAiAssessInfo appAiAssessInfoFilter = new AppAiAssessInfo();
        appAiAssessInfoFilter.setModelAssessId(modelAssessId);
        List<AppAiAssessInfo> appAiAssessInfoList = getAppAiAssessInfoListWithRelation(appAiAssessInfoFilter, null);
        if (appAiAssessInfoList.size() == 0) {
            return new HashMap<>();
        }
        Set<Long> modelDeployIdList = new HashSet<>();
        Map<Long, String> modelDeployIdNameMap = new HashMap<>();
        Map<Long, List<AppAiAssessInfo>> appAiAssessInfoListGroupByModelDeployId = new HashMap<>();

//        for (AppAiAssessInfo appAiAssessInfo : appAiAssessInfoList) {
//            Long modelDeployId = appAiAssessInfo.getModelDeployId();
//            if (!modelDeployIdNameMap.containsKey(modelDeployId)
//                    && appAiAssessInfo.getModelDeployTask() != null
//                    && StrUtil.isNotBlank(appAiAssessInfo.getModelDeployTask().getModelName())) {
//                modelDeployIdList.add(modelDeployId);
//                modelDeployIdNameMap.put(modelDeployId, appAiAssessInfo.getModelDeployTask().getModelName());
//            }
//            if (!modelDeployIdNameMap.containsKey(modelDeployId)) {
//                continue;
//            }
//            if (!appAiAssessInfoListGroupByModelDeployId.containsKey(modelDeployId)) {
//                appAiAssessInfoListGroupByModelDeployId.put(modelDeployId, new ArrayList<>());
//            }
//            appAiAssessInfoListGroupByModelDeployId.get(modelDeployId).add(appAiAssessInfo);
//        }

        Map<String, Object> resultDataMap = new HashMap<>();
        for (Long modelDeployId : modelDeployIdList) {
            if (appAiAssessInfoListGroupByModelDeployId.get(modelDeployId) == null) {
                continue;
            }
            resultDataMap.put(modelDeployIdNameMap.get(modelDeployId), performTheRelevantCalculations(appAiAssessInfoListGroupByModelDeployId.get(modelDeployId)));
        }
        return resultDataMap;
    }
    @NotNull
    private static Map<String, Object> performTheRelevantCalculations(List<AppAiAssessInfo> appAiAssessInfoList) {
        // 最大分数
        int maxScore = 0;
        // 存在的分数统计
        Map<Integer, BigDecimal> scoreCountMap = new HashMap<>();
        for (AppAiAssessInfo appAiAssessInfo : appAiAssessInfoList) {
            Integer refereeModelScore = appAiAssessInfo.getRefereeModelScore();
            if (refereeModelScore == null) {
                continue;
            }
            if (refereeModelScore > maxScore) {
                maxScore = refereeModelScore;
            }
            if (scoreCountMap.containsKey(refereeModelScore)) {
                scoreCountMap.put(refereeModelScore, scoreCountMap.get(refereeModelScore).add(BigDecimal.ONE));
            } else {
                scoreCountMap.put(refereeModelScore, BigDecimal.ONE);
            }
        }
        Map<String, Object> resultDataMap = new HashMap<>();
        // 裁判员模型打分统计
        Map<String, BigDecimal> fractionalFrequencyStatisticsResultDataMap = new LinkedHashMap<>();
        for (int i = 0; i <= maxScore; i++) {
            fractionalFrequencyStatisticsResultDataMap.put(String.valueOf(i), scoreCountMap.getOrDefault(i, BigDecimal.ZERO));
        }
        resultDataMap.put("fractionalFrequencyStatistics", fractionalFrequencyStatisticsResultDataMap);
        // 自动裁判员打分指标集合
        List<Map<String, BigDecimal>> avgScoreAndGuantityList = new LinkedList<>();
        // 标准差
        Map<String, BigDecimal> standardDeviationMap = new HashMap<>();
        standardDeviationMap.put("标准差", calculateStandardDeviation(scoreCountMap).setScale(4, RoundingMode.HALF_UP));
        avgScoreAndGuantityList.add(standardDeviationMap);
        // 平均值
        Map<String, BigDecimal> averageMap = new HashMap<>();
        averageMap.put("平均值", calculateAverage(scoreCountMap).setScale(4, RoundingMode.HALF_UP));
        avgScoreAndGuantityList.add(averageMap);
        // 中位数
        Map<String, BigDecimal> medianMap = new HashMap<>();
        medianMap.put("中位数", calculateMedian(scoreCountMap).setScale(4, RoundingMode.HALF_UP));
        avgScoreAndGuantityList.add(medianMap);
        resultDataMap.put("avgScoreAndGuantity", avgScoreAndGuantityList);
        return resultDataMap;
    }
    /**
     * 计算中位数
     * @param scoreCountMap
     * @return
     */
    public static BigDecimal calculateMedian(Map<Integer, BigDecimal> scoreCountMap) {
        if (scoreCountMap == null || scoreCountMap.isEmpty()) {
            return BigDecimal.ZERO;
        }
        List<Integer> scores = new ArrayList<>(scoreCountMap.keySet());
        Collections.sort(scores);
        BigDecimal totalFrequency = scoreCountMap.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal halfFrequency = totalFrequency.divide(BigDecimal.valueOf(2), BigDecimal.ROUND_HALF_UP);
        BigDecimal cumulativeFrequency = BigDecimal.ZERO;
        Integer prevScore = null;
        for (Integer score : scores) {
            BigDecimal frequency = scoreCountMap.get(score);
            cumulativeFrequency = cumulativeFrequency.add(frequency);
            // Check if cumulative frequency reached or crossed the half of the total frequency
            if (cumulativeFrequency.compareTo(halfFrequency) >= 0) {
                // If total frequency is odd, or this is the first score to reach half frequency
                if (totalFrequency.remainder(BigDecimal.valueOf(2)).compareTo(BigDecimal.ZERO) != 0 ||
                        cumulativeFrequency.subtract(frequency).compareTo(halfFrequency) < 0) {
                    return BigDecimal.valueOf(score);
                }
                // If total frequency is even and this is not the first score to reach half frequency
                return BigDecimal.valueOf(prevScore + score).divide(BigDecimal.valueOf(2), BigDecimal.ROUND_HALF_UP);
            }
            prevScore = score;
        }
        return BigDecimal.ZERO; // Fallback, should not be reached if the map is non-empty
    }
    /**
     * 计算平均值
     * @param scoreCountMap 分数统计
     * @return
     */
    public static BigDecimal calculateAverage(Map<Integer, BigDecimal> scoreCountMap) {
        if (scoreCountMap == null || scoreCountMap.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalSum = BigDecimal.ZERO;
        BigDecimal totalCount = BigDecimal.ZERO;
        // 计算总和和总次数
        for (Map.Entry<Integer, BigDecimal> entry : scoreCountMap.entrySet()) {
            BigDecimal score = BigDecimal.valueOf(entry.getKey());
            BigDecimal count = entry.getValue();

            totalSum = totalSum.add(score.multiply(count));
            totalCount = totalCount.add(count);
        }
        // 计算平均值，避免除以0
        if (totalCount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return totalSum.divide(totalCount, BigDecimal.ROUND_HALF_UP);
    }
    /**
     * 计算标准差
     * @param scoreCountMap
     * @return
     */
    public static BigDecimal calculateStandardDeviation(Map<Integer, BigDecimal> scoreCountMap) {
        if (scoreCountMap == null || scoreCountMap.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal totalCount = BigDecimal.ZERO;
        // 计算总和和总数
        for (Map.Entry<Integer, BigDecimal> entry : scoreCountMap.entrySet()) {
            BigDecimal frequency = entry.getValue();
            sum = sum.add(BigDecimal.valueOf(entry.getKey()).multiply(frequency));
            totalCount = totalCount.add(frequency);
        }
        // 计算平均值
        BigDecimal average = sum.divide(totalCount, MathContext.DECIMAL128);
        BigDecimal varianceSum = BigDecimal.ZERO;
        // 计算方差
        for (Map.Entry<Integer, BigDecimal> entry : scoreCountMap.entrySet()) {
            BigDecimal frequency = entry.getValue();
            BigDecimal value = BigDecimal.valueOf(entry.getKey());
            BigDecimal diff = value.subtract(average);
            varianceSum = varianceSum.add(diff.pow(2).multiply(frequency));
        }
        BigDecimal variance = varianceSum.divide(totalCount, MathContext.DECIMAL128);
        // 开平方根得到标准差
        return sqrt(variance, MathContext.DECIMAL128);
    }

    /**
     * 计算平方根
     * @param value
     * @param context
     * @return
     */
    public static BigDecimal sqrt(BigDecimal value, MathContext context) {
        BigDecimal x = new BigDecimal(Math.sqrt(value.doubleValue()), context);
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        final BigDecimal two = BigDecimal.valueOf(2);
        for (int i = 0; i < context.getPrecision(); i++) {
            x = x.add(value.divide(x, context)).divide(two, context);
        }
        return x;
    }

    /**
     * AI统计接口
     *
     * @param modelAssessId 评估任务ID
     * @return [
     *   {
     *     "modelId": "模型ID",
     *     "modelName": "模型名称",
     *     "average": "指标的平均值",
     *     "dimensionCount": "指标-出现的次数",
     *     "scoresCount": "分数-出现的次数",
     *     "commentStatistics": "指标中的分数-出现的次数"
     *   }
     * ]
     */
    @Override
    public List<Map<String, Object>> aiStatistics(Long modelAssessId) {
        List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList = appAssessModelModelRelationshipMapper.selectList(
                new LambdaQueryWrapper<AppAssessModelModelRelationship>().eq(AppAssessModelModelRelationship::getAssessId, modelAssessId));
        if (appAssessModelModelRelationshipList.isEmpty()) {
            return new ArrayList<>();
        }
        List<AppAiAssessInfo> appAiAssessInfoList = appAiAssessInfoMapper.selectList(new LambdaQueryWrapper<AppAiAssessInfo>().eq(AppAiAssessInfo::getModelAssessId, modelAssessId));
        if (appAiAssessInfoList.isEmpty()) {
            return new ArrayList<>();
        }
        AppModelAssess appModelAssess = appModelAssessMapper.selectById(modelAssessId);
        String scoringPrompt = appModelAssess.getScoringPrompt();
        if (!JSONUtil.isTypeJSON(scoringPrompt)) {
            throw new MyRuntimeException("打分Prompt解析错误！" + scoringPrompt);
        }
        // 获取指标名称
        String metricName = JSONUtil.parse(scoringPrompt).getByPath("metric").toString();
        // 设置模型ID和模型名称
        Long modelId = null;
        String modelName = null;
        for (AppAssessModelModelRelationship appAssessModelModelRelationship : appAssessModelModelRelationshipList) {
            modelId = appAssessModelModelRelationship.getModelId();
            modelName = appAssessModelModelRelationship.getModelName();
        }
        // 设置average（指标的平均值）、dimensionCount（指标-出现的次数）、scoresCount（分数-出现的次数）、commentStatistics（指标中的分数-出现的次数）
        Map<String, BigDecimal> averageMap = new HashMap<>();
        Map<String, BigDecimal> dimensionCountMap = new HashMap<>();
        Map<String, BigDecimal> scoresCountMap = new HashMap<>();
        Map<String, Map<String, BigDecimal>> commentStatisticsMap = new HashMap<>();
        // 获取打分分数集合
        List<BigDecimal> refereeModelScoreList = new ArrayList<>();
        // 打分分数总和
        BigDecimal refereeModelScore = BigDecimal.ZERO;
        // 打分分数总数
        BigDecimal refereeModelScoreSize = BigDecimal.ZERO;
        for (AppAiAssessInfo appAiAssessInfo : appAiAssessInfoList) {
            // 指标分数
            Integer refereeModelScoreInt = appAiAssessInfo.getRefereeModelScore();
            if (refereeModelScoreInt != null) {
                refereeModelScoreList.add(BigDecimal.valueOf(refereeModelScoreInt));
                refereeModelScore = refereeModelScore.add(BigDecimal.valueOf(refereeModelScoreInt));
                refereeModelScoreSize = refereeModelScoreSize.add(BigDecimal.ONE);
                if (scoresCountMap.containsKey(refereeModelScoreInt.toString())) {
                    scoresCountMap.put(refereeModelScoreInt.toString(), scoresCountMap.get(refereeModelScoreInt.toString()).add(BigDecimal.ONE));
                } else {
                    scoresCountMap.put(refereeModelScoreInt.toString(), BigDecimal.ONE);
                }
            } else {
                // 无效
                if (scoresCountMap.containsKey("无效")) {
                    scoresCountMap.put("无效", scoresCountMap.get("无效").add(BigDecimal.ONE));
                } else {
                    scoresCountMap.put("无效", BigDecimal.ONE);
                }
            }
        }
        // 设置平均值
        BigDecimal average = refereeModelScoreSize.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : refereeModelScore.divide(refereeModelScoreSize, 2, RoundingMode.HALF_UP);
        averageMap.put("平均值", average);
        averageMap.put("平方根", sqrt(refereeModelScore, MathContext.DECIMAL128).setScale(2, RoundingMode.HALF_UP));
        averageMap.put("标准差", standardDeviation(refereeModelScoreList).setScale(2, RoundingMode.HALF_UP));
        averageMap.put("中位数", median(refereeModelScoreList).setScale(2, RoundingMode.HALF_UP));

        Map<String, Object> resultDataMap = new HashMap<>();
        resultDataMap.put("modelId", modelId);
        resultDataMap.put("modelName", modelName);
        // 指标的平均值
        resultDataMap.put("average", averageMap);
        // 指标-出现的次数
        dimensionCountMap.put(metricName, BigDecimal.ONE);
        resultDataMap.put("dimensionCount", dimensionCountMap);
        // 分数-出现的次数
        resultDataMap.put("scoresCount", scoresCountMap);
        // 指标中的分数-出现的次数
        commentStatisticsMap.put(metricName, scoresCountMap);
        resultDataMap.put("commentStatistics", commentStatisticsMap);
        List<Map<String, Object>> resultDataList = new ArrayList<>();
        resultDataList.add(resultDataMap);
        return resultDataList;
    }

    /**
     * 标准差
     * @param numbers
     * @return
     */
    public static BigDecimal standardDeviation(List<BigDecimal> numbers) {
        if (numbers.size() == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal mean = numbers.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(numbers.size()), MathContext.DECIMAL128);

        BigDecimal variance = numbers.stream()
                .map(n -> n.subtract(mean).pow(2))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(numbers.size()), MathContext.DECIMAL128);

        return sqrt(variance, new MathContext(10));
    }

    /**
     * 计算中位数
     * @param numbers
     * @return
     */
    public static BigDecimal median(List<BigDecimal> numbers) {
        if (numbers.size() == 0) {
            return BigDecimal.ZERO;
        }
        Collections.sort(numbers);
        int middle = numbers.size() / 2;
        if (numbers.size() % 2 == 0) {
            BigDecimal sumOfMiddleValues = numbers.get(middle - 1).add(numbers.get(middle));
            return sumOfMiddleValues.divide(BigDecimal.valueOf(2), MathContext.DECIMAL128);
        } else {
            return numbers.get(middle);
        }
    }

}
