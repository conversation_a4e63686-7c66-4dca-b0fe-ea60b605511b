package com.supie.webadmin.app.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.model.MsServiceApi;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 开发平台模型服务分享表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@EnableDataPerm
public interface MsServiceApiMapper extends BaseDaoMapper<MsServiceApi> {

    /**
     * 批量插入对象列表。
     *
     * @param msServiceApiList 新增对象列表。
     */
    void insertList(List<MsServiceApi> msServiceApiList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param msServiceApiFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<MsServiceApi> getMsServiceApiList(
            @Param("msServiceApiFilter") MsServiceApi msServiceApiFilter, @Param("orderBy") String orderBy);
}
