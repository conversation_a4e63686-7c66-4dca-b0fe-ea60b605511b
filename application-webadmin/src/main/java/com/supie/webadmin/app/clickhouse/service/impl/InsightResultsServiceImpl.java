package com.supie.webadmin.app.clickhouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.Page;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.clickhouse.dao.DataInsightMapper;
import com.supie.webadmin.app.clickhouse.dao.DataRecordsMapper;
import com.supie.webadmin.app.clickhouse.dao.InsightResultsMapper;
import com.supie.webadmin.app.clickhouse.model.DataInsight;
import com.supie.webadmin.app.clickhouse.model.DataRecords;
import com.supie.webadmin.app.clickhouse.model.InsightResults;
import com.supie.webadmin.app.clickhouse.service.InsightResultsService;
import com.supie.webadmin.app.data.dao.DataDataSetMapper;
import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.data.model.DataInsightImportTask;
import com.supie.webadmin.app.data.model.DataSetInsights;
import com.supie.webadmin.app.data.service.impl.*;
import com.supie.webadmin.app.llmService.service.RemoteHostService;
import com.supie.webadmin.app.llmTrainImpl.NfsPathMgmt;
import com.supie.webadmin.app.util.RemoteSshUtils;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 数据管理-数据表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("insightResultsService")
@MyDataSource(DataSourceType.CLICKHOME)
public class InsightResultsServiceImpl extends BaseService<InsightResults, Long> implements InsightResultsService {

    @Resource
    private DataRecordsMapper dataRecordsMapper;
    @Resource
    private NfsPathMgmt nfsPathMgmt;
    @Resource
    private RemoteSshUtils remoteSshUtils;
    @Resource
    private RemoteHostService remoteHostService;
    @Autowired
    private InsightResultsMapper insightResultsMapper;
    @Resource
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private DataDataSetMapper dataDataSetMapper;
    @Autowired
    private DataInsightServiceImpl dataInsightService;
    @Autowired
    private DataInsightMapper dataInsightMapper;
    @Autowired
    private DataSetInsightsServiceImpl dataSetInsightsService;
    @Resource
    private ShareGptDatasetInsightProcessor shareGptDatasetInsightProcessor;
    @Resource
    private ApacheDatasetInsightProcessor apacheDatasetInsightProcessor;
    @Lazy
    @Autowired
    private DataDataSetServiceImpl dataDataSetService;
    @Autowired
    private DataInsightImportTaskServiceImpl dataInsightImportTaskService;


    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<InsightResults> mapper() {
        return insightResultsMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param insightResults 新增对象。
     * @return 返回新增对象。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public InsightResults saveNew(InsightResults insightResults) {
        processingData(insightResults);
        return insightResults;
    }
    /**
     * 新增对象洞察。
     *
     * @param insightResults 新增对象。
     * @return 返回新增对象。
     */
    private void processingData(InsightResults insightResults){
        //获取数据
        DataSetInsights dataSetInsights=dataSetInsightsService.getDataSetInsightsById(insightResults.getDataSetInsightsId());
        //数据条数加一
        dataSetInsights.setDataNumber(dataSetInsights.getDataNumber()+1);
        List<InsightResults> insightResultsList=new ArrayList<>();
        insightResultsList.add(insightResults);
        List<DataRecords> dataRecordsList = copyData(insightResultsList,dataSetInsights.getId());
        //存入数据洞察主表
        Integer dataFormat =dataSetInsights.getDataFormat();
        //分类型进行处理
        //Alpaca
        if(dataFormat==1){
            apacheDatasetInsightProcessor.processData(dataRecordsList,dataSetInsights);
        }else if(dataFormat==2){
            //Sharegpt
            shareGptDatasetInsightProcessor.processData(dataRecordsList,dataSetInsights);
        }
    }
    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param insightResultsList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<InsightResults> insightResultsList) {
        if (CollUtil.isNotEmpty(insightResultsList)) {
//            insightResultsList.forEach(this::buildDefaultValue);
            insightResultsMapper.insertList(insightResultsList);
        }
    }
    /**
     * 选中数据集另存为。
     *
     * @param dataDataSet          数据集
     * @param insightResultsIdList 新增对象列表。
     * @param newInsightID
     */
    // 用于导入 新id
    private Long insertID;
    @Override
    public Long selectSaveAsBatch(DataDataSet dataDataSet, List<Long> insightResultsIdList, Long newInsightID) {
            insertID=newInsightID;
            List<InsightResults> insightResultsList =insightResultsMapper.getInsightResultsByIdList(insightResultsIdList);
            //生成新一条数据集DataDataSet
            if(dataDataSet.getId()!=null){
                List<DataRecords> dataRecordsList = copyData(insightResultsList,dataDataSet.getId());
                dataRecordsMapper.insertList(dataRecordsList);
                //TODO 同一个方法中使用多个数据源、需调用service层
                LambdaUpdateWrapper<DataInsightImportTask> lambda = new LambdaUpdateWrapper<>();
                lambda.eq(DataInsightImportTask::getDataSetId,dataDataSet.getId()).set(DataInsightImportTask::getImportNum,dataRecordsList.size()).set(DataInsightImportTask::getImportState,1);
                dataInsightImportTaskService.update(lambda);
            }else{
                DataSetInsights dataSetInsights=dataSetInsightsService.getDataSetInsightsById(insightResultsList.get(0).getDataSetInsightsId());
                dataDataSet.setId(idGenerator.nextLongId());
                dataDataSet.setCreateTime(new Date());
                dataDataSet.setCreateUserId(TokenData.takeFromRequest().getUserId());
                dataDataSet.setUpdateTime(new Date());
                dataDataSet.setUpdateUserId(TokenData.takeFromRequest().getUserId());
                dataDataSet.setDataUserId(TokenData.takeFromRequest().getUserId());
                dataDataSet.setDataDeptId(TokenData.takeFromRequest().getDeptId());
                dataDataSet.setStrId(dataSetInsights.getStrId());
                dataDataSet.setIndexId(dataSetInsights.getIndexId());
                dataDataSet.setDataResourcesId(dataSetInsights.getDataResourcesId());
                dataDataSet.setMarkConfig(dataSetInsights.getMarkConfig());
                dataDataSet.setDataNumber((long) insightResultsIdList.size());
                dataDataSet.setDataType(dataSetInsights.getDataType());
                dataDataSet.setDataFormat(dataSetInsights.getDataFormat());
                dataDataSet.setDatasetState(0);
                dataDataSet.setIsDelete(GlobalDeletedFlag.NORMAL);
                dataDataSet.setMultimodalType(dataSetInsights.getMultimodalType());
                    //生成DataRecords数据
                List<DataRecords> dataRecordsList = copyData(insightResultsList,dataDataSet.getId());
                dataDataSetService.insertData(dataDataSet);
                dataRecordsMapper.insertList(dataRecordsList);
                LambdaUpdateWrapper<DataInsightImportTask> lambda = new LambdaUpdateWrapper<>();
                lambda.eq(DataInsightImportTask::getDataSetId,dataDataSet.getId()).set(DataInsightImportTask::getImportNum,dataRecordsList.size()).set(DataInsightImportTask::getImportState,1);
                dataInsightImportTaskService.update(lambda);
            }
            return dataDataSet.getId();
    }

    /**
     * 所有数据集另存为。
     * @param dataDataSet 数据集
     * @param dataInsightFilter 筛选条件。
     */
    @Override
    public Long allSaveAsBatch(DataDataSet dataDataSet, DataInsight dataInsightFilter, InsightResults filter) {
        List<InsightResults> resultList;
        //判断是否对数据集的洞察结果做筛选
        if(dataInsightFilter!=null &&(dataInsightFilter.getDataKey()!=null ||dataInsightFilter.getCharacterNumberStart()!=null||dataInsightFilter.getCharacterNumberEnd()!=null
                ||dataInsightFilter.getSpecialCharacterNumberStart()!=null || dataInsightFilter.getSpecialCharacterNumberEnd()!=null
                ||dataInsightFilter.getPerplexityStart()!=null || dataInsightFilter.getPerplexityEnd() !=null || dataInsightFilter
                .getCharacterNumber()!=null ||dataInsightFilter.getSpecialCharacterNumber()!=null ||
                dataInsightFilter.getPerplexity()!=null || dataInsightFilter.getDataRound()!=null)){
//            dataInsightFilter.setDataSetInsightsId(filter.getDataSetInsightsId());
            //获取筛选后的DataInsight
            List<DataInsight> dataInsights = dataInsightService.getDataInsightListWithRelation(dataInsightFilter, null);
            //提取insightResultsId
            List<Long> idList=dataInsights.stream().map(DataInsight::getInsightResultsId).collect(Collectors.toList());
            if(idList.isEmpty()){
                throw new MyRuntimeException("数据集条数为0！");
            }
            resultList = insightResultsMapper.getInsightResultsListByIdList(filter,idList, null);
        }else {
            resultList = insightResultsMapper.getInsightResultsList(filter, null);
        }
        //判断是另存为新数据集还是已有数据集
        if(dataDataSet.getId()!=null){
            List<DataRecords> dataRecordsList = copyData(resultList,dataDataSet.getId());
            dataRecordsMapper.insertList(dataRecordsList);
        }else{
            DataSetInsights dataSetInsights=dataSetInsightsService.getDataSetInsightsById(filter.getDataSetInsightsId());
            dataDataSet.setId(idGenerator.nextLongId());
            dataDataSet.setCreateTime(new Date());
            dataDataSet.setCreateUserId(TokenData.takeFromRequest().getUserId());
            dataDataSet.setUpdateTime(new Date());
            dataDataSet.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            dataDataSet.setDataUserId(TokenData.takeFromRequest().getUserId());
            dataDataSet.setDataDeptId(TokenData.takeFromRequest().getDeptId());
            dataDataSet.setStrId(dataSetInsights.getStrId());
//            dataDataSet.setIndexId(dataSetInsights.getIndexId());
            dataDataSet.setDataResourcesId(dataSetInsights.getDataResourcesId());
            dataDataSet.setMarkConfig(dataSetInsights.getMarkConfig());
            dataDataSet.setDataNumber((long) resultList.size());
            dataDataSet.setDataType(dataSetInsights.getDataType());
            dataDataSet.setDataFormat(dataSetInsights.getDataFormat());
            dataDataSet.setIsDelete(GlobalDeletedFlag.NORMAL);
            dataDataSet.setDatasetState(0);
            dataDataSet.setMultimodalType(dataSetInsights.getMultimodalType());

            //生成DataRecords数据
            List<DataRecords> dataRecordsList = copyData(resultList,dataDataSet.getId());
            dataDataSetService.insertData(dataDataSet);
//            dataDataSetMapper.insert(dataDataSet);
            //插入数据
            dataRecordsMapper.insertList(dataRecordsList);
        }
        return dataDataSet.getId();
    }
    /**
     * 复制数据InsightResults->DataRecords。
     *
     * @param insightResultsList 新增对象列表。
     */
    private List<DataRecords> copyData(List<InsightResults> insightResultsList,Long dataSetId) {
        List<DataRecords> dataRecordsList = new ArrayList<>();
        if (CollUtil.isNotEmpty(insightResultsList)) {
            for (InsightResults insightResults : insightResultsList) {
                DataRecords dataRecords = new DataRecords();
                dataRecords.setId(idGenerator.nextLongId());
                dataRecords.setStrId(insightResults.getStrId());
                dataRecords.setIsDelete(GlobalDeletedFlag.NORMAL);
                dataRecords.setDataUserId(insightResults.getDataUserId());
                dataRecords.setDataDeptId(insightResults.getDataDeptId());
                dataRecords.setDataSetId(dataSetId);
                dataRecords.setDataContent(insightResults.getDataContent());
                dataRecords.setDataType(insightResults.getDataType());
                dataRecords.setDataFrom(insightResults.getDataFrom());
                dataRecords.setTextBegin(insightResults.getTextBegin());
                dataRecords.setTextEnd(insightResults.getTextEnd());
                dataRecords.setTextBlockLen(insightResults.getTextBlockLen());
                dataRecords.setTextSplitStrategy(insightResults.getTextSplitStrategy());
                dataRecords.setDataMarkTaskId(insertID);
                dataRecords.setCreateUserId(insightResults.getCreateUserId());
                dataRecords.setCreateTime(new Date());
                dataRecords.setUpdateUserId(insightResults.getUpdateUserId());
                dataRecords.setUpdateTime(new Date());
                dataRecords.setDataTags(insightResults.getDataTags());
                dataRecordsList.add(dataRecords);
            }
        }
        return dataRecordsList;
}
    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param insightResultsList 新增对象列表。
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatchByTaken(List<InsightResults> insightResultsList) {

        if (CollUtil.isNotEmpty(insightResultsList)) {
            insightResultsMapper.insertList(insightResultsList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param insightResults         更新的对象。
     * @param originalinsightResults 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(InsightResults insightResults, InsightResults originalinsightResults) {
        MyModelUtil.fillCommonsForUpdate(insightResults, originalinsightResults);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<InsightResults> uw = this.createUpdateQueryForNullValue(insightResults, insightResults.getId());
        return insightResultsMapper.update(insightResults, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Override
    public boolean remove(Long id) {
        InsightResults insightResults = new InsightResults();
        insightResults.setId(id);
        return insightResultsMapper.deleteByInsightResults(insightResults) == 1;

    }
    @Override
    public boolean deleteById(Long id) {
        InsightResults insightResults = insightResultsMapper.getInsightResultsById(id);
        if(insightResultsMapper.deleteByInsightResults(insightResults) != 1){
            return false;
        }

        //异步执行
        CompletableFuture.runAsync(() -> deleteDataInsight(insightResults));
        return true;
    }

    /**
     * 根据待删除的InsightResults对象，同步删除DataInsight及更新DataSetInsights中的统计情况。
     *
     * @param insightResults 需删除insightResults对象。
     */
    private void deleteDataInsight(InsightResults insightResults){
            //同步删除单条数据
            List<DataInsight> dataInsightList=dataInsightService.getDataInsightByInsightResultsId(insightResults.getId());
            //删除指定数据
            dataInsightService.deleteByInsightResultsId(insightResults.getId());
            //获取数据集所有单条数据
            DataInsight dataInsightFilter=new DataInsight();
            dataInsightFilter.setDataSetInsightsId(insightResults.getDataSetInsightsId());
            List<DataInsight> allDataInsightList=dataInsightService.getDataInsightListWithRelation(dataInsightFilter,null);
            //将allDataInsightList根据getDataKey进行分组
            Map<String, List<DataInsight>> dataInsightMap = allDataInsightList.stream().collect(Collectors.groupingBy(DataInsight::getDataKey));
            DataSetInsights dataSetInsights=dataSetInsightsService.getDataSetInsightsById(insightResults.getDataSetInsightsId());
            //数据条数减一
            dataSetInsights.setDataNumber(dataSetInsights.getDataNumber()-1);
            //原统计情况
            String dataStatistics= dataSetInsights.getDataStatistics();

            Map<String, Object> dataStatisticsMap = JSONUtil.toBean(JSONUtil.toJsonStr(dataStatistics), Map.class);
            //更新轮数统计
            Map<String, Object> dataRoundMap = (Map<String, Object>) dataStatisticsMap.getOrDefault("sessionRound",new HashMap<>());
            int count= (int) dataRoundMap.getOrDefault(String.valueOf(insightResults.getSessionRound()),0)-1;
            if(count>0){
                dataRoundMap.put(String.valueOf(insightResults.getSessionRound()),count);
            }else {
                dataRoundMap.remove(String.valueOf(insightResults.getSessionRound()));
            }
            if(dataRoundMap.isEmpty()){
                dataStatisticsMap.remove("sessionRound");
            }
            dataStatisticsMap.put("sessionRound",dataRoundMap);
            //遍历删除的单条数据，更新统计情况
            for (DataInsight originalDataInsight : dataInsightList){
                Map<String, Object> contentMap= (Map) dataStatisticsMap.get(originalDataInsight.getDataKey());
                //获取整体统计情况中的对应轮次的map
                Map<String, Object> statisticsMap = (Map<String, Object>) contentMap.getOrDefault(String.valueOf(originalDataInsight.getDataRound()), new HashMap<>());
                //进行空值判断
                if(statisticsMap.isEmpty()){
                    contentMap.remove(String.valueOf(originalDataInsight.getDataRound()));
                    dataStatisticsMap.put(originalDataInsight.getDataKey(), contentMap);
                    continue;
                }
                if (dataSetInsights.getDataFormat() == 2 || "history".equals(originalDataInsight.getDataKey())) {
                    Map<String, Object> conversationsMap = (Map<String, Object>) statisticsMap.getOrDefault(originalDataInsight.getConversationsKey(), new HashMap<>());
                    //更新统计情况
                    calculatingStatistics(conversationsMap,originalDataInsight);
                    //统计剩余单条数据极值
                    //根据轮次获取dataInsight
                    List<DataInsight> historyDataInsightList=dataInsightMap.get(originalDataInsight.getDataKey());
                        //根据getDataRound分组
                    Map<Integer, List<DataInsight>> historyKeyDataInsightMap = historyDataInsightList.stream().collect(Collectors.groupingBy(DataInsight::getDataRound));
                        //根据getDataRound获取dataInsightList
                    List<DataInsight> historyKeyDataInsightList=historyKeyDataInsightMap.get(originalDataInsight.getDataRound());
                    //剩余数据无该轮数情况
                    if(historyKeyDataInsightList==null||historyKeyDataInsightList.isEmpty()){
                        contentMap.remove(String.valueOf(originalDataInsight.getDataRound()));
                    }else{
                        //根据getConversationsKey分组
                        Map<String, List<DataInsight>> conversationsKeyDataInsightMap = historyKeyDataInsightList.stream().collect(Collectors.groupingBy(DataInsight::getConversationsKey));
                        //根据getConversationsKey获取dataInsightList
                        List<DataInsight> conversationsKeyDataInsightList=conversationsKeyDataInsightMap.get(originalDataInsight.getConversationsKey());
                        //计算极值
                        Map<String, Object>newConversationsMap=dataInsightService.calculatedExtremum(conversationsMap, conversationsKeyDataInsightList);
                        if(newConversationsMap.isEmpty()){
                            statisticsMap.remove(originalDataInsight.getConversationsKey());
                        }else {
                            statisticsMap.put(originalDataInsight.getConversationsKey(), newConversationsMap);
                        }
                        if(statisticsMap.isEmpty()){
                            contentMap.remove(String.valueOf(originalDataInsight.getDataRound()));
                        }else {
                            contentMap.put(String.valueOf(originalDataInsight.getDataRound()), statisticsMap);
                        }
                    }

                } else {
                    //更新统计情况
                    calculatingStatistics(statisticsMap,originalDataInsight);
                    //计算极值
                    //根据轮次获取dataInsight
                    List<DataInsight> historyDataInsightList=dataInsightMap.get(originalDataInsight.getDataKey());
                    //根据getDataKey分组
                    Map<Integer, List<DataInsight>> historyKeyDataInsightMap = historyDataInsightList.stream().collect(Collectors.groupingBy(DataInsight::getDataRound));
                    //根据getDataKey获取dataInsightList
                    List<DataInsight> dataKeyDataInsightList=historyKeyDataInsightMap.get(originalDataInsight.getDataRound());
                    //获取极值
                    Map<String, Object>newStatisticsMap=dataInsightService.calculatedExtremum(statisticsMap, dataKeyDataInsightList);
                    if(newStatisticsMap.isEmpty()){
                        contentMap.remove(String.valueOf(originalDataInsight.getDataRound()));
                    }else {
                        contentMap.put(String.valueOf(originalDataInsight.getDataRound()), newStatisticsMap);
                    }
                }
                //更新整体统计情况
                if(contentMap.isEmpty()){
                    dataStatisticsMap.remove(originalDataInsight.getDataKey());
                }else{
                    dataStatisticsMap.put(originalDataInsight.getDataKey(), contentMap);
                }
            }
            dataSetInsights.setDataStatistics(JSONUtil.toJsonStr(dataStatisticsMap));
            dataSetInsightsService.updateDataSetInsights(dataSetInsights);
    }
    /**
     * 计算并更新统计信息
     * 该方法根据提供的数据洞察对象更新统计信息，减少指定统计指标的计数
     * 如果某个统计指标的计数减少到零，则从统计信息中移除该指标
     *
     * @param map 包含各种统计信息的映射，包括字符数量、特殊字符数量和困惑度分布
     * @param dataInsight 包含新计算的数据洞察的对象，用于更新统计信息
     * @return 更新后的统计信息映射
     */
    public Map<String, Object> calculatingStatistics(Map<String, Object>map,DataInsight dataInsight){
        Map<String, Integer>characterNumberMap = (Map) map.get("characterNumber");
        Map<String, Integer>specialCharacterNumberMap = (Map) map.get("specialCharacterNumber");
        Map<String, Integer>perplexityMap = (Map<String, Integer>) map.get("perplexity");
        //更新统计情况
        //将原来的统计个数减一
        String characterNumberString = String.valueOf(dataInsight.getCharacterNumber());
        int originalCharacterNumber= (int) characterNumberMap.get(characterNumberString);
        int originalSpecialCharacterNumber= (int) specialCharacterNumberMap.get(String.valueOf(dataInsight.getSpecialCharacterNumber()));
        // 计算区间
        int interval = 100;
        int lowerBound = (int) (dataInsight.getPerplexity() / interval) * interval;
        int upperBound = lowerBound + interval;
        // 创建区间字符串
        String rangeKey = lowerBound + "~" + upperBound;

        int originalPerplexity= perplexityMap.get(rangeKey);
        //原统计情况减一
        // 更新 characterNumberMap
        int updatedCharacterNumber = originalCharacterNumber - 1;
        characterNumberMap.put(String.valueOf(dataInsight.getCharacterNumber()), updatedCharacterNumber);
        if (updatedCharacterNumber == 0) {
            characterNumberMap.remove(String.valueOf(dataInsight.getCharacterNumber()));
        }

        // 更新 specialCharacterNumberMap
        int updatedSpecialCharacterNumber = originalSpecialCharacterNumber - 1;
        specialCharacterNumberMap.put(String.valueOf(dataInsight.getSpecialCharacterNumber()), updatedSpecialCharacterNumber);
        if (updatedSpecialCharacterNumber == 0) {
            specialCharacterNumberMap.remove(String.valueOf(dataInsight.getSpecialCharacterNumber()));
        }

        // 更新 perplexityMap
        int updatedPerplexity = originalPerplexity - 1;
        perplexityMap.put(rangeKey, updatedPerplexity);
        if (updatedPerplexity == 0) {
            perplexityMap.remove(rangeKey);
        }
        if(characterNumberMap.isEmpty()){
            map.remove("characterNumber");
        }else {
            map.put("characterNumber", characterNumberMap);
        }
        if(specialCharacterNumberMap.isEmpty()){
            map.remove("specialCharacterNumber");
        }else{
            map.put("specialCharacterNumber", specialCharacterNumberMap);
        }
        if(perplexityMap.isEmpty()){
            map.remove("perplexity");
        }else{
            map.put("perplexity", perplexityMap);
        }
        return map;
    }

    @Override
    public InsightResults getInsightResultsById(Long id) {
        return insightResultsMapper.getInsightResultsById(id);
    }


    /**
     * 根据dataSetId删除指定数据。
     *
     * @param dataSetId 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long removeByDataSetId(Long dataSetId) {
        InsightResults insightResults = new InsightResults();
        insightResults.setDataSetInsightsId(dataSetId);
        return insightResultsMapper.deleteByInsightResults(insightResults);
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getDataRecordsListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<InsightResults> getDataRecordsList(InsightResults filter, String orderBy) {
        return insightResultsMapper.getInsightResultsList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getDataRecordsList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<InsightResults> getInsightResultsListWithRelation(InsightResults filter, DataInsight dataInsightFilter, String orderBy) {
//        int offset = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
        List<InsightResults> resultList = new ArrayList<>();
        //判断是否对数据集的洞察结果做筛选
        if (dataInsightFilter != null && (dataInsightFilter.getDataKey() != null || dataInsightFilter.getCharacterNumberStart() != null || dataInsightFilter.getCharacterNumberEnd() != null
                || dataInsightFilter.getSpecialCharacterNumberStart() != null || dataInsightFilter.getSpecialCharacterNumberEnd() != null
                || dataInsightFilter.getPerplexityStart() != null || dataInsightFilter.getPerplexityEnd() != null || dataInsightFilter
                .getCharacterNumber() != null || dataInsightFilter.getSpecialCharacterNumber() != null ||
                dataInsightFilter.getPerplexity() != null || dataInsightFilter.getDataRound() != null || dataInsightFilter.getConversationsKey() != null)) {
            dataInsightFilter.setDataSetInsightsId(filter.getDataSetInsightsId());
            //获取筛选后的DataInsight
            resultList = dataInsightService.getDataInsightListWithInsightResults(dataInsightFilter, orderBy);
            //提取insightResultsId
//            List<Long> insightResultsIdList=dataInsights.stream().map(DataInsight::getInsightResultsId).collect(Collectors.toList());
//            if(insightResultsIdList.isEmpty()){
//              return  MyPageUtil.makeResponseData(resultList, 0L);
//            }
//            resultList = insightResultsMapper.getInsightResultsListByIdList(filter,insightResultsIdList, orderBy);
        } else {
            resultList=insightResultsMapper.getInsightResultsList(filter, orderBy );
//            List<Map> map = insightResultsMapper.getInsightResultsListAndDataInsight(filter, orderBy, pageParam.getPageSize(), offset);
//            log.info(map);
        }
        //提取insightResultsId 获取DataInsight
        List<Long> insightResultsIdSet = resultList.stream().map(InsightResults::getId).collect(Collectors.toList());
        if(insightResultsIdSet.isEmpty()){
            return new ArrayList<>();
        }
        //查询DataInsight
        List<DataInsight> dataInsightList=dataInsightService.getDataInsightByInsightResultsIdList(insightResultsIdSet,orderBy);
        //根据insightResultsId分组
        Map<Long,List<DataInsight>> dataInsightMap=dataInsightList.stream().collect(Collectors.groupingBy(DataInsight::getInsightResultsId));
        for(InsightResults insightResults:resultList){
            if(dataInsightMap.containsKey(insightResults.getId())){
                insightResults.setDataInsightList(dataInsightMap.get(insightResults.getId()));
            }
        }
//        Long totalCount = (long) resultList.size();
//        if(pageParam!=null){
//            // 分页处理
//            int pageSize = pageParam.getPageSize() != null ? pageParam.getPageSize() : 10; // 每页显示的条目数，按需设置
//            int pageNumber = pageParam.getPageNum() != null ? pageParam.getPageNum() : 1; // 当前页码，按需设置
//            int startIndex = (pageNumber - 1) * pageSize;
//            int endIndex = Math.min(startIndex + pageSize, resultList.size());
//
//            // 确保分页处理不会超出原始数据大小
//            if (startIndex < resultList.size()) {
//                resultList = resultList.subList(startIndex, endIndex);
//            } else {
//                resultList = new ArrayList<>(); // 如果页码超出范围，则返回空列表
//            }
//    }

        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }
    /**
     * 根据DataSetInsightsId删除数据。
     *
     * @param dataSetInsightsId 数据集json。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByDataSetInsightsId(Long dataSetInsightsId) {
        insightResultsMapper.deleteByDataSetInsightsId(dataSetInsightsId);

    }
    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<InsightResults> getGroupedDataRecordsListWithRelation(
            InsightResults filter, String groupSelect, String groupBy, String orderBy) {
        List<InsightResults> resultList =
                insightResultsMapper.getGroupedInsightResultsList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }


    @Override
    public Long deleteByDataRecords(InsightResults insightResultsFilter) {
        return insightResultsMapper.deleteByInsightResults(insightResultsFilter);
    }

    @Override
    public List<InsightResults> getInsightResultsListByDataSetInsightsId(List<Long> dataSetInsightsIdList) {
        return insightResultsMapper.getInsightResultsListByDataSetInsightsId(dataSetInsightsIdList);
    }


    private InsightResults buildDefaultValue(InsightResults insightResults) {
        MyModelUtil.fillCommonsForInsert(insightResults);
        insightResults.setIsDelete(GlobalDeletedFlag.NORMAL);
        return insightResults;
    }


    @Override
    public boolean updateInsightResults(InsightResults insightResults, InsightResults originalInsightResults) {
        MyModelUtil.fillCommonsForUpdate(insightResults, originalInsightResults);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        return insightResultsMapper.updateInsightResults(insightResults) == 1;
    }

    @Override
    public boolean updateInsightResultsById(InsightResults insightResults) {
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        return insightResultsMapper.updateInsightResultsById(insightResults) == 1;
    }




}
