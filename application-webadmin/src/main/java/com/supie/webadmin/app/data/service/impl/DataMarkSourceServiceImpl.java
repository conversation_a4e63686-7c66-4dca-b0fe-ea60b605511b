package com.supie.webadmin.app.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.webadmin.app.data.service.*;
import com.supie.webadmin.app.data.dao.*;
import com.supie.webadmin.app.data.model.*;
import com.supie.webadmin.config.DataSourceType;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.TokenData;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据标注任务表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Slf4j
@Service("dataMarkSourceService")
public class DataMarkSourceServiceImpl extends BaseService<DataMarkSource, Long> implements DataMarkSourceService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private DataMarkSourceMapper dataMarkSourceMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DataMarkSource> mapper() {
        return dataMarkSourceMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataMarkSource saveNew(DataMarkSource dataMarkSource) {
        dataMarkSourceMapper.insert(this.buildDefaultValue(dataMarkSource));
        return dataMarkSource;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DataMarkSource> dataMarkSourceList) {
        if (CollUtil.isNotEmpty(dataMarkSourceList)) {
            dataMarkSourceList.forEach(this::buildDefaultValue);
            dataMarkSourceMapper.insertList(dataMarkSourceList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(DataMarkSource dataMarkSource, DataMarkSource originalDataMarkSource) {
        dataMarkSource.setCreateUserId(originalDataMarkSource.getCreateUserId());
        dataMarkSource.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        dataMarkSource.setCreateTime(originalDataMarkSource.getCreateTime());
        dataMarkSource.setUpdateTime(new Date());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<DataMarkSource> uw = this.createUpdateQueryForNullValue(dataMarkSource, dataMarkSource.getId());
        return dataMarkSourceMapper.update(dataMarkSource, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return dataMarkSourceMapper.deleteById(id) == 1;
    }

    @Override
    public List<DataMarkSource> getDataMarkSourceList(DataMarkSource filter, String orderBy) {
        return dataMarkSourceMapper.getDataMarkSourceList(filter, orderBy);
    }

    @Override
    public List<DataMarkSource> getDataMarkSourceListWithRelation(DataMarkSource filter, String orderBy) {
        List<DataMarkSource> resultList = dataMarkSourceMapper.getDataMarkSourceList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<DataMarkSource> getGroupedDataMarkSourceListWithRelation(
            DataMarkSource filter, String groupSelect, String groupBy, String orderBy) {
        List<DataMarkSource> resultList =
                dataMarkSourceMapper.getGroupedDataMarkSourceList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private DataMarkSource buildDefaultValue(DataMarkSource dataMarkSource) {
        if (dataMarkSource.getId() == null) {
            dataMarkSource.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        dataMarkSource.setCreateUserId(tokenData.getUserId());
        dataMarkSource.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        dataMarkSource.setCreateTime(now);
        dataMarkSource.setUpdateTime(now);
        dataMarkSource.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataMarkSource;
    }
}
