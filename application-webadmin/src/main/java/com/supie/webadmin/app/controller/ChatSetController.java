package com.supie.webadmin.app.controller;

import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.DisableDataFilter;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.dto.ChatSetDto;
import com.supie.webadmin.app.model.ChatSet;
import com.supie.webadmin.app.service.ChatSetService;
import com.supie.webadmin.app.vo.ChatSetVo;
import com.supie.webadmin.interceptor.OpenApi;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对话分组表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "对话分组表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/chatSet")
public class ChatSetController {

    @Autowired
    private ChatSetService chatSetService;

    /**
     * 新增对话分组表数据。
     *
     * @param chatSetDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "chatSetDto.id",
            "chatSetDto.searchString",
            "chatSetDto.createTimeStart",
            "chatSetDto.createTimeEnd",
            "chatSetDto.updateTimeStart",
            "chatSetDto.updateTimeEnd"})
    ////@SaCheckPermission("chatSet.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody ChatSetDto chatSetDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(chatSetDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ChatSet chatSet = MyModelUtil.copyTo(chatSetDto, ChatSet.class);
        chatSet = chatSetService.saveNew(chatSet);
        return ResponseResult.success(chatSet.getId());
    }

    /**
     * 更新对话分组表数据。
     *
     * @param chatSetDto 更新对象。
     * @return 应答结果对象。
     */
    @DisableDataFilter
    @OpenApi
    @ApiOperationSupport(ignoreParameters = {
            "chatSetDto.searchString",
            "chatSetDto.createTimeStart",
            "chatSetDto.createTimeEnd",
            "chatSetDto.updateTimeStart",
            "chatSetDto.updateTimeEnd"})
    ////@SaCheckPermission("chatSet.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody ChatSetDto chatSetDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(chatSetDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ChatSet chatSet = MyModelUtil.copyTo(chatSetDto, ChatSet.class);
        ChatSet originalChatSet = chatSetService.getById(chatSet.getId());
        if (originalChatSet == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!chatSetService.update(chatSet, originalChatSet)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除对话分组表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @DisableDataFilter
    @OpenApi
    ////@SaCheckPermission("chatSet.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除对话分组表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @OpenApi
    ////@SaCheckPermission("chatSet.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的对话分组表列表。
     *
     * @param chatSetDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @OpenApi
    ////@SaCheckPermission("chatSet.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ChatSetVo>> list(
            @MyRequestBody ChatSetDto chatSetDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ChatSet chatSetFilter = MyModelUtil.copyTo(chatSetDtoFilter, ChatSet.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ChatSet.class);
        List<ChatSet> chatSetList = null;
        try {
            chatSetList = chatSetService.getChatSetListWithRelation(chatSetFilter, orderBy);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseResult.success(null);
        }
        return ResponseResult.success(MyPageUtil.makeResponseData(chatSetList, ChatSetVo.class));
    }

    /**
     * 分组列出符合过滤条件的对话分组表列表。
     *
     * @param chatSetDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @OpenApi
    ////@SaCheckPermission("chatSet.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<ChatSetVo>> listWithGroup(
            @MyRequestBody ChatSetDto chatSetDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ChatSet.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, ChatSet.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ChatSet filter = MyModelUtil.copyTo(chatSetDtoFilter, ChatSet.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<ChatSet> resultList = chatSetService.getGroupedChatSetListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, ChatSetVo.class));
    }

    /**
     * 查看指定对话分组表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @OpenApi
    ////@SaCheckPermission("chatSet.view")
    @GetMapping("/view")
    public ResponseResult<ChatSetVo> view(@RequestParam Long id) {
        ChatSet chatSet = chatSetService.getByIdWithRelation(id, MyRelationParam.full());
        if (chatSet == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ChatSetVo chatSetVo = MyModelUtil.copyTo(chatSet, ChatSetVo.class);
        return ResponseResult.success(chatSetVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        ChatSet originalChatSet = chatSetService.getById(id);
        if (originalChatSet == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!chatSetService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
