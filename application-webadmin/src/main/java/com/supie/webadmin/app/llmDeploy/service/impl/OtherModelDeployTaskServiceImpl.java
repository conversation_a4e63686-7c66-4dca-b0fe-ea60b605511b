package com.supie.webadmin.app.llmDeploy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.TokenData;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.llmDeploy.dao.OtherModelDeployTaskMapper;
import com.supie.webadmin.app.llmDeploy.model.OtherModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.OtherModelDeployTaskService;
import com.supie.webadmin.app.llmService.model.RemoteHost;
import com.supie.webadmin.app.llmService.service.impl.RemoteHostServiceImpl;
import com.supie.webadmin.app.llmTrainImpl.NfsPathMgmt;
import com.supie.webadmin.app.other.dao.BusinessFileMapper;
import com.supie.webadmin.app.other.model.BusinessFile;
import com.supie.webadmin.app.other.service.impl.ModelTokensCountServiceImpl;
import com.supie.webadmin.app.util.DockerJavaUtil;
import com.supie.webadmin.app.util.RemoteSshUtils;
import com.supie.webadmin.app.util.SysUtil;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.supie.webadmin.app.util.TaskConstant.*;

/**
 * 部署管理-其他部署表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Slf4j
@MyDataSource(DataSourceType.MAIN)
@Service("otherModelDeployTaskService")
public class OtherModelDeployTaskServiceImpl extends BaseService<OtherModelDeployTask, Long> implements OtherModelDeployTaskService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private OtherModelDeployTaskMapper otherModelDeployTaskMapper;
    @Autowired
    private RemoteHostServiceImpl remoteHostService;
    @Autowired
    private SysUtil sysUtil;
    @Autowired
    private NfsPathMgmt nfsPathMgmt;
    @Autowired
    private RemoteSshUtils remoteSshUtils;
    @Autowired
    private DockerJavaUtil dockerJavaUtil;
    @Value("${imageConfig.whisperStt910B}")
    private String whisperStt910B;
    @Value("${imageConfig.paddle910B}")
    private String paddle910B;
    @Autowired
    private ModelTokensCountServiceImpl modelTokensCountService;
    @Autowired
    private BusinessFileMapper businessFileMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<OtherModelDeployTask> mapper() {
        return otherModelDeployTaskMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OtherModelDeployTask saveNew(OtherModelDeployTask otherModelDeployTask) {
        otherModelDeployTaskMapper.insert(this.buildDefaultValue(otherModelDeployTask));
        return otherModelDeployTask;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<OtherModelDeployTask> otherModelDeployTaskList) {
        if (CollUtil.isNotEmpty(otherModelDeployTaskList)) {
            otherModelDeployTaskList.forEach(this::buildDefaultValue);
            otherModelDeployTaskMapper.insertList(otherModelDeployTaskList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(OtherModelDeployTask otherModelDeployTask, OtherModelDeployTask originalOtherModelDeployTask) {
        otherModelDeployTask.setCreateUserId(originalOtherModelDeployTask.getCreateUserId());
        otherModelDeployTask.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        otherModelDeployTask.setCreateTime(originalOtherModelDeployTask.getCreateTime());
        otherModelDeployTask.setUpdateTime(new Date());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<OtherModelDeployTask> uw = this.createUpdateQueryForNullValue(otherModelDeployTask, otherModelDeployTask.getId());
        return otherModelDeployTaskMapper.update(otherModelDeployTask, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return otherModelDeployTaskMapper.deleteById(id) == 1;
    }

    @Override
    public List<OtherModelDeployTask> getOtherModelDeployTaskList(OtherModelDeployTask filter, String orderBy) {
        return otherModelDeployTaskMapper.getOtherModelDeployTaskList(filter, orderBy);
    }

    @Override
    public List<OtherModelDeployTask> getOtherModelDeployTaskListWithRelation(OtherModelDeployTask filter, String orderBy) {
        List<OtherModelDeployTask> resultList = otherModelDeployTaskMapper.getOtherModelDeployTaskList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<OtherModelDeployTask> getGroupedOtherModelDeployTaskListWithRelation(
            OtherModelDeployTask filter, String groupSelect, String groupBy, String orderBy) {
        List<OtherModelDeployTask> resultList =
                otherModelDeployTaskMapper.getGroupedOtherModelDeployTaskList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private OtherModelDeployTask buildDefaultValue(OtherModelDeployTask otherModelDeployTask) {
        if (otherModelDeployTask.getId() == null) {
            otherModelDeployTask.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        otherModelDeployTask.setCreateUserId(tokenData.getUserId());
        otherModelDeployTask.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        otherModelDeployTask.setCreateTime(now);
        otherModelDeployTask.setUpdateTime(now);
        otherModelDeployTask.setIsDelete(GlobalDeletedFlag.NORMAL);
        return otherModelDeployTask;
    }


    /**
     * 其他模型部署
     */
    @Override
    public void otherDeploy(OtherModelDeployTask otherModelDeployTask) {
        //初始化使用时间断
        initTimePeriods(otherModelDeployTask.getId());
        JSONObject jsonObject = JSONUtil.parseObj(otherModelDeployTask.getCommonParameterJson());
        // 获取devices数组
        JSONArray devices = jsonObject.getJSONArray(DEVICES);
        JSONArray deviceIds = devices.getJSONObject(0).getJSONArray(DEVICE_IDS);
        List<String> ids = new ArrayList<>();
        for (int j = 0; j < deviceIds.size(); j++) {
            ids.add(deviceIds.getStr(j));
        }
        Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
        RemoteHost remoteHost = remoteHostService.getById(remoteHostId);
        HashMap<String, Object> deployTaskMap = new HashMap<>();
        //获取随机端口
        int port = sysUtil.getPort(remoteHost.getHostIp());
        //生成密钥
        String rsa = generateRsa();
        deployTaskMap.put(API_SERVER_PORT, port);
        deployTaskMap.put(ENCRYPTION_KEY, rsa);
        deployTaskMap.put(TASK_NAME, otherModelDeployTask.getModelName());
        deployTaskMap.put(REMOTE_HOST_ID, remoteHostId);
        String taskName = otherModelDeployTask.getModelName();
        String deployTaskPath = nfsPathMgmt.getOtherDeployTaskPath(taskName);
        //部署的模型（ocr,wishper）
        Integer modelType = otherModelDeployTask.getModelType();
        try{
            // 创建部署任务
            if (nfsPathMgmt.createDir(deployTaskPath).equals(false)) {
                throw new MyRuntimeException("其他部署目录创建失败!");
            }
            String composeType = null;
            deployTaskMap.put(TASK_NAME, taskName);
            deployTaskMap.put(TASK_PATH, deployTaskPath);
            //根据部署的模型类型，获取对应的docker-compose.yml文件
            if (modelType.equals(1)) {
                creatOcrEnv(deployTaskMap, ids);
                composeType = nfsPathMgmt.getOcrPath();

            }else if (modelType.equals(2)) {
                creatWhisperSttEnv(deployTaskMap, ids);
                composeType = nfsPathMgmt.getWishperPath();
            }
            //上传compose.yaml文件
            nfsPathMgmt.uploadComposeYaml(composeType, deployTaskPath);
            //执行docker compose
            remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, nfsPathMgmt.getOtherModelDeployComposeYamlPath(taskName), nfsPathMgmt.getEndWithUp());
            String containerId = dockerJavaUtil.getContainerId(taskName, remoteHostId);
            if (containerId == null) {
                throw new MyRuntimeException();
            }
            // 更新模型部署表数据
            otherModelDeployTaskMapper.update(new LambdaUpdateWrapper<OtherModelDeployTask>()
                    .set(OtherModelDeployTask::getDockerId, containerId)
                    .set(OtherModelDeployTask::getServerHost, remoteHost.getHostIp())
                    .set(OtherModelDeployTask::getApiServerPort, port)
                    .set(OtherModelDeployTask::getEncryptionKey, rsa)
                    .eq(OtherModelDeployTask::getId, otherModelDeployTask.getId()));
            //检测是否部署成功
            checkDeployState(containerId, remoteHostId, otherModelDeployTask.getId(), 0);
        }catch (Exception e){
            otherModelDeployTaskMapper.update(new LambdaUpdateWrapper<OtherModelDeployTask>()
                    .eq(OtherModelDeployTask::getId, otherModelDeployTask.getId())
                    .set(OtherModelDeployTask::getDeployStatus, 4)
                    .set(OtherModelDeployTask::getDeployLogs, e.getMessage()));
            e.printStackTrace();
        }
    }

    /**
     * 停止部署
     */
    @Override
    public void stopDeploy(OtherModelDeployTask otherModelDeployTask) {
        //结算时间
        updateTime(otherModelDeployTask.getId());
        //部署服务的路径
        String taskName = otherModelDeployTask.getModelName();
        String deployTaskPath = nfsPathMgmt.getOtherDeployTaskPath(taskName);
        //暂停模型
        String commonParameterJson = otherModelDeployTask.getCommonParameterJson();
        JSONObject json = JSONUtil.parseObj(commonParameterJson);
        JSONArray devices = json.getJSONArray(DEVICES);
        List<Long> remoteHostIds = new ArrayList<>();
        for (int i = 0; i < devices.size(); i++) {
            JSONObject device = devices.getJSONObject(i);
            String id = device.getStr("id");
            remoteHostIds.add(Long.valueOf(id));
        }
        String masterId = json.getStr("mainDeviceId");
        if (!masterId.isEmpty()) {
            remoteHostIds.forEach(id -> remoteSshUtils.checkRemoteHostAndExecution(id, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown()));
        } else {
            Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
            remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
        }
        //更新状态
        this.update(new LambdaUpdateWrapper<OtherModelDeployTask>().eq(OtherModelDeployTask::getId, otherModelDeployTask.getId()).set(OtherModelDeployTask::getDeployStatus, 7));
    }

    /**
     * 删除模型
     */
    @Override
    public void removeModel(OtherModelDeployTask otherModelDeployTask) {
        try {
            //清理模型
            Integer status = otherModelDeployTask.getDeployStatus();
            if (status.equals(6)) {
                // 结算时间
                updateTime(otherModelDeployTask.getId());
            }
            String taskName = otherModelDeployTask.getModelName();
            String deployTaskPath = nfsPathMgmt.getOtherDeployTaskPath(taskName);
            String commonParameterJson = otherModelDeployTask.getCommonParameterJson();
            JSONObject json = JSONUtil.parseObj(commonParameterJson);
            JSONArray devices = json.getJSONArray(DEVICES);
            List<Long> remoteHostIds = new ArrayList<>();
            for (int i = 0; i < devices.size(); i++) {
                JSONObject device = devices.getJSONObject(i);
                String id = device.getStr("id");
                remoteHostIds.add(Long.valueOf(id));
            }
            String masterId = json.getStr("mainDeviceId");
            if (masterId != null && !masterId.isEmpty()) {
                remoteHostIds.forEach(id -> {
                    String containerId = dockerJavaUtil.getContainerId(taskName, id);
                    if (containerId != null) {
                        remoteSshUtils.checkRemoteHostAndExecution(id, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
                    }
                });
            } else {
                Long remoteHostId = Long.valueOf(devices.getJSONObject(0).getStr("id"));
                String containerId = dockerJavaUtil.getContainerId(taskName, remoteHostId);
                if (containerId != null) {
                    remoteSshUtils.checkRemoteHostAndExecution(remoteHostId, deployTaskPath + "compose.yaml", nfsPathMgmt.getEndWithDown());
                }
            }
            String command = "rm -rf " + deployTaskPath;
            remoteSshUtils.localExecution(command);
            otherModelDeployTaskMapper.deleteById(otherModelDeployTask.getId());
            modelTokensCountService.removeByModelName(taskName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 初始化时间段
     */
    void initTimePeriods(Long otherModelDeployTaskId) {
        JSONObject timePeriods = new JSONObject();
        timePeriods.putOpt("time_periods", new JSONArray());
        otherModelDeployTaskMapper.update(new LambdaUpdateWrapper<OtherModelDeployTask>()
                .set(OtherModelDeployTask::getTimePeriods, timePeriods.toString())
                .eq(OtherModelDeployTask::getId, otherModelDeployTaskId));
    }

    /**
     * 生成密钥
     */
    private String generateRsa() {
        //生成密钥
        SecureRandom salt = new SecureRandom();
        StringBuilder sb = new StringBuilder(48);
        for (int i = 0; i < 48; i++) {
            int randomIndex = salt.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        return sb.toString();
    }

    private void creatWhisperSttEnv(Map<String, Object> hashMap, List<String> deviceIds) {
        StringBuilder envScriptBuilder = new StringBuilder();
//        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_PATH=").append(hashMap.get(BASE_MODEL_PATH)).append(System.lineSeparator());
//        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_NAMES=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        envScriptBuilder.append(IMAGE_NAME).append(whisperStt910B).append(System.lineSeparator());
        int port = (int) hashMap.get(API_SERVER_PORT);
        envScriptBuilder.append("API_SERVER_PORT=").append(port).append(System.lineSeparator());
        envScriptBuilder.append("CONTAINER_NAME=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
//        envScriptBuilder.append("FASTCHAT_CONTROLLER_PORT=").append(port + 1).append(System.lineSeparator());
        if (deviceIds != null && !deviceIds.isEmpty()) {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("\"").append(String.join(",", deviceIds)).append("\"").append(System.lineSeparator());
//            envScriptBuilder.append("NUM_GPUS=").append(deviceIds.size()).append(System.lineSeparator());
        } else {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("0").append(System.lineSeparator());
//            envScriptBuilder.append("NUM_GPUS=").append("1").append(System.lineSeparator());
        }
        envScriptBuilder.append("OPENAI_API_KEY=").append("\"").append("sk-").append(hashMap.get(ENCRYPTION_KEY)).append("\"").append(System.lineSeparator());
        assert deviceIds != null;
        int memory = deviceIds.size() * 64;
        envScriptBuilder.append("MAX_GPU_MEMORY=").append(memory).append("GiB");
        String path = hashMap.get(TASK_PATH) + ENV_PATH;
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder.toString());
    }

    private void creatOcrEnv(Map<String, Object> hashMap, List<String> deviceIds) {
        StringBuilder envScriptBuilder = new StringBuilder();
        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_PATH=").append(hashMap.get(BASE_MODEL_PATH)).append(System.lineSeparator());
        envScriptBuilder.append("FASTCHAT_WORKER_MODEL_NAMES=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        envScriptBuilder.append(IMAGE_NAME).append(paddle910B).append(System.lineSeparator());
        envScriptBuilder.append("CONTAINER_NAME=").append(hashMap.get(TASK_NAME)).append(System.lineSeparator());
        int port = (int) hashMap.get(API_SERVER_PORT);
        envScriptBuilder.append("API_SERVER_PORT=").append(port).append(System.lineSeparator());
//        envScriptBuilder.append("FASTCHAT_CONTROLLER_PORT=").append(port + 1).append(System.lineSeparator());
        if (deviceIds != null && !deviceIds.isEmpty()) {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("\"").append(String.join(",", deviceIds)).append("\"").append(System.lineSeparator());
            envScriptBuilder.append("NUM_GPUS=").append(deviceIds.size()).append(System.lineSeparator());
        } else {
            envScriptBuilder.append(DEVICES_NUMS_IDS).append("0").append(System.lineSeparator());
            envScriptBuilder.append("NUM_GPUS=").append("1").append(System.lineSeparator());
        }
        envScriptBuilder.append("OPENAI_API_KEY=").append("\"").append("sk-").append(hashMap.get(ENCRYPTION_KEY)).append("\"").append(System.lineSeparator());
        assert deviceIds != null;
        int memory = deviceIds.size() * 32;
        envScriptBuilder.append("MAX_GPU_MEMORY=").append(memory).append("GiB");
        String path = hashMap.get(TASK_PATH) + ENV_PATH;
        File file = new File(path);
        FileWriter writer = new FileWriter(file);
        writer.write(envScriptBuilder.toString());
    }



    /**
     * 检测是否部署成功
     */
    public void checkDeployState(String containerId, Long remoteHostId, Long deployTaskId, int checkTime) {
        log.info("****开始检测****");
        String logs;
        int attempts = 0;
        int maxAttempts = 0;
        if (checkTime == 0) {
            maxAttempts = 600;
        } else if (checkTime == 1) {
            maxAttempts = 18000;
        }
        boolean isDeploySuccess = false;
        while (attempts <= maxAttempts && !isDeploySuccess) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread was interrupted", e);
                break;
            }
            logs = dockerJavaUtil.getContainerLog(containerId, null, remoteHostId);
            if (logs.contains("Uvicorn running") || logs.contains("Daemon start success") || logs.contains("Application startup complete")) {
                update(new LambdaUpdateWrapper<OtherModelDeployTask>().eq(OtherModelDeployTask::getDockerId, containerId).set(OtherModelDeployTask::getDeployStatus, 6));
                updateTime(deployTaskId);
                isDeploySuccess = true;
                log.info("****检测完成,模型部署成功****");
            } else if (logs.contains("Traceback (most recent call last)") || logs.contains("Failed to init endpoint")) {
                update(new LambdaUpdateWrapper<OtherModelDeployTask>().eq(OtherModelDeployTask::getDockerId, containerId).set(OtherModelDeployTask::getDeployStatus, 4));
                isDeploySuccess = true;
                log.info("****检测完成,模型部署失败****");
            } else {
                attempts++;
            }
        }
        if (!isDeploySuccess) {
            update(new LambdaUpdateWrapper<OtherModelDeployTask>().eq(OtherModelDeployTask::getDockerId, containerId).set(OtherModelDeployTask::getDeployStatus, 4));
            dockerJavaUtil.stopContainer(containerId, remoteHostId);
        }
    }


    /**
     * 更新时间段
     */
    void updateTime(Long otherModelDeployId) {
        OtherModelDeployTask otherModelDeployTask = otherModelDeployTaskMapper.selectById(otherModelDeployId);
        String timePeriods = otherModelDeployTask.getTimePeriods();
        if (timePeriods == null) {
            return;
        }
        JSONObject timePeriodsJson = JSONUtil.parseObj(timePeriods);
        JSONArray jsonArray = timePeriodsJson.getJSONArray("time_periods");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = formatter.format(new Date());
        if (jsonArray.isEmpty()) {
            JSONObject startTime = new JSONObject();
            startTime.putOpt("start_time", formattedDateTime);
            jsonArray.add(startTime);
        } else {
            JSONObject lastJson = jsonArray.getJSONObject(jsonArray.size() - 1);
            if (!lastJson.containsKey("end_time")) {
                lastJson.putOpt("end_time", formattedDateTime);
                JSONObject json = JSONUtil.parseObj(otherModelDeployTask.getCommonParameterJson());
                JSONArray devicesConfig = json.getJSONArray(DEVICES);
                JSONArray deviceIds = devicesConfig.getJSONObject(0).getJSONArray(DEVICE_IDS);
                List<String> ids = new ArrayList<>();
                for (int j = 0; j < deviceIds.size(); j++) {
                    ids.add(deviceIds.getStr(j));
                }
                lastJson.putOpt("device_ids", String.join(",", ids));
            } else {
                JSONObject startTime = new JSONObject();
                startTime.putOpt("start_time", formattedDateTime);
                jsonArray.add(startTime);
            }
        }
        otherModelDeployTaskMapper.update(new LambdaUpdateWrapper<OtherModelDeployTask>()
                .set(OtherModelDeployTask::getTimePeriods, timePeriodsJson.toString())
                .eq(OtherModelDeployTask::getId, otherModelDeployTask.getId()));
    }

    /**
     * 调用模型服务
     *
     * @param otherModelDeployTaskId 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    public String useModel(Long otherModelDeployTaskId,Long businessFileId) {
        //查询模型部署任务
        OtherModelDeployTask otherModelDeployTask = otherModelDeployTaskMapper.selectById(otherModelDeployTaskId);
        if (otherModelDeployTask == null) {
            throw new MyRuntimeException("模型部署任务不存在，请核对后重试！");
        }
        if (otherModelDeployTask.getDeployStatus() != 6) {
            throw new MyRuntimeException("模型未运行，请核对后重试！");
        }
        //查询附件
        BusinessFile businessFile =businessFileMapper.selectById(businessFileId);
        if(businessFile==null){
            throw new MyRuntimeException("文件不存在，请核对后重试！");
        }
        return null;


    }

    /**
     * 语音服务调用
     *
     * @param otherModelDeployTask 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    private String useVoiceModel(OtherModelDeployTask otherModelDeployTask,String filePath ) {
        //拼接地址
        Long remoteHostId = otherModelDeployTask.getRemoteHostId();
        Integer port = otherModelDeployTask.getApiServerPort();
        return null;

    }
}
