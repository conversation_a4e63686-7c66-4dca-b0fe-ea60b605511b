package com.supie.webadmin.app.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.app.dao.ModelServiceApiMapper;
import com.supie.webadmin.app.app.model.AppAssistantBasic;
import com.supie.webadmin.app.app.model.ModelService;
import com.supie.webadmin.app.app.model.ModelServiceApi;
import com.supie.webadmin.app.app.service.AppAssistantBasicService;
import com.supie.webadmin.app.app.service.ModelServiceApiService;
import com.supie.webadmin.app.llm.model.ModelBasic;
import com.supie.webadmin.app.llm.service.ModelBasicService;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.impl.ModelDeployTaskServiceImpl;
import com.supie.webadmin.app.llmService.service.impl.ModelServiceRelationServiceImpl;
import com.supie.webadmin.app.servicesApi.constant.ServiceConstant;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务api关联表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("modelServiceApiService")
@MyDataSource(DataSourceType.MAIN)
public class ModelServiceApiServiceImpl extends BaseService<ModelServiceApi, Long> implements ModelServiceApiService {

    @Autowired
    private ModelServiceApiMapper modelServiceApiMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ModelServiceRelationServiceImpl modelServiceRelationService;
    @Autowired
    private ModelDeployTaskServiceImpl modelDeployTaskService;
    @Autowired
    private ModelBasicService modelBasicService;
    @Autowired
    private ModelServiceServiceImpl modelServiceService;
    @Autowired
    private AppAssistantBasicService appAssistantBasicService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<ModelServiceApi> mapper() {
        return modelServiceApiMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param modelServiceApi 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ModelServiceApi saveNew(ModelServiceApi modelServiceApi) {
        // 判断当前服务下服务名称唯一
        List<ModelServiceApi> modelServiceApis = modelServiceApiMapper.selectList(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceName, modelServiceApi.getServiceName())
                .eq(ModelServiceApi::getServiceId, modelServiceApi.getServiceId()));
        if (modelServiceApis.isEmpty()) {
            modelServiceApiMapper.insert(this.buildDefaultValue(modelServiceApi));
            return modelServiceApi;
        } else {
            throw new DuplicateKeyException("服务名称已存在");
        }
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param modelServiceApiList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<ModelServiceApi> modelServiceApiList) {
        if (CollUtil.isNotEmpty(modelServiceApiList)) {
            modelServiceApiList.forEach(this::buildDefaultValue);
            modelServiceApiMapper.insertList(modelServiceApiList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param modelServiceApi         更新的对象。
     * @param originalModelServiceApi 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ModelServiceApi modelServiceApi, ModelServiceApi originalModelServiceApi) {
        MyModelUtil.fillCommonsForUpdate(modelServiceApi, originalModelServiceApi);

        // 排除当前记录的 QueryWrapper 精确查询
        LambdaQueryWrapper<ModelServiceApi> modelServiceApiLambdaQueryWrapper =
                new LambdaQueryWrapper<>(ModelServiceApi.class)
                        .eq(ModelServiceApi::getServiceName, modelServiceApi.getServiceName())
                        .eq(ModelServiceApi::getServiceId, modelServiceApi.getServiceId())
                        .ne(ModelServiceApi::getId, modelServiceApi.getId());
        // 检查是否存在重名（排除当前记录）
        if (modelServiceApiMapper.selectCount(modelServiceApiLambdaQueryWrapper) > 0) {
            throw new DuplicateKeyException("服务名称已存在");
        }

        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<ModelServiceApi> uw = this.createUpdateQueryForNullValue(modelServiceApi, modelServiceApi.getId());
        return modelServiceApiMapper.update(modelServiceApi, uw) == 1;

        // 当使用 RSemaphore 是基于信号量的标准并发控制机制时 更新使用以下代码
        // 没有修改配额或者并发数
        // if (Objects.equals(modelServiceApi.getSemaphoreNumber(), originalModelServiceApi.getSemaphoreNumber())) {
        //     return modelServiceApiMapper.update(modelServiceApi, uw) == 1;
        // }

        // 读锁 避免修改了并发量和配额导致 服务调用异常
        // RReadWriteLock rwLock = redissonClient.getReadWriteLock(ServiceConstant.READ_WRITE_LOCK_KEY + modelServiceApi.getServiceName());
        // boolean writeLockFlag = false;
        // try {
        //     // 尝试获取写锁，等待 5 秒，租约 10 秒（防止死锁）
        //     writeLockFlag = rwLock.writeLock().tryLock(5, 10, TimeUnit.SECONDS);
        //     if (!writeLockFlag) {
        //         throw new Exception("当前模型服务用户正在使用，无法获取写锁，更新模型服务数据失败，请在业务不妨忙时更新");
        //     }
        //     // 修改并发量
        //     // 构造 Redis key
        //     String semaphoreKey = String.format("model:semaphore:%s", modelServiceApi.getServiceName());
        //     RSemaphore semaphore = redissonClient.getSemaphore(semaphoreKey);
        //     semaphore.delete();
//
        //     semaphore.trySetPermits(modelServiceApi.getSemaphoreNumber());
        //     long currentTimeMillis = System.currentTimeMillis();
        //     long endTimeMillis = modelServiceApi.getEndTime().getTime();
        //     // 计算剩余存活时间（毫秒）
        //     long ttlMillis = endTimeMillis - currentTimeMillis;
        //     semaphore.expire(ttlMillis, TimeUnit.MILLISECONDS);
//
        //     return modelServiceApiMapper.update(modelServiceApi, uw) == 1;
        // } catch (Exception e) {
        //     log.error("更新模型服务数据失败,异常信息：{}", e.getMessage());
        //     throw new MyRuntimeException(e.getMessage());
        // } finally {
        //     // 释放写锁资源 避免死锁
        //     if (writeLockFlag) {
        //         rwLock.writeLock().lock();
        //     }
        // }
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return modelServiceApiMapper.deleteById(id) == 1;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getModelServiceApiListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<ModelServiceApi> getModelServiceApiList(ModelServiceApi filter, String orderBy) {
        return modelServiceApiMapper.getModelServiceApiList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getModelServiceApiList)，以便获取更好的查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<ModelServiceApi> getModelServiceApiListWithRelation(ModelServiceApi filter, String orderBy) {
        List<ModelServiceApi> resultList = modelServiceApiMapper.getModelServiceApiList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        // 填充 调用总次数和当前并发量
        for (ModelServiceApi modelServiceApi : resultList) {
            modelServiceApi.setSemaphoreNumberAndSum(getSemaphoreNumberAndSumByServiceName(modelServiceApi.getServiceId() + ":" + modelServiceApi.getServiceName()));
        }
        for (ModelServiceApi modelServiceApi : resultList) {
            String modelType = modelServiceApi.getModelType();
            Long modelId = modelServiceApi.getModelId();
            ModelBasic modelBasic = new ModelBasic();
            if (modelType.equals("public")) {
                Map<String, Object> publicLlmByModelServiceRelationId = modelServiceRelationService.getPublicLlmByModelServiceRelationId(modelId);
                if (publicLlmByModelServiceRelationId == null) {
                    publicLlmByModelServiceRelationId = new HashMap<>();
                }
                Integer inferenceModel = Integer.valueOf(publicLlmByModelServiceRelationId.getOrDefault("inferenceModel", "-1").toString());
                modelBasic.setInferenceModel(inferenceModel);
                modelServiceApi.setModelBasic(modelBasic);
            } else if (modelType.equals("deploy")) {
                ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
                if (modelDeployTask != null) {
                    modelBasic = modelBasicService.selectOne(new LambdaQueryWrapper<ModelBasic>().eq(ModelBasic::getId, modelDeployTask.getModelId()));
                } else {
                    modelBasic.setInferenceModel(-1);
                }
                modelServiceApi.setModelBasic(modelBasic);
            } else {
                modelBasic.setInferenceModel(-1);
                modelServiceApi.setModelBasic(modelBasic);
            }
        }
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<ModelServiceApi> getGroupedModelServiceApiListWithRelation(
            ModelServiceApi filter, String groupSelect, String groupBy, String orderBy) {
        List<ModelServiceApi> resultList =
                modelServiceApiMapper.getGroupedModelServiceApiList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 根据服务名称获取服务信息。
     *
     * @param serviceName 服务名称。
     * @return 服务信息。
     */
    @Override
    public ModelServiceApi getByName(String serviceName) {
        List<ModelServiceApi> modelServiceApiList = modelServiceApiMapper.selectList(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceName, serviceName).orderByDesc(ModelServiceApi::getCreateTime));
        if (modelServiceApiList == null || modelServiceApiList.isEmpty()) {
            return null;
        }
        if (modelServiceApiList.size() > 1) {
            throw new MyRuntimeException(String.format("服务名称[%s]存在重复数据，请检查", serviceName));
        }
        return modelServiceApiList.get(0);
    }

    /**
     * 根据服务名称返回调用总次数和当前并发量
     *
     * @param serviceName 服务名称
     */
    @Override
    public Map<String, Long> getSemaphoreNumberAndSumByServiceName(String serviceName) {
        Map<String, Long> result = new HashMap<>();
        // 构造 Redis key
        String semaphoreKey = ServiceConstant.SEMAPHORE_NUMBER_KEY + serviceName;
        String sumKey = ServiceConstant.SUM_KEY + serviceName;
        RAtomicLong atomicLongSemaphore = redissonClient.getAtomicLong(semaphoreKey);
        RAtomicLong atomicLongSum = redissonClient.getAtomicLong(sumKey);
        result.put("总调用次数", atomicLongSum.get());
        result.put("当前并发量", atomicLongSemaphore.get());
        return result;
    }

    private ModelServiceApi buildDefaultValue(ModelServiceApi modelServiceApi) {
        if (modelServiceApi.getId() == null) {
            modelServiceApi.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(modelServiceApi);
        modelServiceApi.setIsDelete(GlobalDeletedFlag.NORMAL);
        return modelServiceApi;
    }

    /**
     * 通过短链接获取API信息
     *
     * @param modelServiceApi
     * @return
     */
    @Override
    public Map<String, Object> getApiInfo(ModelServiceApi modelServiceApi) {
        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
        JSONObject result = new JSONObject();
        /**
         * serviceType - type
         * serviceKey - token
         * serviceName- model
         * inferenceModel- thinkFlag
         */
        result.put("type", modelServiceApi.getServiceTypeName());
        result.put("model", modelServiceApi.getServiceName());
        result.put("token", modelService.getNoBearerServiceKey());
        Long modelId = modelServiceApi.getServiceTypeId();
        String modelType = modelServiceApi.getModelType();
        Integer serviceType = modelServiceApi.getServiceType();
        if (serviceType == 3) {
            modelId = modelServiceApi.getModelId();
        } else if (serviceType == 2) {
            AppAssistantBasic appAssistantBasic = appAssistantBasicService.getById(modelServiceApi.getServiceTypeId());
            modelId = appAssistantBasic.getModelId();
            modelType = appAssistantBasic.getModelType();
        }
        if (modelType.equals("public")) {
            Map<String, Object> publicLlmByModelServiceRelationId = modelServiceRelationService.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelationId == null) {
                publicLlmByModelServiceRelationId = new HashMap<>();
            }
            Integer inferenceModel = Integer.valueOf(publicLlmByModelServiceRelationId.getOrDefault("inferenceModel", "-1").toString());
            result.put("thinkFlag", inferenceModel); // 是否推理模型(1是,-1不是)
        } else if (modelType.equals("deploy")) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            ModelBasic modelBasic = new ModelBasic();
            if (modelDeployTask != null) {
                modelBasic = modelBasicService.selectOne(new LambdaQueryWrapper<ModelBasic>().eq(ModelBasic::getId, modelDeployTask.getModelId()));
            } else {
                modelBasic.setInferenceModel(-1);
            }
            result.put("thinkFlag", modelBasic.getInferenceModel()); // 是否推理模型(1是,-1不是)
        } else {
            result.put("thinkFlag", -1); // 是否推理模型(1是,-1不是)
        }
        return result;
    }

}
