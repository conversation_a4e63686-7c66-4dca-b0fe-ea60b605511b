package com.supie.webadmin.app.util;

/**
 * <AUTHOR>
 * @Date: 2024/04/14/ 20:38
 * @description
 */

import com.supie.webadmin.app.tdengine.model.HardwareMonitoringData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部服务管理-模型与服务关联表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Component
@Slf4j
public class CpuMonitoringInformationToolUtil {

    public Map<String, Double>getCpuMonitoringInformationTool( String url) {
        //String url = "http://*************:32768/metrics";
        List<HardwareMonitoringData> lmdServiceHardwareMonitoringData = parseMetrics(url);
        Map<String, Double> saveHash = new HashMap<>();
        String[] filterStrings = {"node_network_speed_bytes", "node_disk_read_bytes_total","node_disk_written_bytes_total",
                "node_disk_read_bytes_total","node_disk_read_bytes_total","node_disk_written_bytes_total","node_filesystem_size_bytes",
                "node_filesystem_avail_bytes","node_filesystem_size_bytes","node_filesystem_free_bytes","go_threads","node_memory_MemAvailable_bytes",
                "process_resident_memory_bytes","node_memory_MemTotal_bytes","process_resident_memory_bytes","node_memory_Active_bytes","node_memory_MemTotal_bytes",
                "process_cpu_seconds_total","node_cpu_seconds_total","process_virtual_memory_bytes","process_cpu_seconds_total","process_start_time_seconds"};

        List<HardwareMonitoringData> filteredEntities = lmdServiceHardwareMonitoringData.stream()
                .filter(entity -> Arrays.asList(filterStrings).contains(entity.getMonitoringName()))
                .collect(Collectors.toList());

        if (!filteredEntities.isEmpty() && filteredEntities.size()>0){


        //过滤出cpu相关节点信息
        List<HardwareMonitoringData> cpuDetailsList = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_cpu_seconds_total"))
                .collect(Collectors.toList());
        //计算各节点cpu使用率
        Map<Integer, Double> cpuUsage = calculateCpuUsage(cpuDetailsList,saveHash);
        //计算总体cpu使用率
        double overallCpuUsage = calculateOverallCpuUsage(cpuUsage);
        saveHash.put("overallCpuUsage",overallCpuUsage);
      //  saveHash.forEach((cpu, usage) -> log.debug(cpu + " : " + usage+"%"));


        //过滤出网络接口速度相关节点信息
        List<HardwareMonitoringData> networkSpeedList = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_network_speed_bytes"))
                .collect(Collectors.toList());
        saveMapNetworkSpeed(networkSpeedList,saveHash);


        //过滤出磁盘IO读写相关节点信息
        List<HardwareMonitoringData> diskWrittenList = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_disk_written_bytes_total"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> diskReadList = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_disk_read_bytes_total"))
                .collect(Collectors.toList());
        saveMapDiskIO(diskWrittenList,diskReadList,saveHash);


        //过滤出磁盘利用率相关信息节点信息
        List<HardwareMonitoringData> filesystemSize = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_filesystem_size_bytes"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> filesystemFree = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_filesystem_free_bytes"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> filesystemAvail = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_filesystem_avail_bytes"))
                .collect(Collectors.toList());
        saveDiskUtilization(filesystemSize,filesystemFree,filesystemAvail,saveHash);


        //当前存在的线程数
        List<HardwareMonitoringData> threads = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("go_threads"))
                .collect(Collectors.toList());
        saveHash.put("nowThread",threads.get(0).getValue());

        //可用内存量
        List<HardwareMonitoringData> availableMemorys = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_memory_MemAvailable_bytes"))
                .collect(Collectors.toList());
        saveHash.put("availableMemory",availableMemorys.get(0).getValue() / (1024*1024));


        //进程内存使用率（%）
        List<HardwareMonitoringData> residentMemorys = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("process_resident_memory_bytes"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> virtualMemorys = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("process_virtual_memory_bytes"))
                .collect(Collectors.toList());
        saveProcessMemoryUsage(residentMemorys,virtualMemorys,saveHash);

        //计算系统内存利用率（以百分比表示）
        List<HardwareMonitoringData> memoryMemTotals = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_memory_MemTotal_bytes"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> memoryMemAvailables = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("node_memory_MemAvailable_bytes"))
                .collect(Collectors.toList());
        saveMemoryUtilizationRate(memoryMemTotals,memoryMemAvailables,saveHash);

        //进程的CPU利用率（以百分比表示）
        List<HardwareMonitoringData> cpuSecondsTotal = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("process_cpu_seconds_total"))
                .collect(Collectors.toList());
        List<HardwareMonitoringData> startTimeSeconds = filteredEntities.stream()
                .filter(entity -> entity.getMonitoringName().equals("process_start_time_seconds"))
                .collect(Collectors.toList());
        saveProcessCpuUtilization(cpuSecondsTotal,startTimeSeconds,saveHash);
        }
        /*test
        过滤出cpu相关节点信息
        testCpuOverallCpuUsage(cpuDetailsList,saveHash);
*/
        return saveHash;
    }

    /**
     * cpu利用率测试，勿删除！
     * @param saveHash
     */
    private void testCpuOverallCpuUsage(List<HardwareMonitoringData> cpuDetailsList, Map<String, Double> saveHash) {
        double idleSum = 0.0;
        double allSum = 0.0;
        List<HardwareMonitoringData> idleList = cpuDetailsList.stream()
                .filter(data -> "idle".equals(data.getModeType()))
                .collect(Collectors.toList());
        for (HardwareMonitoringData lmdServiceHardwareMonitoringData : idleList) {
            idleSum += lmdServiceHardwareMonitoringData.getValue();
        }
        for (HardwareMonitoringData lmdServiceHardwareMonitoringData : cpuDetailsList) {
            allSum += lmdServiceHardwareMonitoringData.getValue();
        }
        log.debug("cpu测试利用率："+idleSum / allSum +"%");
    }

    /**
     * ①要计算系统的CPU利用率（每核），您可以使用以下指标和计算方法：
     * 指标：
     * node_cpu_seconds_total: 表示每个CPU核心的总CPU时间，单位为秒。
     * node_cpu_seconds_total{mode="idle"}: 表示每个CPU核心的空闲CPU时间，单位为秒。
     * 计算方法：
     * 首先，获取node_cpu_seconds_total和node_cpu_seconds_total{mode="idle"}指标的数据。
     * 然后，计算系统的CPU利用率（每核）：
     * 系统CPU利用率（每核）（%） = 100 * (1 - (空闲CPU时间 / 总CPU时间))
     * @param cpuSecondsTotalList
     * @param startTimeSecondsList
     * @param saveHash
     */
    private static void saveCPUUtilizationRate(List<HardwareMonitoringData> cpuSecondsTotalList, List<HardwareMonitoringData> startTimeSecondsList, Map<String, Object> saveHash) {
        // 创建一个Map来存储按cpuNum分类后的数据
        Map<Integer, Double> sumByCpuNum = new HashMap<>();
        //CPU时间总和
        double cpuTimeSum = 0.0;
        double freeCpuTimeSum = 0.0;


        // 遍历cpuSecondsTotalList，按照cpuNum属性进行分类并计算每一类中value属性的总和
        for (HardwareMonitoringData data : cpuSecondsTotalList) {
            int cpuNum = data.getCpuNum();
            double value = data.getValue();

            if (sumByCpuNum.containsKey(cpuNum)) {
                // 如果Map中已经存在该cpuNum，则累加value属性的值
                double sum = sumByCpuNum.get(cpuNum) + value;
                sumByCpuNum.put(cpuNum, sum);
            } else {
                // 如果Map中不存在该cpuNum，则将value属性的值添加到Map中
                sumByCpuNum.put(cpuNum, value);
            }
        }

        // 输出每一类中value属性的总和
        for (Map.Entry<Integer, Double> entry : sumByCpuNum.entrySet()) {

            // 使用Stream API中的filter方法筛选出modeType属性值为idle的元素


            //  log.debug("CPU核心" + entry.getKey() + "的value属性总和为: " + entry.getValue());
        }
        saveHash.put("allCpuUsage",100 * (1 - (freeCpuTimeSum / cpuTimeSum)));

    }

    /**
     * 计算进程的CPU利用率（以百分比表示），
     *
     * 指标：
     * process_cpu_seconds_total: 表示进程消耗的CPU时间总量，单位为秒。
     * process_start_time_seconds: 表示进程启动的时间戳，单位为秒。
     * 计算方法：
     * process_cpu_seconds_total和process_start_time_seconds指标的数据。
     * 然后，计算进程的CPU利用率：
     * 进程CPU利用率（%） = （进程消耗的CPU时间总量 / (当前时间 - 进程启动时间)） * 100
     * @param cpuSecondsTotal
     * @param startTimeSeconds
     * @param saveHash
     */
    private static void saveProcessCpuUtilization(List<HardwareMonitoringData> cpuSecondsTotal, List<HardwareMonitoringData> startTimeSeconds, Map<String, Double> saveHash) {
        // 获取进程消耗的CPU时间总量
        double processCpuTime = cpuSecondsTotal.get(0).getValue() / 1e9; // 转换为秒

        // 获取进程启动时间
        double processStartTime = startTimeSeconds.get(0).getValue() / 1e9; // 转换为秒

        // 计算时间间隔
        double currentTime = System.currentTimeMillis() / 1000; // 转换为秒
        double elapsedTime = currentTime - processStartTime;

        // 计算CPU利用率
        double cpuUtilization = (processCpuTime / elapsedTime) * 100;
        saveHash.put("cpuUtilization",cpuUtilization);
        //log.debug(cpuUtilization);
    }

    /**
     * 计算系统内存利用率（以百分比表示），您可以使用以下指标和计算方法：
     * 指标：
     * node_memory_MemTotal_bytes: 表示系统总内存大小，单位为字节。
     * node_memory_MemAvailable_bytes: 表示系统可用内存大小，单位为字节。
     * 计算方法：
     * node_memory_MemTotal_bytes和node_memory_MemAvailable_bytes指标的数据。
     * 然后，计算系统内存利用率：
     * 系统内存利用率（%） = （系统总内存大小 - 系统可用内存大小） / 系统总内存大小 * 100
     * @param memoryMemTotals
     * @param memoryMemAvailables
     * @param saveHash
     */
    private static void saveMemoryUtilizationRate(List<HardwareMonitoringData> memoryMemTotals, List<HardwareMonitoringData> memoryMemAvailables, Map<String, Double> saveHash) {
        saveHash.put("systemMemoryUtilizatiion",(memoryMemTotals.get(0).getValue() - memoryMemAvailables.get(0).getValue()) / memoryMemTotals.get(0).getValue() * 100);
    }

    /**
     * ①进程内存使用率（%）
     * 指标：
     * process_resident_memory_bytes: 表示进程的驻留内存大小（不包括交换空间），单位为字节。
     * process_virtual_memory_bytes: 表示进程的虚拟内存大小，单位为字节。
     * 计算方法：
     * 指标：
     * process_resident_memory_bytes和process_virtual_memory_bytes
     * 然后，计算在使用中的进程内存（不包括交换空间）的百分比：
     * 进程内存使用率（%） = （进程的驻留内存大小 / 进程的虚拟内存大小） * 100
     *
     * ②进程内存使用量
     * 首先，使用PromQL查询语句获取process_resident_memory_bytes指标的数据。
     * 然后，将进程的驻留内存大小转换为MB：
     * 进程内存使用量（MB） = 进程的驻留内存大小 / (1024 * 1024)
     * @param residentMemorys
     * @param virtualMemorys
     * @param saveHash
     */
    private static void saveProcessMemoryUsage(List<HardwareMonitoringData> residentMemorys, List<HardwareMonitoringData> virtualMemorys, Map<String, Double> saveHash) {
        //封装进程内存使用率
        saveHash.put("processMemoryUsage",(residentMemorys.get(0).getValue() / virtualMemorys.get(0).getValue()) * 100);

        //进程内存使用量（MB）
        saveHash.put("processMemoryAmount",residentMemorys.get(0).getValue() / (1024 * 1024));
    }

    /**
     * 计算磁盘已使用的空间大小和磁盘利用率，并封装两个结果值
     * ①磁盘已使用的空间大小
     * 指标：
     * node_filesystem_size_bytes: 表示磁盘的总大小，单位为字节。
     * node_filesystem_avail_bytes: 表示磁盘的可用空间大小，单位为字节。
     * 磁盘已使用的空间大小（GB） = （磁盘的总大小 - 磁盘的可用空间大小） / 1024^3
     *
     * ②计算磁盘利用率
     * 指标：
     * node_filesystem_size_bytes和node_filesystem_avail_bytes指标的数据。
     * 然后，计算磁盘利用率：
     * 磁盘利用率（%） = （1 - 磁盘的可用空间大小 / 磁盘的总大小） * 100
     * @param filesystemSizeList
     * @param filesystemFreeList
     * @param saveHash
     */
    private static void saveDiskUtilization(List<HardwareMonitoringData> filesystemSizeList, List<HardwareMonitoringData> filesystemFreeList, List<HardwareMonitoringData> filesystemAvailList ,Map<String, Double> saveHash) {
        Double filesystemSizeSum = 0.0;
        Double filesystemFreeSum = 0.0;
        Double filesystemAvailSum = 0.0;
        Double diskUsedSum;
        Double diskUtilization ;

        if(filesystemFreeList != null && filesystemFreeList.size() != 0){
            for (HardwareMonitoringData lmdServiceHardwareMonitoringData : filesystemFreeList) {
                filesystemFreeSum += lmdServiceHardwareMonitoringData.getValue();
            }
        }

        if(filesystemSizeList != null && filesystemSizeList.size() != 0){
            for (HardwareMonitoringData lmdServiceHardwareMonitoringData : filesystemSizeList) {
                filesystemSizeSum += lmdServiceHardwareMonitoringData.getValue();
            }
        }




        //该值为磁盘已使用的空间大小，且单位为GB
        diskUsedSum = (filesystemSizeSum - filesystemFreeSum)/(1024^3);

        if(filesystemAvailList != null && filesystemAvailList.size() != 0){
            for (HardwareMonitoringData lmdServiceHardwareMonitoringData : filesystemAvailList) {
                filesystemAvailSum += lmdServiceHardwareMonitoringData.getValue();
            }
        }
        //该值为磁盘利用率
        diskUtilization = (1 - filesystemAvailSum / filesystemSizeSum) * 100;
        saveHash.put("diskUsedSum",diskUsedSum);
        saveHash.put("diskUtilization",diskUtilization);
    }

    /**
     * 相关参数名称：node_disk_read_bytes和node_disk_written_bytes指标的数据，
     * 将获取的数据进行累加，得到节点上磁盘的总I/O字节数。
     * 将总I/O字节数转换为MB，即除以1024*1024，得到磁盘I/O的总量（MB）。
     * @param diskWrittenList
     * @param diskReadList
     * @param saveHash
     */
    private static void saveMapDiskIO(List<HardwareMonitoringData> diskWrittenList, List<HardwareMonitoringData> diskReadList, Map<String, Double> saveHash) {
        Double diskIOWrittenAndReadSum = 0.0;

        if(diskWrittenList != null && diskWrittenList.size() != 0){
            for (HardwareMonitoringData lmdServiceHardwareMonitoringData : diskWrittenList) {
                diskIOWrittenAndReadSum += lmdServiceHardwareMonitoringData.getValue();
            }
        }

        if(diskReadList != null && diskWrittenList.size() != 0){
            for (HardwareMonitoringData lmdServiceHardwareMonitoringData : diskReadList) {
                diskIOWrittenAndReadSum += lmdServiceHardwareMonitoringData.getValue();
            }
        }
        if(diskIOWrittenAndReadSum != 0.0){
            diskIOWrittenAndReadSum = diskIOWrittenAndReadSum / (1024*1024);
        }
        saveHash.put("diskIOWrittenAndReadSum",diskIOWrittenAndReadSum);
    }


    /**
     * 计算整个节点（node）的网络速度总量，使用node_network_speed_bytes指标。
     * 这个指标表示节点的网络速度，单位为字节/秒。通过对这个指标的数值进行累加来计算整个节点的网络速度总量。
     * @param networkSpeedList
     * @param saveHash
     */
    private static void saveMapNetworkSpeed(List<HardwareMonitoringData> networkSpeedList, Map<String, Double> saveHash) {
        Double networkSpeedSum = 0.0;
        if(networkSpeedList != null && networkSpeedList.size() != 0){
            for (HardwareMonitoringData network : networkSpeedList) {
                networkSpeedSum += network.getValue();
            }
        }
        saveHash.put("networkSpeedSum",networkSpeedSum);
    }

    public static Map<Integer, Double> calculateCpuUsage(List<HardwareMonitoringData> data, Map<String, Double> saveMap) {
        Map<Integer, Double> totalTimes = new HashMap<>();
        Map<Integer, Double> idleTimes = new HashMap<>();

        for (HardwareMonitoringData datum : data) {
            totalTimes.merge(datum.getCpuNum(), datum.getValue(), Double::sum);
            if ("idle".equals(datum.getModeType())) {
                idleTimes.put(datum.getCpuNum(), datum.getValue());
            }
        }

        Map<Integer, Double> cpuUsage = new HashMap<>();
        totalTimes.forEach((cpu, total) -> {
            double idle = idleTimes.getOrDefault(cpu, 0.0);
            double usage = 100.0 * (total - idle) / total;
            cpuUsage.put(cpu, usage);
//            saveMap.put(cpu+"cpuUsage",usage+"%");
        });
        return cpuUsage;
    }

    public static double calculateOverallCpuUsage(Map<Integer, Double> cpuUsage) {
        double sumUsage = 0.0;
        for (double usage : cpuUsage.values()) {
            sumUsage += usage;
        }
        return sumUsage / cpuUsage.size();
    }

    public static void calculateTotalValue(Map<String, List<HardwareMonitoringData>> map,Map<String, Object> stringDoubleMap) {
        for (List<HardwareMonitoringData> dataList : map.values()) {
            double sum = 0.0;
            int count = dataList.size();

            for (HardwareMonitoringData data : dataList) {
                sum += data.getValue();
            }
            stringDoubleMap.put(dataList.get(0).getMonitoringName(),sum/count);
            //totalValues.add(sum / count);
        }
    }

    private static List<HardwareMonitoringData> parseMetrics(String urlString) {
        // Map<String, String> metricsMap = new LinkedHashMap<>();
        List<HardwareMonitoringData> saveList = new ArrayList<>();
        try {
            URL url = new URL(urlString);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            // 设置连接和读取超时时间（以毫秒为单位）
            conn.setConnectTimeout(500); // 连接超时5秒
            conn.setReadTimeout(500); // 读取超时5秒

            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                if (!inputLine.startsWith("# TYPE")&&!inputLine.startsWith("# HELP")) {
                    HardwareMonitoringData lmdServiceHardwareMonitoringData = new HardwareMonitoringData();
                    if(inputLine.contains("{") || inputLine.contains("}")) {// 执行下面的代码
                        String[] parts = inputLine.split("[{}]");
                        lmdServiceHardwareMonitoringData.setMonitoringName(parts[0].trim());
                        lmdServiceHardwareMonitoringData.setValue(Double.valueOf(parts[2].trim()));
                        secondValue(parts[1],lmdServiceHardwareMonitoringData);
                        saveList.add(lmdServiceHardwareMonitoringData);
                    }else {
                        String[] parts = inputLine.split("\\s+");
                        lmdServiceHardwareMonitoringData.setMonitoringName(parts[0]);
                        lmdServiceHardwareMonitoringData.setValue(Double.valueOf(parts[1]));
                        saveList.add(lmdServiceHardwareMonitoringData);
                    }
                }
            }
            in.close();
        } catch (ConnectException e) {
            log.info("当前url:"+urlString+"请求超时，请检查通信是否正常");
        }catch (IOException e){
            log.error("出现错误"+ e.getMessage(), e);
        }
        return saveList;
    }

    private static void secondValue(String input, HardwareMonitoringData lmdServiceHardwareMonitoringData) {
        // 解析字符串，提取属性值并封装到实体对象中
        String[] parts = input.split(",");
        for (String part : parts) {
            String[] keyValue = part.split("=");
            String key = keyValue[0].replaceAll("\"", "");
            String value = keyValue[1].replaceAll("\"", "");

            if (key.equals("cpu")) {
                lmdServiceHardwareMonitoringData.setCpuNum(Integer.valueOf(value));
            } else if (key.equals("mode")) {
                lmdServiceHardwareMonitoringData.setModeType(value);
            } else if(key.equals("device")){
                lmdServiceHardwareMonitoringData.setDeviceName(value);
            }else if(key.equals("fstype")){
                lmdServiceHardwareMonitoringData.setFsType(value);
            }else if(key.equals("mountpoint")){
                lmdServiceHardwareMonitoringData.setMountPoint(value);
            }
        }
    }
}
