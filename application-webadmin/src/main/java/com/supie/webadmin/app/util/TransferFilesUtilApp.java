package com.supie.webadmin.app.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.ResponseResult;
import com.supie.common.core.upload.BaseUpDownloader;
import com.supie.common.core.upload.UpDownloaderFactory;
import com.supie.common.core.upload.UploadResponseInfo;
import com.supie.common.core.upload.UploadStoreInfo;
import com.supie.common.core.util.MyModelUtil;
//import com.supie.webadmin.app.knowledgebase.model.DataResources;
//import com.supie.webadmin.app.translate.model.TranslateTarget;
import com.supie.webadmin.app.data.model.DataResources;
import com.supie.webadmin.app.other.dao.BusinessFileMapper;
import com.supie.webadmin.app.other.model.BusinessFile;
import com.supie.webadmin.config.ApplicationConfig;
import fr.opensagres.poi.xwpf.converter.xhtml.Base64EmbedImgManager;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.PictureType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件转化工具类
 * 来自应用平台
 */
@Slf4j
@Component
public class TransferFilesUtilApp {

    @Resource
    private UpDownloaderFactory upDownloaderFactory;
    @Resource
    private ApplicationConfig appConfig;
    @Resource
    private BusinessFileMapper businessFileMapper;

    public static void main(String[] args) {
        String filePath = "D:\\project\\java\\company\\LargeModelDev1\\BaseModels\\Output.html";
        try {
            // 读取 HTML 文件内容
            String htmlContent = new String(Files.readAllBytes(Paths.get(filePath)), "UTF-8");
            // 将读取的内容传递给 htmlToWord 方法
            ByteArrayInputStream byteArrayInputStream = htmlToWord(htmlContent);
            // 将 ByteArrayInputStream 保存为 Word 文档
            saveWordDocument("D:\\project\\java\\company\\LargeModelDev1\\BaseModels\\output.docx", byteArrayInputStream);
        } catch (IOException e) {
            System.out.println("Error reading the HTML file: " + e.getMessage());
        }
    }

//    public static ByteArrayInputStream htmlToWord(String htmlContent) {
//        // 创建一个新的空白 Word 文档
//        XWPFDocument document = new XWPFDocument();
//        // 假设添加 HTML 内容到 Word 文档中，这里需要实际的转换逻辑
//        // 现在只是简单地添加文本到文档中
//        document.createParagraph().createRun().setText(htmlContent);
//        try {
//            ByteArrayOutputStream out = new ByteArrayOutputStream();
//            document.write(out);
//            document.close();
//            return new ByteArrayInputStream(out.toByteArray());
//        } catch (IOException e) {
//            System.out.println("Error during document creation: " + e.getMessage());
//            return null;
//        }
//    }

    public static void saveWordDocument(String outputFilePath, ByteArrayInputStream inStream) {
        try {
            FileOutputStream out = new FileOutputStream(outputFilePath);
            byte[] buffer = new byte[1024];
            int length;
            // 将 ByteArrayInputStream 中的内容写入文件
            while ((length = inStream.read(buffer)) != -1) {
                out.write(buffer, 0, length);
            }
            inStream.close();
            out.close();
        } catch (IOException e) {
            System.out.println("Error writing the Word file: " + e.getMessage());
        }
    }

    /**
     * 将指定HTML文件的内容转换成Word文档。
     *
     * @param htmlContent HTML文件内容
     * @return 转换后的Word文档的字节数组输入流
     */
    public static ByteArrayInputStream htmlToWord(String htmlContent) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 使用Jsoup解析HTML内容
            Document doc = Jsoup.parse(htmlContent, "UTF-8");
            // 创建一个新的空白Word文档
            XWPFDocument document = new XWPFDocument();
            // 递归处理HTML元素，并转换成Word格式
            processElement(doc.body(), document);
            // 将Word文档写入输出流
            document.write(outputStream);
            log.info("HTML内容已成功转换为Word文档。");
            // 将输出流转换为输入流
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (IOException e) {
            log.error("HTML转Word过程中出现错误：" + e.getMessage());
            throw new MyRuntimeException("HTML转Word过程中出现错误", e);
        }
    }

    /**
     * 递归处理HTML元素，将其添加到Word文档中。
     *
     * @param element  当前要处理的HTML元素
     * @param document 正在添加内容的Word文档
     */
    private static void processElement(Element element, XWPFDocument document) {
        for (Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                // 处理文本节点
                String text = ((TextNode) node).text();
                XWPFParagraph paragraph = document.createParagraph();
                XWPFRun run = paragraph.createRun();
                applyStyles(run, element); // 应用元素的样式
                run.setText(text);
            } else if (node instanceof Element) {
                // 处理元素节点
                Element childElement = (Element) node;
                String tagName = childElement.tagName();
                if ("p".equals(tagName)) {
                    // 处理段落元素
                    XWPFParagraph paragraph = document.createParagraph();
                    processElement(childElement, paragraph);
                } else {
                    // 处理其他元素
                    processElement(childElement, document);
                }
            }
        }
    }

    /**
     * 递归处理HTML元素，将其添加到Word文档的段落中。
     *
     * @param element   当前要处理的HTML元素
     * @param paragraph 正在添加内容的Word文档段落
     */
    private static void processElement(Element element, XWPFParagraph paragraph) {
        for (Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                // 处理文本节点
                String text = ((TextNode) node).text();
                XWPFRun run = paragraph.createRun();
                applyStyles(run, element); // 应用元素的样式
                run.setText(text);
            } else if (node instanceof Element) {
                // 处理元素节点
                Element childElement = (Element) node;
                String tagName = childElement.tagName();
                if ("b".equals(tagName) || "strong".equals(tagName)) {
                    // 处理粗体元素
                    XWPFRun run = paragraph.createRun();
                    run.setBold(true);
                    applyStyles(run, childElement); // 应用子元素的样式
                    processElement(childElement, paragraph);
                } else if ("i".equals(tagName) || "em".equals(tagName)) {
                    // 处理斜体元素
                    XWPFRun run = paragraph.createRun();
                    run.setItalic(true);
                    applyStyles(run, childElement); // 应用子元素的样式
                    processElement(childElement, paragraph);
                } else if ("u".equals(tagName)) {
                    // 处理下划线元素
                    XWPFRun run = paragraph.createRun();
                    run.setUnderline(UnderlinePatterns.SINGLE);
                    applyStyles(run, childElement); // 应用子元素的样式
                    processElement(childElement, paragraph);
                } else if ("span".equals(tagName)) {
                    // 处理span元素
                    XWPFRun run = paragraph.createRun();
                    applyStyles(run, childElement); // 应用子元素的样式
                    processElement(childElement, paragraph);
                } else {
                    // 处理其他元素
                    processElement(childElement, paragraph);
                }
            }
        }
    }

    /**
     * 应用元素的样式。
     *
     * @param run      Run对象
     * @param element  HTML元素
     */
    private static void applyStyles(XWPFRun run, Element element) {
        String style = element.attr("style");
        if (style != null && !style.isEmpty()) {
            String[] styles = style.split(";");
            for (String s : styles) {
                String[] pair = s.split(":");
                if (pair.length == 2) {
                    String property = pair[0].trim();
                    String value = pair[1].trim();
                    switch (property) {
                        case "color":
                            // 移除颜色值中的 '#' 符号
                            String hexColor = value.replace("#", "");
                            // 确保颜色值长度为 6 个字符
                            if (hexColor.length() == 6) {
                                run.setColor(hexColor);
                            }
                            break;
                        case "font-size":
                            int fontSize = Integer.parseInt(value.replaceAll("[^0-9]", ""));
                            run.setFontSize(fontSize);
                            break;
                        case "font-family":
                            run.setFontFamily(value);
                            break;
                        case "text-decoration":
                            if ("underline".equalsIgnoreCase(value)) {
                                run.setUnderline(UnderlinePatterns.SINGLE);  // 设置下划线
                            }
                            break;
                        case "font-weight":
                            if ("bold".equalsIgnoreCase(value)) {
                                run.setBold(true);  // 设置粗体
                            }
                            break;
                        case "font-style":
                            if ("italic".equalsIgnoreCase(value)) {
                                run.setItalic(true);  // 设置斜体
                            }
                            break;
                        default:
                            // 忽略不支持的样式属性
                            break;
                    }
                }
            }
        }
    }



    /**
     * 将XLS文件处理为json
     *
     * @param inputStream 输入的文件流
     * @throws IOException 异常
     */
    public JSONArray xlsToJson(InputStream inputStream) throws IOException {
        // 读取 XLS 文件
        HSSFWorkbook workbook = new HSSFWorkbook(inputStream);
        // 创建 JSON 数组,用于存储 Excel 数据
        JSONArray jsonArray = new JSONArray();
        // 遍历每个 Excel 工作表
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            HSSFSheet sheet = workbook.getSheetAt(i);
            // 创建 JSON 对象,用于存储当前工作表的数据
            JSONObject sheetJson = new JSONObject();
            sheetJson.put("sheetName", sheet.getSheetName());
            // 创建 JSON 数组,用于存储当前工作表的行数据
            JSONArray rowsJson = new JSONArray();
            // 遍历每一行
            for (Row row : sheet) {
                // 创建 JSON 对象,用于存储当前行的数据
                JSONObject rowJson = new JSONObject();
                // 遍历每个单元格
                for (Cell cell : row) {
                    // 获取单元格的列索引
                    int columnIndex = cell.getColumnIndex();
                    // 将单元格的值转换为字符串
                    String cellValue = getCellValueAsString(cell);
                    // 将单元格的值添加到当前行的 JSON 对象中
                    rowJson.put(String.valueOf(columnIndex), cellValue);
                }
                // 将当前行的 JSON 对象添加到行数组中
                rowsJson.add(rowJson);
            }
            // 将行数组添加到当前工作表的 JSON 对象中
            sheetJson.put("rows", rowsJson);
            // 创建 JSON 数组,用于存储合并单元格的位置
            JSONArray mergedCellsJson = new JSONArray();
            // 获取当前工作表的合并单元格区域
            List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
            // 遍历合并单元格区域
            for (CellRangeAddress mergedRegion : mergedRegions) {
                // 创建 JSON 对象,用于存储合并单元格的位置
                JSONObject mergedCellJson = new JSONObject();
                mergedCellJson.put("firstRow", mergedRegion.getFirstRow());
                mergedCellJson.put("lastRow", mergedRegion.getLastRow());
                mergedCellJson.put("firstColumn", mergedRegion.getFirstColumn());
                mergedCellJson.put("lastColumn", mergedRegion.getLastColumn());
                // 将合并单元格的位置添加到合并单元格数组中
                mergedCellsJson.add(mergedCellJson);
            }
            // 将合并单元格数组添加到当前工作表的 JSON 对象中
            sheetJson.put("mergedCells", mergedCellsJson);
            // 将当前工作表的 JSON 对象添加到 JSON 数组中
            jsonArray.add(sheetJson);
        }
        // 将 JSON 数组写入文件
        workbook.close();
        return jsonArray;
    }


    /**
     * 将xlsx转为json
     *
     * @param inputStream 输入文件流
     * @throws IOException 异常
     */
    public JSONArray xlsxToJson(InputStream inputStream) throws IOException {
        // 读取 Excel 文件
        Workbook workbook = WorkbookFactory.create(inputStream);
        // 创建 JSON 数组,用于存储 Excel 数据
        JSONArray jsonArray = new JSONArray();
        // 遍历每个 Excel 工作表
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            // 创建 JSON 对象,用于存储当前工作表的数据
            JSONObject sheetJson = new JSONObject();
            sheetJson.put("sheetName", sheet.getSheetName());
            // 创建 JSON 数组,用于存储当前工作表的行数据
            JSONArray rowsJson = new JSONArray();
            // 遍历每一行
            for (Row row : sheet) {
                // 创建 JSON 对象,用于存储当前行的数据
                JSONObject rowJson = new JSONObject();
                // 遍历每个单元格
                for (Cell cell : row) {
                    // 获取单元格的列索引
                    int columnIndex = cell.getColumnIndex();
                    // 将单元格的值转换为字符串
                    String cellValue = getCellValueAsString(cell);
                    // 将单元格的值添加到当前行的 JSON 对象中
                    rowJson.put(String.valueOf(columnIndex), cellValue);
                }
                // 将当前行的 JSON 对象添加到行数组中
                rowsJson.add(rowJson);
            }
            // 将行数组添加到当前工作表的 JSON 对象中
            sheetJson.put("rows", rowsJson);
            // 创建 JSON 数组,用于存储合并单元格的位置
            JSONArray mergedCellsJson = new JSONArray();
            // 获取当前工作表的合并单元格区域
            List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
            // 遍历合并单元格区域
            for (CellRangeAddress mergedRegion : mergedRegions) {
                // 创建 JSON 对象,用于存储合并单元格的位置
                JSONObject mergedCellJson = new JSONObject();
                mergedCellJson.put("firstRow", mergedRegion.getFirstRow());
                mergedCellJson.put("lastRow", mergedRegion.getLastRow());
                mergedCellJson.put("firstColumn", mergedRegion.getFirstColumn());
                mergedCellJson.put("lastColumn", mergedRegion.getLastColumn());
                // 将合并单元格的位置添加到合并单元格数组中
                mergedCellsJson.add(mergedCellJson);
            }
            // 将合并单元格数组添加到当前工作表的 JSON 对象中
            sheetJson.put("mergedCells", mergedCellsJson);
            // 将当前工作表的 JSON 对象添加到 JSON 数组中
            jsonArray.add(sheetJson);
        }
        workbook.close();
        return jsonArray;
    }

    /**
     * 根据Excel中的显示格式将单元格内容转换为字符串。
     *
     * @param cell 需要转换的Excel单元格。
     * @return 单元格内容的字符串表示形式。
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        // 创建一个DataFormatter来格式化单元格数据
        DataFormatter formatter = new DataFormatter();
        // 使用DataFormatter确保值的格式与Excel显示的一致
        return formatter.formatCellValue(cell);
    }


    /**
     * 将Docx文档通过输入流转换为HTML格式，并返回包含HTML内容的输出流。
     *
     * @param inputStream 包含Docx文档的输入流。
     * @return 包含HTML内容的输出流。
     */
    public static ByteArrayOutputStream docxToHtml(InputStream inputStream) {
        try (XWPFDocument xwpfDocument = new XWPFDocument(inputStream)) {
            CTDocument1 document = xwpfDocument.getDocument();
            CTBody body = document.getBody();
            if (body.getSectPr() == null) {
                setDefaultPageLayout(body.addNewSectPr());
            }
            // XHTML转换选项
            XHTMLOptions options = XHTMLOptions.create().setFragment(false);
            options.setIgnoreStylesIfUnused(false);
            options.setImageManager(new Base64EmbedImgManager());
            options.setOmitHeaderFooterPages(false);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 使用提供的输出流进行文档转换
            XHTMLConverter.getInstance().convert(xwpfDocument, outputStream, options);
            // 添加HTML头部的meta标签以确保字符编码正确
            byte[] bytes = outputStream.toByteArray();
            String htmlContent = new String(bytes, StandardCharsets.UTF_8);
            String htmlWithMeta = htmlContent.replaceFirst("<head>", "<head>\n<meta charset=\"UTF-8\">");
            // 返回新的ByteArrayOutputStream包含处理后的HTML
            ByteArrayOutputStream finalStream = new ByteArrayOutputStream();
            finalStream.write(htmlWithMeta.getBytes(StandardCharsets.UTF_8));
            log.info("Docx转换为HTML完成");
            return finalStream;
        } catch (Exception e) {
            log.error("转换过程出错: {}", e.getMessage(), e);
            throw new RuntimeException("转换过程出错", e);
        }
    }

    /**
     * 设置默认页面布局
     *
     * @param sectPr
     */
    private static void setDefaultPageLayout(CTSectPr sectPr) {
        // 设置页面大小，A4纸尺寸（210mm x 297mm）
        CTPageSz pageSize = sectPr.addNewPgSz();
        pageSize.setW(BigInteger.valueOf(11906)); // 宽度
        pageSize.setH(BigInteger.valueOf(16838)); // 高度
        // 设置页面边距
        CTPageMar pgMar = sectPr.addNewPgMar();
        pgMar.setTop(BigInteger.valueOf(1440));    // 上边距 2.54cm
        pgMar.setBottom(BigInteger.valueOf(1440)); // 下边距 2.54cm
        pgMar.setLeft(BigInteger.valueOf(1800));   // 左边距 3.18cm
        pgMar.setRight(BigInteger.valueOf(1800));  // 右边距 3.18cm
        pgMar.setHeader(BigInteger.valueOf(720));  // 页眉 1.27cm
        pgMar.setFooter(BigInteger.valueOf(720));  // 页脚 1.27cm
        pgMar.setGutter(BigInteger.valueOf(0));    // 装订线
    }

    /**
     * 将Word文档通过输入流转换为HTML格式，并返回包含HTML内容的输出流。
     *
     * @param inputStream 包含Word文档的输入流。
     * @return 包含HTML内容的输出流。
     */
    public static ByteArrayOutputStream docToHtml(InputStream inputStream) {
        try {
            HWPFDocument wordDocument = new HWPFDocument(inputStream);
            WordToHtmlConverter wordToHtmlConverter = new WordToHtmlConverter(
                    DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument());
            // 将图片处理调整为Base64编码
            wordToHtmlConverter.setPicturesManager((content, pictureType, suggestedName, widthInches, heightInches) -> {
                String base64String = Base64.encodeBase64String(content);
                String mime = getMimeType(pictureType);
                return String.format("data:%s;base64,%s", mime, base64String);
            });
            wordToHtmlConverter.processDocument(wordDocument);
            org.w3c.dom.Document htmlDocument = wordToHtmlConverter.getDocument();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            DOMSource domSource = new DOMSource(htmlDocument);
            StreamResult streamResult = new StreamResult(outputStream);
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty(OutputKeys.METHOD, "html");
            transformer.transform(domSource, streamResult);
            return outputStream;  // 返回包含HTML内容的输出流
        } catch (Exception e) {
            log.error("转换过程出错: {}", e.getMessage(), e);
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 根据图片类型获取对应的MIME类型。
     *
     * @param pictureType 图片类型。
     * @return 对应的MIME类型字符串。
     */
    private static String getMimeType(PictureType pictureType) {
        switch (pictureType) {
            case EMF:
                return "image/x-emf";
            case WMF:
                return "image/x-wmf";
            case PICT:
                return "image/x-pict";
            case JPEG:
                return "image/jpeg";
            case PNG:
                return "image/png";
            case BMP:
                return "image/bmp";
            case GIF:
                return "image/gif";
            default:
                return "application/octet-stream"; // 通用二进制数据
        }
    }


    /**
     * 获取minio中文件流
     *
     * @param fileJson fileJson
     * @return 文件流
     * @throws IOException 异常
     */
//    public InputStream getMinioFile(String fileJson) throws IOException {
//        cn.hutool.json.JSONObject json = JSONUtil.parseObj(fileJson);
//        String fieldName = json.getStr("fieldName");
//        String fileName = json.getStr("filename");
//        boolean asImage = json.getBool("asImage");
//
//        // 获取minio中的文件
//        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(DataResources.class, fieldName);
//        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
//        return upDownloader.downloadInternalInputStream(appConfig.getUploadFileBaseDir(),
//                BusinessFile.class.getSimpleName(), fieldName, fileName, asImage);
//    }


    /**
     * 上传文件到minio中
     *
     * @param fieldName  文件名
     * @param asImage    是否文件
     * @param uploadFile 文件
     * @return UploadResponseInfo
     * @throws IOException 异常
     */
    public UploadResponseInfo doUploadMinio(String fieldName, Boolean asImage, MultipartFile uploadFile) throws IOException {

        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);

        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), fieldName, asImage, uploadFile);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.UPLOAD_FAILED, responseInfo.getErrorMessage()));

        }
        return responseInfo;

    }


    /**
     * 处理数据，将文件设置在附件表中
     *
     * @param uploadResponseInfo uploadResponseInfo
     */
    public Long processingData(UploadResponseInfo uploadResponseInfo, String fileName) {
        BusinessFile businessFile = new BusinessFile();
        Map<String, Object> objectHashMap = new HashMap<>();
        objectHashMap.put("fieldName", "fileJson");
        objectHashMap.put("asImage", false);
        objectHashMap.put("filename", uploadResponseInfo.getFilename());
        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(objectHashMap);
        businessFile.setFileJson(entries.toString());
        businessFile.setFileType("text/html");
        businessFile.setFileFormat("html");
        businessFile.setFileName(fileName);
        businessFile.setCreateTime(new Date());
        businessFile.setUpdateTime(new Date());
        businessFile.setAsImage("false");
        businessFile.setIsDelete(1);
        MyModelUtil.fillCommonsForInsert(businessFile);
        businessFileMapper.insert(businessFile);
        log.info("转换pdf成功");
        return businessFile.getId();
    }

//    public Long processingData(UploadResponseInfo uploadResponseInfo, String fileName, TranslateTarget translateTarget) {
//        BusinessFile businessFile = new BusinessFile();
//        Map<String, Object> objectHashMap = new HashMap<>();
//        objectHashMap.put("fieldName", "fileJson");
//        objectHashMap.put("asImage", false);
//        objectHashMap.put("filename", uploadResponseInfo.getFilename());
//        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(objectHashMap);
//        businessFile.setFileJson(entries.toString());
//        businessFile.setFileType("text/html");
//        businessFile.setFileFormat("html");
//        businessFile.setFileName(fileName);
//        businessFile.setCreateTime(new Date());
//        businessFile.setUpdateTime(new Date());
//        businessFile.setAsImage("false");
//        businessFile.setIsDelete(1);
//        businessFile.setCreateUserId(translateTarget.getCreateUserId());
//        businessFile.setCreateTime(new Date());
//        businessFile.setUpdateUserId(translateTarget.getUpdateUserId());
//        businessFile.setUpdateTime(new Date());
//        businessFile.setDataUserId(translateTarget.getDataUserId());
//        businessFile.setDataDeptId(translateTarget.getDataDeptId());
//        businessFileMapper.insert(businessFile);
//        log.info("转换pdf成功");
//        return businessFile.getId();
//    }

}
