package com.supie.webadmin.app.servicesApi.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.ResponseResult;
import com.supie.common.core.upload.BaseUpDownloader;
import com.supie.common.core.upload.UpDownloaderFactory;
import com.supie.common.core.upload.UploadStoreInfo;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.webadmin.app.app.dao.AppAssistantWorkflowMapper;
import com.supie.webadmin.app.app.dao.ModelServiceApiMapper;
import com.supie.webadmin.app.app.dao.UserPluginConfigMapper;
import com.supie.webadmin.app.app.model.*;
import com.supie.webadmin.app.app.service.AppAssistantBasicService;
import com.supie.webadmin.app.app.service.AppKnowledgeBaseBasicInfoService;
import com.supie.webadmin.app.app.service.ModelServiceApiService;
import com.supie.webadmin.app.app.service.ModelServiceService;
import com.supie.webadmin.app.data.model.DataResources;
import com.supie.webadmin.app.data.service.DataResourcesService;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.llmService.dao.ModelServiceRelationMapper;
import com.supie.webadmin.pythonClient.service.impl.PythonClientServiceImpl;
import com.supie.webadmin.app.servicesApi.constant.ServiceConstant;
import com.supie.webadmin.app.servicesApi.service.ServiceApiService;
import com.supie.webadmin.app.util.ModelConfigUtil;
import com.supie.webadmin.app.workflow.model.Workflow;
import com.supie.webadmin.app.workflow.model.WorkflowLog;
import com.supie.webadmin.app.workflow.service.WorkflowService;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.upms.model.SysUser;
import com.supie.webadmin.upms.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.supie.webadmin.app.util.TaskConstant.*;
import static com.supie.webadmin.app.util.TaskConstant.EVENT_STREAM;

@Service
@Slf4j
@RequiredArgsConstructor
public class ServiceApiServiceImpl implements ServiceApiService {

    private final ModelServiceApiService modelServiceApiService;

    private final ModelServiceService modelServiceService;

    private final Executor myTaskExecutor;

    private final ModelServiceApiMapper modelServiceApiMapper;

    private final ModelServiceRelationMapper modelServiceRelationMapper;

    private final ModelDeployTaskService modelDeployTaskService;

    private final AppAssistantBasicService appAssistantBasicService;

    private final UserPluginConfigMapper userPluginConfigMapper;

    private final AppAssistantWorkflowMapper appAssistantWorkflowMapper;

    private final PythonClientServiceImpl pythonClientService;

    private final AppKnowledgeBaseBasicInfoService appKnowledgeBaseBasicInfoService;

    private final WorkflowService workflowService;

    private final SysUserService sysUserService;

    private final DataResourcesService dataResourcesService;

    private final UpDownloaderFactory upDownloaderFactory;

    private final ApplicationConfig appConfig;

    private final CloseableHttpClient httpClient;

    private final RedissonClient redissonClient;

    @Value("${python.baseUrl}")
    private String baseUrl;

    @Value("${python.authorizationKey}")
    private String authorizationKey;


    @Override
    public Object modelChat(String model,
                            JSONArray messages,
                            Boolean store,
                            String reasoning_effort,
                            Object metadata,
                            Integer frequency_penalty,
                            Map logit_bias,
                            Boolean logprobs,
                            Integer top_logprobs,
                            Integer max_tokens,
                            Integer max_completion_tokens,
                            Integer n,
                            JSONArray modalities,
                            Object prediction,
                            Object audio,
                            Long presence_penalty,
                            Object response_format,
                            Integer seed,
                            String service_tier,
                            String stop,
                            Boolean stream,
                            Object stream_options,
                            String temperature,
                            Integer top_p,
                            JSONArray tools,
                            String tool_choice,
                            Boolean parallel_tool_calls,
                            String user,
                            String function_call,
                            JSONArray functions,
                            String serviceKey) throws IOException {
        if (messages == null || model == null || serviceKey == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST));
            return null;
        }
        ModelService modelService = modelServiceService.selectOne(new LambdaQueryWrapper<ModelService>().eq(ModelService::getServiceKey, serviceKey));
        if (modelService == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (stream == null) {
            stream = false;
        }
        log.info(String.valueOf(temperature));
//        log.info(String.valueOf(maxNewTokens));
        List<ModelServiceApi> modelServiceApiList = modelServiceApiService.list(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceName, model).eq(ModelServiceApi::getServiceId,modelService.getId()));
        if (modelServiceApiList.size() > 1) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("服务名称重复，请修改！"));
            return null;
        }
//        ModelServiceApi modelserviceapi = modelServiceApiService.getOne(new LambdaQueryWrapper<ModelServiceApi>()
//                .eq(ModelServiceApi::getServiceName, model));
        if (modelServiceApiList.isEmpty()) {
            log.error(String.valueOf(ErrorCodeEnum.DATA_NOT_EXIST));
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
        ModelServiceApi modelServiceApi = modelServiceApiList.get(0);

        if (!modelServiceApi.getServiceType().equals(1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (modelServiceApi.getIsPublish().equals(-1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.NO_PUBLISH));
            return null;
        }
        //检测key是否正确
//        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
//        if (!modelService.getServiceKey().equals(serviceKey)) {
//            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
//            return null;
//        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startTime = modelServiceApi.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime endTime = modelServiceApi.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (startTime.isAfter(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_BEFORE_ERR));
            return null;
        }

        if (endTime.isBefore(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_AFTER_ERR));
            return null;
        }

        JSONArray dataUserIdList = new JSONArray();
        dataUserIdList.add(modelServiceApi.getDataUserId());
        JSONArray dataDeptIdList = new JSONArray();
        dataDeptIdList.add(modelServiceApi.getDataDeptId());

        // 总配额减少
        decreaseSemaphoreSum(modelServiceApi);

        //参数构建
        Map<String, Object> configJsonMap = new HashMap<>();
//        configJsonMap.put("model", model);
        configJsonMap.put("messages", messages);
        configJsonMap.put("stream", stream);
        configJsonMap.put("data_user_id_list", dataUserIdList);
        configJsonMap.put("data_dept_id_list", dataDeptIdList);
        if (store != null) {
            configJsonMap.put("store", store);
        }
        if (reasoning_effort != null) {
            configJsonMap.put("reasoning_effort", reasoning_effort);
        }
        if (metadata != null) {
            configJsonMap.put("metadata", metadata);
        }
        if (logit_bias != null) {
            configJsonMap.put("logit_bias", logit_bias);
        }
        if (logprobs != null) {
            configJsonMap.put("logprobs", logprobs);
        }
        if (top_logprobs != null) {
            configJsonMap.put("top_logprobs", top_logprobs);
        }
        if (max_completion_tokens != null) {
            configJsonMap.put("max_completion_tokens", max_completion_tokens);
        }
        if (n != null) {
            configJsonMap.put("n", n);
        }
        if (modalities != null) {
            configJsonMap.put("modalities", modalities);
        }
        if (prediction != null) {
            configJsonMap.put("prediction", prediction);
        }
        if (audio != null) {
            configJsonMap.put("audio", audio);
        }
        if (seed != null) {
            configJsonMap.put("seed", seed);
        }
        if (service_tier != null) {
            configJsonMap.put("service_tier", service_tier);
        }
        if (stop != null) {
            configJsonMap.put("stop", stop);
        }
        if (stream_options != null) {
            configJsonMap.put("stream_options", stream_options);
        }
        if (temperature != null) {
            configJsonMap.put("temperature", temperature);
        }
        if (top_p != null) {
            configJsonMap.put("top_p", top_p);
        }
        if (presence_penalty != null) {
            configJsonMap.put("presence_penalty", presence_penalty);
        }
        if (frequency_penalty != null) {
            configJsonMap.put("frequency_penalty", frequency_penalty);
        }
        if (logit_bias != null) {
            configJsonMap.put("logit_bias", logit_bias);
        }
        if (response_format != null) {
            configJsonMap.put("response_format", response_format);
        }
        if (user != null) {
            configJsonMap.put("user", user);
        }
        if (function_call != null) {
            configJsonMap.put("function_call", function_call);
        }
        if (functions != null) {
            configJsonMap.put("functions", functions);
        }
        if (tools != null) {
            configJsonMap.put("tools", tools);
        }
        if (tool_choice != null) {
            configJsonMap.put("system", tool_choice);
        }
        if (parallel_tool_calls != null) {
            configJsonMap.put("parallel_tool_calls", parallel_tool_calls);
        }
        if (max_tokens != null) {
            configJsonMap.put("max_tokens", max_tokens);
        }
        if (modelServiceApi.getServiceType() == 1) {
            Long modelId;
            if (modelServiceApi.getModelType().equals("public")) {
                modelId = modelServiceApi.getModelId();
            } else {
                modelId = modelServiceApi.getServiceTypeId();
            }
            Map<String, Object> modelConfig = ModelConfigUtil.setModelConfigDataByIdAndType(modelServiceApi.getModelType(), modelId, modelServiceApi.getId());
            Map<String, Object> textModelConfig = JSON.parseObject(JSON.toJSONString(modelConfig.get("model_config")));
            configJsonMap.put("model", textModelConfig.get("model_name"));
            configJsonMap.put("base_url", textModelConfig.get("api_base"));
            configJsonMap.put("api_key", textModelConfig.get("api_key"));
//            configJsonMap.put("base_url", "https://api.deepseek.com/v1/chat/completions");
//            configJsonMap.put("api_key", "sk-156ebaa3f76e45be93cc02a4251e16af");
            configJsonMap.put("modelId", modelId);
            configJsonMap.put("modelType", modelServiceApi.getModelType());
            configJsonMap.put("modelServiceApiId", modelServiceApi.getId());
        }
        String jsonString = JSON.toJSONString(configJsonMap);
        StringEntity entity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        log.debug("参数：" + EntityUtils.toString(entity));
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        if (stream) {
            myTaskExecutor.execute(() -> {
//                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                try {
                    String url = baseUrl + "/v1/chat/completions";
                    HttpPost post = new HttpPost(url);
                    post.setEntity(entity);
                    post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                    post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                    post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                    HttpResponse response = httpClient.execute(post);
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.trim().isEmpty()) {
                                continue;
                            }
                            Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                            if (matcher.find()) {
                                String extractedData = matcher.group(1);
                                emitter.send(" " + extractedData, MediaType.TEXT_PLAIN);
                            }
                        }
                        emitter.send(" [DONE]", MediaType.TEXT_PLAIN);
                        emitter.complete();
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                } catch (IOException e) {
                    emitter.completeWithError(e);
                }
            });
            return emitter;
        } else {
            StringBuilder content = new StringBuilder();
//            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            try {
                String url = baseUrl + "/v1/chat/completions";
                HttpPost post = new HttpPost(url);
                post.setEntity(entity);
                post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                HttpResponse response = httpClient.execute(post);
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line);
                    }
                } catch (Exception e) {
                    throw e;
                }
            } catch (Exception e) {
                throw new MyRuntimeException(e.getMessage(), e);
            }
            return JSON.parseObject(content.toString());
        }
    }

    @Override
    public Object webSearchChat(String model,
                                JSONArray messages,
                                Boolean store,
                                String reasoning_effort,
                                Object metadata,
                                Integer frequency_penalty,
                                Map logit_bias,
                                Boolean logprobs,
                                Integer top_logprobs,
                                Integer max_tokens,
                                Integer max_completion_tokens,
                                Integer n,
                                JSONArray modalities,
                                Object prediction,
                                Object audio,
                                Long presence_penalty,
                                Object response_format,
                                Integer seed,
                                String service_tier,
                                String stop,
                                Boolean stream,
                                Object stream_options,
                                String temperature,
                                Integer top_p,
                                JSONArray tools,
                                String tool_choice,
                                Boolean parallel_tool_calls,
                                String user,
                                String function_call,
                                JSONArray functions,
                                String serviceKey) throws IOException {
        if (messages == null || model == null || serviceKey == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST));
            return null;
        }
        ModelService modelService = modelServiceService.selectOne(new LambdaQueryWrapper<ModelService>().eq(ModelService::getServiceKey, serviceKey));
        if (modelService == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (stream == null) {
            stream = false;
        }
        log.info(String.valueOf(temperature));
//        log.info(String.valueOf(maxNewTokens));
        List<ModelServiceApi> modelServiceApiList = modelServiceApiService.list(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceName, model).eq(ModelServiceApi::getServiceId,modelService.getId()));
        if (modelServiceApiList.size() > 1) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("服务名称重复，请修改！"));
            return null;
        }
//        ModelServiceApi modelserviceapi = modelServiceApiService.getOne(new LambdaQueryWrapper<ModelServiceApi>()
//                .eq(ModelServiceApi::getServiceName, model));
        if (modelServiceApiList.isEmpty()) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
        ModelServiceApi modelServiceApi = modelServiceApiList.get(0);

        if (!modelServiceApi.getServiceType().equals(1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("该服务类型无法使用！"));
            return null;
        }
        if (modelServiceApi.getIsPublish().equals(-1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.NO_PUBLISH));
            return null;
        }
        //检测key是否正确
//        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
//        if (!modelService.getServiceKey().equals(serviceKey)) {
//            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error("无效的Key"));
//            return null;
//        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startTime = modelServiceApi.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime endTime = modelServiceApi.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (startTime.isAfter(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_BEFORE_ERR));
            return null;
        }

        if (endTime.isBefore(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_AFTER_ERR));
            return null;
        }

        JSONArray dataUserIdList = new JSONArray();
        dataUserIdList.add(modelServiceApi.getDataUserId());
        JSONArray dataDeptIdList = new JSONArray();
        dataDeptIdList.add(modelServiceApi.getDataDeptId());

        // 总配额减少
        decreaseSemaphoreSum(modelServiceApi);

        //参数构建
        Map<String, Object> configJsonMap = new HashMap<>();
//        configJsonMap.put("model", model);
        JSONObject jsonObject = messages.getJSONObject(messages.size() - 1);
        configJsonMap.put("message", jsonObject.get("content"));
        messages.remove(messages.size() - 1);
        configJsonMap.put("history", messages);
        configJsonMap.put("stream", stream);
        configJsonMap.put("data_user_id_list", dataUserIdList);
        configJsonMap.put("data_dept_id_list", dataDeptIdList);
        if (store != null) {
            configJsonMap.put("store", store);
        }
        if (reasoning_effort != null) {
            configJsonMap.put("reasoning_effort", reasoning_effort);
        }
        if (metadata != null) {
            configJsonMap.put("metadata", metadata);
        }
        if (logit_bias != null) {
            configJsonMap.put("logit_bias", logit_bias);
        }
        if (logprobs != null) {
            configJsonMap.put("logprobs", logprobs);
        }
        if (top_logprobs != null) {
            configJsonMap.put("top_logprobs", top_logprobs);
        }
        if (max_completion_tokens != null) {
            configJsonMap.put("max_completion_tokens", max_completion_tokens);
        }
        if (n != null) {
            configJsonMap.put("n", n);
        }
        if (modalities != null) {
            configJsonMap.put("modalities", modalities);
        }
        if (prediction != null) {
            configJsonMap.put("prediction", prediction);
        }
        if (audio != null) {
            configJsonMap.put("audio", audio);
        }
        if (seed != null) {
            configJsonMap.put("seed", seed);
        }
        if (service_tier != null) {
            configJsonMap.put("service_tier", service_tier);
        }
        if (stop != null) {
            configJsonMap.put("stop", stop);
        }
        if (stream_options != null) {
            configJsonMap.put("stream_options", stream_options);
        }
        if (temperature != null) {
            configJsonMap.put("temperature", temperature);
        }
        if (top_p != null) {
            configJsonMap.put("top_p", top_p);
        }
        if (presence_penalty != null) {
            configJsonMap.put("presence_penalty", presence_penalty);
        }
        if (frequency_penalty != null) {
            configJsonMap.put("frequency_penalty", frequency_penalty);
        }
        if (logit_bias != null) {
            configJsonMap.put("logit_bias", logit_bias);
        }
        if (response_format != null) {
            configJsonMap.put("response_format", response_format);
        }
        if (user != null) {
            configJsonMap.put("user", user);
        }
        if (function_call != null) {
            configJsonMap.put("function_call", function_call);
        }
        if (functions != null) {
            configJsonMap.put("functions", functions);
        }
        if (tools != null) {
            configJsonMap.put("tools", tools);
        }
        if (tool_choice != null) {
            configJsonMap.put("system", tool_choice);
        }
        if (parallel_tool_calls != null) {
            configJsonMap.put("parallel_tool_calls", parallel_tool_calls);
        }
        if (max_tokens != null) {
            configJsonMap.put("max_tokens", max_tokens);
        }
        if (modelServiceApi.getServiceType() == 1) {
            Long modelId;
            if (modelServiceApi.getModelType().equals("public")) {
                modelId = modelServiceApi.getModelId();
            } else {
                modelId = modelServiceApi.getServiceTypeId();
            }
            Map<String, Object> modelConfig = ModelConfigUtil.setModelConfigDataByIdAndType(modelServiceApi.getModelType(), modelId, modelServiceApi.getId());
            Map<String, Object> textModelConfig = JSON.parseObject(JSON.toJSONString(modelConfig.get("model_config")));
            configJsonMap.putAll(modelConfig);
            configJsonMap.put("model", textModelConfig.get("model_name"));
            configJsonMap.put("base_url", textModelConfig.get("api_base"));
            configJsonMap.put("api_key", textModelConfig.get("api_key"));
//            configJsonMap.put("base_url", "https://api.deepseek.com/v1/chat/completions");
//            configJsonMap.put("api_key", "sk-156ebaa3f76e45be93cc02a4251e16af");
            configJsonMap.put("modelId", modelId);
            configJsonMap.put("modelType", modelServiceApi.getModelType());
            configJsonMap.put("modelServiceApiId", modelServiceApi.getId());
        }
        String jsonString = JSON.toJSONString(configJsonMap);
        StringEntity entity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        log.debug("参数：" + EntityUtils.toString(entity));
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        if (stream) {
            myTaskExecutor.execute(() -> {
//                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                try {
                    String url = baseUrl + "/llmApp/chat/web_search_chat";
                    HttpPost post = new HttpPost(url);
                    post.setEntity(entity);
                    post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                    post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                    post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                    HttpResponse response = httpClient.execute(post);
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.trim().isEmpty()) {
                                continue;
                            }
                            Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                            if (matcher.find()) {
                                String extractedData = matcher.group(1);
                                emitter.send(" " + extractedData, MediaType.TEXT_PLAIN);
                            }
                        }
                        emitter.send(" [DONE]", MediaType.TEXT_PLAIN);
                        emitter.complete();
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                } catch (IOException e) {
                    emitter.completeWithError(e);
                }
            });
            return emitter;
        } else {
            StringBuilder content = new StringBuilder();
//            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            try {
                String url = baseUrl + "/v1/chat/completions";
                HttpPost post = new HttpPost(url);
                post.setEntity(entity);
                post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                HttpResponse response = httpClient.execute(post);
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line);
                    }
                } catch (Exception e) {
                    throw e;
                }
            } catch (Exception e) {
                throw new MyRuntimeException(e.getMessage(), e);
            }
            return JSON.parseObject(content.toString());
        }
    }

    @Override
    public Object appAssistant(String model, Boolean stream, JSONArray messages, String serviceKey) throws IOException {
        ModelService modelService = modelServiceService.selectOne(new LambdaQueryWrapper<ModelService>().eq(ModelService::getServiceKey, serviceKey));
        if (modelService == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (stream == null) {
            stream = false;
        }
//        ModelServiceApi modelServiceApi = modelServiceApiService.getByName(model);
        ModelServiceApi modelServiceApi = modelServiceApiService.selectOne(new LambdaQueryWrapper<ModelServiceApi>().eq(ModelServiceApi::getServiceName, model).eq(ModelServiceApi::getServiceId, modelService.getId()));
        if (modelServiceApi == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
        if (!modelServiceApi.getServiceType().equals(2)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (modelServiceApi.getIsPublish().equals(-1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.NO_PUBLISH));
            return null;
        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startTime = modelServiceApi.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime endTime = modelServiceApi.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (startTime.isAfter(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_BEFORE_ERR));
            return null;
        }
        if (endTime.isBefore(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_AFTER_ERR));
            return null;
        }
        AppAssistantBasic appAssistantBasic = appAssistantBasicService.getById(modelServiceApi.getServiceTypeId());
        if (appAssistantBasic == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
//        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
//        if (!modelService.getServiceKey().equals(serviceKey)) {
//            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
//            return null;
//        }

        // 总配额减少
        decreaseSemaphoreSum(modelServiceApi);

        String modelType = appAssistantBasic.getModelType();
        Long modelId = appAssistantBasic.getModelId();
        Long appId = modelServiceApi.getServiceTypeId();
        Long dataUserId = modelServiceApi.getDataUserId();
        Long dataDeptId = modelServiceApi.getDataDeptId();
        JSONObject configJson = new JSONObject();
        configJson.put("template", "m-react");
        configJson.put("file_id", "");
        configJson.put("file_name", "");
        configJson.put("login_user_id", dataUserId);
        configJson.put("app_id", String.valueOf(appId));
        configJson.put("modelId", String.valueOf(modelId));
        configJson.put("modelType", modelType);
        configJson.put("llm_app_name", appAssistantBasic.getAppName());
        List<UserPluginConfig> userPluginConfigs = userPluginConfigMapper.selectList(new LambdaQueryWrapper<UserPluginConfig>()
                .eq(UserPluginConfig::getAppId, modelServiceApi.getServiceTypeId()));
        List<String> userPluginConfigIdList = new ArrayList<>();
        for (UserPluginConfig userPluginConfig : userPluginConfigs) {
            userPluginConfigIdList.add(String.valueOf(userPluginConfig.getId()));
        }
        configJson.put("plugin_id_list", userPluginConfigIdList);
        configJson.put("llm_app_description", appAssistantBasic.getAppDesc());
        JSONObject contextData = parseContextData(messages);
        configJson.put("context_data", contextData);
        JSONObject modelConfig = new JSONObject();
        modelConfig.put("modelId", modelId);
        modelConfig.put("modelServiceApiId", modelServiceApi.getId());
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            if (modelDeployTask == null) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
                return null;
            }
            modelConfig.put("model_type", "ChatOpenAI");
            if (modelDeployTask.getDeployType().equals(2)) {
                modelConfig.put("model_name", VOLUME_MODEL);
            } else {
                modelConfig.put("model_name", modelDeployTask.getModelName());
            }
            // 设置模型地址及其密钥
            StringBuilder baseModelUrl = new StringBuilder(HTTP);
            if (null != modelDeployTask.getServerHost()) {
                baseModelUrl.append(modelDeployTask.getServerHost());
            }
            if (null != modelDeployTask.getApiServerPort()) {
                baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
            }
            baseModelUrl.append("/v1");
            modelConfig.put("api_base", baseModelUrl.toString());
            modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        } else if ("public".equals(modelType)) {
            Map<String, Object> publicLlmByModelServiceRelation = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelation == null) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
                return null;
            }
            String serviceConfiguration = publicLlmByModelServiceRelation.get("serviceConfiguration").toString();
            if (CharSequenceUtil.isNotBlank(serviceConfiguration) && JSONUtil.isTypeJSON(serviceConfiguration)) {
                Map<String, Object> serviceConfigurationMap = JSON.parseObject(serviceConfiguration, new TypeReference<Map<String, Object>>() {
                });
                modelConfig.put("model_type", publicLlmByModelServiceRelation.get("serviceProvider"));
                modelConfig.put("model_name", publicLlmByModelServiceRelation.get("modelName"));
                modelConfig.putAll(serviceConfigurationMap);
            }
        }
        configJson.put("model_config", modelConfig);
        JSONArray dataUserIdList = new JSONArray();
        dataUserIdList.add(dataUserId);
        JSONArray dataDeptIdList = new JSONArray();
        dataDeptIdList.add(dataDeptId);
        configJson.put("data_user_id_list", dataUserIdList);
        configJson.put("data_dept_id_list", dataDeptIdList);
        List<Workflow> workflowList = appAssistantWorkflowMapper.getWorkflowListByAppAssistantBasicId(appId);
        List<Map<String, Object>> workflowConfigList = new ArrayList<>();
        for (Workflow workflow : workflowList) {
            Map<String, Object> workflowConfigMap = new HashMap<>();
            workflowConfigMap.put("id", workflow.getId());
            String workflowInputParams = workflow.getWorkflowInputParams();
            if (CharSequenceUtil.isNotBlank(workflowInputParams)) {
                workflowConfigMap.put("input_params", JSONUtil.toList(workflowInputParams, Map.class));
            } else {
                workflowConfigMap.put("input_params", new ArrayList<>());
            }
            String workflowOutputParams = workflow.getWorkflowOutputParams();
            if (CharSequenceUtil.isNotBlank(workflowOutputParams)) {
                workflowConfigMap.put("output_params", JSONUtil.toList(workflowOutputParams, Map.class));
            } else {
                workflowConfigMap.put("output_params", new ArrayList<>());
            }
            workflowConfigMap.put("desc", workflow.getWorkflowDesc());
            workflowConfigList.add(workflowConfigMap);
        }
        configJson.put("workflow_config_list", workflowConfigList);
//        configJson.putAll(adapterInterfaceService.buildDataUserOrDataDeptInfo(new HashMap<>()));
        StringEntity entity = new StringEntity(configJson.toJSONString(), StandardCharsets.UTF_8);
        log.info(EntityUtils.toString(entity));
        String url = baseUrl + "/llmApp/chatOpenai";
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        if (stream) {
            myTaskExecutor.execute(() -> {
                // try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                try {
                    HttpPost post = new HttpPost(url);
                    post.setEntity(entity);
                    post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                    post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                    post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                    HttpResponse response = httpClient.execute(post);
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.trim().isEmpty()) {
                                continue;
                            }
                            Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                            if (matcher.find()) {
                                String extractedData = matcher.group(1);
                                emitter.send(" " + extractedData, MediaType.TEXT_PLAIN);
                            }
                        }
                        emitter.send(" [DONE]", MediaType.TEXT_PLAIN);
                        emitter.complete();
                    } catch (IOException e) {
                        throw new MyRuntimeException(e);
                    }
                } catch (IOException e) {
                    // 添加失败记录
                    pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                    emitter.completeWithError(e);
                }
            });
            return emitter;
        } else {
            ArrayList<JSONObject> dataList = new ArrayList<>();
            // try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            try {
                HttpPost post = new HttpPost(url);
                post.setEntity(entity);
                post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                HttpResponse response = httpClient.execute(post);
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (line.trim().isEmpty()) {
                            continue;
                        }
                        Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                        if (matcher.find()) {
                            String extractedData = matcher.group(1);
                            JSONObject jsonObject = JSON.parseObject(extractedData);
                            if (jsonObject.get("success") != null && Boolean.FALSE.equals(jsonObject.get("success"))) {
                                pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                            }
                            dataList.add(jsonObject);
                        }
                    }
                }
            } catch (IOException e) {
                // 添加失败记录
                pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                e.printStackTrace();
            }
            StringBuilder msg = new StringBuilder("");
            if (!dataList.isEmpty()) {
                for (JSONObject json : dataList.subList(1, dataList.size() - 1)) {
                    String token = json.getJSONArray("choices").getJSONObject(0).getJSONObject("delta").getString("content");
                    msg.append(token);
                }
            }
            JSONObject responseJson = new JSONObject();
            JSONObject lastJson = dataList.get(dataList.size() - 1);
            String id = lastJson.getString("id");
            String object = lastJson.getString("object");
            String created = lastJson.getString("created");
            String modelName = lastJson.getString("model");
            JSONObject usage = lastJson.getJSONObject("usage");
            responseJson.put("id", id);
            responseJson.put("model", modelName);
            responseJson.put("object", object);
            responseJson.put("created", created);
            responseJson.put("usage", usage);
            JSONObject messageJson = new JSONObject();
            messageJson.put("role", "assistant");
            messageJson.put("content", msg.toString());
            JSONObject choicesJson = new JSONObject();
            choicesJson.put("finish_reason", "stop");
            choicesJson.put("index", 0);
            choicesJson.put("message", messageJson);
            JSONArray choicesArray = new JSONArray();
            choicesArray.add(choicesJson);
            responseJson.put("choices", choicesArray);
            return responseJson;
        }
    }

    @Override
    public Object knowledgeAssistant(String model, Boolean stream, JSONArray messages, String serviceKey) throws IOException {
        ModelService modelService = modelServiceService.selectOne(new LambdaQueryWrapper<ModelService>().eq(ModelService::getServiceKey, serviceKey));
        if (modelService == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (stream == null) {
            stream = false;
        }
        List<ModelServiceApi> modelServiceApiList = modelServiceApiService.selectList(new LambdaQueryWrapper<ModelServiceApi>()
                .eq(ModelServiceApi::getServiceName, model).eq(ModelServiceApi::getServiceId,modelService.getId()).orderByDesc(ModelServiceApi::getCreateTime));
        if (modelServiceApiList == null || modelServiceApiList.isEmpty()) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
        ModelServiceApi modelServiceApi = modelServiceApiList.get(0);
        if (!modelServiceApi.getServiceType().equals(3)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
            return null;
        }
        if (modelServiceApi.getIsPublish().equals(-1)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.NO_PUBLISH));
            return null;
        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startTime = modelServiceApi.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime endTime = modelServiceApi.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (startTime.isAfter(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_BEFORE_ERR));
            return null;
        }
        if (endTime.isBefore(currentDateTime)) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(SERVICE_TIME_AFTER_ERR));
            return null;
        }
        AppKnowledgeBaseBasicInfo appKnowledgeBaseBasicInfo = appKnowledgeBaseBasicInfoService.getById(modelServiceApi.getServiceTypeId());
        if (appKnowledgeBaseBasicInfo == null) {
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        }
//        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
//        if (!modelService.getServiceKey().equals(serviceKey)) {
//            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED));
//            return null;
//        }

        // 总配额减少
        decreaseSemaphoreSum(modelServiceApi);

        String modelType = modelServiceApi.getModelType();
        Long modelId = modelServiceApi.getModelId();
        Long dataUserId = modelServiceApi.getDataUserId();
        JSONArray dataUserIdList = new JSONArray();
        dataUserIdList.add(dataUserId);
        Long dataDeptId = modelServiceApi.getDataDeptId();
        JSONArray dataDeptIdList = new JSONArray();
        dataDeptIdList.add(dataDeptId);
        JSONObject configJson = new JSONObject();
        if (appKnowledgeBaseBasicInfo.getKnowledgeConfig() != null && !appKnowledgeBaseBasicInfo.getKnowledgeConfig().isEmpty()) {
            configJson.putAll(JSON.parseObject(appKnowledgeBaseBasicInfo.getKnowledgeConfig()));
        }
        configJson.put("modelId", modelId);
        configJson.put("matching_rate", 1);
        configJson.put("search_method", 1);
        configJson.put("recall_quantity", 5);
        // 因权限问题，该位置需要设置为空（管理员权限）
//        configJson.put("data_user_id_list", dataUserIdList);
//        configJson.put("data_dept_id_list", dataDeptIdList);
        configJson.put("data_user_id_list", new ArrayList<>());
        configJson.put("data_dept_id_list", new ArrayList<>());
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(modelServiceApi.getServiceTypeId());
        configJson.put("repository_id_list", jsonArray);
        configJson.put("modelType", modelType);
        JSONObject modelConfig = modelConfig(modelType, modelId);
        modelConfig.put("modelId", modelId);
        modelConfig.put("modelServiceApiId", modelServiceApi.getId());
        if (modelConfig.isEmpty()) {
            log.error(String.valueOf(ErrorCodeEnum.DATA_NOT_EXIST));
            ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST, ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST));
            return null;
        } else {
            configJson.put("model_config", modelConfig);
        }
//        if (modelserviceapi.getServiceType() == 1) {
//            // 获取模型配置信息
//            Map<String, Object> modelConfig = ModelConfigUtil.setModelConfigDataByIdAndType(modelserviceapi.getModelType(), modelserviceapi.getModelId(), modelserviceapi.getId());
//            Map<String, Object> textModelConfig = JSON.parseObject(JSON.toJSONString(modelConfig.get("model_config")));
//            textModelConfig.put("streaming", stream);
//            configJson.put("model_config", modelConfig.get("model_config"));
//        }
        JSONObject issue = messages.getJSONObject(messages.size() - 1);
        configJson.put("issue", issue.getString("content"));
        JSONObject historyMessage = parseContextData(messages);
        historyMessage.remove("input");
        configJson.put("history_message", historyMessage.getJSONArray("chat_history"));
        // 添加对应知识库存储的相关配置
        String knowledgeConfig = appKnowledgeBaseBasicInfo.getKnowledgeConfig();
        if (knowledgeConfig != null && !knowledgeConfig.isEmpty()) {
            configJson.putAll(JSON.parseObject(knowledgeConfig));
        }
        StringEntity entity = new StringEntity(configJson.toJSONString(), StandardCharsets.UTF_8);
        log.info(EntityUtils.toString(entity));
        String url = baseUrl + "/llmApp/promptProject/knowledgeRetrievalOpenai";
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        if (stream) {
            myTaskExecutor.execute(() -> {
                // try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                try {
                    HttpPost post = new HttpPost(url);
                    post.setEntity(entity);
                    post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                    post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                    post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                    HttpResponse response = httpClient.execute(post);
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.trim().isEmpty()) {
                                continue;
                            }
                            Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                            if (matcher.find()) {
                                String extractedData = matcher.group(1);
                                emitter.send(" " + extractedData, MediaType.TEXT_PLAIN);
                            }
                        }
                        emitter.send(" [DONE]", MediaType.TEXT_PLAIN);
                        emitter.complete();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                } catch (IOException e) {
                    // 添加失败记录
                    pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                    emitter.completeWithError(e);
                }
            });
            return emitter;
        } else {
            ArrayList<JSONObject> dataList = new ArrayList<>();
            // try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            try {
                HttpPost post = new HttpPost(url);
                post.setEntity(entity);
                post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
                post.setHeader(HttpHeaders.ACCEPT, EVENT_STREAM);
                post.setHeader(HttpHeaders.AUTHORIZATION, authorizationKey);
                HttpResponse response = httpClient.execute(post);
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (line.trim().isEmpty()) {
                            continue;
                        }
                        Matcher matcher = Pattern.compile("data: (\\{.+\\})").matcher(line);
                        if (matcher.find()) {
                            String extractedData = matcher.group(1);
                            JSONObject jsonObject = JSON.parseObject(extractedData);
                            if (jsonObject.get("success") != null && Boolean.FALSE.equals(jsonObject.get("success"))) {
                                pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                            }
                            dataList.add(jsonObject);
                        }
                    }
                }
            } catch (IOException e) {
                pythonClientService.saveFailureModelToken(url, configJson.toJavaObject(Map.class));
                e.printStackTrace();
            }
            StringBuilder msg = new StringBuilder();
            for (JSONObject json : dataList.subList(1, dataList.size() - 1)) {
                String token = json.getJSONArray("choices").getJSONObject(0).getJSONObject("delta").getString("content");
                msg.append(token);
            }
            JSONObject responseJson = new JSONObject();
            JSONObject lastJson = dataList.get(dataList.size() - 1);
            String id = lastJson.getString("id");
            String object = lastJson.getString("object");
            String created = lastJson.getString("created");
            String modelName = lastJson.getString("model");
            JSONObject usage = lastJson.getJSONObject("usage");
            responseJson.put("id", id);
            responseJson.put("model", modelName);
            responseJson.put("object", object);
            responseJson.put("created", created);
            responseJson.put("usage", usage);
            JSONObject messageJson = new JSONObject();
            messageJson.put("role", "assistant");
            messageJson.put("content", msg.toString());
            JSONObject choicesJson = new JSONObject();
            choicesJson.put("finish_reason", "stop");
            choicesJson.put("index", 0);
            choicesJson.put("message", messageJson);
            JSONArray choicesArray = new JSONArray();
            choicesArray.add(choicesJson);
            responseJson.put("choices", choicesArray);
            return responseJson;
        }
    }

    /**
     * 去除Bearer前缀和空格
     * @param token
     * @return
     */
    public static String removeBearerPrefix(String token) {
        if (token == null || token.isEmpty()) {
            return token;
        }
        // Check and remove "Bearer"
        String result = token;
        if (result.startsWith("Bearer")) {
            result = result.substring("Bearer".length());
        }
        // Trim leading spaces
        result = result.trim();
        return result;
    }

    @Override
    public ResponseResult<String> workflow(MultipartHttpServletRequest request) {
        // 获取请求头
        String serviceKey = request.getHeader("Authorization");
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (StringUtils.isBlank(parameterMap.get("serviceName")[0])) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        // 规定死使用 serviceName ，不使用 serviceId！！！
        String serviceName = String.valueOf(parameterMap.get("serviceName")[0]); // 规定死使用 serviceName ，不使用 serviceId！！！
        parameterMap.remove("serviceName");
        ModelServiceApi modelServiceApi = modelServiceApiService.getByName(serviceName);;
        if (modelServiceApi == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        if (!modelServiceApi.getServiceType().equals(4)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED);
        }
        if (modelServiceApi.getIsPublish().equals(-1)) {
            return ResponseResult.error(ErrorCodeEnum.NO_PUBLISH);
        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startTime = modelServiceApi.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime endTime = modelServiceApi.getEndTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (startTime.isAfter(currentDateTime)) {
            throw new IllegalArgumentException(SERVICE_TIME_BEFORE_ERR);
        }
        if (endTime.isBefore(currentDateTime)) {
            throw new IllegalArgumentException(SERVICE_TIME_AFTER_ERR);
        }
        Workflow workflow = workflowService.getById(modelServiceApi.getServiceTypeId());
        if (workflow == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ModelService modelService = modelServiceService.getById(modelServiceApi.getServiceId());
        if (!removeBearerPrefix(modelService.getServiceKey()).equals(removeBearerPrefix(serviceKey))) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED);
        }

        // 总配额减少
        decreaseSemaphoreSum(modelServiceApi);

        SysUser user = sysUserService.getById(modelService.getCreateUserId());

        //获取非文件的请求参数
        Map<String, Object> paramsMap = new HashMap<>();
        for (String paramKey : parameterMap.keySet()) {
            String[] values = parameterMap.get(paramKey);
            if (values.length == 1) {
                paramsMap.put(paramKey, workflowService.convertToAppropriateType(values[0]));
            } else {
                paramsMap.put(paramKey, values);
            }
        }

        // 获取文件并上传至Minio
        Set<String> fileKeySet = request.getFileMap().keySet();
        if (fileKeySet != null && !fileKeySet.isEmpty()) {
            workflowService.saveFile(request, fileKeySet, paramsMap);
        }

        WorkflowLog workflowLog = workflowService.runWorkFlow(user, workflow, paramsMap);
        String outputParams = workflowLog.getOutputParams();
        return ResponseResult.success(outputParams);
    }

    @Override
    public void fileDownload(Long fileId, HttpServletResponse response) throws IOException {
        if (MyCommonUtil.existBlankArgument(fileId)) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        DataResources dataResources = dataResourcesService.getById(fileId);
        if (dataResources == null) {
            ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String fileJson = dataResources.getFileJson();
        JSONObject jsonObject = JSON.parseObject(fileJson);
        String fieldName = jsonObject.getString("fieldName");
        if (StringUtils.isBlank(fieldName)) {
            ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String fileName = jsonObject.getString("filename");
        if (StringUtils.isBlank(fileName)) {
            ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(DataResources.class, fieldName);
        if (!storeInfo.isSupportUpload()) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        InputStream inputStream = upDownloader.downloadInternalInputStream(appConfig.getUploadFileBaseDir(),
                DataResources.class.getSimpleName(), fieldName, fileName, false);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        IoUtil.copy(inputStream, response.getOutputStream());
        inputStream.close();
    }

    private JSONObject parseContextData(JSONArray jsonArray) {
        JSONArray chatHistory = new JSONArray();
        String lastUserInput = "";
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject message = jsonArray.getJSONObject(i);
            String role = message.getString("role");
            String content = message.getString("content");

            if ("user".equals(role)) {
                lastUserInput = content;
                if (i < jsonArray.size() - 1) {
                    chatHistory.add(role + ":" + content);
                }
            } else {
                chatHistory.add(role + ":" + content.replace("\n", "\\n"));
            }
        }
        JSONObject contextData = new JSONObject();
        contextData.put("input", lastUserInput);
        contextData.put("chat_history", chatHistory);
        return contextData;
    }

    private JSONObject modelConfig(String modelType, Long modelId) {
        JSONObject modelConfig = new JSONObject();
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            if (modelDeployTask == null) {
                return modelConfig;
            }
            modelConfig.put("model_type", "ChatOpenAI");
            if (modelDeployTask.getDeployType().equals(2)) {
                modelConfig.put("model_name", VOLUME_MODEL);
            } else {
                modelConfig.put("model_name", modelDeployTask.getModelName());
            }
            // 设置模型地址及其密钥
            StringBuilder baseModelUrl = new StringBuilder(HTTP);
            if (null != modelDeployTask.getServerHost()) {
                baseModelUrl.append(modelDeployTask.getServerHost());
            }
            if (null != modelDeployTask.getApiServerPort()) {
                baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
            }
            baseModelUrl.append("/v1");
            modelConfig.put("api_base", baseModelUrl.toString());
            modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        } else if ("public".equals(modelType)) {
            Map<String, Object> publicLlmByModelServiceRelation = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelation == null) {
                return modelConfig;
            }
            String serviceConfiguration = publicLlmByModelServiceRelation.get("serviceConfiguration").toString();
            if (CharSequenceUtil.isNotBlank(serviceConfiguration) && JSONUtil.isTypeJSON(serviceConfiguration)) {
                Map<String, Object> serviceConfigurationMap = JSON.parseObject(serviceConfiguration, new TypeReference<Map<String, Object>>() {
                });
                modelConfig.put("model_type", publicLlmByModelServiceRelation.get("serviceProvider"));
                modelConfig.put("model_name", publicLlmByModelServiceRelation.get("modelName"));
                modelConfig.putAll(serviceConfigurationMap);
            }
        }
        return modelConfig;
    }

    /**
     * 减少模型服务的总配额
     *
     * @param modelServiceApi 模型服务API对象
     */
    private void decreaseSemaphoreSum(ModelServiceApi modelServiceApi) {
        String sumKey = ServiceConstant.SUM_KEY + modelServiceApi.getServiceId() + ":" + modelServiceApi.getServiceName();
        if (modelServiceApi.getSemaphoreSum() < 0) {
            RAtomicLong atomicLongSum = redissonClient.getAtomicLong(sumKey);
            // 记录调用次数
            Long currentSum = atomicLongSum.incrementAndGet();
            log.info("模型：{} 调用总次数是 {}", modelServiceApi.getServiceName(), currentSum);
            return;
        }

        // 减少总配额 确保根据主键或其他唯一条件定位记录
        LambdaUpdateWrapper<ModelServiceApi> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                // 指定更新条件（例如根据主键ID）
                .eq(ModelServiceApi::getId, modelServiceApi.getId())
                .gt(ModelServiceApi::getSemaphoreSum, 0)
                // 使用原子操作减少semaphoreSum，避免并发问题
                .setSql("semaphore_sum = semaphore_sum - 1, cur_invocation_sum = cur_invocation_sum + 1");
        if (modelServiceApiMapper.update(updateWrapper) == 0) {
            throw new MyRuntimeException("推广总配额已经消耗尽！无法调用该模型服务");
        }
        RAtomicLong atomicLongSum = redissonClient.getAtomicLong(sumKey);
        // 记录调用次数
        Long currentSum = atomicLongSum.incrementAndGet();
        log.info("模型：{} 调用总次数是 {}", modelServiceApi.getServiceName(), currentSum);
    }
}
