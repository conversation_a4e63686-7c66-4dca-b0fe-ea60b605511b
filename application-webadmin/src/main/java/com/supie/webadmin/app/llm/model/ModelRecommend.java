package com.supie.webadmin.app.llm.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.webadmin.app.llm.vo.ModelRecommendVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * ModelRecommend实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_model_recommend")
public class ModelRecommend extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模型名称。
     */
    private String modelName;

    /**
     * 场景名称。
     */
    private String usageName;

    /**
     * 自定义分数。
     */
    private Double recommendScore;

    /**
     * 字符串ID。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * recommendScore 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Double recommendScoreStart;

    /**
     * recommendScore 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Double recommendScoreEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * model_name / usage_name LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    @Mapper
    public interface ModelRecommendModelMapper extends BaseModelMapper<ModelRecommendVo, ModelRecommend> {
    }
    public static final ModelRecommendModelMapper INSTANCE = Mappers.getMapper(ModelRecommendModelMapper.class);
}
