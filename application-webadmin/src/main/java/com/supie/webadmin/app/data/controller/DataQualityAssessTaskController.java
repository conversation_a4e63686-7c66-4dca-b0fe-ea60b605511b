package com.supie.webadmin.app.data.controller;

import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.data.dto.DataQualityAssessTaskDto;
import com.supie.webadmin.app.data.model.DataQualityAssessTask;
import com.supie.webadmin.app.data.service.DataQualityAssessTaskService;
import com.supie.webadmin.app.data.vo.DataQualityAssessTaskVo;
import com.supie.webadmin.interceptor.PyAuthInterface;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数据管理-数据质量评估任务表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "数据管理-数据质量评估任务表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/dataQualityAssessTask")
public class DataQualityAssessTaskController {

    @Autowired
    private DataQualityAssessTaskService dataQualityAssessTaskService;

    /**
     * 新增数据管理-数据质量评估任务表数据。
     *
     * @param dataQualityAssessTaskDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataQualityAssessTaskDto.id",
            "dataQualityAssessTaskDto.searchString",
            "dataQualityAssessTaskDto.createTimeStart",
            "dataQualityAssessTaskDto.createTimeEnd",
            "dataQualityAssessTaskDto.updateTimeStart",
            "dataQualityAssessTaskDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DataQualityAssessTaskDto dataQualityAssessTaskDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataQualityAssessTaskDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataQualityAssessTask dataQualityAssessTask = MyModelUtil.copyTo(dataQualityAssessTaskDto, DataQualityAssessTask.class);
        dataQualityAssessTask = dataQualityAssessTaskService.saveNew(dataQualityAssessTask);
        return ResponseResult.success(dataQualityAssessTask.getId());
    }

    /**
     * 更新数据管理-数据质量评估任务表数据。
     *
     * @param dataQualityAssessTaskDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataQualityAssessTaskDto.searchString",
            "dataQualityAssessTaskDto.createTimeStart",
            "dataQualityAssessTaskDto.createTimeEnd",
            "dataQualityAssessTaskDto.updateTimeStart",
            "dataQualityAssessTaskDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DataQualityAssessTaskDto dataQualityAssessTaskDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataQualityAssessTaskDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataQualityAssessTask dataQualityAssessTask = MyModelUtil.copyTo(dataQualityAssessTaskDto, DataQualityAssessTask.class);
        DataQualityAssessTask originalDataQualityAssessTask = dataQualityAssessTaskService.getById(dataQualityAssessTask.getId());
        if (originalDataQualityAssessTask == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataQualityAssessTaskService.update(dataQualityAssessTask, originalDataQualityAssessTask)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 更新数据管理-数据质量评估任务表数据。
     *
     * @param dataQualityAssessTaskDto 更新对象。
     * @return 应答结果对象。
     */
    @Operation(summary = "Python端-修改数据质量评估任务表")
    @ApiOperationSupport(ignoreParameters = {
            "dataQualityAssessTaskDto.searchString",
            "dataQualityAssessTaskDto.createTimeStart",
            "dataQualityAssessTaskDto.createTimeEnd",
            "dataQualityAssessTaskDto.updateTimeStart",
            "dataQualityAssessTaskDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/py/update")
    @PyAuthInterface
    public ResponseResult<Void> updateOfPy(@MyRequestBody DataQualityAssessTaskDto dataQualityAssessTaskDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataQualityAssessTaskDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataQualityAssessTask dataQualityAssessTask = MyModelUtil.copyTo(dataQualityAssessTaskDto, DataQualityAssessTask.class);
        DataQualityAssessTask originalDataQualityAssessTask = dataQualityAssessTaskService.getById(dataQualityAssessTask.getId());
        if (originalDataQualityAssessTask == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataQualityAssessTaskService.updateOfPy(dataQualityAssessTask, originalDataQualityAssessTask)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除数据管理-数据质量评估任务表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的数据管理-数据质量评估任务表列表。
     *
     * @param dataQualityAssessTaskDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<DataQualityAssessTaskVo>> list(
            @MyRequestBody DataQualityAssessTaskDto dataQualityAssessTaskDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam,
            @MyRequestBody Boolean hasKMeansJson) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (hasKMeansJson == null) {
            hasKMeansJson = true;
        }
        DataQualityAssessTask dataQualityAssessTaskFilter = MyModelUtil.copyTo(dataQualityAssessTaskDtoFilter, DataQualityAssessTask.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DataQualityAssessTask.class);
//        Date startDate = new Date();
        List<DataQualityAssessTask> dataQualityAssessTaskList =
                dataQualityAssessTaskService.getDataQualityAssessTaskListWithRelation(dataQualityAssessTaskFilter, orderBy, hasKMeansJson);
        // 总耗时（xx秒xx毫秒）
//        log.error("==========> 查询耗时：{}毫秒! <==========", new Date().getTime() - startDate.getTime());
        return ResponseResult.success(MyPageUtil.makeResponseData(dataQualityAssessTaskList, DataQualityAssessTask.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的数据管理-数据质量评估任务表列表。
     *
     * @param dataQualityAssessTaskDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<DataQualityAssessTaskVo>> listWithGroup(
            @MyRequestBody DataQualityAssessTaskDto dataQualityAssessTaskDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DataQualityAssessTask.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, DataQualityAssessTask.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        DataQualityAssessTask filter = MyModelUtil.copyTo(dataQualityAssessTaskDtoFilter, DataQualityAssessTask.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<DataQualityAssessTask> resultList = dataQualityAssessTaskService.getGroupedDataQualityAssessTaskListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, DataQualityAssessTask.INSTANCE));
    }

    /**
     * 查看指定数据管理-数据质量评估任务表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<DataQualityAssessTaskVo> view(@RequestParam Long id) {
        DataQualityAssessTask dataQualityAssessTask = dataQualityAssessTaskService.getByIdWithRelation(id, MyRelationParam.full());
        if (dataQualityAssessTask == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        DataQualityAssessTaskVo dataQualityAssessTaskVo = DataQualityAssessTask.INSTANCE.fromModel(dataQualityAssessTask);
        return ResponseResult.success(dataQualityAssessTaskVo);
    }

    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DataQualityAssessTask originalDataQualityAssessTask = dataQualityAssessTaskService.getById(id);
        if (originalDataQualityAssessTask == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataQualityAssessTaskService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 获取相关的文件的大小
     */
    @Operation(summary = "获取相关的文件的大小")
    @PostMapping("/getRelatedFileSize")
    public ResponseResult<BigDecimal> getRelatedFileSize(@MyRequestBody Long assessTaskId) {
        if (MyCommonUtil.existBlankArgument(assessTaskId)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        DataQualityAssessTask assessTask = dataQualityAssessTaskService.getById(assessTaskId);
        if (assessTask == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(dataQualityAssessTaskService.getRelatedFileSize(assessTask));
    }

}
