<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.llmTrain.dao.TrainTaskMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.llmTrain.model.TrainTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="train_task_type" jdbcType="VARCHAR" property="trainTaskType"/>
        <result column="train_task_state" jdbcType="VARCHAR" property="trainTaskState"/>
        <result column="train_task_describe" jdbcType="VARCHAR" property="trainTaskDescribe"/>
        <result column="train_task_config_json" jdbcType="LONGVARCHAR" property="trainTaskConfigJson"/>
        <result column="deepspeed_config_json" jdbcType="LONGVARCHAR" property="deepspeedConfigJson"/>
        <result column="common_parameter_json" jdbcType="LONGVARCHAR" property="commonParameterJson"/>
        <result column="weights_merged_config_json" jdbcType="LONGVARCHAR" property="weightsMergedConfigJson"/>
        <result column="train_task_method" jdbcType="VARCHAR" property="trainTaskMethod"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="train_info_id" jdbcType="BIGINT" property="trainInfoId"/>
        <result column="yaml_config" jdbcType="LONGVARCHAR" property="yamlConfig"/>
        <result column="experment_plan_id" jdbcType="BIGINT" property="expermentPlanId"/>
        <result column="base_model_id" jdbcType="BIGINT" property="baseModelId"/>
        <result column="train_task_id" jdbcType="BIGINT" property="trainTaskId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="model_code" jdbcType="VARCHAR" property="modelCode"/>
        <result column="edition_status" jdbcType="VARCHAR" property="editionStatus"/>
        <result column="graphic_number" jdbcType="INTEGER" property="graphicNumber"/>
        <result column="train_framework" jdbcType="INTEGER" property="trainFramework"/>
        <result column="merge_id" jdbcType="BIGINT" property="mergeId"/>
        <result column="time_periods" jdbcType="VARCHAR" property="timePeriods"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_train_task
            (id,
            str_id,
            train_task_type,
            train_task_state,
            train_task_describe,
            train_task_config_json,
            deepspeed_config_json,
            common_parameter_json,
            weights_merged_config_json,
            train_task_method,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            train_info_id,
            yaml_config,
            experment_plan_id,
            base_model_id,
            train_task_id,
            model_name,
            model_code,
            edition_status,
            graphic_number,
            train_framework,
            merge_id,
            time_periods)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.trainTaskType},
            #{item.trainTaskState},
            #{item.trainTaskDescribe},
            #{item.trainTaskConfigJson},
            #{item.deepspeedConfigJson},
            #{item.commonParameterJson},
            #{item.weightsMergedConfigJson},
            #{item.trainTaskMethod},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.trainInfoId},
            #{item.yamlConfig},
            #{item.expermentPlanId},
            #{item.baseModelId},
            #{item.trainTaskId},
            #{item.modelName},
            #{item.modelCode},
            #{item.editionStatus},
            #{item.graphicNumber},
            #{item.trainFramework},
            #{item.mergeId},
            #{item.timePeriods})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.llmTrain.dao.TrainTaskMapper.inputFilterRef"/>
        AND lmd_train_task.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="trainTaskFilter != null">
            <if test="trainTaskFilter.id != null">
                AND lmd_train_task.id = #{trainTaskFilter.id}
            </if>
            <if test="trainTaskFilter.strId != null and trainTaskFilter.strId != ''">
                AND lmd_train_task.str_id = #{trainTaskFilter.strId}
            </if>
            <if test="trainTaskFilter.trainTaskType != null and trainTaskFilter.trainTaskType != ''">
                <bind name = "safeTrainTaskTrainTaskType" value = "'%' + trainTaskFilter.trainTaskType + '%'" />
                AND lmd_train_task.train_task_type LIKE #{safeTrainTaskTrainTaskType}
            </if>
            <if test="trainTaskFilter.trainTaskState != null and trainTaskFilter.trainTaskState != ''">
                AND lmd_train_task.train_task_state = #{trainTaskFilter.trainTaskState}
            </if>
            <if test="trainTaskFilter.trainTaskDescribe != null and trainTaskFilter.trainTaskDescribe != ''">
                <bind name = "safeTrainTaskTrainTaskDescribe" value = "'%' + trainTaskFilter.trainTaskDescribe + '%'" />
                AND lmd_train_task.train_task_describe LIKE #{safeTrainTaskTrainTaskDescribe}
            </if>
            <if test="trainTaskFilter.trainTaskConfigJson != null and trainTaskFilter.trainTaskConfigJson != ''">
                <bind name = "safeTrainTaskTrainTaskConfigJson" value = "'%' + trainTaskFilter.trainTaskConfigJson + '%'" />
                AND lmd_train_task.train_task_config_json LIKE #{safeTrainTaskTrainTaskConfigJson}
            </if>
            <if test="trainTaskFilter.deepspeedConfigJson != null and trainTaskFilter.deepspeedConfigJson != ''">
                <bind name = "safeTrainTaskDeepspeedConfigJson" value = "'%' + trainTaskFilter.deepspeedConfigJson + '%'" />
                AND lmd_train_task.deepspeed_config_json LIKE #{safeTrainTaskDeepspeedConfigJson}
            </if>
            <if test="trainTaskFilter.commonParameterJson != null and trainTaskFilter.commonParameterJson != ''">
                <bind name = "safeTrainTaskCommonParameterJson" value = "'%' + trainTaskFilter.commonParameterJson + '%'" />
                AND lmd_train_task.common_parameter_json LIKE #{safeTrainTaskCommonParameterJson}
            </if>
            <if test="trainTaskFilter.weightsMergedConfigJson != null and trainTaskFilter.weightsMergedConfigJson != ''">
                <bind name = "safeTrainTaskWeightsMergedConfigJson" value = "'%' + trainTaskFilter.weightsMergedConfigJson + '%'" />
                AND lmd_train_task.weights_merged_config_json LIKE #{safeTrainTaskWeightsMergedConfigJson}
            </if>
            <if test="trainTaskFilter.trainTaskMethod != null and trainTaskFilter.trainTaskMethod != ''">
                <bind name = "safeTrainTaskTrainTaskMethod" value = "'%' + trainTaskFilter.trainTaskMethod + '%'" />
                AND lmd_train_task.train_task_method LIKE #{safeTrainTaskTrainTaskMethod}
            </if>
            <if test="trainTaskFilter.yamlConfig != null and trainTaskFilter.yamlConfig != ''">
                <bind name = "safeTrainTaskYamlConfig" value = "'%' + trainTaskFilter.yamlConfig + '%'" />
                AND lmd_train_task.yaml_config LIKE #{safeTrainTaskYamlConfig}
            </if>
            <if test="trainTaskFilter.expermentPlanId != null">
                AND lmd_train_task.experment_plan_id = #{trainTaskFilter.expermentPlanId}
            </if>
            <if test="trainTaskFilter.baseModelId != null">
                AND lmd_train_task.base_model_id = #{trainTaskFilter.baseModelId}
            </if>
            <if test="trainTaskFilter.trainTaskId != null">
                AND lmd_train_task.train_task_id = #{trainTaskFilter.trainTaskId}
            </if>
            <if test="trainTaskFilter.modelName != null and trainTaskFilter.modelName != ''">
                AND lmd_train_task.model_name = #{trainTaskFilter.modelName}
            </if>
            <if test="trainTaskFilter.modelCode != null and trainTaskFilter.modelCode != ''">
                AND lmd_train_task.model_code = #{trainTaskFilter.modelCode}
            </if>
            <if test="trainTaskFilter.editionStatus != null and trainTaskFilter.editionStatus != ''">
                AND lmd_train_task.edition_status = #{trainTaskFilter.editionStatus}
            </if>
            <if test="trainTaskFilter.graphicNumber != null and trainTaskFilter.graphicNumber != ''">
                AND lmd_train_task.graphic_number = #{trainTaskFilter.graphicNumber}
            </if>
            <if test="trainTaskFilter.trainFramework != null and trainTaskFilter.trainFramework != ''">
                AND lmd_train_task.train_framework = #{trainTaskFilter.trainFramework}
            </if>
            <if test="trainTaskFilter.mergeId != null">
                AND lmd_train_task.merge_id = #{trainTaskFilter.mergeId}
            </if>
            <if test="trainTaskFilter.isDelete != null">
                AND lmd_train_task.is_delete = #{trainTaskFilter.isDelete}
            </if>
            <if test="trainTaskFilter.createTime != null">
                AND lmd_train_task.create_time = #{trainTaskFilter.createTime}
            </if>
            <if test="trainTaskFilter.createUserId != null">
                AND lmd_train_task.create_user_id = #{trainTaskFilter.createUserId}
            </if>
            <if test="trainTaskFilter.updateTime != null">
                AND lmd_train_task.update_time = #{trainTaskFilter.updateTime}
            </if>
            <if test="trainTaskFilter.updateUserId != null">
                AND lmd_train_task.update_user_id = #{trainTaskFilter.updateUserId}
            </if>
            <if test="trainTaskFilter.dataUserId != null">
                AND lmd_train_task.data_user_id = #{trainTaskFilter.dataUserId}
            </if>
            <if test="trainTaskFilter.dataDeptId != null">
                AND lmd_train_task.data_dept_id = #{trainTaskFilter.dataDeptId}
            </if>
            <if test="trainTaskFilter.trainInfoId != null and trainTaskFilter.trainInfoId != ''">
                AND lmd_train_task.train_info_id = #{trainTaskFilter.trainInfoId}
            </if>
            <if test="trainTaskFilter.timePeriods != null and trainTaskFilter.timePeriods != ''">
                AND lmd_train_task.time_periods = #{trainTaskFilter.timePeriods}
            </if>
            <if test="trainTaskFilter.searchString != null and trainTaskFilter.searchString != ''">
                <bind name = "safeTrainTaskSearchString" value = "'%' + trainTaskFilter.searchString + '%'" />
                AND CONCAT(
                    IFNULL(lmd_train_task.train_task_name,''),
                    IFNULL(lmd_train_task.train_task_type,''),
                    IFNULL(lmd_train_task.train_task_state,''),
                    IFNULL(lmd_train_task.train_task_describe,''),
                    IFNULL(lmd_train_task.train_task_method,''),
                    IFNULL(lmd_train_task.model_name,''),
                    IFNULL(lmd_train_task.model_code,'')
                ) LIKE #{safeTrainTaskSearchString}
            </if>

            <if test="trainTaskFilter.createTimeStart != null and trainTaskFilter.createTimeStart != ''">
                AND lmd_train_task.create_time &gt;= #{trainTaskFilter.createTimeStart}
            </if>
            <if test="trainTaskFilter.createTimeEnd != null and trainTaskFilter.createTimeEnd != ''">
                AND lmd_train_task.create_time &lt;= #{trainTaskFilter.createTimeEnd}
            </if>
            <if test="trainTaskFilter.updateTimeStart != null and trainTaskFilter.updateTimeStart != ''">
                AND lmd_train_task.update_time &gt;= #{trainTaskFilter.updateTimeStart}
            </if>
            <if test="trainTaskFilter.updateTimeEnd != null and trainTaskFilter.updateTimeEnd != ''">
                AND lmd_train_task.update_time &lt;= #{trainTaskFilter.updateTimeEnd}
            </if>

            <if test="trainTaskFilter.trainStartTimeStart != null and trainTaskFilter.trainStartTimeStart != ''">
                AND lmd_train_task.train_start_time &gt;= #{trainTaskFilter.trainStartTimeStart}
            </if>
            <if test="trainTaskFilter.trainStartTimeEnd != null and trainTaskFilter.trainStartTimeEnd != ''">
                AND lmd_train_task.train_start_time &lt;= #{trainTaskFilter.trainStartTimeEnd}
            </if>
            <if test="trainTaskFilter.trainEndTimeStart != null and trainTaskFilter.trainEndTimeStart != ''">
                AND lmd_train_task.train_end_time &gt;= #{trainTaskFilter.trainEndTimeStart}
            </if>
            <if test="trainTaskFilter.trainEndTimeEnd != null and trainTaskFilter.trainEndTimeEnd != ''">
                AND lmd_train_task.train_end_time &lt;= #{trainTaskFilter.trainEndTimeEnd}
            </if>
        </if>
    </sql>

    <select id="getGroupedTrainTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.llmTrain.model.TrainTask">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_train_task
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_train_task
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getTrainTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.llmTrain.model.TrainTask">
        SELECT * FROM lmd_train_task
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getTrainTask" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.llmTrain.model.TrainTask">
        SELECT * FROM lmd_train_task
        where train_task_state in ("训练中","发布中")
    </select>

    <update id="updateTrainTaskState">
        UPDATE lmd_train_task
        <set>
            train_task_state = #{trainTaskState},
            update_time = now(),
            <if test="trainTaskState != null and trainTaskState == '训练中'">
                train_start_time = now(),
                train_end_time = null
            </if>
            <if test="trainTaskState != null and (trainTaskState == '已完成' or trainTaskState == '失败' or trainTaskState == '暂停' or trainTaskState == '终止')">
                train_end_time = now()
            </if>
        </set>
        WHERE id IN (
            <foreach collection="trainTaskIdList" index="index" item="item" separator="," >
                #{item}
            </foreach>
        ) AND lmd_train_task.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </update>
</mapper>
