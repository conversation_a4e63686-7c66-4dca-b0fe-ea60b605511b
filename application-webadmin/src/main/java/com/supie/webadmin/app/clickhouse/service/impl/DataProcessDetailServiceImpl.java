package com.supie.webadmin.app.clickhouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.clickhouse.dao.DataProcessDetailMapper;
import com.supie.webadmin.app.clickhouse.model.DataProcessDetail;
import com.supie.webadmin.app.clickhouse.service.DataProcessDetailService;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据管理-数据处理详情数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Slf4j
@MyDataSource(DataSourceType.CLICKHOME)
@Service("dataProcessDetailService")
public class DataProcessDetailServiceImpl extends BaseService<DataProcessDetail, Long> implements DataProcessDetailService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private DataProcessDetailMapper dataProcessDetailMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DataProcessDetail> mapper() {
        return dataProcessDetailMapper;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataProcessDetail saveNew(DataProcessDetail dataProcessDetail) {
        dataProcessDetailMapper.insert(this.buildDefaultValue(dataProcessDetail));
        return dataProcessDetail;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DataProcessDetail> dataProcessDetailList) {
        if (CollUtil.isNotEmpty(dataProcessDetailList)) {
            dataProcessDetailList.forEach(this::buildDefaultValue);
            dataProcessDetailMapper.insertList(dataProcessDetailList);
        }
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(DataProcessDetail dataProcessDetail, DataProcessDetail originalDataProcessDetail) {
        MyModelUtil.fillCommonsForUpdate(dataProcessDetail, originalDataProcessDetail);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<DataProcessDetail> uw = this.createUpdateQueryForNullValue(dataProcessDetail, dataProcessDetail.getId());
        return dataProcessDetailMapper.update(dataProcessDetail, uw) == 1;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return dataProcessDetailMapper.deleteById(id) == 1;
    }

    @Override
    public List<DataProcessDetail> getDataProcessDetailList(DataProcessDetail filter, String orderBy) {
        return dataProcessDetailMapper.getDataProcessDetailList(filter, orderBy);
    }

    @Override
    public List<DataProcessDetail> getDataProcessDetailListWithRelation(DataProcessDetail filter, String orderBy) {
        List<DataProcessDetail> resultList = dataProcessDetailMapper.getDataProcessDetailList(filter, orderBy);
//        MyPageData<DataProcessDe tailVo> dataProcessDetailVoMyPageData = MyPageUtil.makeResponseData(resultList, DataProcessDetailVo.class);

        Integer processType = filter.getProcessType();
        switch (processType) {
            // 清洗数据
            case 2:
                resultList.removeIf(item -> item.getProcessConfig() == null ||
                        item.getProcessConfig().isEmpty() ||
                        item.getProcessConfig().equals("{}") ||
                        item.getProcessConfig().equals("[]"));
                break;
            // 过滤数据
            case 3:
                break;
            // 去重数据
            case 4:
                break;
            // 去隐私数据
            case 5:
                try {
                    resultList.removeIf(item -> item.getProcessConfig() == null ||
                            item.getProcessConfig().isEmpty() ||
                            item.getProcessConfig().equals("{}") ||
                            item.getProcessConfig().equals("[]"));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                break;
            // 自定义规则数据
            case 6:
                resultList.removeIf(item -> item.getProcessConfig() == null ||
                        item.getProcessConfig().isEmpty() ||
                        item.getProcessConfig().equals("{}") ||
                        item.getProcessConfig().equals("[]"));
                break;
        }

        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public BaseDaoMapper<DataProcessDetail> getBaseMapper() {
        return super.getBaseMapper();
    }

    @Override
    public List<String> selectOriginalContentByIndex(Long dataProcessTaskId) {
        return dataProcessDetailMapper.selectOriginalContentByIndex(dataProcessTaskId);
    }

    @Override
    public List<String> selectOriginalContentByIndexFormMaxProcessOrder(Long dataProcessTaskId, Integer maxProcessOrder) {
        return dataProcessDetailMapper.selectOriginalContentByIndexFormMaxProcessOrder(dataProcessTaskId, maxProcessOrder);
    }

    /**
     * 获取processType最大处理顺序
     * @param dataProcessTaskId
     * @return
     */
    @Override
    public Integer getMaxProcessOrder(Long dataProcessTaskId) {
        return dataProcessDetailMapper.getMaxProcessOrder(dataProcessTaskId);
    }

    /**
     * 获取processType最大处理顺序
     * @param dataProcessTaskId
     * @return
     */
    @Override
    public Integer getMaxOrder(Long dataProcessTaskId) {
        return dataProcessDetailMapper.getMaxOrder(dataProcessTaskId);
    }

    private DataProcessDetail buildDefaultValue(DataProcessDetail dataProcessDetail) {
        if (dataProcessDetail.getId() == null) {
            dataProcessDetail.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(dataProcessDetail);
        dataProcessDetail.setIsDelete(GlobalDeletedFlag.NORMAL);
        return dataProcessDetail;
    }
}
