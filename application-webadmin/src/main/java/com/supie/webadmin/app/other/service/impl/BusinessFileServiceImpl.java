package com.supie.webadmin.app.other.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.ResponseResult;
import com.supie.common.core.upload.BaseUpDownloader;
import com.supie.common.core.upload.UpDownloaderFactory;
import com.supie.common.core.upload.UploadResponseInfo;
import com.supie.common.core.upload.UploadStoreInfo;
import com.supie.webadmin.app.dataSync.annotation.DataSync;
import com.supie.webadmin.app.other.dao.BusinessFileMapper;
import com.supie.webadmin.app.other.model.BusinessFile;
import com.supie.webadmin.app.other.service.BusinessFileService;
import com.github.pagehelper.Page;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.dataSync.annotation.DataSync;
import com.supie.webadmin.app.other.dao.BusinessFileMapper;
import com.supie.webadmin.app.other.dao.FileDescribeMapper;
import com.supie.webadmin.app.other.model.BusinessFile;
import com.supie.webadmin.app.other.model.FileDescribe;
import com.supie.webadmin.app.other.service.BusinessFileService;
import com.supie.webadmin.app.removeFromMinio.RemoveFromMinio;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.config.DataSourceType;
import com.supie.webadmin.train.service.PythonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 业务附件表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("businessFileService")
@MyDataSource(DataSourceType.MAIN)
public class BusinessFileServiceImpl extends BaseService<BusinessFile, Long> implements BusinessFileService {

    @Autowired
    private BusinessFileMapper businessFileMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private ApplicationConfig appConfig;
    @Autowired
    private UpDownloaderFactory upDownloaderFactory;
    @Lazy
    @Autowired
    private PythonService pythonService;
    @Autowired
    private FileDescribeServiceImpl fileDescribeService;
    @Autowired
    private FileDescribeMapper fileDescribeMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<BusinessFile> mapper() {
        return businessFileMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param businessFile 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "BusinessFile", method = "add", idKey = "id", parentId = "1000002", nameKey = "fileName")
    public BusinessFile saveNew(BusinessFile businessFile) {
        businessFile.setDataDeptId(1787740798154969088L);
        businessFileMapper.insert(this.buildDefaultValue(businessFile));
        return businessFile;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "BusinessFile", method = "add", idKey = "id", parentId = "1000002", nameKey = "fileName")
    public Integer noAuthSaveNew(BusinessFile businessFile) {
        return businessFileMapper.insert(businessFile);
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param businessFileList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<BusinessFile> businessFileList) {
        if (CollUtil.isNotEmpty(businessFileList)) {
            businessFileList.forEach(this::buildDefaultValue);
            businessFileMapper.insertList(businessFileList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param businessFile         更新的对象。
     * @param originalBusinessFile 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "BusinessFile", method = "update", idKey = "id", parentId = "1000002", nameKey = "fileName")
    public boolean update(BusinessFile businessFile, BusinessFile originalBusinessFile) {
        MyModelUtil.fillCommonsForUpdate(businessFile, originalBusinessFile);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<BusinessFile> uw = this.createUpdateQueryForNullValue(businessFile, businessFile.getId());
        return businessFileMapper.update(businessFile, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DataSync(sourceTableName = "BusinessFile", method = "delete", idKey = "id", parentId = "", nameKey = "fileName")
    @RemoveFromMinio(sourceTableName = "BusinessFile", method = "delete",nameKey = "fileName")
    public boolean remove(Long id) {
        return businessFileMapper.deleteById(id) == 1;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getBusinessFileListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<BusinessFile> getBusinessFileList(BusinessFile filter, String orderBy) {
        return businessFileMapper.getBusinessFileList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getBusinessFileList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<BusinessFile> getBusinessFileListWithRelation(BusinessFile filter, String orderBy) {
        List<BusinessFile> resultList = businessFileMapper.getBusinessFileList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<BusinessFile> getGroupedBusinessFileListWithRelation(
            BusinessFile filter, String groupSelect, String groupBy, String orderBy) {
        List<BusinessFile> resultList =
                businessFileMapper.getGroupedBusinessFileList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private BusinessFile buildDefaultValue(BusinessFile businessFile) {
        if (businessFile.getId() == null) {
            businessFile.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(businessFile);
        businessFile.setIsDelete(GlobalDeletedFlag.NORMAL);
        return businessFile;
    }

    /**
     * 上传文件
     *
     * @param file 文件
     * @return BusinessFile
     */
    @Override
    public BusinessFile saveFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }
        String originalFilename = file.getOriginalFilename();
        boolean asImage = isImage(file, originalFilename);
        BaseUpDownloader upDownloader = upDownloaderFactory.get(MyModelUtil.getUploadStoreInfo(BusinessFile.class, "fileJson").getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), "fileJson", asImage, file);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            throw new MyRuntimeException(responseInfo.getErrorMessage());
        }
        BusinessFile businessFile = new BusinessFile();
        businessFile.setFileName(originalFilename);
        businessFile.setFileSize(file.getSize());
        businessFile.setFileFormat(FilenameUtils.getExtension(originalFilename));
        businessFile.setFileJson("{\"fieldName\":\"fileJson\",\"filename\":\"" + responseInfo.getFilename() + "\",\"asImage\":" + asImage + "}");
        businessFile.setFileType(FilenameUtils.getExtension(originalFilename));
        businessFile.setAsImage(String.valueOf(asImage));
        businessFile = saveNew(businessFile);
        return businessFile;
    }

    private boolean isImage(MultipartFile file, String filename) {
        String contentType = file.getContentType();
        if (contentType != null && contentType.startsWith("image")) {
            return true;
        }
        // 作为备用方法，检查文件扩展名
        if (filename != null) {
            String extension = filename.toLowerCase().substring(filename.lastIndexOf(".") + 1);
            switch (extension) {
                case "jpg":
                case "jpeg":
                case "png":
                case "gif":
                case "bmp":
                case "tiff":
                case "webp":
                    return true;
                default:
                    return false;
            }
        }
        return false;
    }


    /**
     * 文件解析
     *
     * @param businessFile 文件内容。
     * @return 查询结果集。
     */
    @Async
    @Override
    public void fileContentParse(BusinessFile businessFile) {
        List<Long> fieldIdList = new ArrayList<>();
        fieldIdList.add(businessFile.getId());
        Map<String, Object> modelConfig = new HashMap<>();
        modelConfig.put("model_name","gpt-4o-mini");
        modelConfig.put("api_key","***************************************************");
        modelConfig.put("api_base","https://openaiapi.aidb.site/v1/");
        modelConfig.put("model_type","ChatOpenAI");
        Object object=pythonService.fileContentParse(modelConfig,fieldIdList);
        log.debug("文件解析结果："+object);
        FileDescribe fileDescribe = new FileDescribe();
        fileDescribe.setId(idGenerator.nextLongId());
        fileDescribe.setDataUserId(businessFile.getDataUserId());
        fileDescribe.setDataDeptId(businessFile.getDataDeptId());
        fileDescribe.setCreateTime(new Date());
        fileDescribe.setUpdateTime(new Date());
        fileDescribe.setCreateUserId(businessFile.getCreateUserId());
        fileDescribe.setUpdateUserId(businessFile.getUpdateUserId());
        fileDescribe.setIsDelete(GlobalDeletedFlag.NORMAL);
        fileDescribe.setBindType(1);
        fileDescribe.setFileId(businessFile.getId());
        fileDescribe.setFileDescribe(object.toString());
        fileDescribeMapper.insert(fileDescribe);

    }
}
