package com.supie.webadmin.app.knowledge.service;

import com.supie.common.core.object.TokenData;
import com.supie.webadmin.app.knowledge.model.*;
import com.supie.common.core.base.service.IBaseService;
import com.supie.webadmin.app.other.model.BusinessFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.nio.file.Path;
import java.util.*;

/**
 * 知识库-文件关联表数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
public interface KnowledgeFileService extends IBaseService<KnowledgeFile, Long> {

    /**
     * 保存新增对象。
     *
     * @param knowledgeFile 新增对象。
     * @return 返回新增对象。
     */
    List<KnowledgeFile> saveNew(KnowledgeFile knowledgeFile);

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param knowledgeFileList 新增对象列表。
     */
    void saveNewBatch(List<KnowledgeFile> knowledgeFileList);

    /**
     * 更新数据对象。
     *
     * @param knowledgeFile         更新的对象。
     * @param originalKnowledgeFile 原有数据对象。
     * @return 成功返回true，否则false。
     */
    boolean update(KnowledgeFile knowledgeFile, KnowledgeFile originalKnowledgeFile);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getKnowledgeFileListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<KnowledgeFile> getKnowledgeFileList(KnowledgeFile filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getKnowledgeFileList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<KnowledgeFile> getKnowledgeFileListWithRelation(KnowledgeFile filter, String orderBy);

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    List<KnowledgeFile> getGroupedKnowledgeFileListWithRelation(
            KnowledgeFile filter, String groupSelect, String groupBy, String orderBy);

    void appendExcel(KnowledgeFile knowledgeFile, BusinessFile businessFile);

    /**
     * 批量修改知识库文件配置
     * @param knowledgeFileIdList 文件id集合
     * @param modelType modelType
     * @param modeId modelId
     * @param parseTactics 解析策略
     * @param splitKnowledgeConfig 切片策略
     * @param knowledgeEnhancement 是否知识增强（questionGeneration,paragraphSummary,tripleExtraction,textSummary）
     * @param
     */
    void configUpdateBatch(List<Long> knowledgeFileIdList, String modelType, Long modeId, String parseTactics, String splitKnowledgeConfig, String knowledgeEnhancement);

    /**
     * 删除指定数据。
     *
     * @param knowledgeMainId 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean deleteKnowledgeFileByMainId(Long knowledgeMainId);

    /**
     * 根据主表Id查询知识库文件地址
     * @param knowledgeFile 知识库文件对象
     * @return 文件地址
     */
    String queryFileAddress(KnowledgeFile knowledgeFile);

    /**
     * 新增知识库文件（无权限）
     *
     * @param tokenData     tokenData
     * @param knowledgeFile knowledgeFile
     */
    void saveNewNoAuthInterface(TokenData tokenData, KnowledgeFile knowledgeFile);

    /**
     * 批量导入
     * @param jsonlFilePath json文件路径
     */
    void batchImport(SseEmitter sseEmitter, Path jsonlFilePath, Long knowledgeMainId);

}
