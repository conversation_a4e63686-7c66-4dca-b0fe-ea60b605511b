package com.supie.webadmin.app.clickhouse.controller;

import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.clickhouse.dto.DataProcessDetailDto;
import com.supie.webadmin.app.clickhouse.model.DataProcessDetail;
import com.supie.webadmin.app.clickhouse.service.DataProcessDetailService;
import com.supie.webadmin.app.clickhouse.vo.DataProcessDetailVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据管理-数据处理详情操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "数据管理-数据处理详情管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/dataProcessDetail")
public class DataProcessDetailController {

    @Autowired
    private DataProcessDetailService dataProcessDetailService;

    /**
     * 新增数据管理-数据处理详情数据。
     *
     * @param dataProcessDetailDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataProcessDetailDto.id",
            "dataProcessDetailDto.createTimeStart",
            "dataProcessDetailDto.createTimeEnd",
            "dataProcessDetailDto.updateTimeStart",
            "dataProcessDetailDto.updateTimeEnd"})
    //@SaCheckPermission("dataProcessDetail.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DataProcessDetailDto dataProcessDetailDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataProcessDetailDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataProcessDetail dataProcessDetail = MyModelUtil.copyTo(dataProcessDetailDto, DataProcessDetail.class);
        dataProcessDetail = dataProcessDetailService.saveNew(dataProcessDetail);
        return ResponseResult.success(dataProcessDetail.getId());
    }

    /**
     * 更新数据管理-数据处理详情数据。
     *
     * @param dataProcessDetailDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataProcessDetailDto.createTimeStart",
            "dataProcessDetailDto.createTimeEnd",
            "dataProcessDetailDto.updateTimeStart",
            "dataProcessDetailDto.updateTimeEnd"})
    //@SaCheckPermission("dataProcessDetail.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DataProcessDetailDto dataProcessDetailDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataProcessDetailDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataProcessDetail dataProcessDetail = MyModelUtil.copyTo(dataProcessDetailDto, DataProcessDetail.class);
        DataProcessDetail originalDataProcessDetail = dataProcessDetailService.getById(dataProcessDetail.getId());
        if (originalDataProcessDetail == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataProcessDetailService.update(dataProcessDetail, originalDataProcessDetail)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除数据管理-数据处理详情数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //@SaCheckPermission("dataProcessDetail.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除数据管理-数据处理详情数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //@SaCheckPermission("dataProcessDetail.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的数据管理-数据处理详情列表。
     *
     * @param dataProcessDetailDtoFilter 过滤对象。
     * @param orderParam                 排序参数。
     * @param pageParam                  分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //@SaCheckPermission("dataProcessDetail.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<DataProcessDetailVo>> list(
            @MyRequestBody DataProcessDetailDto dataProcessDetailDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DataProcessDetail dataProcessDetailFilter = MyModelUtil.copyTo(dataProcessDetailDtoFilter, DataProcessDetail.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DataProcessDetail.class);
        List<DataProcessDetail> dataProcessDetailList =
                dataProcessDetailService.getDataProcessDetailListWithRelation(dataProcessDetailFilter, orderBy);
        MyPageData<DataProcessDetailVo> dataProcessDetailVoMyPageData = MyPageUtil.makeResponseData(dataProcessDetailList, DataProcessDetailVo.class);
        if(dataProcessDetailVoMyPageData.getTotalCount()!=dataProcessDetailList.size()){
            dataProcessDetailVoMyPageData.setTotalCount((long) dataProcessDetailList.size());
        }
        return ResponseResult.success(dataProcessDetailVoMyPageData);
    }

    /**
     * 查看指定数据管理-数据处理详情对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //@SaCheckPermission("dataProcessDetail.view")
    @GetMapping("/view")
    public ResponseResult<DataProcessDetailVo> view(@RequestParam Long id) {
        DataProcessDetail dataProcessDetail = dataProcessDetailService.getByIdWithRelation(id, MyRelationParam.full());
        if (dataProcessDetail == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        DataProcessDetailVo dataProcessDetailVo = MyModelUtil.copyTo(dataProcessDetail, DataProcessDetailVo.class);
        return ResponseResult.success(dataProcessDetailVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DataProcessDetail originalDataProcessDetail = dataProcessDetailService.getById(id);
        if (originalDataProcessDetail == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataProcessDetailService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
