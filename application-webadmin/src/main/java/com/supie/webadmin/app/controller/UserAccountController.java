package com.supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.vo.*;
import com.supie.webadmin.app.dto.*;
import com.supie.webadmin.app.model.*;
import com.supie.webadmin.app.service.*;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 用户账户表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "用户账户表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/userAccount")
public class UserAccountController {

    @Autowired
    private UserAccountService userAccountService;

    @Operation(summary = "用户积分变更")
    @PostMapping("/updateUserBalance")
    public ResponseResult<Void> updateUserBalance(@MyRequestBody Long userAccountId, @MyRequestBody Integer balance) {
        if (balance == null || balance < 0 || userAccountId == null) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        UserAccount userAccount = userAccountService.getById(userAccountId);
        if (userAccount == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        userAccountService.updateUserBalance(userAccount, balance);
        return ResponseResult.success();
    }

    /**
     * 新增用户账户表数据。
     *
     * @param userAccountDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "userAccountDto.id",
            "userAccountDto.createTimeStart",
            "userAccountDto.createTimeEnd",
            "userAccountDto.updateTimeStart",
            "userAccountDto.updateTimeEnd"})
    ////@SaCheckPermission("userAccount.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody UserAccountDto userAccountDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(userAccountDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        UserAccount userAccount = MyModelUtil.copyTo(userAccountDto, UserAccount.class);
        userAccount = userAccountService.saveNew(userAccount);
        return ResponseResult.success(userAccount.getId());
    }

    /**
     * 更新用户账户表数据。
     *
     * @param userAccountDto 更新对象。
     * @return 应答结果对象。
     */
//    @ApiOperationSupport(ignoreParameters = {
//            "userAccountDto.createTimeStart",
//            "userAccountDto.createTimeEnd",
//            "userAccountDto.updateTimeStart",
//            "userAccountDto.updateTimeEnd"})
//    ////@SaCheckPermission("userAccount.update")
//    @OperationLog(type = SysOperationLogType.UPDATE)
//    @PostMapping("/update")
//    public ResponseResult<Void> update(@MyRequestBody UserAccountDto userAccountDto) {
//        String errorMessage = MyCommonUtil.getModelValidationError(userAccountDto, true);
//        if (errorMessage != null) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
//        }
//        UserAccount userAccount = MyModelUtil.copyTo(userAccountDto, UserAccount.class);
//        UserAccount originalUserAccount = userAccountService.getById(userAccount.getId());
//        if (originalUserAccount == null) {
//            // NOTE: 修改下面方括号中的话述
//            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
//        }
//        if (!userAccountService.update(userAccount, originalUserAccount)) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
//        return ResponseResult.success();
//    }

    /**
     * 删除用户账户表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("userAccount.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除用户账户表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("userAccount.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的用户账户表列表。
     *
     * @param userAccountDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("userAccount.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<UserAccountVo>> list(
            @MyRequestBody UserAccountDto userAccountDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        UserAccount userAccountFilter = MyModelUtil.copyTo(userAccountDtoFilter, UserAccount.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, UserAccount.class);
        List<UserAccount> userAccountList =
                userAccountService.getUserAccountListWithRelation(userAccountFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(userAccountList, UserAccountVo.class));
    }

    /**
     * 分组列出符合过滤条件的用户账户表列表。
     *
     * @param userAccountDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("userAccount.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<UserAccountVo>> listWithGroup(
            @MyRequestBody UserAccountDto userAccountDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, UserAccount.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, UserAccount.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        UserAccount filter = MyModelUtil.copyTo(userAccountDtoFilter, UserAccount.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<UserAccount> resultList = userAccountService.getGroupedUserAccountListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, UserAccountVo.class));
    }

    /**
     * 查看指定用户账户表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////@SaCheckPermission("userAccount.view")
    @GetMapping("/view")
    public ResponseResult<UserAccountVo> view(@RequestParam Long id) {
        UserAccount userAccount = userAccountService.getByIdWithRelation(id, MyRelationParam.full());
        if (userAccount == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        UserAccountVo userAccountVo = MyModelUtil.copyTo(userAccount, UserAccountVo.class);
        return ResponseResult.success(userAccountVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        UserAccount originalUserAccount = userAccountService.getById(id);
        if (originalUserAccount == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!userAccountService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
