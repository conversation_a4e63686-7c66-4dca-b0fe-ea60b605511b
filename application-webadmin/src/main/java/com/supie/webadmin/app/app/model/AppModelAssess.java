package com.supie.webadmin.app.app.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.webadmin.app.app.vo.AppModelAssessVo;
import com.supie.webadmin.upms.model.SysUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * AppModelAssess实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_app_model_assess")
public class AppModelAssess extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 评估任务名称。
     */
    private String assessName;

    /**
     * 描述。
     */
    private String assessDesc;

    /**
     * 任务状态。
     */
    private String assessState;

    /**
     * 评分模式（人工打分。自动裁判员打分。多选。）。
     */
    private String assessPattern;

    /**
     * 数据集ID。
     */
    private Long dataSetId;

    /**
     * 人工评分维度。
     */
    private String assessDimension;

    /**
     * 打分Prompt。
     */
    private String scoringPrompt;

    /**
     * 避免报错弃用字段
     */
    @TableField(exist = false)
    private Long modelDeployId;

    /**
     * 模型Id
     */
    private Long modelId;

    /**
     * 模型类型
     */
    private String modelType;


    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 失败信息。
     */
    private String errorMsg;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * assess_name / assess_desc / assess_state / assess_pattern / assess_dimension / referee_model / scoring_prompt LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    /**
     * 创建用户id字典。
     */
    @RelationDict(
            masterIdField = "createUserId",
            slaveModelClass = SysUser.class,
            slaveIdField = "userId",
            slaveNameField = "showName")
    @TableField(exist = false)
    private Map<String, Object> createUserIdDictMap;

    @RelationOneToMany(
            masterIdField = "id",
            slaveModelClass = AppAssessModelModelRelationship.class,
            slaveIdField = "assessId")
    @TableField(exist = false)
    private List<AppAssessModelModelRelationship> appAssessModelModelRelationshipList;

    @Mapper
    public interface AppModelAssessModelMapper extends BaseModelMapper<AppModelAssessVo, AppModelAssess> {
    }
    public static final AppModelAssessModelMapper INSTANCE = Mappers.getMapper(AppModelAssessModelMapper.class);
}
