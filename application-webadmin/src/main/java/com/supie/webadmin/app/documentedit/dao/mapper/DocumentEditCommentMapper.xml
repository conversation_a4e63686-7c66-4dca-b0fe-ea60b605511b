<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.documentedit.dao.DocumentEditCommentMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.documentedit.model.DocumentEditComment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="proofread_id" jdbcType="BIGINT" property="proofreadId"/>
        <result column="document_id" jdbcType="BIGINT" property="documentId"/>
        <result column="paragraph_index" jdbcType="INTEGER" property="paragraphIndex"/>
        <result column="location_start" jdbcType="INTEGER" property="locationStart"/>
        <result column="location_end" jdbcType="INTEGER" property="locationEnd"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_document_edit_comment
            (id,
            str_id,
            is_delete,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            proofread_id,
            document_id,
            paragraph_index,
            location_start,
            location_end,
            error_msg)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.proofreadId},
            #{item.documentId},
            #{item.paragraphIndex},
            #{item.locationStart},
            #{item.locationEnd},
            #{item.errorMsg})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.documentedit.dao.DocumentEditCommentMapper.inputFilterRef"/>
        AND lmd_document_edit_comment.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="documentEditCommentFilter != null">
            <if test="documentEditCommentFilter.isDelete != null">
                AND lmd_document_edit_comment.is_delete = #{documentEditCommentFilter.isDelete}
            </if>
            <if test="documentEditCommentFilter.createTimeStart != null and documentEditCommentFilter.createTimeStart != ''">
                AND lmd_document_edit_comment.create_time &gt;= #{documentEditCommentFilter.createTimeStart}
            </if>
            <if test="documentEditCommentFilter.createTimeEnd != null and documentEditCommentFilter.createTimeEnd != ''">
                AND lmd_document_edit_comment.create_time &lt;= #{documentEditCommentFilter.createTimeEnd}
            </if>
            <if test="documentEditCommentFilter.updateTimeStart != null and documentEditCommentFilter.updateTimeStart != ''">
                AND lmd_document_edit_comment.update_time &gt;= #{documentEditCommentFilter.updateTimeStart}
            </if>
            <if test="documentEditCommentFilter.updateTimeEnd != null and documentEditCommentFilter.updateTimeEnd != ''">
                AND lmd_document_edit_comment.update_time &lt;= #{documentEditCommentFilter.updateTimeEnd}
            </if>
            <if test="documentEditCommentFilter.proofreadId != null">
                AND lmd_document_edit_comment.proofread_id = #{documentEditCommentFilter.proofreadId}
            </if>
            <if test="documentEditCommentFilter.documentId != null">
                AND lmd_document_edit_comment.document_id = #{documentEditCommentFilter.documentId}
            </if>
            <if test="documentEditCommentFilter.paragraphIndex != null">
                AND lmd_document_edit_comment.paragraph_index = #{documentEditCommentFilter.paragraphIndex}
            </if>
            <if test="documentEditCommentFilter.locationStart != null">
                AND lmd_document_edit_comment.location_start = #{documentEditCommentFilter.locationStart}
            </if>
            <if test="documentEditCommentFilter.locationEnd != null">
                AND lmd_document_edit_comment.location_end = #{documentEditCommentFilter.locationEnd}
            </if>
            <if test="documentEditCommentFilter.errorMsg != null and documentEditCommentFilter.errorMsg != ''">
                <bind name = "safeDocumentEditCommentErrorMsg" value = "'%' + documentEditCommentFilter.errorMsg + '%'" />
                AND lmd_document_edit_comment.error_msg LIKE #{safeDocumentEditCommentErrorMsg}
            </if>
            <if test="documentEditCommentFilter.searchString != null and documentEditCommentFilter.searchString != ''">
                <bind name = "safeDocumentEditCommentSearchString" value = "'%' + documentEditCommentFilter.searchString + '%'" />
                AND IFNULL(lmd_document_edit_comment.error_msg,'') LIKE #{safeDocumentEditCommentSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDocumentEditCommentList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.documentedit.model.DocumentEditComment">
        SELECT * FROM
            (SELECT
                SUM(id) id,
                ${groupSelect}
            FROM lmd_document_edit_comment
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_document_edit_comment
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDocumentEditCommentList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.documentedit.model.DocumentEditComment">
        SELECT * FROM lmd_document_edit_comment
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
