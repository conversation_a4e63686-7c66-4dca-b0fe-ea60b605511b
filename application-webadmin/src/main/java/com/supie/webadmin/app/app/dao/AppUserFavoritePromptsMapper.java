package com.supie.webadmin.app.app.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.app.model.AppUserFavoritePrompts;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 应用管理-prompt用户收藏关联表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm
public interface AppUserFavoritePromptsMapper extends BaseDaoMapper<AppUserFavoritePrompts> {

    /**
     * 批量插入对象列表。
     *
     * @param appUserFavoritePromptsList 新增对象列表。
     */
    void insertList(List<AppUserFavoritePrompts> appUserFavoritePromptsList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param appUserFavoritePromptsFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<AppUserFavoritePrompts> getAppUserFavoritePromptsList(
            @Param("appUserFavoritePromptsFilter") AppUserFavoritePrompts appUserFavoritePromptsFilter, @Param("orderBy") String orderBy);
}
