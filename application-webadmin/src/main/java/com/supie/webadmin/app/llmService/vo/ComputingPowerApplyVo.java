package com.supie.webadmin.app.llmService.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supie.common.core.base.vo.BaseVo;
import com.supie.webadmin.app.llmService.model.ComputingPowerManagement;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map;

/**
 * 工作流-算力申请表VO视图对象。
 * 算力申请表管理VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Schema(description = "工作流-算力申请表VO视图对象")
@Data
public class ComputingPowerApplyVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 创建者ID。
     */
    @Schema(description = "创建者ID")
    private Long createUserId;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改者ID。
     */
    @Schema(description = "修改者ID")
    private Long updateUserId;

    /**
     * 修改时间。
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 流程状态流程状态(1、未提交；2、待审批；3、通过；4、不通过；5、时间耗尽；6、终止)。。
     * 流程状态((1未提交，2待审批，3通过，4,未通过, 5终止, 6时间耗尽)))。
     */
    @Schema(description = "流程状态流程状态(1、未提交；2、待审批；3、通过；4、不通过；5、时间耗尽；6、终止)。")
    private Integer processState;

    /**
     * 流程审批状态。
     */
    @Schema(description = "流程审批状态")
    private Integer processApproveState;

    /**
     * 使用状态（1未开始，2进行中，3时间耗尽，4已删除）。
     */
    @Schema(description = "使用状态（1未开始，2进行中，3时间耗尽，4已删除）")
    private Integer useState;

    /**
     * 申请人id。
     */
    @Schema(description = "申请人id")
    private Long applyUserId;

    /**
     * 申请人姓名。
     */
    @Schema(description = "申请人姓名。")
    private String applyUserName;

    /**
     * 申请使用开始时间。
     */
    @Schema(description = "申请使用开始时间")
    private Date useStartTime;

    /**
     * 申请使用结束时间。
     */
    @Schema(description = "申请使用结束时间")
    private Date useEndTime;

    /**
     * （申请使用算力id集合）算力表id。
     */
    @Schema(description = "（申请使用算力id集合）算力表id")
    private String computePowerId;

    /**
     * 申请状态（1进行中，2使用进行中，3使用时长耗尽）。
     */
    @Schema(description = "申请状态（1进行中，2使用进行中，3使用时长耗尽）")
    private Integer applyState;

    /**
     * 申请原因。
     */
    @Schema(description = "申请原因")
    private String applyReason;

    /**
     * 申请备注。
     */
    @Schema(description = "申请备注")
    private String applyRemarks;

    /**
     * 培训任务Id。
     */
    @Schema(description = "培训任务Id。")
    private Long trainingTaskId;

    /**
     * 申请通过时间。
     */
    @Schema(description = "申请通过时间。")
    private Date applyAdoptTime;

    /**
     * 审核意见。
     */
    @Schema(description = "审核意见")
    private String reviewComments;

    /**
     * 算力信息。
     */
    @Schema(description = "算力信息。")
    private List<ComputingPowerManagement> computingPowerManagementList;

    /**
     * 创建用户id字典。
     */
    @Schema(description = "创建用户id字典")
    private Map<String, Object> createUserIdDict;
    @Schema(description = "申请人idDict。")
    private Map<String, Object> applyUserDict;
}
