package com.supie.webadmin.app.data.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supie.common.core.annotation.DeptFilterColumn;
import com.supie.common.core.annotation.RelationDict;
import com.supie.common.core.annotation.RelationOneToOne;
import com.supie.common.core.annotation.UserFilterColumn;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.webadmin.upms.model.SysUser;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

/**
 * 数据洞察导入数据集任务表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Data
@TableName(value = "lmd_data_insight_import_task")
@Accessors(chain = true)
public class DataInsightImportTask {

    /**
     * 数据来源
     */
    @TableField(exist = false)
    private String dataFrom;

    /**
     * 导入数据大小
     */
    @TableField(exist = false)
    private Integer dataSize;

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建者ID。
     */
    @UserFilterColumn
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 修改时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 修改者ID。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人ID。
     */
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 数据洞察ID。
     */
    @TableField(value = "data_insight_id")
    private Long dataInsightId;

    /**
     * 数据集ID。
     */
    @TableField(value = "data_set_id")
    private Long dataSetId;

    /**
     * 导入状态（0：未开始。1：进行中。2：标注完成。3:失败）。
     */
    @TableField(value = "import_state")
    private Integer importState;

    /**
     * 导入数量。
     */
    @TableField(value = "import_num")
    private Integer importNum;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * str_id LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    @RelationDict(
            masterIdField = "createUserId",
            slaveModelClass = SysUser.class,
            slaveIdField = "userId",
            slaveNameField = "showName")
    @TableField(exist = false)
    private Map<String, Object> createUserIdDictMap;


    /**
     * 数据集对象
     */
    @RelationOneToOne(
            masterIdField = "dataSetId",
            slaveModelClass =DataDataSet.class,
            slaveIdField = "id"
    )
    @TableField(exist = false)
    private DataDataSet dataSet;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
