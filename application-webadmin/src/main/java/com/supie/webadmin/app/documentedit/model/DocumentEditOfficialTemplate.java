package com.supie.webadmin.app.documentedit.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 公文写作模版实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_document_edit_official_template")
public class DocumentEditOfficialTemplate extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 模版名称。
     */
    @TableField(value = "template_title")
    private String templateTitle;

    /**
     * 模版描述。
     */
    @TableField(value = "template_desc")
    private String templateDesc;

    /**
     * 模版英文名称。
     */
    @TableField(value = "template_en_title")
    private String templateEnTitle;

    /**
     * 标签。
     */
    @TableField(value = "template_tags")
    private String templateTags;

    /**
     * 公文类型（通知, 通告, 报告, 请示, 批复, 指示, 决定, 命令, 议案, 纪要, 公函, 简报, 条例, 合同, 备忘录）。
     */
    @TableField(value = "official_document_type")
    private String officialDocumentType;

    /**
     * 图标地址。
     */
    @TableField(value = "icon_url")
    private String iconUrl;

    /**
     * 模版分块内容json。
     */
    @TableField(value = "template_chunk_json")
    private String templateChunkJson;

    /**
     * 模版类型（法定公文、规范应用）。
     */
    @TableField(value = "template_type")
    private String templateType;

    /**
     * 标签列表。
     * NOTE: 支持 IN 操作符的列表数据过滤。
     */
    @TableField(exist = false)
    private List<String> templateTagsList;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * str_id / template_title / template_en_title / official_document_type LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
