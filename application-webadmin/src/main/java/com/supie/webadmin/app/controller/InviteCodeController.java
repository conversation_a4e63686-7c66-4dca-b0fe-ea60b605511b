package com.supie.webadmin.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.dao.InviteCodeMapper;
import com.supie.webadmin.app.dto.InviteCodeDto;
import com.supie.webadmin.app.model.InviteCode;
import com.supie.webadmin.app.service.InviteCodeService;
import com.supie.webadmin.app.vo.InviteCodeVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 邀请注册验证码表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "邀请注册验证码表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/inviteCode")
public class InviteCodeController {

    @Autowired
    private InviteCodeService inviteCodeService;
    @Autowired
    private InviteCodeMapper inviteCodeMapper;

    /**
     * 新增邀请注册验证码表数据。
     *
     * @param inviteCodeDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */

    ////@SaCheckPermission("inviteCode.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody InviteCodeDto inviteCodeDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(inviteCodeDto, false);
        if (errorMessage != null ) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        if(inviteCodeDto.getValidCount()<=0 ){
            return ResponseResult.error("无效的使用次数");
        }
        InviteCode inviteCode = MyModelUtil.copyTo(inviteCodeDto, InviteCode.class);
        inviteCode.setRemainder(inviteCode.getValidCount());
        //TODO 次数待讨论
        int maxAttempt=10000;
        int attempt=0;
        String code;
        do{
            code = RandomStringUtils.randomAlphanumeric(6);
            attempt+=1;
        }while (!inviteCodeService.selectList(new LambdaQueryWrapper<InviteCode>()
                .eq(InviteCode::getInviteCode, code)).isEmpty() && attempt<maxAttempt);
        if(attempt>=maxAttempt){
            throw new  MyRuntimeException("唯一邀请码失效请联系管理员");
        }
        inviteCode.setInviteCode(code);
        inviteCode = inviteCodeService.saveNew(inviteCode);
        return ResponseResult.success(inviteCode.getId());
    }

    /**
     * 更新邀请注册验证码表数据。
     *
     * @param inviteCodeDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "inviteCodeDto.searchString",
            "inviteCodeDto.validTimeStart",
            "inviteCodeDto.validTimeEnd",
            "inviteCodeDto.expireTimeStart",
            "inviteCodeDto.expireTimeEnd",
            "inviteCodeDto.createTimeStart",
            "inviteCodeDto.createTimeEnd",
            "inviteCodeDto.updateTimeStart",
            "inviteCodeDto.updateTimeEnd"})
   // //@SaCheckPermission("inviteCode.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody InviteCodeDto inviteCodeDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(inviteCodeDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        InviteCode inviteCode = MyModelUtil.copyTo(inviteCodeDto, InviteCode.class);
        InviteCode originalInviteCode = inviteCodeService.getById(inviteCode.getId());
        if (originalInviteCode == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!inviteCodeService.update(inviteCode, originalInviteCode)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除邀请注册验证码表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("inviteCode.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除邀请注册验证码表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("inviteCode.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的邀请注册验证码表列表。
     *
     * @param inviteCodeDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("inviteCode.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<InviteCodeVo>> list(
            @MyRequestBody InviteCodeDto inviteCodeDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        InviteCode inviteCodeFilter = MyModelUtil.copyTo(inviteCodeDtoFilter, InviteCode.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, InviteCode.class);
        List<InviteCode> inviteCodeList = inviteCodeService.getInviteCodeListWithRelation(inviteCodeFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(inviteCodeList, InviteCodeVo.class));
    }

    /**
     * 分组列出符合过滤条件的邀请注册验证码表列表。
     *
     * @param inviteCodeDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("inviteCode.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<InviteCodeVo>> listWithGroup(
            @MyRequestBody InviteCodeDto inviteCodeDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, InviteCode.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, InviteCode.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        InviteCode filter = MyModelUtil.copyTo(inviteCodeDtoFilter, InviteCode.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<InviteCode> resultList = inviteCodeService.getGroupedInviteCodeListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, InviteCodeVo.class));
    }

    /**
     * 查看指定邀请注册验证码表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////@SaCheckPermission("inviteCode.view")
    @GetMapping("/view")
    public ResponseResult<InviteCodeVo> view(@RequestParam Long id) {
        InviteCode inviteCode = inviteCodeService.getByIdWithRelation(id, MyRelationParam.full());
        if (inviteCode == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        InviteCodeVo inviteCodeVo = MyModelUtil.copyTo(inviteCode, InviteCodeVo.class);
        return ResponseResult.success(inviteCodeVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        InviteCode originalInviteCode = inviteCodeService.getById(id);
        if (originalInviteCode == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (inviteCodeMapper.deleteBy(id)!=1) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
