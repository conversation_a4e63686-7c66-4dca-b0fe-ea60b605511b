package com.supie.webadmin.app.data.controller;

import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.app.data.dto.DataSetInsightsDto;
import com.supie.webadmin.app.data.model.DataSetInsights;
import com.supie.webadmin.app.data.service.DataSetInsightsService;
import com.supie.webadmin.app.data.vo.DataSetInsightsVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据管理-数据集洞察表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "数据管理-数据集洞察表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/dataSetInsights")
public class DataSetInsightsController {

    @Autowired
    private DataSetInsightsService dataSetInsightsService;

    /**
     * 新增数据管理-数据集洞察表数据。
     *
     * @param dataSetInsightsDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataSetInsightsDto.id",
            "dataSetInsightsDto.searchString",
            "dataSetInsightsDto.createTimeStart",
            "dataSetInsightsDto.createTimeEnd",
            "dataSetInsightsDto.updateTimeStart",
            "dataSetInsightsDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DataSetInsightsDto dataSetInsightsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataSetInsightsDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataSetInsights dataSetInsights = MyModelUtil.copyTo(dataSetInsightsDto, DataSetInsights.class);
        dataSetInsights = dataSetInsightsService.saveNew(dataSetInsights);
        return ResponseResult.success(dataSetInsights.getId());
    }

    /**
     * 更新数据管理-数据集洞察表数据。
     *
     * @param dataSetInsightsDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "dataSetInsightsDto.searchString",
            "dataSetInsightsDto.createTimeStart",
            "dataSetInsightsDto.createTimeEnd",
            "dataSetInsightsDto.updateTimeStart",
            "dataSetInsightsDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DataSetInsightsDto dataSetInsightsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataSetInsightsDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataSetInsights dataSetInsights = MyModelUtil.copyTo(dataSetInsightsDto, DataSetInsights.class);
        DataSetInsights originalDataSetInsights = dataSetInsightsService.getById(dataSetInsights.getId());
        if (originalDataSetInsights == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataSetInsightsService.update(dataSetInsights, originalDataSetInsights)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }
    /**
     * 开始洞察。
     *
     * @param dataSetInsightsDto dataSetInsightsDto对象。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/startDataInsight")
    public ResponseResult<Void> startDataInsight(@MyRequestBody DataSetInsightsDto dataSetInsightsDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(dataSetInsightsDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DataSetInsights dataSetInsights = MyModelUtil.copyTo(dataSetInsightsDto, DataSetInsights.class);
        dataSetInsightsService.startDataInsight(dataSetInsights);
        return ResponseResult.success();
    }
    /**
     * 删除数据管理-数据集洞察表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除数据管理-数据集洞察表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的数据管理-数据集洞察表列表。
     *
     * @param dataSetInsightsDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<DataSetInsightsVo>> list(
            @MyRequestBody DataSetInsightsDto dataSetInsightsDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DataSetInsights dataSetInsightsFilter = MyModelUtil.copyTo(dataSetInsightsDtoFilter, DataSetInsights.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DataSetInsights.class);
        List<DataSetInsights> dataSetInsightsList =
                dataSetInsightsService.getDataSetInsightsListWithRelation(dataSetInsightsFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(dataSetInsightsList, DataSetInsightsVo.class));
    }

    /**
     * 分组列出符合过滤条件的数据管理-数据集洞察表列表。
     *
     * @param dataSetInsightsDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<DataSetInsightsVo>> listWithGroup(
            @MyRequestBody DataSetInsightsDto dataSetInsightsDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DataSetInsights.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, DataSetInsights.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DataSetInsights filter = MyModelUtil.copyTo(dataSetInsightsDtoFilter, DataSetInsights.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<DataSetInsights> resultList = dataSetInsightsService.getGroupedDataSetInsightsListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, DataSetInsightsVo.class));
    }

    /**
     * 查看指定数据管理-数据集洞察表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<DataSetInsightsVo> view(@RequestParam Long id) {
        DataSetInsights dataSetInsights = dataSetInsightsService.getByIdWithRelation(id, MyRelationParam.full());
        if (dataSetInsights == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        dataSetInsightsService.processJson(dataSetInsights);
        DataSetInsightsVo dataSetInsightsVo = MyModelUtil.copyTo(dataSetInsights, DataSetInsightsVo.class);
        return ResponseResult.success(dataSetInsightsVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DataSetInsights originalDataSetInsights = dataSetInsightsService.getById(id);
        if (originalDataSetInsights == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!dataSetInsightsService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
