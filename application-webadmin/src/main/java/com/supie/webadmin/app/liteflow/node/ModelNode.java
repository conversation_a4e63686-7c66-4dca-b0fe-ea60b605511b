package com.supie.webadmin.app.liteflow.node;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.supie.webadmin.app.liteflow.model.NodeResult;
import com.supie.webadmin.app.llmDeploy.dao.ModelDeployTaskMapper;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import com.supie.webadmin.app.llmDeploy.service.ModelDeployTaskService;
import com.supie.webadmin.app.llmService.dao.ModelServiceRelationMapper;
import com.supie.webadmin.app.other.service.AdapterInterfaceService;
import com.supie.webadmin.pythonClient.ApiUrlEnums.AppUrlEnums;
import com.supie.webadmin.pythonClient.model.PythonResponse;
import com.supie.webadmin.pythonClient.service.PythonClientService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
@LiteflowComponent(id = "llmNode", name = "大模型组件")
public class ModelNode extends BaseNode {

    @Autowired
    private ModelDeployTaskService modelDeployTaskService;
    @Autowired
    private PythonClientService pythonClientService;
    @Autowired
    private AdapterInterfaceService adapterInterfaceService;
    @Autowired
    private ModelDeployTaskMapper modelDeployTaskMapper;
    @Autowired
    private ModelServiceRelationMapper modelServiceRelationMapper;

    @Override
    public void process() throws Exception {
        String tag = getTag();
        NodeResult nodeResult = nodeResultMap.get(tag);
        JsonNode thisNodeConfigJsonNode = thisNodeConfigJsonNodeMap.get(tag);
        JsonNode promptJsonNode = thisNodeConfigJsonNode.get("prompt");
        JsonNode modelTypeJsonNode = thisNodeConfigJsonNode.get("modelType");
        JsonNode modelIdJsonNode = thisNodeConfigJsonNode.get("modelId");
        if (promptJsonNode == null) {
            throw new NullPointerException("prompt配置不存在!");
        }
        if (modelIdJsonNode == null) {
            throw new NullPointerException("未配置访问的模型!");
        }
        if (modelTypeJsonNode == null) {
            throw new NullPointerException("未配置模型类型!");
        }
        String modelId = modelIdJsonNode.textValue();
        if (modelId.isEmpty()) {
            throw new NullPointerException("未配置访问的模型!");
        }
        // 设置 modelConfig
        Map<String, Object> modelConfig = new HashMap<>();
        if ("deploy".equals(modelTypeJsonNode.textValue())) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            if (modelDeployTask == null) {
                throw new NullPointerException("不存在该部署的模型!");
            }
            modelConfig.put("modelId",modelDeployTask.getId());
            modelConfig.put("model_type", "ChatOpenAI");
            modelConfig.put("model_name", modelDeployTask.getModelName());
            // 设置模型地址及其密钥
            StringBuilder baseModelUrl = new StringBuilder("http://");
            if (null != modelDeployTask.getServerHost()) {
                baseModelUrl.append(modelDeployTask.getServerHost());
            }
            if (null != modelDeployTask.getApiServerPort()) {
                baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
            }
            baseModelUrl.append("/v1");
            modelConfig.put("api_base", baseModelUrl.toString());
            modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());
        } else if ("public".equals(modelTypeJsonNode.textValue())) {
            Map<String, Object> publicLlmByModelServiceRelation = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(Long.valueOf(modelId));
            if (publicLlmByModelServiceRelation == null) {
                throw new NullPointerException("访问的模型不存在!");
            }
            modelConfig.put("modelId",publicLlmByModelServiceRelation.get("id"));
            modelConfig.put("model_type", publicLlmByModelServiceRelation.get("serviceProvider"));
            modelConfig.put("model_name", publicLlmByModelServiceRelation.get("modelName"));
            String serviceConfiguration = publicLlmByModelServiceRelation.get("serviceConfiguration").toString();
            if (StrUtil.isNotBlank(serviceConfiguration) && JSONUtil.isTypeJSON(serviceConfiguration)) {
                Map serviceConfigurationMap = JSONUtil.toBean(serviceConfiguration, Map.class);
                serviceConfigurationMap.forEach((key, value) -> {
                    modelConfig.put((String) key, value);
                });
            }
        } else {
            throw (RuntimeException) new RuntimeException("模型类型[" + modelTypeJsonNode.textValue() + "]不支持！").initCause(null);
        }
        String promptStr = promptJsonNode.textValue();
        Map<String, Object> inputParamsMap = nodeResult.getInputParamsMap();
        if (inputParamsMap != null) {
            Set<String> inputParamsMapKeySet = inputParamsMap.keySet();
            for (String inputParamsMapKey : inputParamsMapKeySet) {
                String paramName = "${" + inputParamsMapKey + "}";
                Object paramValue = inputParamsMap.get(inputParamsMapKey);
                // 将 promptStr 中的 ${paramName} 替换为 paramValue
                promptStr = promptStr.replace(paramName, paramValue.toString());
            }
        }
        // 查询模型
        String llmChatResult = llmChat(modelConfig, promptStr, "");
        // 将结果设置到outputParamsMap中
        ArrayNode outPutParamsJsonNode = (ArrayNode) thisNodeConfigJsonNode.get("outPutParams");
        if (outPutParamsJsonNode != null) {
            for (JsonNode jsonNode : outPutParamsJsonNode) {
                Map<String, Object> outputParamsMap = new HashMap<>();
                outputParamsMap.put(jsonNode.get("name").textValue(), llmChatResult);
                nodeResult.setOutputParamsMap(outputParamsMap);
            }
        }
        nodeResultMap.put(tag, nodeResult);
        thisNodeConfigJsonNodeMap.put(tag, thisNodeConfigJsonNode);
    }

    /**
     * 模型对话
     * @param modelConfig modelConfig
     * @param prompt 提示词
     * @param problem 问题
     * @return 模型回答结果
     */
    public String llmChat(Map<String, Object> modelConfig, String prompt, String problem) {
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        Map<String, Object> parametersMap = new HashMap<>();
        parametersMap.put("model_config", modelConfig);
        parametersMap.put("prompt_str", prompt);
        parametersMap.put("problem_str", problem);
        parametersMap = setTheBasicParametersOfTheRequest(parametersMap);
        parametersMap = adapterInterfaceService.buildDataUserOrDataDeptInfo(extensionUser, parametersMap);
        // 使用 Hutool 发送一个 POST 请求给url,以 JSON 格式进行传参和接收
        // 发送POST请求
        PythonResponse response = pythonClientService.request(AppUrlEnums.BASE_CHAT, headers, null, parametersMap);
        if(response.isSuccess()) {
            Object data = response.getData();
            if (data == null) {
                log.warn("大模型节点执行结果为null");
                return "";
            }
            return data.toString();
        } else {
            // 错误处理
            throw new RuntimeException("请求失败，状态：" + response.getErrorCode() + " --> " + response.getErrorMessage());
        }
    }

    private Map<String, Object> setTheBasicParametersOfTheRequest(Map<String, Object> dataMap) {
        // dataMap设置
        if (dataMap != null && !dataMap.isEmpty() && dataMap.containsKey("model_config")) {
            Map<String, Object> modelConfig = (Map) dataMap.get("model_config");
            String baseModelName = modelConfig.get("model_name").toString();
            // 如果 baseModelName 变量值 like yi-34b-chat-200k（可以不要后面的200k，200k比较慢）。
            if (baseModelName.contains("yi-34b-chat")) {
                modelConfig.put("model_url", "https://api.01ww.xyz/v1");
                modelConfig.put("model_key", "01ww-test-240125-A5IRSk9DAN");
            } else {
                // 通过部署的模型的名称在数据库中查询到 base_model_url 和 base_model_key
                ModelDeployTask modelDeployTask = modelDeployTaskMapper.getModelDeployTaskByModelName(baseModelName);
                if (null != modelDeployTask) {
                    StringBuilder baseModelUrl = new StringBuilder("http://");
                    if (null != modelDeployTask.getServerHost()) {
                        baseModelUrl.append(modelDeployTask.getServerHost());
                    }
                    if (null != modelDeployTask.getApiServerPort()) {
                        baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
                    }
                    baseModelUrl.append("/v1");
                    modelConfig.put("model_url", baseModelUrl.toString());
                    modelConfig.put("model_key", "sk-" + modelDeployTask.getEncryptionKey());
                }
            }
        }
        return dataMap;
    }

}
