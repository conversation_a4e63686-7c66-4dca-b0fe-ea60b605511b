package com.supie.webadmin.app.llmDeploy.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.llmDeploy.model.ModelDeployTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部署管理-模型部署表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm
public interface ModelDeployTaskMapper extends BaseDaoMapper<ModelDeployTask> {

    /**
     * 批量插入对象列表。
     *
     * @param modelDeployTaskList 新增对象列表。
     */
    void insertList(List<ModelDeployTask> modelDeployTaskList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param modelDeployTaskFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<ModelDeployTask> getGroupedModelDeployTaskList(
            @Param("modelDeployTaskFilter") ModelDeployTask modelDeployTaskFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param modelDeployTaskFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<ModelDeployTask> getModelDeployTaskList(
            @Param("modelDeployTaskFilter") ModelDeployTask modelDeployTaskFilter, @Param("orderBy") String orderBy);

    /**
     * 根据模型名称获取模型部署任务（区分大小写）。
     * @param modelName 模型名称。
     * @return 模型部署任务。
     */
    ModelDeployTask getModelDeployTaskByModelName(@Param("modelName") String modelName);

    List<ModelDeployTask> selectListToCallStatistics();

    /**
     * 根据模型ID查询模型部署任务，不进行权限控制。
     * @param id
     * @return
     */
    ModelDeployTask selectByIdNoQx(Long id);

}
