package com.supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.GlobalThreadLocal;
import com.supie.common.core.object.TokenData;
import com.supie.webadmin.app.service.*;
import com.supie.webadmin.app.dao.*;
import com.supie.webadmin.app.model.*;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模型分享表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Slf4j
@Service("modelShareService")
public class ModelShareServiceImpl extends BaseService<ModelShare, Long> implements ModelShareService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private ModelShareMapper modelShareMapper;

    @Resource
    private ModelInfoMapper modelInfoMapper;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<ModelShare> mapper() {
        return modelShareMapper;
    }

    /*
    * 用户模型链接分享
    * 0.
    *
    *
    * */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ModelShare saveNew(ModelShare modelShare) {
        Boolean originalFlag= GlobalThreadLocal.setDataFilter(false);
        ModelInfo modelInfo = modelInfoMapper.selectById(modelShare.getModelInfoId());
        GlobalThreadLocal.setDataFilter(originalFlag);
        if(ObjectUtil.isNull(modelInfo)){
            throw  new MyRuntimeException("模型不存在");
        }
        modelShareMapper.insert(this.buildDefaultValue(modelShare));
        return modelShare;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<ModelShare> modelShareList) {
        if (CollUtil.isNotEmpty(modelShareList)) {
            modelShareList.forEach(this::buildDefaultValue);
            modelShareMapper.insertList(modelShareList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ModelShare modelShare, ModelShare originalModelShare) {
        MyModelUtil.fillCommonsForUpdate(modelShare, originalModelShare);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<ModelShare> uw = this.createUpdateQueryForNullValue(modelShare, modelShare.getId());
        return modelShareMapper.update(modelShare, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return modelShareMapper.deleteById(id) == 1;
    }

    @Override
    public List<ModelShare> getModelShareList(ModelShare filter, String orderBy) {
        return modelShareMapper.getModelShareList(filter, orderBy);
    }

    @Override
    public List<ModelShare> getModelShareListWithRelation(ModelShare filter, String orderBy) {
        List<ModelShare> resultList = modelShareMapper.getModelShareList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        for (ModelShare modelShare : resultList) {
            String secretKey = modelShare.getSecretKey();
            RBucket<String> sessionData = redissonClient.getBucket("OpenApiRequestNumberTotalRequest:" + secretKey);
            if (sessionData.isExists()) {
                String sessionDataStr = sessionData.get();
                List<Date> sessionDataList = JSON.parseArray(sessionDataStr, Date.class);
                modelShare.setAllRequestNumber(sessionDataList.size());
            } else {
                modelShare.setAllRequestNumber(0);
            }
        }
        return resultList;
    }

    @Override
    public List<ModelShare> getGroupedModelShareListWithRelation(
            ModelShare filter, String groupSelect, String groupBy, String orderBy) {
        List<ModelShare> resultList =
                modelShareMapper.getGroupedModelShareList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private ModelShare buildDefaultValue(ModelShare modelShare) {
        if (modelShare.getId() == null) {
            modelShare.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(modelShare);
        modelShare.setIsDelete(GlobalDeletedFlag.NORMAL);
        return modelShare;
    }
}
