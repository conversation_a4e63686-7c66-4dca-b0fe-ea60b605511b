package com.supie.webadmin.app.app.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import com.supie.common.core.base.mapper.BaseModelMapper;
import com.supie.webadmin.app.app.vo.ToolSetInfoVo;
import com.supie.webadmin.upms.model.SysUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import com.supie.webadmin.app.other.model.BusinessFile;

import java.util.List;
import java.util.Map;

/**
 * ToolSetInfo实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_tool_set_info")
public class ToolSetInfo extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    private Long dataDeptId;

    /**
     * 名称。
     */
    private String toolSetName;

    /**
     * 描述。
     */
    private String toolSetDesc;

    /**
     * 分类。
     */
    private String toolSetCategory;

    @RelationDict(
            masterIdField = "createUserId",
            slaveModelClass = SysUser.class,
            slaveIdField = "userId",
            slaveNameField = "showName")
    @TableField(exist = false)
    private Map<String, Object> createUserIdDictMap;

    /**
     * 附件数据列表。
     */
    @RelationOneToMany(
            masterIdField = "strId",
            slaveModelClass = BusinessFile.class,
            slaveIdField = "bindStrId")
    @TableField(exist = false)
    private List<BusinessFile> fileList;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * tool_set_name / tool_set_desc / tool_set_category LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    @Mapper
    public interface ToolSetInfoModelMapper extends BaseModelMapper<ToolSetInfoVo, ToolSetInfo> {
    }
    public static final ToolSetInfoModelMapper INSTANCE = Mappers.getMapper(ToolSetInfoModelMapper.class);
}
