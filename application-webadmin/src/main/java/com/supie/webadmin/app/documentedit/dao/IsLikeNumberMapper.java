package com.supie.webadmin.app.documentedit.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.documentedit.model.IsLikeNumber;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 点赞和点踩统计表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@EnableDataPerm
public interface IsLikeNumberMapper extends BaseDaoMapper<IsLikeNumber> {

    /**
     * 批量插入对象列表。
     *
     * @param isLikeNumberList 新增对象列表。
     */
    void insertList(List<IsLikeNumber> isLikeNumberList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param isLikeNumberFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<IsLikeNumber> getGroupedIsLikeNumberList(
            @Param("isLikeNumberFilter") IsLikeNumber isLikeNumberFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param isLikeNumberFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<IsLikeNumber> getIsLikeNumberList(
            @Param("isLikeNumberFilter") IsLikeNumber isLikeNumberFilter, @Param("orderBy") String orderBy);
}
