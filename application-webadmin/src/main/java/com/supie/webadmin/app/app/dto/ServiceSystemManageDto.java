package com.supie.webadmin.app.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 服务管理-系统服务管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ServiceSystemManageDto对象")
@Data
public class ServiceSystemManageDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     *  服务端口Docker 容器扶额端口
     */
    @Schema(description = "服务端口")
    private  Integer servicePort;
    /**
     * 服务类型(python主程序服务 python服务)
     */
    @Schema(description = "服务类型(1 python主程序服务 0 python服务)")
    private Integer serviceType;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 容器ID。
     */
    @Schema(description = "容器ID")
    private String dockerId;

    /**
     * 服务器ID。
     */
    @Schema(description = "服务器ID")
    private Long remoteHostId;

    /**
     * 服务名称。
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 服务描述。
     */
    @Schema(description = "服务描述")
    private String serviceDesc;

    /**
     * 服务状态。
     */
    @Schema(description = "服务状态")
    private Integer serviceStatus;

    /**
     * 服务路径。
     */
    @Schema(description = "服务路径")
    private String servicePath;

    /**
     * 显卡数量。
     */
    @Schema(description = "显卡数量")
    private Integer graphicNumber;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * docker_id / service_name / service_path LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
