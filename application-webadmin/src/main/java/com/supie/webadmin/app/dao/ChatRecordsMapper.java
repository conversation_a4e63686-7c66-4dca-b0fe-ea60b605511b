package com.supie.webadmin.app.dao;

import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.app.model.ChatRecords;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 对话记录表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Mapper
@Repository("chatRecordsMapper")
//@EnableDataPerm
public interface ChatRecordsMapper extends BaseDaoMapper<ChatRecords> {

    /**
     * 批量插入对象列表。
     *
     * @param chatRecordsList 新增对象列表。
     */
    void insertList(List<ChatRecords> chatRecordsList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param chatRecordsFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<ChatRecords> getGroupedChatRecordsList(
            @Param("chatRecordsFilter") ChatRecords chatRecordsFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param chatRecordsFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<ChatRecords> getChatRecordsList(
            @Param("chatRecordsFilter") ChatRecords chatRecordsFilter, @Param("orderBy") String orderBy);
}
