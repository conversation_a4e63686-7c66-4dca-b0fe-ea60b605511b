package com.supie.webadmin.app.documentedit.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档编辑禁止规则详细信息表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "lmd_document_edit_prohibite_rule_detail")
public class DocumentEditProhibiteRuleDetail extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 应为。
     */
    @TableField(value = "should_be")
    private String shouldBe;

    /**
     * 不应为。
     */
    @TableField(value = "should_not_be")
    private String shouldNotBe;

    /**
     * 描述。
     */
    @TableField(value = "table_desc")
    private String tableDesc;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * createUserId 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private Long createUserIdStart;

    /**
     * createUserId 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private Long createUserIdEnd;

    /**
     * should_be / should_not_be LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
