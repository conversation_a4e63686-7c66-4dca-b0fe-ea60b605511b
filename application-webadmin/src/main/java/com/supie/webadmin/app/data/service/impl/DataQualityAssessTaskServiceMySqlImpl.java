//package com.supie.webadmin.app.data.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
//import com.github.pagehelper.Page;
//import com.supie.common.core.annotation.MyDataSource;
//import com.supie.common.core.base.dao.BaseDaoMapper;
//import com.supie.common.core.base.service.BaseService;
//import com.supie.common.core.constant.GlobalDeletedFlag;
//import com.supie.common.core.object.MyRelationParam;
//import com.supie.common.core.util.MyModelUtil;
//import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
//import com.supie.webadmin.app.data.dao.DataQualityAssessTaskMapper;
//import com.supie.webadmin.app.data.model.DataDataSet;
//import com.supie.webadmin.app.data.model.DataQualityAssessTask;
//import com.supie.webadmin.app.data.model.DataResources;
//import com.supie.webadmin.app.data.service.DataDataSetService;
//import com.supie.webadmin.app.data.service.DataQualityAssessTaskService;
//import com.supie.webadmin.app.data.service.DataResourcesService;
//import com.supie.webadmin.config.DataSourceType;
//import lombok.extern.slf4j.Slf4j;
//import org.jetbrains.annotations.NotNull;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 数据管理-数据质量评估任务表数据操作服务类。
// *
// * <AUTHOR> -rf .bug
// * @date 2024-01-02
//// */
//@Slf4j
//@MyDataSource(DataSourceType.MAIN)
//@Service("dataQualityAssessTaskServiceMySql")
//public class DataQualityAssessTaskServiceMySqlImpl extends BaseService<DataQualityAssessTask, Long> implements DataQualityAssessTaskService {
//
//    @Autowired
//    private DataQualityAssessTaskMapper dataQualityAssessTaskMapper;
//    @Autowired
//    private IdGeneratorWrapper idGenerator;
//    @Autowired
//    private DataResourcesService dataResourcesService;
//    @Autowired
//    private DataDataSetService dataDataSetService;
//
//    /**
//     * 返回当前Service的主表Mapper对象。
//     *
//     * @return 主表Mapper对象。
//     */
//    @Override
//    protected BaseDaoMapper<DataQualityAssessTask> mapper() {
//        return dataQualityAssessTaskMapper;
//    }
//
//    /**
//     * 保存新增对象。
//     *
//     * @param dataQualityAssessTask 新增对象。
//     * @return 返回新增对象。
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public DataQualityAssessTask saveNew(DataQualityAssessTask dataQualityAssessTask) {
//        dataQualityAssessTaskMapper.insert(this.buildDefaultValue(dataQualityAssessTask));
//        return dataQualityAssessTask;
//    }
//
//    /**
//     * 利用数据库的insertList语法，批量插入对象列表。
//     *
//     * @param dataQualityAssessTaskList 新增对象列表。
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public void saveNewBatch(List<DataQualityAssessTask> dataQualityAssessTaskList) {
//        if (CollUtil.isNotEmpty(dataQualityAssessTaskList)) {
//            dataQualityAssessTaskList.forEach(this::buildDefaultValue);
//            dataQualityAssessTaskMapper.insertList(dataQualityAssessTaskList);
//        }
//    }
//
//    /**
//     * 更新数据对象。
//     *
//     * @param dataQualityAssessTask         更新的对象。
//     * @param originalDataQualityAssessTask 原有数据对象。
//     * @return 成功返回true，否则false。
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public boolean update(DataQualityAssessTask dataQualityAssessTask, DataQualityAssessTask originalDataQualityAssessTask) {
//        MyModelUtil.fillCommonsForUpdate(dataQualityAssessTask, originalDataQualityAssessTask);
//        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
//        UpdateWrapper<DataQualityAssessTask> uw = this.createUpdateQueryForNullValue(dataQualityAssessTask, dataQualityAssessTask.getId());
//        return dataQualityAssessTaskMapper.update(dataQualityAssessTask, uw) == 1;
//    }
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public boolean updateOfPy(DataQualityAssessTask dataQualityAssessTask, DataQualityAssessTask originalDataQualityAssessTask) {
////        dataQualityAssessTask.setCreateUserId(originalDataQualityAssessTask.getCreateUserId());
////        dataQualityAssessTask.setCreateTime(originalDataQualityAssessTask.getCreateTime());
//        dataQualityAssessTask.setUpdateTime(new Date());
//        // TODO 设置修改者ID
//        dataQualityAssessTask.setUpdateUserId(1001L);
//        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
////        UpdateWrapper<DataQualityAssessTask> uw = this.createUpdateQueryForNullValue(dataQualityAssessTask, dataQualityAssessTask.getId());
//        return dataQualityAssessTaskMapper.updateById(dataQualityAssessTask) == 1;
//    }
//
//    /**
//     * 删除指定数据。
//     *
//     * @param id 主键Id。
//     * @return 成功返回true，否则false。
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public boolean remove(Long id) {
//        return dataQualityAssessTaskMapper.deleteById(id) == 1;
//    }
//
//    /**
//     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
//     * 如果需要同时获取关联数据，请移步(getDataQualityAssessTaskListWithRelation)方法。
//     *
//     * @param filter  过滤对象。
//     * @param orderBy 排序参数。
//     * @return 查询结果集。
//     */
//    @Override
//    public List<DataQualityAssessTask> getDataQualityAssessTaskList(DataQualityAssessTask filter, String orderBy) {
////        return dataQualityAssessTaskMapper.getDataQualityAssessTaskList(filter, orderBy);
//        LambdaQueryWrapper<DataQualityAssessTask> queryWrapper = new LambdaQueryWrapper<>(filter);
//        if (filter != null && filter.getSearchString() != null && !filter.getSearchString().isEmpty()) {
//            // 查询到匹配的数据
//            String searchString = filter.getSearchString();
//            // Step 1: 查询符合条件的 lmd_data_resources ID 列表
//            List<Long> resourceIdList = dataResourcesService.selectList(new LambdaQueryWrapper<DataResources>()
//                    .like(DataResources::getFileName, searchString)
//                    .or().like(DataResources::getFileFormat, searchString)
//                    .or().like(DataResources::getFileRemark, searchString)
//            ).stream().map(DataResources::getId).collect(Collectors.toList());
//            // Step 1: 查询符合条件的 lmd_data_dataset ID 列表
//            List<Long> datasetIdList = dataDataSetService.selectList(new LambdaQueryWrapper<DataDataSet>()
//                    .like(DataDataSet::getDatasetName, searchString)
//            ).stream().map(DataDataSet::getId).collect(Collectors.toList());
//            // 动态添加条件：根据资源 ID 列表和数据集 ID 列表查询
//            if (!datasetIdList.isEmpty()) {
//                queryWrapper.and(wrapper -> wrapper.eq(DataQualityAssessTask::getAssessType, "数据集")
//                        .and(w -> datasetIdList.forEach(id ->
//                                w.apply("FIND_IN_SET({0}, REPLACE(assess_id_list, '|', ',')) > 0", id))));
//            }
//            if (!resourceIdList.isEmpty()) {
//                queryWrapper.or(wrapper -> wrapper.eq(DataQualityAssessTask::getAssessType, "文件")
//                        .and(w -> resourceIdList.forEach(id ->
//                                w.apply("FIND_IN_SET({0}, REPLACE(assess_id_list, '|', ',')) > 0", id))));
//            }
//        }
//        return dataQualityAssessTaskMapper.selectList(queryWrapper);
//    }
//
//    /**
//     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
//     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
//     * 如果仅仅需要获取主表数据，请移步(getDataQualityAssessTaskList)，以便获取更好的查询性能。
//     *
//     * @param filter 主表过滤对象。
//     * @param orderBy 排序参数。
//     * @param hasKMeansJson 是否包含kMeansJson
//     * @return 查询结果集。
//     */
//    @Override
//    public List<DataQualityAssessTask> getDataQualityAssessTaskListWithRelation(DataQualityAssessTask filter, String orderBy, Boolean hasKMeansJson) {
//        LambdaQueryWrapper<DataQualityAssessTask> queryWrapper = new LambdaQueryWrapper<>(filter);
//        if (filter != null && filter.getSearchString() != null && !filter.getSearchString().isEmpty()) {
//            // 查询到匹配的数据
//            String searchString = filter.getSearchString();
//            // Step 1: 查询符合条件的 lmd_data_resources ID 列表
//            List<Long> resourceIdList = dataResourcesService.selectList(new LambdaQueryWrapper<DataResources>()
//                    .like(DataResources::getFileName, searchString)
//                    .or().like(DataResources::getFileFormat, searchString)
//                    .or().like(DataResources::getFileRemark, searchString)
//            ).stream().map(DataResources::getId).collect(Collectors.toList());
//            // Step 1: 查询符合条件的 lmd_data_dataset ID 列表
//            List<Long> datasetIdList = dataDataSetService.selectList(new LambdaQueryWrapper<DataDataSet>()
//                    .like(DataDataSet::getDatasetName, searchString)
//            ).stream().map(DataDataSet::getId).collect(Collectors.toList());
//            // 动态添加条件：根据资源 ID 列表和数据集 ID 列表查询
//            if (!datasetIdList.isEmpty()) {
//                queryWrapper.and(wrapper -> wrapper.eq(DataQualityAssessTask::getAssessType, "数据集")
//                        .and(w -> datasetIdList.forEach(id ->
//                                w.apply("FIND_IN_SET({0}, REPLACE(assess_id_list, '|', ',')) > 0", id))));
//            }
//            if (!resourceIdList.isEmpty()) {
//                queryWrapper.or(wrapper -> wrapper.eq(DataQualityAssessTask::getAssessType, "文件")
//                        .and(w -> resourceIdList.forEach(id ->
//                                w.apply("FIND_IN_SET({0}, REPLACE(assess_id_list, '|', ',')) > 0", id))));
//            }
//        }
//        // 根据 hasKMeansJson 的值选择字段
//        if (!hasKMeansJson) {
//            queryWrapper.select(DataQualityAssessTask.class,
//                    tableField -> !tableField.getColumn().equals("k_means_json"));
//        }
//        List<DataQualityAssessTask> resultList = dataQualityAssessTaskMapper.selectList(queryWrapper);
////        List<DataQualityAssessTask> resultList;
////        if (hasKMeansJson) {
////            resultList = dataQualityAssessTaskMapper.getDataQualityAssessTaskList(filter, orderBy);
////        } else {
////            resultList = dataQualityAssessTaskMapper.getDataQualityAssessTaskListNoKMeansJson(filter, orderBy);
////        }
//        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
//        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
//        int batchSize = resultList instanceof Page ? 0 : 1000;
//        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
//        // assessType 为文件，去查询数据资源中文件的名称
//        Set<Long> allAssessIdListOfFile = new HashSet<>();
//        // assessType 为数据集，去查询数据集中数据集的名称
//        Set<Long> allAssessIdListOfDatasetGroup = new HashSet<>();
//        for (DataQualityAssessTask assessTask : resultList) {
//            List<Long> assessIdList = assessIdListStrToAssessIdList(assessTask.getAssessIdList());
//            if (assessIdList.isEmpty()) {
//                continue;
//            }
//            if ("文件".equals(assessTask.getAssessType())) {
//                allAssessIdListOfFile.addAll(assessIdList);
//            } else if ("数据集".equals(assessTask.getAssessType())) {
//                allAssessIdListOfDatasetGroup.addAll(assessIdList);
//            }
//        }
//        if (allAssessIdListOfFile.isEmpty() && allAssessIdListOfDatasetGroup.isEmpty()) {
//            return resultList;
//        }
//        Map<Long, String> dataResourcesFileNameAndDataSetNameMap = new HashMap<>();
//        if (!allAssessIdListOfFile.isEmpty()) {
//            LambdaQueryWrapper<DataResources> dataResourcesQueryWrapper = new LambdaQueryWrapper<>();
//            dataResourcesQueryWrapper.in(DataResources::getId, allAssessIdListOfFile);
//            List<DataResources> dataResourcesList = dataResourcesService.selectList(dataResourcesQueryWrapper);
//            for (DataResources dataResources : dataResourcesList) {
//                dataResourcesFileNameAndDataSetNameMap.put(dataResources.getId(), dataResources.getFileName());
//            }
//        }
//        if (!allAssessIdListOfDatasetGroup.isEmpty()) {
//            // 数据集存储的数据由 dataset_group表 更改为了 datadataset表
////            QueryWrapper<DatasetGroup> datasetGroupQueryWrapper = new QueryWrapper<>();
////            datasetGroupQueryWrapper.in("id", allAssessIdListOfDatasetGroup);
////            List<DatasetGroup> datasetGroupList = datasetGroupMapper.selectList(datasetGroupQueryWrapper);
//            List<DataDataSet> dataDataSetList =
//                    dataDataSetService.selectList(new LambdaQueryWrapper<DataDataSet>().in(DataDataSet::getId, allAssessIdListOfDatasetGroup));
//            for (DataDataSet dataDataSet : dataDataSetList) {
//                dataResourcesFileNameAndDataSetNameMap.put(dataDataSet.getId(), dataDataSet.getDatasetName());
//            }
//        }
//        for (DataQualityAssessTask assessTask : resultList) {
//            List<Long> assessIdList = assessIdListStrToAssessIdList(assessTask.getAssessIdList());
//            if (assessIdList.isEmpty()) {
//                continue;
//            }
//            Map<String, String> assessIdFileNameMap = new HashMap<>();
//            for (Long assessId : assessIdList) {
//                if (dataResourcesFileNameAndDataSetNameMap.containsKey(assessId)) {
//                    assessIdFileNameMap.put(String.valueOf(assessId), dataResourcesFileNameAndDataSetNameMap.get(assessId));
//                }
//            }
//            assessTask.setAssessIdListMap(assessIdFileNameMap);
//        }
//        return resultList;
//    }
//
//    @NotNull
//    private static List<Long> assessIdListStrToAssessIdList(String assessIdListStr) {
//        // 创建一个Long类型的列表来存储转换后的数值
//        List<Long> assessIdList = new ArrayList<>();
//        if (StrUtil.isBlank(assessIdListStr)) {
//            return assessIdList;
//        }
//        // 将 assessIdListStr 以“|”分割成数组 assessIdList
//        // 使用split方法分割字符串
//        String[] assessIdStrArray = assessIdListStr.split("\\|");
//        // 遍历字符串数组，将每个元素转换为Long并添加到列表中
//        for (String assessId : assessIdStrArray) {
//            try {
//                assessIdList.add(Long.parseLong(assessId));
//            } catch (NumberFormatException e) {
//                log.warn("无效的assessId：{}", assessId);
//                // 可以在这里处理错误，例如跳过无效的输入或记录错误等
//            }
//        }
//        return assessIdList;
//    }
//
//    /**
//     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
//     *
//     * @param filter      过滤对象。
//     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
//     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
//     * @param orderBy     排序字符串，ORDER BY从句的参数。
//     * @return 分组过滤结果集。
//     */
//    @Override
//    public List<DataQualityAssessTask> getGroupedDataQualityAssessTaskListWithRelation(
//            DataQualityAssessTask filter, String groupSelect, String groupBy, String orderBy) {
//        List<DataQualityAssessTask> resultList =
//                dataQualityAssessTaskMapper.getGroupedDataQualityAssessTaskList(filter, groupSelect, groupBy, orderBy);
//        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
//        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
//        int batchSize = resultList instanceof Page ? 0 : 1000;
//        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
//        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
//        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
//        return resultList;
//    }
//
//    private DataQualityAssessTask buildDefaultValue(DataQualityAssessTask dataQualityAssessTask) {
//        if (dataQualityAssessTask.getId() == null) {
//            dataQualityAssessTask.setId(idGenerator.nextLongId());
//        }
//        MyModelUtil.fillCommonsForInsert(dataQualityAssessTask);
//        dataQualityAssessTask.setIsDeleted(GlobalDeletedFlag.NORMAL);
//        return dataQualityAssessTask;
//    }
//
//    /**
//     * 获取相关的文件的大小。
//     *
//     * @param assessTask 数据对象。
//     * @return 字节大小。
//     */
//    @Override
//    public BigDecimal getRelatedFileSize(DataQualityAssessTask assessTask) {
//        BigDecimal fileSizeSum = BigDecimal.ZERO;
//        try {
//            List<Long> assessIdListLong = assessIdListStrToAssessIdList(assessTask.getAssessIdList());
//            if (assessIdListLong.isEmpty()) {
//                return fileSizeSum;
//            }
//            LambdaQueryWrapper<DataResources> dataResourcesQueryWrapper = new LambdaQueryWrapper<>();
//            dataResourcesQueryWrapper.in(DataResources::getId, assessIdListLong);
//            List<DataResources> dataResourcesList = dataResourcesService.selectList(dataResourcesQueryWrapper);
//            for (DataResources dataResources : dataResourcesList) {
//                fileSizeSum = fileSizeSum.add(BigDecimal.valueOf(dataResources.getFileSize()));
//            }
//            return fileSizeSum;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return fileSizeSum;
//        }
//    }
//}
