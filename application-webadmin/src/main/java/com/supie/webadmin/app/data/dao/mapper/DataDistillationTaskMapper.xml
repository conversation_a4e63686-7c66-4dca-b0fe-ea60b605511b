<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.data.dao.DataDistillationTaskMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.data.model.DataDistillationTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="model_id" jdbcType="BIGINT" property="modelId"/>
        <result column="seed_data_set_id" jdbcType="BIGINT" property="seedDataSetId"/>
        <result column="data_set_id" jdbcType="BIGINT" property="dataSetId"/>
        <result column="data_set_number" jdbcType="BIGINT" property="dataSetNumber"/>
        <result column="task_state" jdbcType="INTEGER" property="taskState"/>
        <result column="distillation_strategy" jdbcType="VARCHAR" property="distillationStrategy"/>
        <result column="distillation_config" jdbcType="LONGVARCHAR" property="distillationConfig"/>
        <result column="distillation_data_type" jdbcType="INTEGER" property="distillationDataType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="seed_data_set_name" jdbcType="VARCHAR" property="seedDataSetName"/>
        <result column="data_set_name" jdbcType="VARCHAR" property="dataSetName"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_data_distillation_task
            (id,
            str_id,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            task_name,
            model_type,
            model_id,
            seed_data_set_id,
            data_set_id,
            data_set_number,
            task_state,
            distillation_strategy,
            distillation_config,
            distillation_data_type,
            start_time,
            end_time,
            model_name,
            seed_data_set_name,
            data_set_name)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.taskName},
            #{item.modelType},
            #{item.modelId},
            #{item.seedDataSetId},
            #{item.dataSetId},
            #{item.dataSetNumber},
            #{item.taskState},
            #{item.distillationStrategy},
            #{item.distillationConfig},
            #{item.distillationDataType},
            #{item.startTime},
            #{item.endTime},
            #{item.modelName},
            #{item.seedDataSetName},
            #{item.dataSetName})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.data.dao.DataDistillationTaskMapper.inputFilterRef"/>
        AND lmd_data_distillation_task.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="dataDistillationTaskFilter != null">
            <if test="dataDistillationTaskFilter.id != null">
                AND lmd_data_distillation_task.id = #{dataDistillationTaskFilter.id}
            </if>
            <if test="dataDistillationTaskFilter.strId != null and dataDistillationTaskFilter.strId != ''">
                AND lmd_data_distillation_task.str_id = #{dataDistillationTaskFilter.strId}
            </if>
            <if test="dataDistillationTaskFilter.createTimeStart != null and dataDistillationTaskFilter.createTimeStart != ''">
                AND lmd_data_distillation_task.create_time &gt;= #{dataDistillationTaskFilter.createTimeStart}
            </if>
            <if test="dataDistillationTaskFilter.createTimeEnd != null and dataDistillationTaskFilter.createTimeEnd != ''">
                AND lmd_data_distillation_task.create_time &lt;= #{dataDistillationTaskFilter.createTimeEnd}
            </if>
            <if test="dataDistillationTaskFilter.createUserId != null">
                AND lmd_data_distillation_task.create_user_id = #{dataDistillationTaskFilter.createUserId}
            </if>
            <if test="dataDistillationTaskFilter.updateTimeStart != null and dataDistillationTaskFilter.updateTimeStart != ''">
                AND lmd_data_distillation_task.update_time &gt;= #{dataDistillationTaskFilter.updateTimeStart}
            </if>
            <if test="dataDistillationTaskFilter.updateTimeEnd != null and dataDistillationTaskFilter.updateTimeEnd != ''">
                AND lmd_data_distillation_task.update_time &lt;= #{dataDistillationTaskFilter.updateTimeEnd}
            </if>
            <if test="dataDistillationTaskFilter.updateUserId != null">
                AND lmd_data_distillation_task.update_user_id = #{dataDistillationTaskFilter.updateUserId}
            </if>
            <if test="dataDistillationTaskFilter.dataUserId != null">
                AND lmd_data_distillation_task.data_user_id = #{dataDistillationTaskFilter.dataUserId}
            </if>
            <if test="dataDistillationTaskFilter.dataDeptId != null">
                AND lmd_data_distillation_task.data_dept_id = #{dataDistillationTaskFilter.dataDeptId}
            </if>
            <if test="dataDistillationTaskFilter.taskName != null and dataDistillationTaskFilter.taskName != ''">
                <bind name = "safeDataDistillationTaskTaskName" value = "'%' + dataDistillationTaskFilter.taskName + '%'" />
                AND lmd_data_distillation_task.task_name LIKE #{safeDataDistillationTaskTaskName}
            </if>
            <if test="dataDistillationTaskFilter.modelType != null and dataDistillationTaskFilter.modelType != ''">
                AND lmd_data_distillation_task.model_type = #{dataDistillationTaskFilter.modelType}
            </if>
            <if test="dataDistillationTaskFilter.modelId != null">
                AND lmd_data_distillation_task.model_id = #{dataDistillationTaskFilter.modelId}
            </if>
            <if test="dataDistillationTaskFilter.seedDataSetId != null">
                AND lmd_data_distillation_task.seed_data_set_id = #{dataDistillationTaskFilter.seedDataSetId}
            </if>
            <if test="dataDistillationTaskFilter.dataSetId != null">
                AND lmd_data_distillation_task.data_set_id = #{dataDistillationTaskFilter.dataSetId}
            </if>
            <if test="dataDistillationTaskFilter.dataSetNumberStart != null">
                AND lmd_data_distillation_task.data_set_number &gt;= #{dataDistillationTaskFilter.dataSetNumberStart}
            </if>
            <if test="dataDistillationTaskFilter.dataSetNumberEnd != null">
                AND lmd_data_distillation_task.data_set_number &lt;= #{dataDistillationTaskFilter.dataSetNumberEnd}
            </if>
            <if test="dataDistillationTaskFilter.taskState != null">
                AND lmd_data_distillation_task.task_state = #{dataDistillationTaskFilter.taskState}
            </if>
            <if test="dataDistillationTaskFilter.distillationStrategy != null and dataDistillationTaskFilter.distillationStrategy != ''">
                AND lmd_data_distillation_task.distillation_strategy = #{dataDistillationTaskFilter.distillationStrategy}
            </if>
            <if test="dataDistillationTaskFilter.distillationConfig != null and dataDistillationTaskFilter.distillationConfig != ''">
                <bind name = "safeDataDistillationTaskDistillationConfig" value = "'%' + dataDistillationTaskFilter.distillationConfig + '%'" />
                AND lmd_data_distillation_task.distillation_config LIKE #{safeDataDistillationTaskDistillationConfig}
            </if>
            <if test="dataDistillationTaskFilter.distillationDataType != null">
                AND lmd_data_distillation_task.distillation_data_type = #{dataDistillationTaskFilter.distillationDataType}
            </if>
            <if test="dataDistillationTaskFilter.startTimeStart != null and dataDistillationTaskFilter.startTimeStart != ''">
                AND lmd_data_distillation_task.start_time &gt;= #{dataDistillationTaskFilter.startTimeStart}
            </if>
            <if test="dataDistillationTaskFilter.startTimeEnd != null and dataDistillationTaskFilter.startTimeEnd != ''">
                AND lmd_data_distillation_task.start_time &lt;=  #{dataDistillationTaskFilter.startTimeEnd}
            </if>
            <if test="dataDistillationTaskFilter.endTimeStart != null and dataDistillationTaskFilter.endTimeStart != ''">
                AND lmd_data_distillation_task.end_time &gt;=  #{dataDistillationTaskFilter.endTimeStart}
            </if>
            <if test="dataDistillationTaskFilter.endTimeEnd != null and dataDistillationTaskFilter.endTimeEnd != ''">
                AND lmd_data_distillation_task.endTime &lt;=  #{dataDistillationTaskFilter.endTimeEnd}
            </if>

            <if test="dataDistillationTaskFilter.modelName != null and dataDistillationTaskFilter.modelName != ''">
                <bind name = "safeDataDistillationTaskModelName" value = "'%' + dataDistillationTaskFilter.modelName + '%'" />
                AND lmd_data_distillation_task.model_name LIKE #{safeDataDistillationTaskModelName}
            </if>
            <if test="dataDistillationTaskFilter.seedDataSetName != null and dataDistillationTaskFilter.seedDataSetName != ''">
                <bind name = "safeDataDistillationSeedDataSetName" value = "'%' + dataDistillationTaskFilter.seedDataSetName + '%'" />
                AND lmd_data_distillation_task.seed_data_set_name LIKE #{safeDataDistillationSeedDataSetName}
            </if>
            <if test="dataDistillationTaskFilter.dataSetName != null and dataDistillationTaskFilter.dataSetName != ''">
                <bind name = "safeDataDistillationDataSetName" value = "'%' + dataDistillationTaskFilter.dataSetName + '%'" />
                AND lmd_data_distillation_task.data_set_name LIKE #{safeDataDistillationDataSetName}
            </if>
            <if test="dataDistillationTaskFilter.searchString != null and dataDistillationTaskFilter.searchString != ''">
                <bind name = "safeDataDistillationTaskSearchString" value = "'%' + dataDistillationTaskFilter.searchString + '%'" />
                AND CONCAT(IFNULL(lmd_data_distillation_task.task_name,''), IFNULL(lmd_data_distillation_task.distillation_strategy,''), IFNULL(lmd_data_distillation_task.distillation_config,''))    , IFNULL(lmd_data_distillation_task.model_name,'')) , IFNULL(lmd_data_distillation_task.seed_data_set_name,'')) , IFNULL(lmd_data_distillation_task.data_set_name,''))  LIKE #{safeDataDistillationTaskSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDataDistillationTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataDistillationTask">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_data_distillation_task
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_data_distillation_task
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDataDistillationTaskList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.data.model.DataDistillationTask">
        SELECT * FROM lmd_data_distillation_task
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
