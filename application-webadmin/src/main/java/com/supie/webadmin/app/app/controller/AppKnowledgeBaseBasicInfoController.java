package com.supie.webadmin.app.app.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.webadmin.app.app.dto.AppKnowledgeBaseBasicInfoDto;
import com.supie.webadmin.app.app.model.AppKnowledgeBaseBasicInfo;
import com.supie.webadmin.app.app.service.AppKnowledgeBaseBasicInfoService;
import com.supie.webadmin.app.app.vo.AppKnowledgeBaseBasicInfoVo;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 应用中心-知识库的基础信息表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Tag(name = "应用中心-知识库的基础信息表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/appKnowledgeBaseBasicInfo")
public class AppKnowledgeBaseBasicInfoController {

    @Autowired
    private AppKnowledgeBaseBasicInfoService appKnowledgeBaseBasicInfoService;

    /**
     * 新增应用中心-知识库的基础信息表数据。
     *
     * @param appKnowledgeBaseBasicInfoDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseBasicInfoDto.id",
            "appKnowledgeBaseBasicInfoDto.searchString",
            "appKnowledgeBaseBasicInfoDto.createTimeStart",
            "appKnowledgeBaseBasicInfoDto.createTimeEnd",
            "appKnowledgeBaseBasicInfoDto.updateTimeStart",
            "appKnowledgeBaseBasicInfoDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody AppKnowledgeBaseBasicInfoDto appKnowledgeBaseBasicInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appKnowledgeBaseBasicInfoDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppKnowledgeBaseBasicInfo appKnowledgeBaseBasicInfo = MyModelUtil.copyTo(appKnowledgeBaseBasicInfoDto, AppKnowledgeBaseBasicInfo.class);
        appKnowledgeBaseBasicInfo = appKnowledgeBaseBasicInfoService.saveNew(appKnowledgeBaseBasicInfo);
        return ResponseResult.success(appKnowledgeBaseBasicInfo.getId());
    }

    /**
     * 更新应用中心-知识库的基础信息表数据。
     *
     * @param appKnowledgeBaseBasicInfoDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appKnowledgeBaseBasicInfoDto.searchString",
            "appKnowledgeBaseBasicInfoDto.createTimeStart",
            "appKnowledgeBaseBasicInfoDto.createTimeEnd",
            "appKnowledgeBaseBasicInfoDto.updateTimeStart",
            "appKnowledgeBaseBasicInfoDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody AppKnowledgeBaseBasicInfoDto appKnowledgeBaseBasicInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appKnowledgeBaseBasicInfoDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppKnowledgeBaseBasicInfo appKnowledgeBaseBasicInfo = MyModelUtil.copyTo(appKnowledgeBaseBasicInfoDto, AppKnowledgeBaseBasicInfo.class);
        AppKnowledgeBaseBasicInfo originalAppKnowledgeBaseBasicInfo = appKnowledgeBaseBasicInfoService.getById(appKnowledgeBaseBasicInfo.getId());
        if (originalAppKnowledgeBaseBasicInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appKnowledgeBaseBasicInfoService.update(appKnowledgeBaseBasicInfo, originalAppKnowledgeBaseBasicInfo)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除应用中心-知识库的基础信息表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 列出符合过滤条件的应用中心-知识库的基础信息表列表。
     *
     * @param appKnowledgeBaseBasicInfoDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<AppKnowledgeBaseBasicInfoVo>> list(
            @MyRequestBody AppKnowledgeBaseBasicInfoDto appKnowledgeBaseBasicInfoDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppKnowledgeBaseBasicInfo appKnowledgeBaseBasicInfoFilter = MyModelUtil.copyTo(appKnowledgeBaseBasicInfoDtoFilter, AppKnowledgeBaseBasicInfo.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppKnowledgeBaseBasicInfo.class);
        List<AppKnowledgeBaseBasicInfo> appKnowledgeBaseBasicInfoList =
                appKnowledgeBaseBasicInfoService.getAppKnowledgeBaseBasicInfoListWithRelation(appKnowledgeBaseBasicInfoFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(appKnowledgeBaseBasicInfoList, AppKnowledgeBaseBasicInfo.INSTANCE));
    }

    /**
     * 分组列出符合过滤条件的应用中心-知识库的基础信息表列表。
     *
     * @param appKnowledgeBaseBasicInfoDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<AppKnowledgeBaseBasicInfoVo>> listWithGroup(
            @MyRequestBody AppKnowledgeBaseBasicInfoDto appKnowledgeBaseBasicInfoDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppKnowledgeBaseBasicInfo.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, AppKnowledgeBaseBasicInfo.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        AppKnowledgeBaseBasicInfo filter = MyModelUtil.copyTo(appKnowledgeBaseBasicInfoDtoFilter, AppKnowledgeBaseBasicInfo.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<AppKnowledgeBaseBasicInfo> resultList = appKnowledgeBaseBasicInfoService.getGroupedAppKnowledgeBaseBasicInfoListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, AppKnowledgeBaseBasicInfo.INSTANCE));
    }

    /**
     * 查看指定应用中心-知识库的基础信息表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<AppKnowledgeBaseBasicInfoVo> view(@RequestParam Long id) {
        AppKnowledgeBaseBasicInfo appKnowledgeBaseBasicInfo = appKnowledgeBaseBasicInfoService.getByIdWithRelation(id, MyRelationParam.full());
        if (appKnowledgeBaseBasicInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        AppKnowledgeBaseBasicInfoVo appKnowledgeBaseBasicInfoVo = AppKnowledgeBaseBasicInfo.INSTANCE.fromModel(appKnowledgeBaseBasicInfo);
        return ResponseResult.success(appKnowledgeBaseBasicInfoVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        AppKnowledgeBaseBasicInfo originalAppKnowledgeBaseBasicInfo = appKnowledgeBaseBasicInfoService.getById(id);
        if (originalAppKnowledgeBaseBasicInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appKnowledgeBaseBasicInfoService.remove(originalAppKnowledgeBaseBasicInfo)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
