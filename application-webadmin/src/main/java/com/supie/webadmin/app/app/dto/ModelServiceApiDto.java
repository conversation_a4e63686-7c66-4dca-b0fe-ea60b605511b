package com.supie.webadmin.app.app.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

import java.util.Date;
import java.util.Map;

/**
 * 服务api关联表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "ModelServiceApiDto对象")
@Data
public class ModelServiceApiDto {

    /**
     * 分享码
     */
    @Schema(description = "分享码")
    private String shareCode;

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 服务名称。
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 服务id。
     */
    @Schema(description = "服务id")
    private Long serviceId;

    /**
     * 服务类型(1.部署模型 2.应用助手 3.知识助手 4.流程编排)。
     */
    @Schema(description = "服务类型(1.部署模型 2.应用助手 3.知识助手 4.流程编排)")
    private Integer serviceType;

    /**
     * 服务类型id。
     */
    @Schema(description = "服务类型id")
    private Long serviceTypeId;

    /**
     * 模型id。
     */
    @Schema(description = "模型id")
    private Long modelId;

    /**
     * 模型类型。
     */
    @Schema(description = "模型类型")
    private String modelType;

    /**
     * 开始时间。
     */
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间。
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 是否发布。
     */
    @Schema(description = "是否发布")
    private Integer isPublish;

    /**
     * api配置
     */
    @Schema(description = "api配置")
    private String apiConfig;

    /**
     * 配额总量 -1为无限制
     */
    @Schema(description = "配额总量 -1为无限制")
    private Integer semaphoreSum;

    /**
     * 并发量 -1为无限制
     */
    @Schema(description = "配额总量 -1为无限制")
    private Integer semaphoreNumber;

    /**
     * IP白名单
     */
    @Schema(description = "IP白名单")
    private String ipWhiteList;

    /**
     * 调用总次数 和 当前并发量
     */
    @Schema(description = "调用总次数 和 当前并发量")
    private Map<String,Long> SemaphoreNumberAndSum;

    /**
     * 当前模型服务API调用总次数
     */
    @Schema(description = "当前模型服务API调用总次数")
    private Integer curInvocationSum;

    /**
     * 是否关联到推理平台(-1:未关联,1:关联)
     */
    @Schema(description = "是否关联到推理平台(-1:未关联,1:关联)")
    private Integer releaseApp;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * startTime 范围过滤起始值(>=)。
     */
    @Schema(description = "startTime 范围过滤起始值(>=)")
    private String startTimeStart;

    /**
     * startTime 范围过滤结束值(<=)。
     */
    @Schema(description = "startTime 范围过滤结束值(<=)")
    private String startTimeEnd;

    /**
     * endTime 范围过滤起始值(>=)。
     */
    @Schema(description = "endTime 范围过滤起始值(>=)")
    private String endTimeStart;

    /**
     * endTime 范围过滤结束值(<=)。
     */
    @Schema(description = "endTime 范围过滤结束值(<=)")
    private String endTimeEnd;

    /**
     * str_id LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
