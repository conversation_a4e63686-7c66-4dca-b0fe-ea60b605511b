package com.supie.webadmin.app.controller;


import com.supie.common.core.annotation.DisableDataFilter;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.webadmin.app.vo.*;
import com.supie.webadmin.app.dto.*;
import com.supie.webadmin.app.model.*;
import com.supie.webadmin.app.service.*;
import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 业务字典表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "业务字典表管理接口")
@Slf4j
@Controller(value = "appBusinessDictController")
@ResponseBody
@RequestMapping("/admin/app/businessDict")
public class BusinessDictController {
    @Autowired
    @Qualifier("appBusinessDictService")
    private AppBusinessDictService businessDictService;
    @Resource(name="idGenerator")
    private IdGeneratorWrapper idGenerator;

    /**
     * 新增业务字典表数据。
     *
     * @param businessDictDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "businessDictDto.id",
            "businessDictDto.searchString",
            "businessDictDto.updateTimeStart",
            "businessDictDto.updateTimeEnd",
            "businessDictDto.createTimeStart",
            "businessDictDto.createTimeEnd"})
    ////@SaCheckPermission("businessDict.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody BusinessDictDto businessDictDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessDictDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessDict businessDict = MyModelUtil.copyTo(businessDictDto, BusinessDict.class);
        businessDict = businessDictService.saveNew(businessDict);
        return ResponseResult.success(businessDict.getId());
    }

    /**
     * 更新业务字典表数据。
     *
     * @param businessDictDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "businessDictDto.searchString",
            "businessDictDto.updateTimeStart",
            "businessDictDto.updateTimeEnd",
            "businessDictDto.createTimeStart",
            "businessDictDto.createTimeEnd"})
    ////@SaCheckPermission("businessDict.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody BusinessDictDto businessDictDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessDictDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessDict businessDict = MyModelUtil.copyTo(businessDictDto, BusinessDict.class);
        BusinessDict originalBusinessDict = businessDictService.getById(businessDict.getId());
        if (originalBusinessDict == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!businessDictService.update(businessDict, originalBusinessDict)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除业务字典表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("businessDict.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除业务字典表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("businessDict.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的业务字典表列表。
     *
     * @param businessDictDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @DisableDataFilter
    ////@SaCheckPermission("businessDict.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<BusinessDictVo>> list(
            @MyRequestBody BusinessDictDto businessDictDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        BusinessDict businessDictFilter = MyModelUtil.copyTo(businessDictDtoFilter, BusinessDict.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessDict.class);
        List<BusinessDict> businessDictList =
                businessDictService.getBusinessDictListWithRelation(businessDictFilter, orderBy);
        MyPageData<BusinessDictVo> bu = MyPageUtil.makeResponseData(businessDictList, BusinessDictVo.class);

        log.info("结果{}",bu);

        return ResponseResult.success(MyPageUtil.makeResponseData(businessDictList, BusinessDictVo.class));
    }

    /**
     * 分组列出符合过滤条件的业务字典表列表。
     *
     * @param businessDictDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("businessDict.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<BusinessDictVo>> listWithGroup(
            @MyRequestBody BusinessDictDto businessDictDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessDict.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, BusinessDict.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        BusinessDict filter = MyModelUtil.copyTo(businessDictDtoFilter, BusinessDict.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<BusinessDict> resultList = businessDictService.getGroupedBusinessDictListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, BusinessDictVo.class));
    }

    /**
     * 查看指定业务字典表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////@SaCheckPermission("businessDict.view")
    @GetMapping("/view")
    public ResponseResult<BusinessDictVo> view(@RequestParam Long id) {
        BusinessDict businessDict = businessDictService.getByIdWithRelation(id, MyRelationParam.full());
        if (businessDict == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        BusinessDictVo businessDictVo = MyModelUtil.copyTo(businessDict, BusinessDictVo.class);
        return ResponseResult.success(businessDictVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        BusinessDict originalBusinessDict = businessDictService.getById(id);
        if (originalBusinessDict == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!businessDictService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
