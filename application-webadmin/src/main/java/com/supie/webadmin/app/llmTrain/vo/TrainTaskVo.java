package com.supie.webadmin.app.llmTrain.vo;

import com.supie.webadmin.app.data.model.DataDataSet;
import com.supie.webadmin.app.llm.model.ModelBasic;
import com.supie.webadmin.app.llmTrain.model.ModelWeightMerge;
import com.supie.webadmin.app.other.model.FileExportRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import com.supie.common.core.base.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TrainTaskVO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "TrainTaskVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainTaskVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 任务类型。
     */
    @Schema(description = "任务类型")
    private String trainTaskType;

    /**
     * 任务状态（未开始，训练中，暂停，失败，已完成，已发布）。
     */
    @Schema(description = "任务状态（未开始，训练中，暂停，失败，已完成，已发布）")
    private String trainTaskState;

    /**
     * 描述。
     */
    @Schema(description = "描述")
    private String trainTaskDescribe;

    /**
     * 训练参数配置。
     */
    @Schema(description = "训练参数配置")
    private String trainTaskConfigJson;

    /**
     * deepspeed多卡训练配置信息 deepspeed_config
     */
    @Schema(description = "deepspeed多卡训练配置信息")
    private String deepspeedConfigJson;

    /**
     * 通用参数JSON。
     */
    @Schema(description = "通用参数JSON")
    private String commonParameterJson;

    /**
     * 权重合并配置 weights_merged_config_json
     */
    @Schema(description = "权重合并配置")
    private String weightsMergedConfigJson;

    /**
     * 训练方法。
     */
    @Schema(description = "训练方法")
    private String trainTaskMethod;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;
    /**
     * 培训任务id。
     */
    @Schema(description = "培训任务id")
    private Long trainingTaskId;
    /**
     * 课程id。
     */
    @Schema(description = "课程id")
    private Long courseId;
    /**
     * 用户id。
     */
    @Schema(description = "用户id")
    private Long userId;
    /**
     * 课程与功能模块关联关系id。
     */
    @Schema(description = "课程与功能模块关联关系id")
    private Long courseFunctionRelationId;

    /**
     * 创建用户id字典。
     */
    @Schema(description = "创建用户id字典")
    private Map<String, Object> createUserIdDict;

    /**
     * 训练日志信息ID。
     */
    @Schema(description = "训练日志信息ID")
    private Long trainInfoId;

    /**
     *mindformers训练yaml配置
     */
    @Schema(description = "mindformers训练yaml配置")
    private String yamlConfig;

    /**
     * 训练开始时间。
     */
    @Schema(description = "训练开始时间")
    private Date trainStartTime;

    /**
     * 训练结束时间。
     */
    @Schema(description = "训练结束时间")
    private Date trainEndTime;

    /**
     * DataDataSetList
     */
    @Schema(description = "DataDataSetList")
    private List<DataDataSet> dataDataSetList;

    /**
     * 实验计划id
     */
    @Schema(description = "实验计划id")
    private Long expermentPlanId;

    /**
     * 基础模型id
     */
    @Schema(description = "基础模型id")
    private Long baseModelId;

    /**
     * 训练任务id
     */
    @Schema(description = "训练任务id")
    private Long trainTaskId;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 模型标识符
     */
    @Schema(description = "模型标识符")
    private String modelCode;

    /**
     * 版本管理状态：最新的为now（历史的为版本更迭时的当前时间）
     */
    @Schema(description = "版本管理状态：最新的为now（历史的为版本更迭时的当前时间）")
    private String editionStatus;

    /**
     * 显卡数量
     */
    @Schema(description = "显卡数量")
    private Integer graphicNumber;

    /**
     * 训练框架（1，torch  2，mindformers）
     */
    @Schema(description = "训练框架（1，torch  2，mindformers）")
    private Integer trainFramework;

    /**
     * 使用时间段
     */
    @Schema(description = "使用时间段")
    private String timePeriods;

    /**
     * 模型权重合并id
     */
    @Schema(description = "模型权重合并id")
    private Long mergeId;

    private ModelBasic modelBasic;

    private List<ModelWeightMerge> modelWeightMergeList;

    private FileExportRecord FileExportRecord;

}
