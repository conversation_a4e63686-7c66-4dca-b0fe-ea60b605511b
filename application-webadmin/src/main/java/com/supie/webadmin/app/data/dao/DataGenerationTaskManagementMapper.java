package com.supie.webadmin.app.data.dao;

import com.supie.webadmin.app.data.model.DataGenerationTaskManagement;
import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 数据管理-数据生成任务管理表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@EnableDataPerm
public interface DataGenerationTaskManagementMapper extends BaseDaoMapper<DataGenerationTaskManagement> {

    /**
     * 批量插入对象列表。
     *
     * @param dataGenerationTaskManagementList 新增对象列表。
     */
    void insertList(List<DataGenerationTaskManagement> dataGenerationTaskManagementList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param dataGenerationTaskManagementFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<DataGenerationTaskManagement> getGroupedDataGenerationTaskManagementList(
            @Param("dataGenerationTaskManagementFilter") DataGenerationTaskManagement dataGenerationTaskManagementFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param dataGenerationTaskManagementFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<DataGenerationTaskManagement> getDataGenerationTaskManagementList(
            @Param("dataGenerationTaskManagementFilter") DataGenerationTaskManagement dataGenerationTaskManagementFilter, @Param("orderBy") String orderBy);
}
