package com.supie.webadmin.app.other.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.annotation.MyDataSource;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import com.supie.webadmin.app.other.dao.BusinessDictMapper;
import com.supie.webadmin.app.other.model.BusinessDict;
import com.supie.webadmin.app.other.service.BusinessDictService;
import com.supie.webadmin.config.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 其他-业务字典表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("businessDictService")
@MyDataSource(DataSourceType.MAIN)
public class BusinessDictServiceImpl extends BaseService<BusinessDict, Long> implements BusinessDictService {

    @Autowired
    private BusinessDictMapper businessDictMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<BusinessDict> mapper() {
        return businessDictMapper;
    }

    /**
     * 保存新增对象。
     *
     * @param businessDict 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BusinessDict saveNew(BusinessDict businessDict) {
        businessDictMapper.insert(this.buildDefaultValue(businessDict));
        return businessDict;
    }

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param businessDictList 新增对象列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<BusinessDict> businessDictList) {
        if (CollUtil.isNotEmpty(businessDictList)) {
            businessDictList.forEach(this::buildDefaultValue);
            businessDictMapper.insertList(businessDictList);
        }
    }

    /**
     * 更新数据对象。
     *
     * @param businessDict         更新的对象。
     * @param originalBusinessDict 原有数据对象。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(BusinessDict businessDict, BusinessDict originalBusinessDict) {
        MyModelUtil.fillCommonsForUpdate(businessDict, originalBusinessDict);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<BusinessDict> uw = this.createUpdateQueryForNullValue(businessDict, businessDict.getId());
        return businessDictMapper.update(businessDict, uw) == 1;
    }

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return businessDictMapper.deleteById(id) == 1;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getBusinessDictListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<BusinessDict> getBusinessDictList(BusinessDict filter, String orderBy) {
        return businessDictMapper.getBusinessDictList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getBusinessDictList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<BusinessDict> getBusinessDictListWithRelation(BusinessDict filter, String orderBy) {
        List<BusinessDict> resultList = businessDictMapper.getBusinessDictList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    @Override
    public List<BusinessDict> getGroupedBusinessDictListWithRelation(
            BusinessDict filter, String groupSelect, String groupBy, String orderBy) {
        List<BusinessDict> resultList =
                businessDictMapper.getGroupedBusinessDictList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private BusinessDict buildDefaultValue(BusinessDict businessDict) {
        if (businessDict.getId() == null) {
            businessDict.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(businessDict);
        businessDict.setIsDelete(GlobalDeletedFlag.NORMAL);
        return businessDict;
    }
}
