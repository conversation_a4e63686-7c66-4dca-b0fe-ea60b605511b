<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.app.llm.dao.ModelMyModelMainMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.app.llm.model.ModelMyModelMain">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="base_model_id" jdbcType="BIGINT" property="baseModelId"/>
        <result column="my_model_version_id" jdbcType="BIGINT" property="myModelVersionId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="model_desc" jdbcType="VARCHAR" property="modelDesc"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO lmd_model_my_model_main
            (id,
            str_id,
            base_model_id,
            my_model_version_id,
            model_name,
            model_desc,
            is_delete,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.baseModelId},
            #{item.myModelVersionId},
            #{item.modelName},
            #{item.modelDesc},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.app.llm.dao.ModelMyModelMainMapper.inputFilterRef"/>
        AND lmd_model_my_model_main.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="modelMyModelMainFilter != null">
            <if test="modelMyModelMainFilter.id != null">
                AND lmd_model_my_model_main.id = #{modelMyModelMainFilter.id}
            </if>
            <if test="modelMyModelMainFilter.strId != null and modelMyModelMainFilter.strId != ''">
                AND lmd_model_my_model_main.str_id = #{modelMyModelMainFilter.strId}
            </if>
            <if test="modelMyModelMainFilter.baseModelId != null">
                AND lmd_model_my_model_main.base_model_id = #{modelMyModelMainFilter.baseModelId}
            </if>
            <if test="modelMyModelMainFilter.myModelVersionId != null">
                AND lmd_model_my_model_main.my_model_version_id = #{modelMyModelMainFilter.myModelVersionId}
            </if>
            <if test="modelMyModelMainFilter.modelName != null and modelMyModelMainFilter.modelName != ''">
                <bind name = "safeModelMyModelMainModelName" value = "'%' + modelMyModelMainFilter.modelName + '%'" />
                AND lmd_model_my_model_main.model_name LIKE #{safeModelMyModelMainModelName}
            </if>
            <if test="modelMyModelMainFilter.modelDesc != null and modelMyModelMainFilter.modelDesc != ''">
                <bind name = "safeModelMyModelMainModelDesc" value = "'%' + modelMyModelMainFilter.modelDesc + '%'" />
                AND lmd_model_my_model_main.model_desc LIKE #{safeModelMyModelMainModelDesc}
            </if>
            <if test="modelMyModelMainFilter.isDelete != null">
                AND lmd_model_my_model_main.is_delete = #{modelMyModelMainFilter.isDelete}
            </if>
            <if test="modelMyModelMainFilter.createTimeStart != null and modelMyModelMainFilter.createTimeStart != ''">
                AND lmd_model_my_model_main.create_time &gt;= #{modelMyModelMainFilter.createTimeStart}
            </if>
            <if test="modelMyModelMainFilter.createTimeEnd != null and modelMyModelMainFilter.createTimeEnd != ''">
                AND lmd_model_my_model_main.create_time &lt;= #{modelMyModelMainFilter.createTimeEnd}
            </if>
            <if test="modelMyModelMainFilter.createUserId != null">
                AND lmd_model_my_model_main.create_user_id = #{modelMyModelMainFilter.createUserId}
            </if>
            <if test="modelMyModelMainFilter.updateTimeStart != null and modelMyModelMainFilter.updateTimeStart != ''">
                AND lmd_model_my_model_main.update_time &gt;= #{modelMyModelMainFilter.updateTimeStart}
            </if>
            <if test="modelMyModelMainFilter.updateTimeEnd != null and modelMyModelMainFilter.updateTimeEnd != ''">
                AND lmd_model_my_model_main.update_time &lt;= #{modelMyModelMainFilter.updateTimeEnd}
            </if>
            <if test="modelMyModelMainFilter.updateUserId != null">
                AND lmd_model_my_model_main.update_user_id = #{modelMyModelMainFilter.updateUserId}
            </if>
            <if test="modelMyModelMainFilter.dataUserId != null">
                AND lmd_model_my_model_main.data_user_id = #{modelMyModelMainFilter.dataUserId}
            </if>
            <if test="modelMyModelMainFilter.dataDeptId != null">
                AND lmd_model_my_model_main.data_dept_id = #{modelMyModelMainFilter.dataDeptId}
            </if>
            <if test="modelMyModelMainFilter.searchString != null and modelMyModelMainFilter.searchString != ''">
                <bind name = "safeModelMyModelMainSearchString" value = "'%' + modelMyModelMainFilter.searchString + '%'" />
                AND CONCAT(IFNULL(lmd_model_my_model_main.model_name,''), IFNULL(lmd_model_my_model_main.model_desc,'')) LIKE #{safeModelMyModelMainSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedModelMyModelMainList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.llm.model.ModelMyModelMain">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM lmd_model_my_model_main
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) lmd_model_my_model_main
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getModelMyModelMainList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.app.llm.model.ModelMyModelMain">
        SELECT * FROM lmd_model_my_model_main
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
