<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.knowledgebase.dao.DataMapMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.knowledgebase.model.DataMap">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="map_id" jdbcType="INTEGER" property="mapId"/>
        <result column="tags_str" jdbcType="LONGVARCHAR" property="tagsStr"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="model_info_id" jdbcType="BIGINT" property="modelInfoId"/>
        <result column="data_table_name" jdbcType="VARCHAR" property="dataTableName"/>
        <result column="data_table_id" jdbcType="BIGINT" property="dataTableId"/>
        <result column="map_type" jdbcType="INTEGER" property="mapType"/>
        <result column="map_name" jdbcType="VARCHAR" property="mapName"/>
        <result column="data_config" jdbcType="VARCHAR" property="dataConfig"/>
        <result column="is_copy" jdbcType="INTEGER" property="isCopy"/>
        <result column="modal_type" jdbcType="INTEGER" property="modalType"/>
        <result column="map_name_key" jdbcType="VARCHAR" property="mapNameKey"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="data_json" jdbcType="LONGVARCHAR" property="dataJson"/>
        <result column="vectorize_status" jdbcType="INTEGER" property="vectorizeStatus"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_data_map
        (id,
        str_id,
        is_delete,
        create_time,
        create_user_id,
        update_time,
        update_user_id,
        data_user_id,
        data_dept_id,
        map_id,
        tags_str,
        parent_id,
        data_table_name,
        data_table_id,
        map_type,
        map_name,
        is_copy,
        modal_type,
        map_name_key,
        show_order,
        data_json,
        data_config,
        model_info_id,
        vectorize_status)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.mapId},
            #{item.tagsStr},
            #{item.parentId},
            #{item.dataTableName},
            #{item.dataTableId},
            #{item.mapType},
            #{item.mapName},
            #{item.isCopy},
            #{item.modalType},
            #{item.mapNameKey},
            #{item.showOrder},
            #{item.dataJson},
            #{item.dataConfig},
            #{item.modelInfoId},
            #{item.vectorizeStatus})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.knowledgebase.dao.DataMapMapper.inputFilterRef"/>
        AND ms_data_map.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="dataMapFilter != null">
            <if test="dataMapFilter.id != null">
                AND ms_data_map.id = #{dataMapFilter.id}
            </if>
            <if test="dataMapFilter.modelInfoId != null">
                AND ms_data_map.model_info_id = #{dataMapFilter.modelInfoId}
            </if>
            <if test="dataMapFilter.idList != null and dataMapFilter.idList.size() > 0">
                <foreach collection="dataMapFilter.idList" item="item"
                         open="AND ms_data_map.id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.strId != null and dataMapFilter.strId != ''">
                AND ms_data_map.str_id = #{dataMapFilter.strId}
            </if>
            <if test="dataMapFilter.dataConfig != null and dataMapFilter.dataConfig != ''">
                AND ms_data_map.data_config = #{dataMapFilter.dataConfig}
            </if>
            <if test="dataMapFilter.createTimeStart != null and dataMapFilter.createTimeStart != ''">
                AND ms_data_map.create_time &gt;= #{dataMapFilter.createTimeStart}
            </if>
            <if test="dataMapFilter.createTimeEnd != null and dataMapFilter.createTimeEnd != ''">
                AND ms_data_map.create_time &lt;= #{dataMapFilter.createTimeEnd}
            </if>
            <if test="dataMapFilter.createUserId != null">
                AND ms_data_map.create_user_id = #{dataMapFilter.createUserId}
            </if>
            <if test="dataMapFilter.updateTimeStart != null and dataMapFilter.updateTimeStart != ''">
                AND ms_data_map.update_time &gt;= #{dataMapFilter.updateTimeStart}
            </if>
            <if test="dataMapFilter.updateTimeEnd != null and dataMapFilter.updateTimeEnd != ''">
                AND ms_data_map.update_time &lt;= #{dataMapFilter.updateTimeEnd}
            </if>
            <if test="dataMapFilter.updateUserId != null">
                AND ms_data_map.update_user_id = #{dataMapFilter.updateUserId}
            </if>
            <if test="dataMapFilter.dataUserId != null">
                AND ms_data_map.data_user_id = #{dataMapFilter.dataUserId}
            </if>
            <if test="dataMapFilter.dataDeptId != null">
                AND ms_data_map.data_dept_id = #{dataMapFilter.dataDeptId}
            </if>
            <if test="dataMapFilter.mapId != null">
                AND ms_data_map.map_id = #{dataMapFilter.mapId}
            </if>
            <if test="dataMapFilter.tagsStr != null and dataMapFilter.tagsStr != ''">
                <bind name = "safeDataMapTagsStr" value = "'%' + dataMapFilter.tagsStr + '%'" />
                AND ms_data_map.tags_str LIKE #{safeDataMapTagsStr}
            </if>
            <if test="dataMapFilter.parentId != null">
                AND ms_data_map.parent_id = #{dataMapFilter.parentId}
            </if>
            <if test="dataMapFilter.dataTableName != null and dataMapFilter.dataTableName != ''">
                <bind name = "safeDataMapDataTableName" value = "'%' + dataMapFilter.dataTableName + '%'" />
                AND ms_data_map.data_table_name LIKE #{safeDataMapDataTableName}
            </if>
            <if test="dataMapFilter.dataTableNameList != null and dataMapFilter.dataTableNameList.size() > 0">
                <foreach collection="dataMapFilter.dataTableNameList" item="item"
                         open="AND ms_data_map.data_table_name IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.dataTableId != null">
                AND ms_data_map.data_table_id = #{dataMapFilter.dataTableId}
            </if>
            <if test="dataMapFilter.dataTableIdList != null and dataMapFilter.dataTableIdList.size() > 0">
                <foreach collection="dataMapFilter.dataTableIdList" item="item"
                         open="AND ms_data_map.data_table_id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.mapType != null">
                AND ms_data_map.map_type = #{dataMapFilter.mapType}
            </if>
            <if test="dataMapFilter.mapName != null and dataMapFilter.mapName != ''">
                <bind name = "safeDataMapMapName" value = "'%' + dataMapFilter.mapName + '%'" />
                AND ms_data_map.map_name LIKE #{safeDataMapMapName}
            </if>
            <if test="dataMapFilter.isCopy != null">
                AND ms_data_map.is_copy = #{dataMapFilter.isCopy}
            </if>
            <if test="dataMapFilter.modalType != null">
                AND ms_data_map.modal_type = #{dataMapFilter.modalType}
            </if>
            <if test="dataMapFilter.mapNameKey != null and dataMapFilter.mapNameKey != ''">
                <bind name = "safeDataMapMapNameKey" value = "'%' + dataMapFilter.mapNameKey + '%'" />
                AND ms_data_map.map_name_key LIKE #{safeDataMapMapNameKey}
            </if>
            <if test="dataMapFilter.showOrder != null">
                AND ms_data_map.show_order = #{dataMapFilter.showOrder}
            </if>
            <if test="dataMapFilter.vectorizeStatus != null">
                AND ms_data_map.vectorize_status = #{dataMapFilter.vectorizeStatus}
            </if>
            <if test="dataMapFilter.dataJson != null and dataMapFilter.dataJson != ''">
                <bind name = "safeDataMapDataJson" value = "'%' + dataMapFilter.dataJson + '%'" />
                AND ms_data_map.data_json LIKE #{safeDataMapDataJson}
            </if>
            <if test="dataMapFilter.searchString != null and dataMapFilter.searchString != ''">
                <bind name = "safeDataMapSearchString" value = "'%' + dataMapFilter.searchString + '%'" />
                AND CONCAT(IFNULL(ms_data_map.tags_str,''), IFNULL(ms_data_map.data_table_name,''), IFNULL(ms_data_map.map_name,''), IFNULL(ms_data_map.map_name_key,'')) LIKE #{safeDataMapSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDataMapList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.knowledgebase.model.DataMap">
        SELECT * FROM
        (SELECT
        COUNT(id) id,
        ${groupSelect}
        FROM ms_data_map
        <where>
            <include refid="filterRef"/>
        </where>
        GROUP BY ${groupBy}) ms_data_map
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDataMapList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.knowledgebase.model.DataMap">
        SELECT * FROM ms_data_map
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="findAllChildren" resultType="com.supie.webadmin.application.knowledgebase.model.DataMap" resultMap="BaseResultMap">
        WITH RECURSIVE SubData AS (
            SELECT * FROM ms_data_map WHERE id = #{id}
            UNION ALL
            SELECT ms_data_map.* FROM ms_data_map
            INNER JOIN SubData s ON s.id = ms_data_map.parent_id
        )
        SELECT * FROM SubData
        <where>
            <include refid="filterRefLmd"/>
        </where>
        ORDER BY create_time DESC
    </select>

    <sql id="filterRefLmd">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.knowledgebase.dao.DataMapMapper.inputFilterRefLmd"/>
        AND SubData.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRefLmd">
        <if test="dataMapFilter != null">
            <if test="dataMapFilter.id != null">
                AND SubData.id = #{dataMapFilter.id}
            </if>
            <if test="dataMapFilter.idList != null and dataMapFilter.idList.size() > 0">
                <foreach collection="dataMapFilter.idList" item="item"
                         open="AND SubData.id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.strId != null and dataMapFilter.strId != ''">
                AND SubData.str_id = #{dataMapFilter.strId}
            </if>
            <if test="dataMapFilter.createTimeStart != null and dataMapFilter.createTimeStart != ''">
                AND SubData.create_time &gt;= #{dataMapFilter.createTimeStart}
            </if>
            <if test="dataMapFilter.createTimeEnd != null and dataMapFilter.createTimeEnd != ''">
                AND SubData.create_time &lt;= #{dataMapFilter.createTimeEnd}
            </if>
            <if test="dataMapFilter.createUserId != null">
                AND SubData.create_user_id = #{dataMapFilter.createUserId}
            </if>
            <if test="dataMapFilter.updateTimeStart != null and dataMapFilter.updateTimeStart != ''">
                AND SubData.update_time &gt;= #{dataMapFilter.updateTimeStart}
            </if>
            <if test="dataMapFilter.updateTimeEnd != null and dataMapFilter.updateTimeEnd != ''">
                AND SubData.update_time &lt;= #{dataMapFilter.updateTimeEnd}
            </if>
            <if test="dataMapFilter.updateUserId != null">
                AND SubData.update_user_id = #{dataMapFilter.updateUserId}
            </if>
            <if test="dataMapFilter.dataUserId != null">
                AND SubData.data_user_id = #{dataMapFilter.dataUserId}
            </if>
            <if test="dataMapFilter.dataDeptId != null">
                AND SubData.data_dept_id = #{dataMapFilter.dataDeptId}
            </if>
            <if test="dataMapFilter.mapId != null">
                AND SubData.map_id = #{dataMapFilter.mapId}
            </if>
            <if test="dataMapFilter.tagsStr != null and dataMapFilter.tagsStr != ''">
                <bind name = "safeDataMapTagsStr" value = "'%' + dataMapFilter.tagsStr + '%'" />
                AND SubData.tags_str LIKE #{safeDataMapTagsStr}
            </if>
            <if test="dataMapFilter.parentId != null">
                AND SubData.parent_id = #{dataMapFilter.parentId}
            </if>
            <if test="dataMapFilter.dataTableName != null and dataMapFilter.dataTableName != ''">
                <bind name = "safeDataMapDataTableName" value = "'%' + dataMapFilter.dataTableName + '%'" />
                AND SubData.data_table_name LIKE #{safeDataMapDataTableName}
            </if>
            <if test="dataMapFilter.dataTableNameList != null and dataMapFilter.dataTableNameList.size() > 0">
                <foreach collection="dataMapFilter.dataTableNameList" item="item"
                         open="AND SubData.data_table_name IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.dataTableId != null">
                AND SubData.data_table_id = #{dataMapFilter.dataTableId}
            </if>
            <if test="dataMapFilter.dataTableIdList != null and dataMapFilter.dataTableIdList.size() > 0">
                <foreach collection="dataMapFilter.dataTableIdList" item="item"
                         open="AND SubData.data_table_id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataMapFilter.mapType != null">
                AND SubData.map_type = #{dataMapFilter.mapType}
            </if>
            <if test="dataMapFilter.mapName != null and dataMapFilter.mapName != ''">
                <bind name = "safeDataMapMapName" value = "'%' + dataMapFilter.mapName + '%'" />
                AND SubData.map_name LIKE #{safeDataMapMapName}
            </if>
            <if test="dataMapFilter.modalType != null">
                AND SubData.modal_type = #{dataMapFilter.modalType}
            </if>
            <if test="dataMapFilter.mapNameKey != null and dataMapFilter.mapNameKey != ''">
                <bind name = "safeDataMapMapNameKey" value = "'%' + dataMapFilter.mapNameKey + '%'" />
                AND SubData.map_name_key LIKE #{safeDataMapMapNameKey}
            </if>
            <if test="dataMapFilter.showOrder != null">
                AND SubData.show_order = #{dataMapFilter.showOrder}
            </if>
            <if test="dataMapFilter.vectorizeStatus != null">
                AND SubData.vectorize_status = #{dataMapFilter.vectorizeStatus}
            </if>
            <if test="dataMapFilter.dataJson != null and dataMapFilter.dataJson != ''">
                <bind name = "safeDataMapDataJson" value = "'%' + dataMapFilter.dataJson + '%'" />
                AND SubData.data_json LIKE #{safeDataMapDataJson}
            </if>
            <if test="dataMapFilter.searchString != null and dataMapFilter.searchString != ''">
                <bind name = "safeDataMapSearchString" value = "'%' + dataMapFilter.searchString + '%'" />
                AND CONCAT(IFNULL(SubData.tags_str,''), IFNULL(SubData.data_table_name,''), IFNULL(SubData.map_name,''), IFNULL(SubData.map_name_key,'')) LIKE #{safeDataMapSearchString}
            </if>
        </if>
    </sql>



</mapper>
