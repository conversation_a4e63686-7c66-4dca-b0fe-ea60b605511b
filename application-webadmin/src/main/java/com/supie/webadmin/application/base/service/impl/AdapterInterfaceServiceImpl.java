package com.supie.webadmin.application.base.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.supie.common.core.object.TokenData;
import com.supie.common.pythonclient.config.PythonClientProperties;
import com.supie.webadmin.application.base.service.AdapterInterfaceService;
import com.supie.webadmin.application.base.model.ModelDeployTask;
import com.supie.webadmin.application.base.service.ModelDeployTaskService;
import com.supie.webadmin.application.base.dao.ModelServiceRelationMapper;
import com.supie.webadmin.application.knowledgebase.dao.AppAssistantBasicMapper;
import com.supie.webadmin.application.knowledgebase.model.AppAssistantBasic;
import com.supie.webadmin.util.ModelConfigUtil;
import com.supie.webadmin.upms.model.SysUser;
import com.supie.webadmin.upms.service.SysDataPermService;
import com.supie.webadmin.upms.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.util.*;

/**
 * 业务附件表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("adapterInterfaceService")
public class AdapterInterfaceServiceImpl implements AdapterInterfaceService {

    @Autowired
    @Qualifier("modelDeployTaskService")
    private ModelDeployTaskService modelDeployTaskService;
    @Autowired
    @Qualifier("modelServiceRelationMapper")
    private ModelServiceRelationMapper modelServiceRelationMapper;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysDataPermService sysDataPermService;

    @Autowired
    @Qualifier("appAssistantBasicMapper")
    private AppAssistantBasicMapper appAssistantBasicMapper;
    @Resource
    private ModelConfigUtil modelConfigUtil;
    @Resource
    protected PythonClientProperties pythonClientProperties;

    /**
     * 设置模型的相关信息，拼接modelConfig
     */
    @NotNull
    private Map<String, Object> setModelConfigMap(Long modelId , String modelType) {
        Map<String, Object> modelConfig = new HashMap<>();
        if ("deploy".equals(modelType)) {
            ModelDeployTask modelDeployTask = modelDeployTaskService.getById(modelId);
            if (modelDeployTask == null) {
                throw (RuntimeException) new RuntimeException("模型部署任务[" + modelId + "]不存在！").initCause(null);
            }
            modelConfig.put("model_type", "ChatOpenAI");
            modelConfig.put("model_name", modelDeployTask.getModelName());
            StringBuilder baseModelUrl = new StringBuilder("http://");
            if (null != modelDeployTask.getServerHost()) {
                baseModelUrl.append(modelDeployTask.getServerHost());
            }
            if (null != modelDeployTask.getApiServerPort()) {
                baseModelUrl.append(":").append(modelDeployTask.getApiServerPort());
            }
            baseModelUrl.append("/v1");
            modelConfig.put("api_base", baseModelUrl.toString());
            modelConfig.put("api_key", "sk-" + modelDeployTask.getEncryptionKey());

        } else if ("public".equals(modelType)) {
            Map<String, Object> publicLlmByModelServiceRelationId = modelServiceRelationMapper.getPublicLlmByModelServiceRelationId(modelId);
            if (publicLlmByModelServiceRelationId == null) {
                throw new InvalidParameterException("模型[" + modelId + "]不存在！");
            }
            String serviceConfiguration = publicLlmByModelServiceRelationId.get("serviceConfiguration").toString();
            if (StrUtil.isNotBlank(serviceConfiguration) && JSONUtil.isTypeJSON(serviceConfiguration)) {
                Map serviceConfigurationMap = JSONUtil.toBean(serviceConfiguration, Map.class);
                modelConfig.put("model_type", publicLlmByModelServiceRelationId.get("serviceProvider"));
                modelConfig.put("model_name", publicLlmByModelServiceRelationId.get("modelName"));
                serviceConfigurationMap.forEach((key, value) -> {
                    modelConfig.put((String) key, value);
                });
            }
        } else {
            throw new InvalidParameterException("模型类型[" + modelType + "]错误！");
        }

        return modelConfig;
    }

    /**
     * 构建数据权限相关参数
     * @param dataMap
     * @return
     */
    @Override
    public Map<String, Object> buildDataUserOrDataDeptInfo(Map<String, Object> dataMap) {
        SysUser user = sysUserService.getById(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId());
        //sysDataPermService.getSysDataPermListByUserId2()
        Map<String, Set<Long>> dataPermByUserId = sysDataPermService.getDataPermByUserId(user);
        if (dataPermByUserId.containsKey("dataDeptIdList")) {
            dataMap.put("data_dept_id_list", dataPermByUserId.get("dataDeptIdList"));
        }
        if (dataPermByUserId.containsKey("dataUserIdList")) {
            dataMap.put("data_user_id_list", dataPermByUserId.get("dataUserIdList"));
        }
        return dataMap;
    }


//    /**
//     * 查询url相关信息
//     *
//     * @param url
//     * @param dataMap
//     */
//    @Override
//    public Map<String, Object> queryURLInformation(String url, Map<String, Object> dataMap) {
//        Long UserId = TokenData.takeFromRequest().getUserId();
//        dataMap.putAll(modelConfigUtil.getModelConfigInfoByPyUrl(url, UserId));
//        return dataMap;
//    }

    /**
     * 设置登录用户ID
     *
     * @param dataMap
     */
    @Override
    public Map<String, Object> setLoginUserId(Map<String, Object> dataMap) {
        dataMap.put("login_user_id", Objects.requireNonNull(TokenData.takeFromRequest()).getUserId());
        return dataMap;
    }

    @Override
    public Map<String, Object> setLlmChatRequestParametersData(Map<String, Object> dataMap) {
        if (!dataMap.containsKey("app_id")) {
            throw (RuntimeException) new RuntimeException("缺少必要的 app_id 参数！").initCause(null);
        }
        Long appAssistantBasicId = Long.valueOf(String.valueOf(dataMap.get("app_id")));
        // 设置 llm_app_name、llm_app_description 参数
        AppAssistantBasic appAssistantBasic = appAssistantBasicMapper.selectById(appAssistantBasicId);
        if (appAssistantBasic != null) {
            dataMap.put("llm_app_name", appAssistantBasic.getAppName());
            dataMap.put("llm_app_description", appAssistantBasic.getAppDesc());
        } else {
            dataMap.put("llm_app_name", "");
            dataMap.put("llm_app_description", "");
        }
        // TODO dataMap 设置 workflow_config_list 节点的参数
        return dataMap;
    }

    /**
     * 建立HTTP连接，向指定的URL发送JSON数据，并将响应流式传输回客户端。
     *
     * @param emitter     用于发送数据的SseEmitter
     * @param sseUrl      要连接的SSE URL
     * @param jsonPayload 要发送的JSON负载
     */
    @Override
    public void connectToSseAndStreamData(SseEmitter emitter, String sseUrl, String jsonPayload) {
        try {
            URL url = new URL(sseUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置HTTP请求方法为POST
            connection.setRequestMethod("POST");
            // 允许输出，因为这是一个POST请求
            connection.setDoOutput(true);
            // 设置请求头，发送和接收JSON数据
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setRequestProperty("Authorization", pythonClientProperties.getAuthorizationKey());

            // 发送JSON负载
            try (OutputStream outputStream = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                outputStream.write(input, 0, input.length);
            }
            // 读取响应数据并通过SseEmitter发送
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.isEmpty()) {
                        // 如果字符串以 "data:" 开头，则移除该前缀
                        if (line.startsWith("data:")) {
                            line = line.substring(5).trim();
                        }
                        // 将每行数据转换为JSON对象，然后转换为Map并发送
                        emitter.send(line);
                    }
                }
            } catch (Exception e) {
                log.error("python请求报错{}",e.getMessage());
                // 在读取或发送数据时发生错误
                emitter.completeWithError(e);
            } finally {
                // 完成传输并关闭连接
                emitter.complete();
                connection.disconnect();
            }
        } catch (Exception e) {
            log.error("请求错误{}",e.getMessage());
            // 发生连接错误
            emitter.completeWithError(e);
        }finally {
            // 关闭资源
            emitter.complete();
        }
    }

    /**
     * 设置模型的相关信息
     *
     * @param dataMap
     */
    @Override
    public Map<String, Object> setModelConfigData(Map<String, Object> dataMap) {
        if (dataMap.containsKey("modelType") && dataMap.containsKey("modelId")) {
            String modelIdStr = (String) dataMap.get("modelId");
            if (modelIdStr.isEmpty()) {
                throw new InvalidParameterException("模型ID不能为空！");
            }
            Long modelId = Long.valueOf(modelIdStr);
            // 设置模型的配置信息
            String modelType = dataMap.get("modelType").toString();
            dataMap.put("model_config", modelConfigUtil.buildModelConfig(modelType, modelId));
        }
        if (dataMap.containsKey("app_id")) {
            Long appAssistantBasicId = Long.valueOf(String.valueOf(dataMap.get("app_id")));
            AppAssistantBasic appAssistantBasic = appAssistantBasicMapper.selectById(appAssistantBasicId);
            if (appAssistantBasic != null) {
                dataMap = setModelConfigOtherConfigData(dataMap, appAssistantBasic);
            }
        }
        return dataMap;
    }

    public Map<String, Object> setModelConfigOtherConfigData(Map<String, Object> dataMap, AppAssistantBasic appAssistantBasic) {
        if (!dataMap.containsKey("model_config")) {
            return dataMap;
        }
        Map<String, Object> modelConfig = (Map<String, Object>) dataMap.get("model_config");
        // 设置模型温度
        if (appAssistantBasic.getTemperature() != null) {
            modelConfig.put("temperature", appAssistantBasic.getTemperature());
        }
        // 设置停止标识
        if (appAssistantBasic.getStopSign() != null) {
            modelConfig.put("stop_sequences", appAssistantBasic.getStopSign());
        }
        dataMap.put("model_config", modelConfig);
        return dataMap;
    }

}
