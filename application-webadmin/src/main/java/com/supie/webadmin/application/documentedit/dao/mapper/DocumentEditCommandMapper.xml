<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.documentedit.dao.DocumentEditCommandMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.documentedit.model.DocumentEditCommand">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="command_name" jdbcType="VARCHAR" property="commandName"/>
        <result column="prompt_content" jdbcType="LONGVARCHAR" property="promptContent"/>
        <result column="variable_mark" jdbcType="VARCHAR" property="variableMark"/>
        <result column="variable_define_json" jdbcType="LONGVARCHAR" property="variableDefineJson"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_document_edit_command
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            command_name,
            prompt_content,
            variable_mark,
            variable_define_json)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.commandName},
            #{item.promptContent},
            #{item.variableMark},
            #{item.variableDefineJson})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.documentedit.dao.DocumentEditCommandMapper.inputFilterRef"/>
        AND ms_document_edit_command.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="documentEditCommandFilter != null">
            <if test="documentEditCommandFilter.id != null">
                AND ms_document_edit_command.id = #{documentEditCommandFilter.id}
            </if>
            <if test="documentEditCommandFilter.strId != null and documentEditCommandFilter.strId != ''">
                AND ms_document_edit_command.str_id = #{documentEditCommandFilter.strId}
            </if>
            <if test="documentEditCommandFilter.updateTimeStart != null and documentEditCommandFilter.updateTimeStart != ''">
                AND ms_document_edit_command.update_time &gt;= #{documentEditCommandFilter.updateTimeStart}
            </if>
            <if test="documentEditCommandFilter.updateTimeEnd != null and documentEditCommandFilter.updateTimeEnd != ''">
                AND ms_document_edit_command.update_time &lt;= #{documentEditCommandFilter.updateTimeEnd}
            </if>
            <if test="documentEditCommandFilter.createTimeStart != null and documentEditCommandFilter.createTimeStart != ''">
                AND ms_document_edit_command.create_time &gt;= #{documentEditCommandFilter.createTimeStart}
            </if>
            <if test="documentEditCommandFilter.createTimeEnd != null and documentEditCommandFilter.createTimeEnd != ''">
                AND ms_document_edit_command.create_time &lt;= #{documentEditCommandFilter.createTimeEnd}
            </if>
            <if test="documentEditCommandFilter.createUserId != null">
                AND ms_document_edit_command.create_user_id = #{documentEditCommandFilter.createUserId}
            </if>
            <if test="documentEditCommandFilter.updateUserId != null">
                AND ms_document_edit_command.update_user_id = #{documentEditCommandFilter.updateUserId}
            </if>
            <if test="documentEditCommandFilter.dataUserId != null">
                AND ms_document_edit_command.data_user_id = #{documentEditCommandFilter.dataUserId}
            </if>
            <if test="documentEditCommandFilter.dataDeptId != null">
                AND ms_document_edit_command.data_dept_id = #{documentEditCommandFilter.dataDeptId}
            </if>
            <if test="documentEditCommandFilter.isDelete != null">
                AND ms_document_edit_command.is_delete = #{documentEditCommandFilter.isDelete}
            </if>
            <if test="documentEditCommandFilter.commandName != null and documentEditCommandFilter.commandName != ''">
                <bind name = "safeDocumentEditCommandCommandName" value = "'%' + documentEditCommandFilter.commandName + '%'" />
                AND ms_document_edit_command.command_name LIKE #{safeDocumentEditCommandCommandName}
            </if>
            <if test="documentEditCommandFilter.promptContent != null and documentEditCommandFilter.promptContent != ''">
                <bind name = "safeDocumentEditCommandPromptContent" value = "'%' + documentEditCommandFilter.promptContent + '%'" />
                AND ms_document_edit_command.prompt_content LIKE #{safeDocumentEditCommandPromptContent}
            </if>
            <if test="documentEditCommandFilter.variableMark != null and documentEditCommandFilter.variableMark != ''">
                <bind name = "safeDocumentEditCommandVariableMark" value = "'%' + documentEditCommandFilter.variableMark + '%'" />
                AND ms_document_edit_command.variable_mark LIKE #{safeDocumentEditCommandVariableMark}
            </if>
            <if test="documentEditCommandFilter.variableDefineJson != null and documentEditCommandFilter.variableDefineJson != ''">
                <bind name = "safeDocumentEditCommandVariableDefineJson" value = "'%' + documentEditCommandFilter.variableDefineJson + '%'" />
                AND ms_document_edit_command.variable_define_json LIKE #{safeDocumentEditCommandVariableDefineJson}
            </if>
            <if test="documentEditCommandFilter.searchString != null and documentEditCommandFilter.searchString != ''">
                <bind name = "safeDocumentEditCommandSearchString" value = "'%' + documentEditCommandFilter.searchString + '%'" />
                AND CONCAT(IFNULL(ms_document_edit_command.command_name,''), IFNULL(ms_document_edit_command.prompt_content,''), IFNULL(ms_document_edit_command.variable_mark,'')) LIKE #{safeDocumentEditCommandSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDocumentEditCommandList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.DocumentEditCommand">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM ms_document_edit_command
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) ms_document_edit_command
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDocumentEditCommandList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.DocumentEditCommand">
        SELECT * FROM ms_document_edit_command
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
