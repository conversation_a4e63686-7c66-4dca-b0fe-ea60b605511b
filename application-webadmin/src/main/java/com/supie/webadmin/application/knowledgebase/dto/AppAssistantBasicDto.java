package com.supie.webadmin.application.knowledgebase.dto;

import com.supie.common.core.validator.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 应用中心-应用助手基础信息表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Schema(description = "AppAssistantBasicDto对象")
@Data
public class AppAssistantBasicDto {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 应用名称。
     */
    @Schema(description = "应用名称")
    private String appName;

    /**
     * 应用描述。
     */
    @Schema(description = "应用描述")
    private String appDesc;

    /**
     * 应用类型。
     */
    @Schema(description = "应用类型")
    private String appType;

    /**
     * 部署模型ID。
     */
    @Schema(description = "部署模型ID")
    private Long modelDeployTaskId;

    /**
     * 公共大预言模型名称（gpt-3.5/gpt-4...）。
     */
    @Schema(description = "模型类型（'public'、'deploy'）")
    private String modelType;

    /**
     * 模型ID。
     */
    @Schema(description = "模型ID")
    private Long modelId;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)")
    private String updateTimeEnd;

    /**
     * app_name / app_desc LIKE搜索字符串。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;

    /**
     * 开场白对话
     */
    @Schema(description = "开场白对话")
    private String openingRemark;

    /**
     * 快捷提问
     */
    @Schema(description = "快捷提问")
    private String quickQuestion;

    /**
     * 温度
     */
    @Schema(description = "温度")
    private Float temperature;

    /**
     * 停止标识
     */
    @Schema(description = "停止标识")
    private String stopSign;

    /**
     * 生成次数
     */
    @Schema(description = "生成次数")
    private Integer generateEach;

    /**
     * 最大生成tokens数
     */
    @Schema(description = "最大生成tokens数")
    private Integer maxTokens;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型")
    private String templateType;

}
