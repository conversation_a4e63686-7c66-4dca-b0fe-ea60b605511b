package com.supie.webadmin.application.infoextraction.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.infoextraction.dto.ExtractionRecordDto;
import com.supie.webadmin.application.infoextraction.model.ExtractionRecord;
import com.supie.webadmin.application.infoextraction.service.ExtractionRecordService;
import com.supie.webadmin.application.infoextraction.vo.ExtractionRecordVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 信息抽取记录表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "信息抽取记录表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/extractionRecord")
public class ExtractionRecordController {

    @Autowired
    private ExtractionRecordService extractionRecordService;

    /**
     * 新增信息抽取记录表数据。
     *
     * @param extractionRecordDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "extractionRecordDto.id",
            "extractionRecordDto.searchString",
            "extractionRecordDto.updateTimeStart",
            "extractionRecordDto.updateTimeEnd",
            "extractionRecordDto.createTimeStart",
            "extractionRecordDto.createTimeEnd"})
    //// @SaCheckPermission("extractionRecord.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody ExtractionRecordDto extractionRecordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(extractionRecordDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ExtractionRecord extractionRecord = MyModelUtil.copyTo(extractionRecordDto, ExtractionRecord.class);
        extractionRecord = extractionRecordService.saveNew(extractionRecord);
        return ResponseResult.success(extractionRecord.getId());
    }

    /**
     * 更新信息抽取记录表数据。
     *
     * @param extractionRecordDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "extractionRecordDto.searchString",
            "extractionRecordDto.updateTimeStart",
            "extractionRecordDto.updateTimeEnd",
            "extractionRecordDto.createTimeStart",
            "extractionRecordDto.createTimeEnd"})
    //// @SaCheckPermission("extractionRecord.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody ExtractionRecordDto extractionRecordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(extractionRecordDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ExtractionRecord extractionRecord = MyModelUtil.copyTo(extractionRecordDto, ExtractionRecord.class);
        ExtractionRecord originalExtractionRecord = extractionRecordService.getById(extractionRecord.getId());
        if (originalExtractionRecord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!extractionRecordService.update(extractionRecord, originalExtractionRecord)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除信息抽取记录表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("extractionRecord.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除信息抽取记录表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("extractionRecord.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的信息抽取记录表列表。
     *
     * @param extractionRecordDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //// @SaCheckPermission("extractionRecord.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ExtractionRecordVo>> list(
            @MyRequestBody ExtractionRecordDto extractionRecordDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ExtractionRecord extractionRecordFilter = MyModelUtil.copyTo(extractionRecordDtoFilter, ExtractionRecord.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ExtractionRecord.class);
        List<ExtractionRecord> extractionRecordList =
                extractionRecordService.getExtractionRecordListWithRelation(extractionRecordFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(extractionRecordList, ExtractionRecordVo.class));
    }

    /**
     * 分组列出符合过滤条件的信息抽取记录表列表。
     *
     * @param extractionRecordDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //// @SaCheckPermission("extractionRecord.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<ExtractionRecordVo>> listWithGroup(
            @MyRequestBody ExtractionRecordDto extractionRecordDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ExtractionRecord.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, ExtractionRecord.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ExtractionRecord filter = MyModelUtil.copyTo(extractionRecordDtoFilter, ExtractionRecord.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<ExtractionRecord> resultList = extractionRecordService.getGroupedExtractionRecordListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, ExtractionRecordVo.class));
    }

    /**
     * 查看指定信息抽取记录表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //// @SaCheckPermission("extractionRecord.view")
    @GetMapping("/view")
    public ResponseResult<ExtractionRecordVo> view(@RequestParam Long id) {
        ExtractionRecord extractionRecord = extractionRecordService.getByIdWithRelation(id, MyRelationParam.full());
        if (extractionRecord == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ExtractionRecordVo extractionRecordVo = MyModelUtil.copyTo(extractionRecord, ExtractionRecordVo.class);
        return ResponseResult.success(extractionRecordVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        ExtractionRecord originalExtractionRecord = extractionRecordService.getById(id);
        if (originalExtractionRecord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!extractionRecordService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
