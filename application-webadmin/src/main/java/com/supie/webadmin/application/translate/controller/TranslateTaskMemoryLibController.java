package com.supie.webadmin.application.translate.controller;

import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.translate.dto.TranslateTaskMemoryLibDto;
import com.supie.webadmin.application.translate.model.TranslateTaskMemoryLib;
import com.supie.webadmin.application.translate.service.TranslateTaskMemoryLibService;
import com.supie.webadmin.application.translate.vo.TranslateTaskMemoryLibVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 服务管理-翻译任务-记忆库多对多关联表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Tag(name = "服务管理-翻译任务-记忆库多对多关联表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/translateTaskMemoryLib")
public class TranslateTaskMemoryLibController {

    @Resource
    private TranslateTaskMemoryLibService translateTaskMemoryLibService;

    /**
     * 新增服务管理-翻译任务-记忆库多对多关联表数据。
     *
     * @param translateTaskMemoryLibDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "translateTaskMemoryLibDto.id",
            "translateTaskMemoryLibDto.createTimeStart",
            "translateTaskMemoryLibDto.createTimeEnd",
            "translateTaskMemoryLibDto.updateTimeStart",
            "translateTaskMemoryLibDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody TranslateTaskMemoryLibDto translateTaskMemoryLibDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(translateTaskMemoryLibDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        TranslateTaskMemoryLib translateTaskMemoryLib = MyModelUtil.copyTo(translateTaskMemoryLibDto, TranslateTaskMemoryLib.class);
        translateTaskMemoryLib = translateTaskMemoryLibService.saveNew(translateTaskMemoryLib);
        return ResponseResult.success(translateTaskMemoryLib.getId());
    }

    /**
     * 更新服务管理-翻译任务-记忆库多对多关联表数据。
     *
     * @param translateTaskMemoryLibDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "translateTaskMemoryLibDto.createTimeStart",
            "translateTaskMemoryLibDto.createTimeEnd",
            "translateTaskMemoryLibDto.updateTimeStart",
            "translateTaskMemoryLibDto.updateTimeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody TranslateTaskMemoryLibDto translateTaskMemoryLibDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(translateTaskMemoryLibDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        TranslateTaskMemoryLib translateTaskMemoryLib = MyModelUtil.copyTo(translateTaskMemoryLibDto, TranslateTaskMemoryLib.class);
        TranslateTaskMemoryLib originalTranslateTaskMemoryLib = translateTaskMemoryLibService.getById(translateTaskMemoryLib.getId());
        if (originalTranslateTaskMemoryLib == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!translateTaskMemoryLibService.update(translateTaskMemoryLib, originalTranslateTaskMemoryLib)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除服务管理-翻译任务-记忆库多对多关联表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除服务管理-翻译任务-记忆库多对多关联表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的服务管理-翻译任务-记忆库多对多关联表列表。
     *
     * @param translateTaskMemoryLibDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<TranslateTaskMemoryLibVo>> list(
            @MyRequestBody TranslateTaskMemoryLibDto translateTaskMemoryLibDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        TranslateTaskMemoryLib translateTaskMemoryLibFilter = MyModelUtil.copyTo(translateTaskMemoryLibDtoFilter, TranslateTaskMemoryLib.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, TranslateTaskMemoryLib.class);
        List<TranslateTaskMemoryLib> translateTaskMemoryLibList =
                translateTaskMemoryLibService.getTranslateTaskMemoryLibListWithRelation(translateTaskMemoryLibFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(translateTaskMemoryLibList, TranslateTaskMemoryLibVo.class));
    }

    /**
     * 查看指定服务管理-翻译任务-记忆库多对多关联表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<TranslateTaskMemoryLibVo> view(@RequestParam Long id) {
        TranslateTaskMemoryLib translateTaskMemoryLib = translateTaskMemoryLibService.getByIdWithRelation(id, MyRelationParam.full());
        if (translateTaskMemoryLib == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        TranslateTaskMemoryLibVo translateTaskMemoryLibVo = MyModelUtil.copyTo(translateTaskMemoryLib, TranslateTaskMemoryLibVo.class);
        return ResponseResult.success(translateTaskMemoryLibVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        TranslateTaskMemoryLib originalTranslateTaskMemoryLib = translateTaskMemoryLibService.getById(id);
        if (originalTranslateTaskMemoryLib == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!translateTaskMemoryLibService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
