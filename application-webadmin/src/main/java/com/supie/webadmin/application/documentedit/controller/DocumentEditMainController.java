package com.supie.webadmin.application.documentedit.controller;


import com.supie.common.core.annotation.DisableDataFilter;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import com.supie.webadmin.util.TransferFilesUtil;
import com.supie.webadmin.application.documentedit.dto.DocumentEditMainDto;
import com.supie.webadmin.application.documentedit.model.DocumentEditMain;
import com.supie.webadmin.application.documentedit.service.DocumentEditMainService;
import com.supie.webadmin.application.documentedit.vo.DocumentEditMainVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 文档编写主表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "文档编写主表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/documentEditMain")
public class DocumentEditMainController {

    @Autowired
    private DocumentEditMainService documentEditMainService;

    /**
     * 新增文档编写主表数据。
     *
     * @param documentEditMainDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditMainDto.id",
            "documentEditMainDto.searchString",
            "documentEditMainDto.createTimeStart",
            "documentEditMainDto.createTimeEnd",
            "documentEditMainDto.updateTimeStart",
            "documentEditMainDto.updateTimeEnd",
            "documentEditMainDto.editContentStart",
            "documentEditMainDto.editContentEnd"})
    ////@SaCheckPermission("documentEditMain.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DocumentEditMainDto documentEditMainDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditMainDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditMain documentEditMain = MyModelUtil.copyTo(documentEditMainDto, DocumentEditMain.class);
        documentEditMain = documentEditMainService.saveNew(documentEditMain);
        return ResponseResult.success(documentEditMain.getId());
    }

    /**
     * 更新文档编写主表数据。
     *
     * @param documentEditMainDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditMainDto.searchString",
            "documentEditMainDto.createTimeStart",
            "documentEditMainDto.createTimeEnd",
            "documentEditMainDto.updateTimeStart",
            "documentEditMainDto.updateTimeEnd",
            "documentEditMainDto.editContentStart",
            "documentEditMainDto.editContentEnd"})
    ////@SaCheckPermission("documentEditMain.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DocumentEditMainDto documentEditMainDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditMainDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditMain documentEditMain = MyModelUtil.copyTo(documentEditMainDto, DocumentEditMain.class);
        DocumentEditMain originalDocumentEditMain = documentEditMainService.getById(documentEditMain.getId());
        if (originalDocumentEditMain == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!documentEditMainService.update(documentEditMain, originalDocumentEditMain)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 更新文档编写主表数据。
     *
     * @param documentEditMainDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditMainDto.searchString",
            "documentEditMainDto.createTimeStart",
            "documentEditMainDto.createTimeEnd",
            "documentEditMainDto.updateTimeStart",
            "documentEditMainDto.updateTimeEnd",
            "documentEditMainDto.editContentStart",
            "documentEditMainDto.editContentEnd"})
    ////@SaCheckPermission("documentEditMain.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/updateById")
    public ResponseResult<Void> updateById(@MyRequestBody DocumentEditMainDto documentEditMainDto) {
        if (documentEditMainDto == null || documentEditMainDto.getId() == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "数据验证失败，主键ID不能为空！");
        }
        DocumentEditMain documentEditMain = MyModelUtil.copyTo(documentEditMainDto, DocumentEditMain.class);
        DocumentEditMain originalDocumentEditMain = documentEditMainService.getById(documentEditMain.getId());
        if (originalDocumentEditMain == null) {
            // NOTE: 修改下面方括号中的话述
            String errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        documentEditMain.setUpdateTime(new Date());
        documentEditMain.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        if (!documentEditMainService.updateById(documentEditMain)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除文档编写主表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("documentEditMain.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除文档编写主表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////@SaCheckPermission("documentEditMain.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的文档编写主表列表。
     *
     * @param documentEditMainDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("documentEditMain.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<DocumentEditMainVo>> list(
            @MyRequestBody DocumentEditMainDto documentEditMainDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DocumentEditMain documentEditMainFilter = MyModelUtil.copyTo(documentEditMainDtoFilter, DocumentEditMain.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DocumentEditMain.class);
        List<DocumentEditMain> documentEditMainList =
                documentEditMainService.getDocumentEditMainListWithRelation(documentEditMainFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(documentEditMainList, DocumentEditMainVo.class));
    }

    /**
     * 分组列出符合过滤条件的文档编写主表列表。
     *
     * @param documentEditMainDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////@SaCheckPermission("documentEditMain.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<DocumentEditMainVo>> listWithGroup(
            @MyRequestBody DocumentEditMainDto documentEditMainDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DocumentEditMain.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, DocumentEditMain.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DocumentEditMain filter = MyModelUtil.copyTo(documentEditMainDtoFilter, DocumentEditMain.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<DocumentEditMain> resultList = documentEditMainService.getGroupedDocumentEditMainListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, DocumentEditMainVo.class));
    }

    /**
     * 查看指定文档编写主表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @DisableDataFilter
    ////@SaCheckPermission("documentEditMain.view")
    @GetMapping("/view")
    public ResponseResult<DocumentEditMainVo> view(@RequestParam Long id) {
        DocumentEditMain documentEditMain = documentEditMainService.getByIdWithRelation(id, MyRelationParam.full());
        if (documentEditMain == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        DocumentEditMainVo documentEditMainVo = MyModelUtil.copyTo(documentEditMain, DocumentEditMainVo.class);
        return ResponseResult.success(documentEditMainVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DocumentEditMain originalDocumentEditMain = documentEditMainService.getById(id);
        if (originalDocumentEditMain == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!documentEditMainService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * Word 转换为Html。
     */
    @Operation(summary = "Word 转换为Html")
    @PostMapping("/wordToHtml")
    public ResponseResult<String> wordToHtml(@RequestParam Boolean isDocx, @RequestParam("wordFile") MultipartFile wordFile) throws IOException {
        if (wordFile == null || wordFile.isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.NO_ERROR, "未上传文件!");
        }
        ByteArrayOutputStream byteArrayOutputStream;
        if (Boolean.TRUE.equals(isDocx)) {
            byteArrayOutputStream = TransferFilesUtil.docxToHtml(wordFile.getInputStream());
        } else {
            byteArrayOutputStream = TransferFilesUtil.docToHtml(wordFile.getInputStream());
        }
        return ResponseResult.success(byteArrayOutputStream.toString(StandardCharsets.UTF_8));
    }

}
