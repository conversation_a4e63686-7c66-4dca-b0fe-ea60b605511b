package com.supie.webadmin.application.translate.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 翻译目标表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "TranslateTargetVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class TranslateTargetVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 翻译任务id。
     */
    @Schema(description = "翻译任务id")
    private Long translateTaskId;

    /**
     * 目标语言。
     */
    @Schema(description = "目标语言")
    private String targetLanguage;

    /**
     * 翻译后的文件(pdf)。
     */
    @Schema(description = "翻译后的文件(pdf)")
    private Long translateStrId;

    /**
     * 翻译任务状态（未开始、进行中、失败、已完成）。
     */
    @Schema(description = "翻译任务状态（未开始、进行中、失败、已完成）")
    private String translateState;

    /**
     * 目标内容。
     */
    @Schema(description = "目标内容")
    private String targetContent;

    /**
     * 目标配置。
     */
    @Schema(description = "目标配置")
    private String targetConfig;

    /**
     * 使用的模型ID。
     */
    @Schema(description = "使用的模型ID")
    private Long modelId;

    /**
     * 使用的模型类型（deploy、public）。
     */
    @Schema(description = "使用的模型类型（deploy、public）")
    private String modelType;

}
