package com.supie.webadmin.application.base.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.application.base.model.DefaultConfiguration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型服务-服务默认配置表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Repository
@Mapper
@EnableDataPerm(excluseMethodName = {"queryModelInfo"})
public interface DefaultConfigurationMapper extends BaseDaoMapper<DefaultConfiguration> {

    /**
     * 批量插入对象列表。
     *
     * @param defaultConfigurationList 新增对象列表。
     */
    void insertList(List<DefaultConfiguration> defaultConfigurationList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param defaultConfigurationFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<DefaultConfiguration> getGroupedDefaultConfigurationList(
            @Param("defaultConfigurationFilter") DefaultConfiguration defaultConfigurationFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param defaultConfigurationFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<DefaultConfiguration> getDefaultConfigurationList(
            @Param("defaultConfigurationFilter") DefaultConfiguration defaultConfigurationFilter, @Param("orderBy") String orderBy);


    /**
     * 根据模型调用地址查询模型信息
     * @param url 模型调用地址
     * @return 返回模型信息
     */
    DefaultConfiguration queryModelInfo(@Param("url") String url);


}
