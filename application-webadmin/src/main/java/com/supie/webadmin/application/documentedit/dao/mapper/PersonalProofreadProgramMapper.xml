<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.documentedit.dao.PersonalProofreadProgramMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.documentedit.model.PersonalProofreadProgram">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="program_name" jdbcType="VARCHAR" property="programName"/>
        <result column="rule_config" jdbcType="LONGVARCHAR" property="ruleConfig"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_documentedit_personal_proofread_program
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            user_id,
            program_name,
            rule_config)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.userId},
            #{item.programName},
            #{item.ruleConfig})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.documentedit.dao.PersonalProofreadProgramMapper.inputFilterRef"/>
        AND ms_documentedit_personal_proofread_program.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="personalProofreadProgramFilter != null">
            <if test="personalProofreadProgramFilter.id != null">
                AND ms_documentedit_personal_proofread_program.id = #{personalProofreadProgramFilter.id}
            </if>
            <if test="personalProofreadProgramFilter.strId != null and personalProofreadProgramFilter.strId != ''">
                AND ms_documentedit_personal_proofread_program.str_id = #{personalProofreadProgramFilter.strId}
            </if>
            <if test="personalProofreadProgramFilter.updateTimeStart != null and personalProofreadProgramFilter.updateTimeStart != ''">
                AND ms_documentedit_personal_proofread_program.update_time &gt;= #{personalProofreadProgramFilter.updateTimeStart}
            </if>
            <if test="personalProofreadProgramFilter.updateTimeEnd != null and personalProofreadProgramFilter.updateTimeEnd != ''">
                AND ms_documentedit_personal_proofread_program.update_time &lt;= #{personalProofreadProgramFilter.updateTimeEnd}
            </if>
            <if test="personalProofreadProgramFilter.createTimeStart != null and personalProofreadProgramFilter.createTimeStart != ''">
                AND ms_documentedit_personal_proofread_program.create_time &gt;= #{personalProofreadProgramFilter.createTimeStart}
            </if>
            <if test="personalProofreadProgramFilter.createTimeEnd != null and personalProofreadProgramFilter.createTimeEnd != ''">
                AND ms_documentedit_personal_proofread_program.create_time &lt;= #{personalProofreadProgramFilter.createTimeEnd}
            </if>
            <if test="personalProofreadProgramFilter.createUserId != null">
                AND ms_documentedit_personal_proofread_program.create_user_id = #{personalProofreadProgramFilter.createUserId}
            </if>
            <if test="personalProofreadProgramFilter.updateUserId != null">
                AND ms_documentedit_personal_proofread_program.update_user_id = #{personalProofreadProgramFilter.updateUserId}
            </if>
            <if test="personalProofreadProgramFilter.dataUserId != null">
                AND ms_documentedit_personal_proofread_program.data_user_id = #{personalProofreadProgramFilter.dataUserId}
            </if>
            <if test="personalProofreadProgramFilter.dataDeptId != null">
                AND ms_documentedit_personal_proofread_program.data_dept_id = #{personalProofreadProgramFilter.dataDeptId}
            </if>
            <if test="personalProofreadProgramFilter.userId != null">
                AND ms_documentedit_personal_proofread_program.user_id = #{personalProofreadProgramFilter.userId}
            </if>
            <if test="personalProofreadProgramFilter.programName != null and personalProofreadProgramFilter.programName != ''">
                <bind name = "safePersonalProofreadProgramProgramName" value = "'%' + personalProofreadProgramFilter.programName + '%'" />
                AND ms_documentedit_personal_proofread_program.program_name LIKE #{safePersonalProofreadProgramProgramName}
            </if>
            <if test="personalProofreadProgramFilter.ruleConfig != null and personalProofreadProgramFilter.ruleConfig != ''">
                <bind name = "safePersonalProofreadProgramRuleConfig" value = "'%' + personalProofreadProgramFilter.ruleConfig + '%'" />
                AND ms_documentedit_personal_proofread_program.rule_config LIKE #{safePersonalProofreadProgramRuleConfig}
            </if>
            <if test="personalProofreadProgramFilter.searchString != null and personalProofreadProgramFilter.searchString != ''">
                <bind name = "safePersonalProofreadProgramSearchString" value = "'%' + personalProofreadProgramFilter.searchString + '%'" />
                AND IFNULL(ms_documentedit_personal_proofread_program.program_name,'') LIKE #{safePersonalProofreadProgramSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedPersonalProofreadProgramList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.PersonalProofreadProgram">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM ms_documentedit_personal_proofread_program
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) ms_documentedit_personal_proofread_program
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getPersonalProofreadProgramList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.PersonalProofreadProgram">
        SELECT * FROM ms_documentedit_personal_proofread_program
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
