package com.supie.webadmin.application.documentedit.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 文档编写模版表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ms_document_edit_template")
public class DocumentEditTemplate extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 模版名称。
     */
    @TableField(value = "template_title")
    private String templateTitle;

    /**
     * 模版描述。
     */
    @TableField(value = "template_desc")
    private String templateDesc;

    /**
     * 标签。
     */
    @TableField(value = "template_tags")
    private String templateTags;

    /**
     * 模版类型（营销助手、学术助手、办公助手、公文助手）。
     */
    @TableField(value = "template_type")
    private String templateType;

    /**
     * 方法类型（0，提示语言；1，外接api）。
     */
    @TableField(value = "method_type")
    private Integer methodType;

    /**
     * api路由地址。
     */
    @TableField(value = "api_url")
    private String apiUrl;

    /**
     * 图标地址。
     */
    @TableField(value = "icon_url")
    private String iconUrl;

    /**
     * 提示内容。
     */
    @TableField(value = "prompt_content")
    private String promptContent;

    /**
     * 变量标志（ {{}}   {}   []   [[]]   {[]}）。
     */
    @TableField(value = "variable_mark")
    private String variableMark;

    /**
     * 变量定义json。
     */
    @TableField(value = "variable_define_json")
    private String variableDefineJson;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * str_id / template_title LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    /**
     * 标签列表。
     * NOTE: 支持 IN 操作符的列表数据过滤。
     */
    @TableField(exist = false)
    private List<String> templateTagsList;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
