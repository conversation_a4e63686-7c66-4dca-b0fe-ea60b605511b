package com.supie.webadmin.application.documentedit.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 个人校对方案表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ms_documentedit_personal_proofread_program")
public class PersonalProofreadProgram extends BaseModel {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符编号。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 所属用户ID。
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 方案名称。
     */
    @TableField(value = "program_name")
    private String programName;

    /**
     * 规则配置（JSON格式）。
     */
    @TableField(value = "rule_config")
    private String ruleConfig;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * program_name LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
