package com.supie.webadmin.application.documentedit.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import com.supie.webadmin.application.documentedit.dao.DocumentEditProhibiteWordLibMapper;
import com.supie.webadmin.application.documentedit.model.DocumentEditProhibiteWordLib;
import com.supie.webadmin.application.documentedit.service.DocumentEditProhibiteWordLibService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 违禁词库数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Slf4j
@Service("documentEditProhibiteWordLibService")
public class DocumentEditProhibiteWordLibServiceImpl extends BaseService<DocumentEditProhibiteWordLib, Long> implements DocumentEditProhibiteWordLibService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private DocumentEditProhibiteWordLibMapper documentEditProhibiteWordLibMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<DocumentEditProhibiteWordLib> mapper() {
        return documentEditProhibiteWordLibMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DocumentEditProhibiteWordLib saveNew(DocumentEditProhibiteWordLib documentEditProhibiteWordLib) {
        documentEditProhibiteWordLibMapper.insert(this.buildDefaultValue(documentEditProhibiteWordLib));
        return documentEditProhibiteWordLib;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<DocumentEditProhibiteWordLib> documentEditProhibiteWordLibList) {
        if (CollUtil.isNotEmpty(documentEditProhibiteWordLibList)) {
            documentEditProhibiteWordLibList.forEach(this::buildDefaultValue);
            documentEditProhibiteWordLibMapper.insertList(documentEditProhibiteWordLibList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(DocumentEditProhibiteWordLib documentEditProhibiteWordLib, DocumentEditProhibiteWordLib originalDocumentEditProhibiteWordLib) {
        MyModelUtil.fillCommonsForUpdate(documentEditProhibiteWordLib, originalDocumentEditProhibiteWordLib);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<DocumentEditProhibiteWordLib> uw = this.createUpdateQueryForNullValue(documentEditProhibiteWordLib, documentEditProhibiteWordLib.getId());
        return documentEditProhibiteWordLibMapper.update(documentEditProhibiteWordLib, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return documentEditProhibiteWordLibMapper.deleteById(id) == 1;
    }

    @Override
    public List<DocumentEditProhibiteWordLib> getDocumentEditProhibiteWordLibList(DocumentEditProhibiteWordLib filter, String orderBy) {
        return documentEditProhibiteWordLibMapper.getDocumentEditProhibiteWordLibList(filter, orderBy);
    }

    @Override
    public List<DocumentEditProhibiteWordLib> getDocumentEditProhibiteWordLibListWithRelation(DocumentEditProhibiteWordLib filter, String orderBy) {
        List<DocumentEditProhibiteWordLib> resultList = documentEditProhibiteWordLibMapper.getDocumentEditProhibiteWordLibList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<DocumentEditProhibiteWordLib> getGroupedDocumentEditProhibiteWordLibListWithRelation(
            DocumentEditProhibiteWordLib filter, String groupSelect, String groupBy, String orderBy) {
        List<DocumentEditProhibiteWordLib> resultList =
                documentEditProhibiteWordLibMapper.getGroupedDocumentEditProhibiteWordLibList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private DocumentEditProhibiteWordLib buildDefaultValue(DocumentEditProhibiteWordLib documentEditProhibiteWordLib) {
        if (documentEditProhibiteWordLib.getId() == null) {
            documentEditProhibiteWordLib.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(documentEditProhibiteWordLib);
        documentEditProhibiteWordLib.setIsDelete(GlobalDeletedFlag.NORMAL);
        return documentEditProhibiteWordLib;
    }
}
