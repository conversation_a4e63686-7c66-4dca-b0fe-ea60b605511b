package com.supie.webadmin.application.infoextraction.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.application.infoextraction.model.EvaluationRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 信息评价规则表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Mapper
@EnableDataPerm
public interface EvaluationRuleMapper extends BaseDaoMapper<EvaluationRule> {

    /**
     * 批量插入对象列表。
     *
     * @param evaluationRuleList 新增对象列表。
     */
    void insertList(List<EvaluationRule> evaluationRuleList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param evaluationRuleFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<EvaluationRule> getGroupedEvaluationRuleList(
            @Param("evaluationRuleFilter") EvaluationRule evaluationRuleFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param evaluationRuleFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<EvaluationRule> getEvaluationRuleList(
            @Param("evaluationRuleFilter") EvaluationRule evaluationRuleFilter, @Param("orderBy") String orderBy);
}
