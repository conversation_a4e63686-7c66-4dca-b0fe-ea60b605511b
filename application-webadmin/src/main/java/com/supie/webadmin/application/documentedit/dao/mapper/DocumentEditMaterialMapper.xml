<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.documentedit.dao.DocumentEditMaterialMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.documentedit.model.DocumentEditMaterial">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="material_title" jdbcType="VARCHAR" property="materialTitle"/>
        <result column="material_tags" jdbcType="LONGVARCHAR" property="materialTags"/>
        <result column="material_content" jdbcType="LONGVARCHAR" property="materialContent"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_document_edit_material
            (id,
            str_id,
            is_delete,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            material_title,
            material_tags,
            material_content)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.materialTitle},
            #{item.materialTags},
            #{item.materialContent})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.documentedit.dao.DocumentEditMaterialMapper.inputFilterRef"/>
        AND ms_document_edit_material.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="documentEditMaterialFilter != null">
            <if test="documentEditMaterialFilter.materialTagsList != null and documentEditMaterialFilter.materialTagsList.size() > 0">
                <foreach item="item" index="index" collection="documentEditMaterialFilter.materialTagsList" open="AND (" separator="OR" close=")">
                    ms_document_edit_material.material_tags LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="documentEditMaterialFilter.id != null">
                AND ms_document_edit_material.id = #{documentEditMaterialFilter.id}
            </if>
            <if test="documentEditMaterialFilter.strId != null and documentEditMaterialFilter.strId != ''">
                <bind name = "safeDocumentEditMaterialStrId" value = "'%' + documentEditMaterialFilter.strId + '%'" />
                AND ms_document_edit_material.str_id LIKE #{safeDocumentEditMaterialStrId}
            </if>
            <if test="documentEditMaterialFilter.createUserId != null">
                AND ms_document_edit_material.create_user_id = #{documentEditMaterialFilter.createUserId}
            </if>
            <if test="documentEditMaterialFilter.createTimeStart != null and documentEditMaterialFilter.createTimeStart != ''">
                AND ms_document_edit_material.create_time &gt;= #{documentEditMaterialFilter.createTimeStart}
            </if>
            <if test="documentEditMaterialFilter.createTimeEnd != null and documentEditMaterialFilter.createTimeEnd != ''">
                AND ms_document_edit_material.create_time &lt;= #{documentEditMaterialFilter.createTimeEnd}
            </if>
            <if test="documentEditMaterialFilter.updateUserId != null">
                AND ms_document_edit_material.update_user_id = #{documentEditMaterialFilter.updateUserId}
            </if>
            <if test="documentEditMaterialFilter.updateTimeStart != null and documentEditMaterialFilter.updateTimeStart != ''">
                AND ms_document_edit_material.update_time &gt;= #{documentEditMaterialFilter.updateTimeStart}
            </if>
            <if test="documentEditMaterialFilter.updateTimeEnd != null and documentEditMaterialFilter.updateTimeEnd != ''">
                AND ms_document_edit_material.update_time &lt;= #{documentEditMaterialFilter.updateTimeEnd}
            </if>
            <if test="documentEditMaterialFilter.dataUserId != null">
                AND ms_document_edit_material.data_user_id = #{documentEditMaterialFilter.dataUserId}
            </if>
            <if test="documentEditMaterialFilter.dataDeptId != null">
                AND ms_document_edit_material.data_dept_id = #{documentEditMaterialFilter.dataDeptId}
            </if>
            <if test="documentEditMaterialFilter.materialTitle != null and documentEditMaterialFilter.materialTitle != ''">
                <bind name = "safeDocumentEditMaterialMaterialTitle" value = "'%' + documentEditMaterialFilter.materialTitle + '%'" />
                AND ms_document_edit_material.material_title LIKE #{safeDocumentEditMaterialMaterialTitle}
            </if>
            <if test="documentEditMaterialFilter.materialTags != null and documentEditMaterialFilter.materialTags != ''">
                <bind name = "safeDocumentEditMaterialMaterialTags" value = "'%' + documentEditMaterialFilter.materialTags + '%'" />
                AND ms_document_edit_material.material_tags LIKE #{safeDocumentEditMaterialMaterialTags}
            </if>
            <if test="documentEditMaterialFilter.materialContent != null and documentEditMaterialFilter.materialContent != ''">
                <bind name = "safeDocumentEditMaterialMaterialContent" value = "'%' + documentEditMaterialFilter.materialContent + '%'" />
                AND ms_document_edit_material.material_content LIKE #{safeDocumentEditMaterialMaterialContent}
            </if>
            <if test="documentEditMaterialFilter.searchString != null and documentEditMaterialFilter.searchString != ''">
                <bind name = "safeDocumentEditMaterialSearchString" value = "'%' + documentEditMaterialFilter.searchString + '%'" />
                AND CONCAT(IFNULL(ms_document_edit_material.str_id,''), IFNULL(ms_document_edit_material.material_title,'')) LIKE #{safeDocumentEditMaterialSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedDocumentEditMaterialList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.DocumentEditMaterial">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM ms_document_edit_material
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) ms_document_edit_material
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getDocumentEditMaterialList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.documentedit.model.DocumentEditMaterial">
        SELECT * FROM ms_document_edit_material
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
