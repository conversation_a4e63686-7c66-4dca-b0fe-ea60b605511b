package com.supie.webadmin.application.tatoeba.vo;

import com.supie.common.core.base.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Tatoeba翻译数据-详细句子表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Schema(description = "SentencesDetailedVO视图对象")
@Data
@EqualsAndHashCode(callSuper = true)
public class SentencesDetailedVo extends BaseVo {

    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 句子编号。
     */
    @Schema(description = "句子编号")
    private Long sentenceNumber;

    /**
     * 语言。
     */
    @Schema(description = "语言")
    private String isoLanguageCode;

    /**
     * 文本。
     */
    @Schema(description = "文本")
    private String text;

    /**
     * 用户名(所有人)。
     */
    @Schema(description = "用户名(所有人)")
    private String username;

    /**
     * 添加日期。
     */
    @Schema(description = "添加日期")
    private Date addedDate;

    /**
     * 最后修改。
     */
    @Schema(description = "最后修改")
    private Date lastModified;
}
