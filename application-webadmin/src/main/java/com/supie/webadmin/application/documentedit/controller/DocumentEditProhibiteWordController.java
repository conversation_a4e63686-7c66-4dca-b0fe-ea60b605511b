package com.supie.webadmin.application.documentedit.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.exception.MyRuntimeException;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.application.documentedit.dto.DocumentEditProhibiteWordDto;
import com.supie.webadmin.application.documentedit.model.DocumentEditProhibiteWord;
import com.supie.webadmin.application.documentedit.model.DocumentEditProhibiteWordLib;
import com.supie.webadmin.application.documentedit.service.DocumentEditProhibiteWordLibService;
import com.supie.webadmin.application.documentedit.service.DocumentEditProhibiteWordService;
import com.supie.webadmin.application.documentedit.vo.DocumentEditProhibiteWordVo;
import com.supie.webadmin.interceptor.PythonApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档编辑禁止词句表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "文档编辑禁止词句表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/documentEditProhibiteWord")
public class DocumentEditProhibiteWordController {

    @Autowired
    private DocumentEditProhibiteWordService documentEditProhibiteWordService;
    @Autowired
    private DocumentEditProhibiteWordLibService documentEditProhibiteWordLibService;

    /**
     * 新增文档编辑禁止词句表数据。
     *
     * @param documentEditProhibiteWordDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditProhibiteWordDto.id",
            "documentEditProhibiteWordDto.searchString",
            "documentEditProhibiteWordDto.createTimeStart",
            "documentEditProhibiteWordDto.createTimeEnd",
            "documentEditProhibiteWordDto.updateTimeStart",
            "documentEditProhibiteWordDto.updateTimeEnd"})
    //// @SaCheckPermission("documentEditProhibiteWord.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DocumentEditProhibiteWordDto documentEditProhibiteWordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditProhibiteWordDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditProhibiteWord documentEditProhibiteWord = MyModelUtil.copyTo(documentEditProhibiteWordDto, DocumentEditProhibiteWord.class);
        // 验证父Id的数据合法性
        DocumentEditProhibiteWord parentDocumentEditProhibiteWord;
        if (MyCommonUtil.isNotBlankOrNull(documentEditProhibiteWord.getCreateUserId())) {
            parentDocumentEditProhibiteWord = documentEditProhibiteWordService.getById(documentEditProhibiteWord.getCreateUserId());
            if (parentDocumentEditProhibiteWord == null) {
                errorMessage = "数据验证失败，关联的父节点并不存在，请刷新后重试！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        documentEditProhibiteWord = documentEditProhibiteWordService.saveNew(documentEditProhibiteWord);
        return ResponseResult.success(documentEditProhibiteWord.getId());
    }

    /**
     * 更新文档编辑禁止词句表数据。
     *
     * @param documentEditProhibiteWordDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditProhibiteWordDto.searchString",
            "documentEditProhibiteWordDto.createTimeStart",
            "documentEditProhibiteWordDto.createTimeEnd",
            "documentEditProhibiteWordDto.updateTimeStart",
            "documentEditProhibiteWordDto.updateTimeEnd"})
    //// @SaCheckPermission("documentEditProhibiteWord.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DocumentEditProhibiteWordDto documentEditProhibiteWordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditProhibiteWordDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditProhibiteWord documentEditProhibiteWord = MyModelUtil.copyTo(documentEditProhibiteWordDto, DocumentEditProhibiteWord.class);
        DocumentEditProhibiteWord originalDocumentEditProhibiteWord = documentEditProhibiteWordService.getById(documentEditProhibiteWord.getId());
        if (originalDocumentEditProhibiteWord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // 验证父Id的数据合法性
        if (MyCommonUtil.isNotBlankOrNull(documentEditProhibiteWord.getCreateUserId())
                && ObjectUtil.notEqual(documentEditProhibiteWord.getCreateUserId(), originalDocumentEditProhibiteWord.getCreateUserId())) {
            DocumentEditProhibiteWord parentDocumentEditProhibiteWord = documentEditProhibiteWordService.getById(documentEditProhibiteWord.getCreateUserId());
            if (parentDocumentEditProhibiteWord == null) {
                // NOTE: 修改下面方括号中的话述
                errorMessage = "数据验证失败，关联的 [父节点] 并不存在，请刷新后重试！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        if (!documentEditProhibiteWordService.update(documentEditProhibiteWord, originalDocumentEditProhibiteWord)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除文档编辑禁止词句表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("documentEditProhibiteWord.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除文档编辑禁止词句表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("documentEditProhibiteWord.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的文档编辑禁止词句表列表。
     *
     * @param documentEditProhibiteWordDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //// @SaCheckPermission("documentEditProhibiteWord.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<DocumentEditProhibiteWordVo>> list(
            @MyRequestBody DocumentEditProhibiteWordDto documentEditProhibiteWordDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DocumentEditProhibiteWord documentEditProhibiteWordFilter = MyModelUtil.copyTo(documentEditProhibiteWordDtoFilter, DocumentEditProhibiteWord.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DocumentEditProhibiteWord.class);
        List<DocumentEditProhibiteWord> documentEditProhibiteWordList =
                documentEditProhibiteWordService.getDocumentEditProhibiteWordListWithRelation(documentEditProhibiteWordFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(documentEditProhibiteWordList, DocumentEditProhibiteWordVo.class));
    }

    /**
     * 查看指定文档编辑禁止词句表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //// @SaCheckPermission("documentEditProhibiteWord.view")
    @GetMapping("/view")
    public ResponseResult<DocumentEditProhibiteWordVo> view(@RequestParam Long id) {
        DocumentEditProhibiteWord documentEditProhibiteWord = documentEditProhibiteWordService.getByIdWithRelation(id, MyRelationParam.full());
        if (documentEditProhibiteWord == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        DocumentEditProhibiteWordVo documentEditProhibiteWordVo = MyModelUtil.copyTo(documentEditProhibiteWord, DocumentEditProhibiteWordVo.class);
        return ResponseResult.success(documentEditProhibiteWordVo);
    }

    @PythonApi
    @Operation(summary = "python获取文档编辑禁止词句表数据")
    @PostMapping("/getProhibiteWords")
    public ResponseResult<List<Map<String, String>>> getProhibiteWords(@MyRequestBody DocumentEditProhibiteWordDto documentEditProhibiteWordDtoFilter) {
        if (documentEditProhibiteWordDtoFilter == null) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        DocumentEditProhibiteWord documentEditProhibiteWord = MyModelUtil.copyTo(documentEditProhibiteWordDtoFilter, DocumentEditProhibiteWord.class);
        List<DocumentEditProhibiteWord> prohibiteWordsList = documentEditProhibiteWordService.getDocumentEditProhibiteWordList(documentEditProhibiteWord, null);
        List<Map<String, String>> resultList = new ArrayList<>();
        for (DocumentEditProhibiteWord prohibiteWord : prohibiteWordsList) {
            Map<String, String> map = new HashMap<>();
            map.put("prohibiteWord", prohibiteWord.getProhibiteWord());
            map.put("replaceWord", prohibiteWord.getReplaceWord());
            map.put("tableDesc", prohibiteWord.getTableDesc());
            map.put("id", String.valueOf(prohibiteWord.getId()));
            resultList.add(map);
        }
        return ResponseResult.success(resultList);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DocumentEditProhibiteWord originalDocumentEditProhibiteWord = documentEditProhibiteWordService.getById(id);
        if (originalDocumentEditProhibiteWord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (documentEditProhibiteWordService.hasChildren(id)) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象存在子对象] ，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.HAS_CHILDREN_DATA, errorMessage);
        }
        if (!documentEditProhibiteWordService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 下载模板文件
     * @param response
     */
    @Operation(summary = "下载模板文件")
    @GetMapping("/downloadTemplateFile")
    public void downloadTemplateFile(HttpServletResponse response) throws IOException {
        // 将 resources 下的（BaseScriptFile/template/TermTiesTemplate.xlsx）文件通过 response 输出到浏览器
        // 从 resources 目录下读取文件
        Resource resource = new ClassPathResource("template/template/ProhibiteWordTemplate.xlsx");
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=ProhibiteWordTemplate.xlsx");
        response.setHeader("Content-Length", String.valueOf(resource.contentLength()));
        // 将文件内容写入响应流
        try (InputStream inputStream = resource.getInputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
        }
    }

    /**
     * 批量导入
     */
    @Operation(summary = "批量导入")
    @PostMapping("/bulkImport")
    public ResponseResult<Void> importDocumentEditProhibiteWord(@RequestParam MultipartFile file, @RequestParam Long prohibiteWordLibId) throws IOException {
        if (file == null || prohibiteWordLibId == null) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "文件格式错误，请上传[.xlsx]格式文件！");
        }
        DocumentEditProhibiteWordLib prohibiteWordLib = documentEditProhibiteWordLibService.getById(prohibiteWordLibId);
        if (prohibiteWordLib == null) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "导入失败，违禁词库不存在！");
        }
        try {
            documentEditProhibiteWordService.importProhibiteWord(file, prohibiteWordLib);
        } catch (Exception e) {
            throw new MyRuntimeException("导入失败！" + e.getMessage());
        }
        return ResponseResult.success();
    }

}
