package com.supie.webadmin.application.tatoeba.controller;

// import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.tatoeba.dto.SentencesDetailedDto;
import com.supie.webadmin.application.tatoeba.model.SentencesDetailed;
import com.supie.webadmin.application.tatoeba.service.SentencesDetailedService;
import com.supie.webadmin.application.tatoeba.vo.SentencesDetailedVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Tatoeba翻译数据-详细句子表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "Tatoeba翻译数据-详细句子表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/sentencesDetailed")
public class SentencesDetailedController {

    @Autowired
    private SentencesDetailedService sentencesDetailedService;

    /**
     * 新增Tatoeba翻译数据-详细句子表数据。
     *
     * @param sentencesDetailedDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "sentencesDetailedDto.id",
            "sentencesDetailedDto.searchString",
            "sentencesDetailedDto.createTimeStart",
            "sentencesDetailedDto.createTimeEnd",
            "sentencesDetailedDto.updateTimeStart",
            "sentencesDetailedDto.updateTimeEnd",
            "sentencesDetailedDto.addedDateStart",
            "sentencesDetailedDto.addedDateEnd",
            "sentencesDetailedDto.lastModifiedStart",
            "sentencesDetailedDto.lastModifiedEnd"})
    //////// @SaCheckPermission("sentencesDetailed.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SentencesDetailedDto sentencesDetailedDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sentencesDetailedDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SentencesDetailed sentencesDetailed = MyModelUtil.copyTo(sentencesDetailedDto, SentencesDetailed.class);
        sentencesDetailed = sentencesDetailedService.saveNew(sentencesDetailed);
        return ResponseResult.success(sentencesDetailed.getId());
    }

    /**
     * 更新Tatoeba翻译数据-详细句子表数据。
     *
     * @param sentencesDetailedDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "sentencesDetailedDto.searchString",
            "sentencesDetailedDto.createTimeStart",
            "sentencesDetailedDto.createTimeEnd",
            "sentencesDetailedDto.updateTimeStart",
            "sentencesDetailedDto.updateTimeEnd",
            "sentencesDetailedDto.addedDateStart",
            "sentencesDetailedDto.addedDateEnd",
            "sentencesDetailedDto.lastModifiedStart",
            "sentencesDetailedDto.lastModifiedEnd"})
    //////// @SaCheckPermission("sentencesDetailed.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SentencesDetailedDto sentencesDetailedDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sentencesDetailedDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SentencesDetailed sentencesDetailed = MyModelUtil.copyTo(sentencesDetailedDto, SentencesDetailed.class);
        SentencesDetailed originalSentencesDetailed = sentencesDetailedService.getById(sentencesDetailed.getId());
        if (originalSentencesDetailed == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sentencesDetailedService.update(sentencesDetailed, originalSentencesDetailed)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除Tatoeba翻译数据-详细句子表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //////// @SaCheckPermission("sentencesDetailed.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除Tatoeba翻译数据-详细句子表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //////// @SaCheckPermission("sentencesDetailed.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的Tatoeba翻译数据-详细句子表列表。
     *
     * @param sentencesDetailedDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////// @SaCheckPermission("sentencesDetailed.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SentencesDetailedVo>> list(
            @MyRequestBody SentencesDetailedDto sentencesDetailedDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SentencesDetailed sentencesDetailedFilter = MyModelUtil.copyTo(sentencesDetailedDtoFilter, SentencesDetailed.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SentencesDetailed.class);
        List<SentencesDetailed> sentencesDetailedList =
                sentencesDetailedService.getSentencesDetailedListWithRelation(sentencesDetailedFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(sentencesDetailedList, SentencesDetailedVo.class));
    }

    /**
     * 分组列出符合过滤条件的Tatoeba翻译数据-详细句子表列表。
     *
     * @param sentencesDetailedDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////// @SaCheckPermission("sentencesDetailed.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SentencesDetailedVo>> listWithGroup(
            @MyRequestBody SentencesDetailedDto sentencesDetailedDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SentencesDetailed.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SentencesDetailed.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SentencesDetailed filter = MyModelUtil.copyTo(sentencesDetailedDtoFilter, SentencesDetailed.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SentencesDetailed> resultList = sentencesDetailedService.getGroupedSentencesDetailedListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SentencesDetailedVo.class));
    }

    /**
     * 查看指定Tatoeba翻译数据-详细句子表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //////// @SaCheckPermission("sentencesDetailed.view")
    @GetMapping("/view")
    public ResponseResult<SentencesDetailedVo> view(@RequestParam Long id) {
        SentencesDetailed sentencesDetailed = sentencesDetailedService.getByIdWithRelation(id, MyRelationParam.full());
        if (sentencesDetailed == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SentencesDetailedVo sentencesDetailedVo = MyModelUtil.copyTo(sentencesDetailed, SentencesDetailedVo.class);
        return ResponseResult.success(sentencesDetailedVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SentencesDetailed originalSentencesDetailed = sentencesDetailedService.getById(id);
        if (originalSentencesDetailed == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sentencesDetailedService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
