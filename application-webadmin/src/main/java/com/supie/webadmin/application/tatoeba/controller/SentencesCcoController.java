package com.supie.webadmin.application.tatoeba.controller;

// import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.tatoeba.dto.SentencesCcoDto;
import com.supie.webadmin.application.tatoeba.model.SentencesCco;
import com.supie.webadmin.application.tatoeba.service.SentencesCcoService;
import com.supie.webadmin.application.tatoeba.vo.SentencesCcoVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Tatoeba翻译数据-句子（CC0）表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "Tatoeba翻译数据-句子（CC0）表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/sentencesCco")
public class SentencesCcoController {

    @Autowired
    private SentencesCcoService sentencesCcoService;

    /**
     * 新增Tatoeba翻译数据-句子（CC0）表数据。
     *
     * @param sentencesCcoDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "sentencesCcoDto.id",
            "sentencesCcoDto.searchString",
            "sentencesCcoDto.createTimeStart",
            "sentencesCcoDto.createTimeEnd",
            "sentencesCcoDto.updateTimeStart",
            "sentencesCcoDto.updateTimeEnd",
            "sentencesCcoDto.lastModifiedStart",
            "sentencesCcoDto.lastModifiedEnd"})
    //////@SaCheckPermission("sentencesCco.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SentencesCcoDto sentencesCcoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sentencesCcoDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SentencesCco sentencesCco = MyModelUtil.copyTo(sentencesCcoDto, SentencesCco.class);
        sentencesCco = sentencesCcoService.saveNew(sentencesCco);
        return ResponseResult.success(sentencesCco.getId());
    }

    /**
     * 更新Tatoeba翻译数据-句子（CC0）表数据。
     *
     * @param sentencesCcoDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "sentencesCcoDto.searchString",
            "sentencesCcoDto.createTimeStart",
            "sentencesCcoDto.createTimeEnd",
            "sentencesCcoDto.updateTimeStart",
            "sentencesCcoDto.updateTimeEnd",
            "sentencesCcoDto.lastModifiedStart",
            "sentencesCcoDto.lastModifiedEnd"})
    //////@SaCheckPermission("sentencesCco.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SentencesCcoDto sentencesCcoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sentencesCcoDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SentencesCco sentencesCco = MyModelUtil.copyTo(sentencesCcoDto, SentencesCco.class);
        SentencesCco originalSentencesCco = sentencesCcoService.getById(sentencesCco.getId());
        if (originalSentencesCco == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sentencesCcoService.update(sentencesCco, originalSentencesCco)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除Tatoeba翻译数据-句子（CC0）表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //////@SaCheckPermission("sentencesCco.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除Tatoeba翻译数据-句子（CC0）表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //////@SaCheckPermission("sentencesCco.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的Tatoeba翻译数据-句子（CC0）表列表。
     *
     * @param sentencesCcoDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////@SaCheckPermission("sentencesCco.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SentencesCcoVo>> list(
            @MyRequestBody SentencesCcoDto sentencesCcoDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SentencesCco sentencesCcoFilter = MyModelUtil.copyTo(sentencesCcoDtoFilter, SentencesCco.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SentencesCco.class);
        List<SentencesCco> sentencesCcoList =
                sentencesCcoService.getSentencesCcoListWithRelation(sentencesCcoFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(sentencesCcoList, SentencesCcoVo.class));
    }

    /**
     * 分组列出符合过滤条件的Tatoeba翻译数据-句子（CC0）表列表。
     *
     * @param sentencesCcoDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////@SaCheckPermission("sentencesCco.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SentencesCcoVo>> listWithGroup(
            @MyRequestBody SentencesCcoDto sentencesCcoDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SentencesCco.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SentencesCco.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SentencesCco filter = MyModelUtil.copyTo(sentencesCcoDtoFilter, SentencesCco.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SentencesCco> resultList = sentencesCcoService.getGroupedSentencesCcoListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SentencesCcoVo.class));
    }

    /**
     * 查看指定Tatoeba翻译数据-句子（CC0）表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //////@SaCheckPermission("sentencesCco.view")
    @GetMapping("/view")
    public ResponseResult<SentencesCcoVo> view(@RequestParam Long id) {
        SentencesCco sentencesCco = sentencesCcoService.getByIdWithRelation(id, MyRelationParam.full());
        if (sentencesCco == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SentencesCcoVo sentencesCcoVo = MyModelUtil.copyTo(sentencesCco, SentencesCcoVo.class);
        return ResponseResult.success(sentencesCcoVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SentencesCco originalSentencesCco = sentencesCcoService.getById(id);
        if (originalSentencesCco == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sentencesCcoService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
