package com.supie.webadmin.application.translate.dao;

import com.supie.common.core.annotation.EnableDataPerm;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.application.translate.model.TranslateTaskMemoryLib;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 服务管理-翻译任务-记忆库多对多关联表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@EnableDataPerm
public interface TranslateTaskMemoryLibMapper extends BaseDaoMapper<TranslateTaskMemoryLib> {

    /**
     * 批量插入对象列表。
     *
     * @param translateTaskMemoryLibList 新增对象列表。
     */
    void insertList(List<TranslateTaskMemoryLib> translateTaskMemoryLibList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param translateTaskMemoryLibFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<TranslateTaskMemoryLib> getTranslateTaskMemoryLibList(
            @Param("translateTaskMemoryLibFilter") TranslateTaskMemoryLib translateTaskMemoryLibFilter, @Param("orderBy") String orderBy);
}
