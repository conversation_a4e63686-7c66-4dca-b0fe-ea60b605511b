package com.supie.webadmin.application.documentedit.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 违禁词库实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ms_document_edit_prohibite_word_lib")
public class DocumentEditProhibiteWordLib extends BaseModel {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符编号。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 违禁词库名称。
     */
    @TableField(value = "lib_name")
    private String libName;

    /**
     * 违禁词库备注。
     */
    @TableField(value = "lib_remark")
    private String libRemark;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * lib_name / lib_remark LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
