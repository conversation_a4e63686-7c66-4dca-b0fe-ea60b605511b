package com.supie.webadmin.application.tatoeba.model;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.annotation.*;
import com.supie.common.core.base.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;

/**
 * Tatoeba翻译数据-句子表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ms_tatoeba_sentences")
public class Sentences extends BaseModel {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符串ID。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 句子编号。
     */
    @TableField(value = "sentence_number")
    private Long sentenceNumber;

    /**
     * ISO 639-3 语言编码。
     */
    @TableField(value = "iso_language_code")
    private String isoLanguageCode;

    /**
     * 句子文本。
     */
    @TableField(value = "text")
    private String text;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * text LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

    public Sentences(Long id, Date createTime, Long createUserId, Date updateTime, Long updateUserId, Long dataUserId, Long dataDeptId,
                     Long sentenceNumber, String isoLanguageCode, String text) {
        this.id = id;
        this.createTime = createTime;
        this.createUserId = createUserId;
        this.updateTime = updateTime;
        this.updateUserId = updateUserId;
        this.dataUserId = dataUserId;
        this.dataDeptId = dataDeptId;
        this.isDelete = GlobalDeletedFlag.NORMAL;

        this.sentenceNumber = sentenceNumber;
        this.isoLanguageCode = isoLanguageCode;
        this.text = text;
    }

    /**
     * 生成 'ms_tatoeba_sentences' 表的SQL插入语句
     * @param csvFilePath  csv文件路径
     * @return             SQL文件路径
     * @throws IOException IO异常
     */
    public static Path generateSQL(Snowflake snowflake, Path csvFilePath) throws IOException {
        String csvFileName = csvFilePath.getFileName().toString();
        // 构建SQL脚本文件应存放的目录路径（与csvFilePath同级的SqlScript目录）
        Path sqlDirectoryPath = csvFilePath.getParent().resolve("SqlScript");
        // 检查并创建SqlScript目录（如果不存在）
        if (!Files.exists(sqlDirectoryPath)) {
            Files.createDirectories(sqlDirectoryPath);
        }
        // 构建SQL文件的完整路径
        Path sqlFilePath = sqlDirectoryPath.resolve(csvFileName.replace(".csv", ".sql"));
        try (BufferedReader reader = Files.newBufferedReader(csvFilePath);
             BufferedWriter writer = Files.newBufferedWriter(sqlFilePath)) {
            String line;
            Long index = 0L;
            while ((line = reader.readLine()) != null) {
                index++;
                log.info("处理【{}】弟【{}】条数据", csvFileName, index);
                String[] columns = line.split("\t", -1); // 假设是制表符分隔的数据
                String sentenceNumber = columns[0];
                String isoLanguageCode = columns[1];
                String text = columns[2];
                Date newDate = new Date();
                ThreadUtil.sleep(1);
                String sql = String.format(
                        "INSERT INTO ms_tatoeba_sentences (id, create_time, create_user_id, update_time, update_user_id, data_user_id, data_dept_id, sentence_number, iso_language_code, text) " +
                                "VALUES (%s, '%s', %s, '%s', %s, %s, %s, '%s', '%s', '%s');",
                        snowflake.nextId(), newDate, 100001L, newDate, 100001L, 100001L, 100001L,
                        sentenceNumber, isoLanguageCode, text.replace("'", "''"));
                writer.write(sql);
                writer.newLine();
            }
            log.info("==========>【{}】处理完成，共【{}】条数据！", csvFileName, index);
        }
        return sqlFilePath;
    }

}
