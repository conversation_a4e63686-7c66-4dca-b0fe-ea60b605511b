package com.supie.webadmin.application.tatoeba.dao;

import com.supie.common.core.annotation.EnableDataPerm;

import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.webadmin.application.tatoeba.model.UserLanguages;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * Tatoeba翻译数据-每种语言的用户技能水平表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@EnableDataPerm
public interface UserLanguagesMapper extends BaseDaoMapper<UserLanguages> {

    /**
     * 批量插入对象列表。
     *
     * @param userLanguagesList 新增对象列表。
     */
    void insertList(List<UserLanguages> userLanguagesList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param userLanguagesFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<UserLanguages> getGroupedUserLanguagesList(
            @Param("userLanguagesFilter") UserLanguages userLanguagesFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param userLanguagesFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<UserLanguages> getUserLanguagesList(
            @Param("userLanguagesFilter") UserLanguages userLanguagesFilter, @Param("orderBy") String orderBy);
}
