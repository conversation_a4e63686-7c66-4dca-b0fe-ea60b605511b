package com.supie.webadmin.application.knowledgebase.controller;

// import cn.dev33.satoken.annotation.SaCheckPermission;

import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;

import com.supie.webadmin.application.knowledgebase.dto.AppAssistantBasicDto;
import com.supie.webadmin.application.knowledgebase.model.AppAssistantBasic;
import com.supie.webadmin.application.knowledgebase.service.AppAssistantBasicService;
import com.supie.webadmin.application.knowledgebase.vo.AppAssistantBasicVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用中心-应用助手基础信息表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "应用中心-应用助手基础信息表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/appAssistantBasic")
public class AppAssistantBasicController {

    @Lazy
    @Resource
    private AppAssistantBasicService appAssistantBasicService;

    /**
     * 新增应用中心-应用助手基础信息表数据。
     *
     * @param appAssistantBasicDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appAssistantBasicDto.id",
            "appAssistantBasicDto.searchString",
            "appAssistantBasicDto.createTimeStart",
            "appAssistantBasicDto.createTimeEnd",
            "appAssistantBasicDto.updateTimeStart",
            "appAssistantBasicDto.updateTimeEnd",
            "appAssistantBasicDto.temperatureStart",
            "appAssistantBasicDto.temperatureEnd",
            "appAssistantBasicDto.generateEachStart",
            "appAssistantBasicDto.generateEachEnd",
            "appAssistantBasicDto.maxTokensStart",
            "appAssistantBasicDto.maxTokensEnd"})
    ////// @SaCheckPermission("appAssistantBasic.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody AppAssistantBasicDto appAssistantBasicDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appAssistantBasicDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppAssistantBasic appAssistantBasic = MyModelUtil.copyTo(appAssistantBasicDto, AppAssistantBasic.class);
        appAssistantBasic = appAssistantBasicService.saveNew(appAssistantBasic);
        return ResponseResult.success(appAssistantBasic.getId());
    }

    /**
     * 更新应用中心-应用助手基础信息表数据。
     *
     * @param appAssistantBasicDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "appAssistantBasicDto.searchString",
            "appAssistantBasicDto.createTimeStart",
            "appAssistantBasicDto.createTimeEnd",
            "appAssistantBasicDto.updateTimeStart",
            "appAssistantBasicDto.updateTimeEnd",
            "appAssistantBasicDto.temperatureStart",
            "appAssistantBasicDto.temperatureEnd",
            "appAssistantBasicDto.generateEachStart",
            "appAssistantBasicDto.generateEachEnd",
            "appAssistantBasicDto.maxTokensStart",
            "appAssistantBasicDto.maxTokensEnd"})
    ////// @SaCheckPermission("appAssistantBasic.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody AppAssistantBasicDto appAssistantBasicDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(appAssistantBasicDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        AppAssistantBasic appAssistantBasic = MyModelUtil.copyTo(appAssistantBasicDto, AppAssistantBasic.class);
        AppAssistantBasic originalAppAssistantBasic = appAssistantBasicService.getById(appAssistantBasic.getId());
        if (originalAppAssistantBasic == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appAssistantBasicService.update(appAssistantBasic, originalAppAssistantBasic)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除应用中心-应用助手基础信息表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    ////// @SaCheckPermission("appAssistantBasic.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除应用中心-应用助手基础信息表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    ////// @SaCheckPermission("appAssistantBasic.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的应用中心-应用助手基础信息表列表。
     *
     * @param appAssistantBasicDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////// @SaCheckPermission("appAssistantBasic.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<AppAssistantBasicVo>> list(
            @MyRequestBody AppAssistantBasicDto appAssistantBasicDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        AppAssistantBasic appAssistantBasicFilter = MyModelUtil.copyTo(appAssistantBasicDtoFilter, AppAssistantBasic.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppAssistantBasic.class);
        List<AppAssistantBasic> appAssistantBasicList =
                appAssistantBasicService.getAppAssistantBasicListWithRelation(appAssistantBasicFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(appAssistantBasicList, AppAssistantBasicVo.class));
    }

    /**
     * 分组列出符合过滤条件的应用中心-应用助手基础信息表列表。
     *
     * @param appAssistantBasicDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    ////// @SaCheckPermission("appAssistantBasic.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<AppAssistantBasicVo>> listWithGroup(
            @MyRequestBody AppAssistantBasicDto appAssistantBasicDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, AppAssistantBasic.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, AppAssistantBasic.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        AppAssistantBasic filter = MyModelUtil.copyTo(appAssistantBasicDtoFilter, AppAssistantBasic.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<AppAssistantBasic> resultList = appAssistantBasicService.getGroupedAppAssistantBasicListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, AppAssistantBasicVo.class));
    }

    /**
     * 查看指定应用中心-应用助手基础信息表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    ////// @SaCheckPermission("appAssistantBasic.view")
    @GetMapping("/view")
    public ResponseResult<AppAssistantBasicVo> view(@RequestParam Long id) {
        AppAssistantBasic appAssistantBasic = appAssistantBasicService.getByIdWithRelation(id, MyRelationParam.full());
        if (appAssistantBasic == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        AppAssistantBasicVo appAssistantBasicVo = MyModelUtil.copyTo(appAssistantBasic, AppAssistantBasicVo.class);
        return ResponseResult.success(appAssistantBasicVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        AppAssistantBasic originalAppAssistantBasic = appAssistantBasicService.getById(id);
        if (originalAppAssistantBasic == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appAssistantBasicService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
