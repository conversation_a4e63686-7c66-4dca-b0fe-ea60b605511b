<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.tatoeba.dao.SentencesDetailedMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.tatoeba.model.SentencesDetailed">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="sentence_number" jdbcType="BIGINT" property="sentenceNumber"/>
        <result column="iso_language_code" jdbcType="VARCHAR" property="isoLanguageCode"/>
        <result column="text" jdbcType="LONGVARCHAR" property="text"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="added_date" jdbcType="TIMESTAMP" property="addedDate"/>
        <result column="last_modified" jdbcType="TIMESTAMP" property="lastModified"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_tatoeba_sentences_detailed
            (id,
            str_id,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            sentence_number,
            iso_language_code,
            text,
            username,
            added_date,
            last_modified)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.sentenceNumber},
            #{item.isoLanguageCode},
            #{item.text},
            #{item.username},
            #{item.addedDate},
            #{item.lastModified})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.tatoeba.dao.SentencesDetailedMapper.inputFilterRef"/>
        AND ms_tatoeba_sentences_detailed.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="sentencesDetailedFilter != null">
            <if test="sentencesDetailedFilter.id != null">
                AND ms_tatoeba_sentences_detailed.id = #{sentencesDetailedFilter.id}
            </if>
            <if test="sentencesDetailedFilter.strId != null and sentencesDetailedFilter.strId != ''">
                AND ms_tatoeba_sentences_detailed.str_id = #{sentencesDetailedFilter.strId}
            </if>
            <if test="sentencesDetailedFilter.createTimeStart != null and sentencesDetailedFilter.createTimeStart != ''">
                AND ms_tatoeba_sentences_detailed.create_time &gt;= #{sentencesDetailedFilter.createTimeStart}
            </if>
            <if test="sentencesDetailedFilter.createTimeEnd != null and sentencesDetailedFilter.createTimeEnd != ''">
                AND ms_tatoeba_sentences_detailed.create_time &lt;= #{sentencesDetailedFilter.createTimeEnd}
            </if>
            <if test="sentencesDetailedFilter.createUserId != null">
                AND ms_tatoeba_sentences_detailed.create_user_id = #{sentencesDetailedFilter.createUserId}
            </if>
            <if test="sentencesDetailedFilter.updateTimeStart != null and sentencesDetailedFilter.updateTimeStart != ''">
                AND ms_tatoeba_sentences_detailed.update_time &gt;= #{sentencesDetailedFilter.updateTimeStart}
            </if>
            <if test="sentencesDetailedFilter.updateTimeEnd != null and sentencesDetailedFilter.updateTimeEnd != ''">
                AND ms_tatoeba_sentences_detailed.update_time &lt;= #{sentencesDetailedFilter.updateTimeEnd}
            </if>
            <if test="sentencesDetailedFilter.updateUserId != null">
                AND ms_tatoeba_sentences_detailed.update_user_id = #{sentencesDetailedFilter.updateUserId}
            </if>
            <if test="sentencesDetailedFilter.dataUserId != null">
                AND ms_tatoeba_sentences_detailed.data_user_id = #{sentencesDetailedFilter.dataUserId}
            </if>
            <if test="sentencesDetailedFilter.dataDeptId != null">
                AND ms_tatoeba_sentences_detailed.data_dept_id = #{sentencesDetailedFilter.dataDeptId}
            </if>
            <if test="sentencesDetailedFilter.sentenceNumber != null and sentencesDetailedFilter.sentenceNumber != ''">
                AND ms_tatoeba_sentences_detailed.sentence_number = #{sentencesDetailedFilter.sentenceNumber}
            </if>
            <if test="sentencesDetailedFilter.isoLanguageCode != null and sentencesDetailedFilter.isoLanguageCode != ''">
                AND ms_tatoeba_sentences_detailed.iso_language_code = #{sentencesDetailedFilter.isoLanguageCode}
            </if>
            <if test="sentencesDetailedFilter.text != null and sentencesDetailedFilter.text != ''">
                <bind name = "safeSentencesDetailedText" value = "'%' + sentencesDetailedFilter.text + '%'" />
                AND ms_tatoeba_sentences_detailed.text LIKE #{safeSentencesDetailedText}
            </if>
            <if test="sentencesDetailedFilter.username != null and sentencesDetailedFilter.username != ''">
                <bind name = "safeSentencesDetailedUsername" value = "'%' + sentencesDetailedFilter.username + '%'" />
                AND ms_tatoeba_sentences_detailed.username LIKE #{safeSentencesDetailedUsername}
            </if>
            <if test="sentencesDetailedFilter.addedDateStart != null and sentencesDetailedFilter.addedDateStart != ''">
                AND ms_tatoeba_sentences_detailed.added_date &gt;= #{sentencesDetailedFilter.addedDateStart}
            </if>
            <if test="sentencesDetailedFilter.addedDateEnd != null and sentencesDetailedFilter.addedDateEnd != ''">
                AND ms_tatoeba_sentences_detailed.added_date &lt;= #{sentencesDetailedFilter.addedDateEnd}
            </if>
            <if test="sentencesDetailedFilter.lastModifiedStart != null and sentencesDetailedFilter.lastModifiedStart != ''">
                AND ms_tatoeba_sentences_detailed.last_modified &gt;= #{sentencesDetailedFilter.lastModifiedStart}
            </if>
            <if test="sentencesDetailedFilter.lastModifiedEnd != null and sentencesDetailedFilter.lastModifiedEnd != ''">
                AND ms_tatoeba_sentences_detailed.last_modified &lt;= #{sentencesDetailedFilter.lastModifiedEnd}
            </if>
            <if test="sentencesDetailedFilter.searchString != null and sentencesDetailedFilter.searchString != ''">
                <bind name = "safeSentencesDetailedSearchString" value = "'%' + sentencesDetailedFilter.searchString + '%'" />
                AND IFNULL(ms_tatoeba_sentences_detailed.text,'') LIKE #{safeSentencesDetailedSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSentencesDetailedList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.tatoeba.model.SentencesDetailed">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM ms_tatoeba_sentences_detailed
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) ms_tatoeba_sentences_detailed
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSentencesDetailedList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.tatoeba.model.SentencesDetailed">
        SELECT * FROM ms_tatoeba_sentences_detailed
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
