package com.supie.webadmin.application.tatoeba.controller;

// import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.tatoeba.dto.UserLanguagesDto;
import com.supie.webadmin.application.tatoeba.model.UserLanguages;
import com.supie.webadmin.application.tatoeba.service.UserLanguagesService;
import com.supie.webadmin.application.tatoeba.vo.UserLanguagesVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Tatoeba翻译数据-每种语言的用户技能水平表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "Tatoeba翻译数据-每种语言的用户技能水平表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/userLanguages")
public class UserLanguagesController {

    @Autowired
    private UserLanguagesService userLanguagesService;

    /**
     * 新增Tatoeba翻译数据-每种语言的用户技能水平表数据。
     *
     * @param userLanguagesDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "userLanguagesDto.id",
            "userLanguagesDto.searchString",
            "userLanguagesDto.createTimeStart",
            "userLanguagesDto.createTimeEnd",
            "userLanguagesDto.updateTimeStart",
            "userLanguagesDto.updateTimeEnd"})
    //////// @SaCheckPermission("userLanguages.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody UserLanguagesDto userLanguagesDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(userLanguagesDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        UserLanguages userLanguages = MyModelUtil.copyTo(userLanguagesDto, UserLanguages.class);
        userLanguages = userLanguagesService.saveNew(userLanguages);
        return ResponseResult.success(userLanguages.getId());
    }

    /**
     * 更新Tatoeba翻译数据-每种语言的用户技能水平表数据。
     *
     * @param userLanguagesDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "userLanguagesDto.searchString",
            "userLanguagesDto.createTimeStart",
            "userLanguagesDto.createTimeEnd",
            "userLanguagesDto.updateTimeStart",
            "userLanguagesDto.updateTimeEnd"})
    //////// @SaCheckPermission("userLanguages.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody UserLanguagesDto userLanguagesDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(userLanguagesDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        UserLanguages userLanguages = MyModelUtil.copyTo(userLanguagesDto, UserLanguages.class);
        UserLanguages originalUserLanguages = userLanguagesService.getById(userLanguages.getId());
        if (originalUserLanguages == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!userLanguagesService.update(userLanguages, originalUserLanguages)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除Tatoeba翻译数据-每种语言的用户技能水平表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //////// @SaCheckPermission("userLanguages.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除Tatoeba翻译数据-每种语言的用户技能水平表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //////// @SaCheckPermission("userLanguages.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的Tatoeba翻译数据-每种语言的用户技能水平表列表。
     *
     * @param userLanguagesDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////// @SaCheckPermission("userLanguages.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<UserLanguagesVo>> list(
            @MyRequestBody UserLanguagesDto userLanguagesDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        UserLanguages userLanguagesFilter = MyModelUtil.copyTo(userLanguagesDtoFilter, UserLanguages.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, UserLanguages.class);
        List<UserLanguages> userLanguagesList =
                userLanguagesService.getUserLanguagesListWithRelation(userLanguagesFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(userLanguagesList, UserLanguagesVo.class));
    }

    /**
     * 分组列出符合过滤条件的Tatoeba翻译数据-每种语言的用户技能水平表列表。
     *
     * @param userLanguagesDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //////// @SaCheckPermission("userLanguages.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<UserLanguagesVo>> listWithGroup(
            @MyRequestBody UserLanguagesDto userLanguagesDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, UserLanguages.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, UserLanguages.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        UserLanguages filter = MyModelUtil.copyTo(userLanguagesDtoFilter, UserLanguages.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<UserLanguages> resultList = userLanguagesService.getGroupedUserLanguagesListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, UserLanguagesVo.class));
    }

    /**
     * 查看指定Tatoeba翻译数据-每种语言的用户技能水平表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //////// @SaCheckPermission("userLanguages.view")
    @GetMapping("/view")
    public ResponseResult<UserLanguagesVo> view(@RequestParam Long id) {
        UserLanguages userLanguages = userLanguagesService.getByIdWithRelation(id, MyRelationParam.full());
        if (userLanguages == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        UserLanguagesVo userLanguagesVo = MyModelUtil.copyTo(userLanguages, UserLanguagesVo.class);
        return ResponseResult.success(userLanguagesVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        UserLanguages originalUserLanguages = userLanguagesService.getById(id);
        if (originalUserLanguages == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!userLanguagesService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
