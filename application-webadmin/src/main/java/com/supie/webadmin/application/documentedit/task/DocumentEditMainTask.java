package com.supie.webadmin.application.documentedit.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.webadmin.application.documentedit.model.DocumentEditMain;
import com.supie.webadmin.application.documentedit.service.DocumentEditMainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Configuration
@EnableScheduling
public class DocumentEditMainTask {

    @Resource
    private DocumentEditMainService documentEditMainService;

    /**
     * 每日凌晨2点执行一次
     * 逻辑删除掉 TextNumber 为 0 或为 NULL 超过1天的数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void documentEditMainTask() {
        log.info("============> 开始检查<文档编写主>无效数据 <============");
        Date nowDate = new Date();
        DateTime beforeDateOfDay = DateUtil.offsetDay(nowDate, -1);
        List<DocumentEditMain> documentEditMainList = documentEditMainService.getBaseMapper().selectList(
            new LambdaQueryWrapper<DocumentEditMain>().and(lqw -> lqw.eq(DocumentEditMain::getTextNumber, 0).or().isNull(DocumentEditMain::getTextNumber))
                    .lt(DocumentEditMain::getCreateTime, beforeDateOfDay)
        );
        log.info("==> 共 {} 条符合条件的数据，这些数据将被逻辑删除！", documentEditMainList.size());
        if (documentEditMainList.isEmpty()) {
            log.info("==> 无符合条件的数据，本次任务结束！");
            return;
        }
        Set<Long> documentEditMainIdSet = new HashSet<>();
        for (DocumentEditMain documentEditMain : documentEditMainList) {
            documentEditMainIdSet.add(documentEditMain.getId());
        }
        documentEditMainService.getBaseMapper().update(
                new LambdaUpdateWrapper<DocumentEditMain>().in(DocumentEditMain::getId, documentEditMainIdSet)
                        .set(DocumentEditMain::getIsDelete, GlobalDeletedFlag.DELETED).set(DocumentEditMain::getUpdateTime, nowDate)
        );
    }

}
