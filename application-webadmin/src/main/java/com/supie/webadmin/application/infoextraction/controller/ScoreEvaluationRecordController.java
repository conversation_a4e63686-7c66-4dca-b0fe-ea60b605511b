package com.supie.webadmin.application.infoextraction.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;

import com.supie.common.core.object.*;
import com.supie.common.core.util.*;
import com.supie.common.core.constant.*;
import com.supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.webadmin.application.infoextraction.dto.ScoreEvaluationRecordDto;
import com.supie.webadmin.application.infoextraction.model.ScoreEvaluationRecord;
import com.supie.webadmin.application.infoextraction.service.ScoreEvaluationRecordService;
import com.supie.webadmin.application.infoextraction.vo.ScoreEvaluationRecordVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 分数评价信息记录表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "分数评价信息记录表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/scoreEvaluationRecord")
public class ScoreEvaluationRecordController {

    @Autowired
    private ScoreEvaluationRecordService scoreEvaluationRecordService;

    /**
     * 新增分数评价信息记录表数据。
     *
     * @param scoreEvaluationRecordDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "scoreEvaluationRecordDto.id",
            "scoreEvaluationRecordDto.searchString",
            "scoreEvaluationRecordDto.updateTimeStart",
            "scoreEvaluationRecordDto.updateTimeEnd",
            "scoreEvaluationRecordDto.createTimeStart",
            "scoreEvaluationRecordDto.createTimeEnd"})
    //@SaCheckPermission("scoreEvaluationRecord.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody ScoreEvaluationRecordDto scoreEvaluationRecordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(scoreEvaluationRecordDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ScoreEvaluationRecord scoreEvaluationRecord = MyModelUtil.copyTo(scoreEvaluationRecordDto, ScoreEvaluationRecord.class);
        scoreEvaluationRecord = scoreEvaluationRecordService.saveNew(scoreEvaluationRecord);
        return ResponseResult.success(scoreEvaluationRecord.getId());
    }

    /**
     * 更新分数评价信息记录表数据。
     *
     * @param scoreEvaluationRecordDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "scoreEvaluationRecordDto.searchString",
            "scoreEvaluationRecordDto.updateTimeStart",
            "scoreEvaluationRecordDto.updateTimeEnd",
            "scoreEvaluationRecordDto.createTimeStart",
            "scoreEvaluationRecordDto.createTimeEnd"})
    //@SaCheckPermission("scoreEvaluationRecord.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody ScoreEvaluationRecordDto scoreEvaluationRecordDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(scoreEvaluationRecordDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        ScoreEvaluationRecord scoreEvaluationRecord = MyModelUtil.copyTo(scoreEvaluationRecordDto, ScoreEvaluationRecord.class);
        ScoreEvaluationRecord originalScoreEvaluationRecord = scoreEvaluationRecordService.getById(scoreEvaluationRecord.getId());
        if (originalScoreEvaluationRecord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!scoreEvaluationRecordService.update(scoreEvaluationRecord, originalScoreEvaluationRecord)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除分数评价信息记录表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //@SaCheckPermission("scoreEvaluationRecord.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除分数评价信息记录表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //@SaCheckPermission("scoreEvaluationRecord.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的分数评价信息记录表列表。
     *
     * @param scoreEvaluationRecordDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //@SaCheckPermission("scoreEvaluationRecord.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ScoreEvaluationRecordVo>> list(
            @MyRequestBody ScoreEvaluationRecordDto scoreEvaluationRecordDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ScoreEvaluationRecord scoreEvaluationRecordFilter = MyModelUtil.copyTo(scoreEvaluationRecordDtoFilter, ScoreEvaluationRecord.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ScoreEvaluationRecord.class);
        List<ScoreEvaluationRecord> scoreEvaluationRecordList =
                scoreEvaluationRecordService.getScoreEvaluationRecordListWithRelation(scoreEvaluationRecordFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(scoreEvaluationRecordList, ScoreEvaluationRecordVo.class));
    }

    /**
     * 分组列出符合过滤条件的分数评价信息记录表列表。
     *
     * @param scoreEvaluationRecordDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //@SaCheckPermission("scoreEvaluationRecord.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<ScoreEvaluationRecordVo>> listWithGroup(
            @MyRequestBody ScoreEvaluationRecordDto scoreEvaluationRecordDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, ScoreEvaluationRecord.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, ScoreEvaluationRecord.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        ScoreEvaluationRecord filter = MyModelUtil.copyTo(scoreEvaluationRecordDtoFilter, ScoreEvaluationRecord.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<ScoreEvaluationRecord> resultList = scoreEvaluationRecordService.getGroupedScoreEvaluationRecordListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, ScoreEvaluationRecordVo.class));
    }

    /**
     * 查看指定分数评价信息记录表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //@SaCheckPermission("scoreEvaluationRecord.view")
    @GetMapping("/view")
    public ResponseResult<ScoreEvaluationRecordVo> view(@RequestParam Long id) {
        ScoreEvaluationRecord scoreEvaluationRecord = scoreEvaluationRecordService.getByIdWithRelation(id, MyRelationParam.full());
        if (scoreEvaluationRecord == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        ScoreEvaluationRecordVo scoreEvaluationRecordVo = MyModelUtil.copyTo(scoreEvaluationRecord, ScoreEvaluationRecordVo.class);
        return ResponseResult.success(scoreEvaluationRecordVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        ScoreEvaluationRecord originalScoreEvaluationRecord = scoreEvaluationRecordService.getById(id);
        if (originalScoreEvaluationRecord == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!scoreEvaluationRecordService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
