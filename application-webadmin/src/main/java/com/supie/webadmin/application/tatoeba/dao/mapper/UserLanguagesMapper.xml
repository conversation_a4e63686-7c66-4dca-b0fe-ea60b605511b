<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.application.tatoeba.dao.UserLanguagesMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.application.tatoeba.model.UserLanguages">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="iso_language_code" jdbcType="VARCHAR" property="isoLanguageCode"/>
        <result column="skill_level" jdbcType="VARCHAR" property="skillLevel"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="details" jdbcType="LONGVARCHAR" property="details"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO ms_tatoeba_user_languages
            (id,
            str_id,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            iso_language_code,
            skill_level,
            username,
            details)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.isoLanguageCode},
            #{item.skillLevel},
            #{item.username},
            #{item.details})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.application.tatoeba.dao.UserLanguagesMapper.inputFilterRef"/>
        AND ms_tatoeba_user_languages.is_delete = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="userLanguagesFilter != null">
            <if test="userLanguagesFilter.id != null">
                AND ms_tatoeba_user_languages.id = #{userLanguagesFilter.id}
            </if>
            <if test="userLanguagesFilter.strId != null and userLanguagesFilter.strId != ''">
                AND ms_tatoeba_user_languages.str_id = #{userLanguagesFilter.strId}
            </if>
            <if test="userLanguagesFilter.createTimeStart != null and userLanguagesFilter.createTimeStart != ''">
                AND ms_tatoeba_user_languages.create_time &gt;= #{userLanguagesFilter.createTimeStart}
            </if>
            <if test="userLanguagesFilter.createTimeEnd != null and userLanguagesFilter.createTimeEnd != ''">
                AND ms_tatoeba_user_languages.create_time &lt;= #{userLanguagesFilter.createTimeEnd}
            </if>
            <if test="userLanguagesFilter.createUserId != null">
                AND ms_tatoeba_user_languages.create_user_id = #{userLanguagesFilter.createUserId}
            </if>
            <if test="userLanguagesFilter.updateTimeStart != null and userLanguagesFilter.updateTimeStart != ''">
                AND ms_tatoeba_user_languages.update_time &gt;= #{userLanguagesFilter.updateTimeStart}
            </if>
            <if test="userLanguagesFilter.updateTimeEnd != null and userLanguagesFilter.updateTimeEnd != ''">
                AND ms_tatoeba_user_languages.update_time &lt;= #{userLanguagesFilter.updateTimeEnd}
            </if>
            <if test="userLanguagesFilter.updateUserId != null">
                AND ms_tatoeba_user_languages.update_user_id = #{userLanguagesFilter.updateUserId}
            </if>
            <if test="userLanguagesFilter.dataUserId != null">
                AND ms_tatoeba_user_languages.data_user_id = #{userLanguagesFilter.dataUserId}
            </if>
            <if test="userLanguagesFilter.dataDeptId != null">
                AND ms_tatoeba_user_languages.data_dept_id = #{userLanguagesFilter.dataDeptId}
            </if>
            <if test="userLanguagesFilter.isoLanguageCode != null and userLanguagesFilter.isoLanguageCode != ''">
                AND ms_tatoeba_user_languages.iso_language_code = #{userLanguagesFilter.isoLanguageCode}
            </if>
            <if test="userLanguagesFilter.skillLevel != null and userLanguagesFilter.skillLevel != ''">
                AND ms_tatoeba_user_languages.skill_level = #{userLanguagesFilter.skillLevel}
            </if>
            <if test="userLanguagesFilter.username != null and userLanguagesFilter.username != ''">
                AND ms_tatoeba_user_languages.username = #{userLanguagesFilter.username}
            </if>
            <if test="userLanguagesFilter.details != null and userLanguagesFilter.details != ''">
                <bind name = "safeUserLanguagesDetails" value = "'%' + userLanguagesFilter.details + '%'" />
                AND ms_tatoeba_user_languages.details LIKE #{safeUserLanguagesDetails}
            </if>
            <if test="userLanguagesFilter.searchString != null and userLanguagesFilter.searchString != ''">
                <bind name = "safeUserLanguagesSearchString" value = "'%' + userLanguagesFilter.searchString + '%'" />
                AND CONCAT(IFNULL(ms_tatoeba_user_languages.iso_language_code,''), IFNULL(ms_tatoeba_user_languages.skill_level,''), IFNULL(ms_tatoeba_user_languages.username,''), IFNULL(ms_tatoeba_user_languages.details,'')) LIKE #{safeUserLanguagesSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedUserLanguagesList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.tatoeba.model.UserLanguages">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM ms_tatoeba_user_languages
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) ms_tatoeba_user_languages
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getUserLanguagesList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.application.tatoeba.model.UserLanguages">
        SELECT * FROM ms_tatoeba_user_languages
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
