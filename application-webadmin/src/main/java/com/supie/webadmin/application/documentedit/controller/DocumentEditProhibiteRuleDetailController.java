package com.supie.webadmin.application.documentedit.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.supie.common.core.annotation.MyRequestBody;
import com.supie.common.core.constant.ErrorCodeEnum;
import com.supie.common.core.object.*;
import com.supie.common.core.util.MyCommonUtil;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.MyPageUtil;
import com.supie.common.log.annotation.OperationLog;
import com.supie.common.log.model.constant.SysOperationLogType;
import com.supie.webadmin.application.documentedit.dto.DocumentEditProhibiteRuleDetailDto;
import com.supie.webadmin.application.documentedit.model.DocumentEditProhibiteRuleDetail;
import com.supie.webadmin.application.documentedit.service.DocumentEditProhibiteRuleDetailService;
import com.supie.webadmin.application.documentedit.vo.DocumentEditProhibiteRuleDetailVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文档编辑禁止规则详细信息表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-07-28
 */
@Tag(name = "文档编辑禁止规则详细信息表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/documentEditProhibiteRuleDetail")
public class DocumentEditProhibiteRuleDetailController {

    @Autowired
    private DocumentEditProhibiteRuleDetailService documentEditProhibiteRuleDetailService;

    /**
     * 新增文档编辑禁止规则详细信息表数据。
     *
     * @param documentEditProhibiteRuleDetailDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditProhibiteRuleDetailDto.id",
            "documentEditProhibiteRuleDetailDto.searchString",
            "documentEditProhibiteRuleDetailDto.createTimeStart",
            "documentEditProhibiteRuleDetailDto.createTimeEnd",
            "documentEditProhibiteRuleDetailDto.createUserIdStart",
            "documentEditProhibiteRuleDetailDto.createUserIdEnd"})
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody DocumentEditProhibiteRuleDetailDto documentEditProhibiteRuleDetailDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditProhibiteRuleDetailDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditProhibiteRuleDetail documentEditProhibiteRuleDetail = MyModelUtil.copyTo(documentEditProhibiteRuleDetailDto, DocumentEditProhibiteRuleDetail.class);
        // 验证父Id的数据合法性
        DocumentEditProhibiteRuleDetail parentDocumentEditProhibiteRuleDetail;
        if (MyCommonUtil.isNotBlankOrNull(documentEditProhibiteRuleDetail.getCreateUserId())) {
            parentDocumentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.getById(documentEditProhibiteRuleDetail.getCreateUserId());
            if (parentDocumentEditProhibiteRuleDetail == null) {
                errorMessage = "数据验证失败，关联的父节点并不存在，请刷新后重试！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        documentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.saveNew(documentEditProhibiteRuleDetail);
        return ResponseResult.success(documentEditProhibiteRuleDetail.getId());
    }

    /**
     * 更新文档编辑禁止规则详细信息表数据。
     *
     * @param documentEditProhibiteRuleDetailDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "documentEditProhibiteRuleDetailDto.searchString",
            "documentEditProhibiteRuleDetailDto.createTimeStart",
            "documentEditProhibiteRuleDetailDto.createTimeEnd",
            "documentEditProhibiteRuleDetailDto.createUserIdStart",
            "documentEditProhibiteRuleDetailDto.createUserIdEnd"})
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody DocumentEditProhibiteRuleDetailDto documentEditProhibiteRuleDetailDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(documentEditProhibiteRuleDetailDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        DocumentEditProhibiteRuleDetail documentEditProhibiteRuleDetail = MyModelUtil.copyTo(documentEditProhibiteRuleDetailDto, DocumentEditProhibiteRuleDetail.class);
        DocumentEditProhibiteRuleDetail originalDocumentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.getById(documentEditProhibiteRuleDetail.getId());
        if (originalDocumentEditProhibiteRuleDetail == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // 验证父Id的数据合法性
        if (MyCommonUtil.isNotBlankOrNull(documentEditProhibiteRuleDetail.getCreateUserId())
                && ObjectUtil.notEqual(documentEditProhibiteRuleDetail.getCreateUserId(), originalDocumentEditProhibiteRuleDetail.getCreateUserId())) {
            DocumentEditProhibiteRuleDetail parentDocumentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.getById(documentEditProhibiteRuleDetail.getCreateUserId());
            if (parentDocumentEditProhibiteRuleDetail == null) {
                // NOTE: 修改下面方括号中的话述
                errorMessage = "数据验证失败，关联的 [父节点] 并不存在，请刷新后重试！";
                return ResponseResult.error(ErrorCodeEnum.DATA_PARENT_ID_NOT_EXIST, errorMessage);
            }
        }
        if (!documentEditProhibiteRuleDetailService.update(documentEditProhibiteRuleDetail, originalDocumentEditProhibiteRuleDetail)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除文档编辑禁止规则详细信息表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除文档编辑禁止规则详细信息表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的文档编辑禁止规则详细信息表列表。
     *
     * @param documentEditProhibiteRuleDetailDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<DocumentEditProhibiteRuleDetailVo>> list(
            @MyRequestBody DocumentEditProhibiteRuleDetailDto documentEditProhibiteRuleDetailDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        DocumentEditProhibiteRuleDetail documentEditProhibiteRuleDetailFilter = MyModelUtil.copyTo(documentEditProhibiteRuleDetailDtoFilter, DocumentEditProhibiteRuleDetail.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, DocumentEditProhibiteRuleDetail.class);
        List<DocumentEditProhibiteRuleDetail> documentEditProhibiteRuleDetailList =
                documentEditProhibiteRuleDetailService.getDocumentEditProhibiteRuleDetailListWithRelation(documentEditProhibiteRuleDetailFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(documentEditProhibiteRuleDetailList, DocumentEditProhibiteRuleDetailVo.class));
    }

    /**
     * 查看指定文档编辑禁止规则详细信息表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    //// @SaCheckPermission("documentEditProhibiteRuleDetail.view")
    @GetMapping("/view")
    public ResponseResult<DocumentEditProhibiteRuleDetailVo> view(@RequestParam Long id) {
        DocumentEditProhibiteRuleDetail documentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.getByIdWithRelation(id, MyRelationParam.full());
        if (documentEditProhibiteRuleDetail == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        DocumentEditProhibiteRuleDetailVo documentEditProhibiteRuleDetailVo = MyModelUtil.copyTo(documentEditProhibiteRuleDetail, DocumentEditProhibiteRuleDetailVo.class);
        return ResponseResult.success(documentEditProhibiteRuleDetailVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        DocumentEditProhibiteRuleDetail originalDocumentEditProhibiteRuleDetail = documentEditProhibiteRuleDetailService.getById(id);
        if (originalDocumentEditProhibiteRuleDetail == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (documentEditProhibiteRuleDetailService.hasChildren(id)) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象存在子对象] ，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.HAS_CHILDREN_DATA, errorMessage);
        }
        if (!documentEditProhibiteRuleDetailService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
