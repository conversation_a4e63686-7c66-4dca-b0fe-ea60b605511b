package com.supie.webadmin.application.textclassifier.dto;

import com.supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 信息分类与抽取任务管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Schema(description = "ServiceInfoExtractionTaskDto对象")
@Data
public class ServiceInfoExtractionTaskDto {

    /**
     * modelInfoId
     */
    private Long modelInfoId;

    /**
     * 主键ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "主键ID。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符串ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符串ID。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人ID。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门ID。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 任务名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务名称。可支持等于操作符的列表数据过滤。")
    private String extractionTaskName;

    /**
     * 任务类型（分类、抽取、分类+抽取）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务类型（分类、抽取、分类+抽取）。可支持等于操作符的列表数据过滤。")
    private String extractionTaskType;

    /**
     * 任务配置（json）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务配置（json）。可支持等于操作符的列表数据过滤。")
    private String extractionTaskConfig;

    /**
     * 分类类型（ebay、amazon）。
     */
    @Schema(description = "分类类型（ebay、amazon）")
    private String classifyType;

    /**
     * 任务状态（未开始、进行中、已完成、异常）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务状态（未开始、进行中、已完成、异常）。可支持等于操作符的列表数据过滤。")
    private String extractionTaskStatus;

    /**
     * 原文件id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "原文件id。可支持等于操作符的列表数据过滤。")
    private Long sourceFileId;

    /**
     * 处理后文件id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "处理后文件id。可支持等于操作符的列表数据过滤。")
    private Long processedFileId;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * extraction_task_name / extraction_task_config LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
