package com.supie.webadmin.upms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.supie.common.core.object.TokenData;
import com.supie.common.mobile.dao.MobileEntryDataPermMapper;
import com.supie.common.mobile.model.MobileEntryDataPerm;
import com.supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.supie.common.core.constant.DataPermRuleType;
import com.supie.common.core.base.service.BaseService;
import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.core.constant.GlobalDeletedFlag;
import com.supie.common.core.object.MyRelationParam;
import com.supie.common.core.object.CallResult;
import com.supie.common.core.util.MyModelUtil;
import com.supie.common.core.util.RedisKeyUtil;
import com.supie.common.core.constant.ApplicationConstant;
import com.supie.webadmin.config.ApplicationConfig;
import com.supie.webadmin.upms.dao.SysDataPermDeptMapper;
import com.supie.webadmin.upms.dao.SysDataPermMapper;
import com.supie.webadmin.upms.dao.SysDataPermUserMapper;
import com.supie.webadmin.upms.dao.SysDataPermMenuMapper;
import com.supie.webadmin.upms.model.*;
import com.supie.webadmin.upms.model.constant.SysUserType;
import com.supie.webadmin.upms.service.SysDataPermService;
import com.supie.webadmin.upms.service.SysDeptService;
import com.supie.webadmin.upms.service.SysMenuService;
import com.supie.webadmin.upms.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数据权限数据服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
@Service("sysDataPermService")
public class SysDataPermServiceImpl extends BaseService<SysDataPerm, Long> implements SysDataPermService {

    @Autowired
    private SysDataPermMapper sysDataPermMapper;
    @Autowired
    private SysDataPermDeptMapper sysDataPermDeptMapper;
    @Autowired
    private SysDataPermUserMapper sysDataPermUserMapper;
    @Autowired
    private SysDataPermMenuMapper sysDataPermMenuMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ApplicationConfig applicationConfig;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private MobileEntryDataPermMapper mobileEntryDataPermMapper;

    /**
     * 返回主对象的Mapper对象。
     *
     * @return 主对象的Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SysDataPerm> mapper() {
        return sysDataPermMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysDataPerm saveNew(SysDataPerm dataPerm, Set<Long> deptIdSet, Set<Long> menuIdSet, Set<Long> entryIdSet) {
        dataPerm.setDataPermId(idGenerator.nextLongId());
        MyModelUtil.fillCommonsForInsert(dataPerm);
        dataPerm.setDeletedFlag(GlobalDeletedFlag.NORMAL);
        sysDataPermMapper.insert(dataPerm);
        this.insertRelationData(dataPerm, deptIdSet, menuIdSet, entryIdSet);
        return dataPerm;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(
            SysDataPerm dataPerm, SysDataPerm originalDataPerm, Set<Long> deptIdSet, Set<Long> menuIdSet, Set<Long> entryIdSet) {
        MyModelUtil.fillCommonsForUpdate(dataPerm, originalDataPerm);
        UpdateWrapper<SysDataPerm> uw = this.createUpdateQueryForNullValue(dataPerm, dataPerm.getDataPermId());
        if (sysDataPermMapper.update(dataPerm, uw) != 1) {
            return false;
        }
        SysDataPermDept dataPermDept = new SysDataPermDept();
        dataPermDept.setDataPermId(dataPerm.getDataPermId());
        sysDataPermDeptMapper.delete(new QueryWrapper<>(dataPermDept));
        SysDataPermMenu dataPermMenu = new SysDataPermMenu();
        dataPermMenu.setDataPermId(dataPerm.getDataPermId());
        sysDataPermMenuMapper.delete(new QueryWrapper<>(dataPermMenu));
        MobileEntryDataPerm mobileEntryDataPerm = new MobileEntryDataPerm();
        mobileEntryDataPerm.setDataPermId(dataPerm.getDataPermId());
        mobileEntryDataPermMapper.delete(new QueryWrapper<>(mobileEntryDataPerm));
        this.insertRelationData(dataPerm, deptIdSet, menuIdSet, entryIdSet);
        return true;
    }

    /**
     * 删除指定数据权限。
     *
     * @param dataPermId 数据权限主键Id。
     * @return 删除成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long dataPermId) {
        if (sysDataPermMapper.deleteById(dataPermId) != 1) {
            return false;
        }
        SysDataPermDept dataPermDept = new SysDataPermDept();
        dataPermDept.setDataPermId(dataPermId);
        sysDataPermDeptMapper.delete(new QueryWrapper<>(dataPermDept));
        SysDataPermUser dataPermUser = new SysDataPermUser();
        dataPermUser.setDataPermId(dataPermId);
        sysDataPermUserMapper.delete(new QueryWrapper<>(dataPermUser));
        SysDataPermMenu dataPermMenu = new SysDataPermMenu();
        dataPermMenu.setDataPermId(dataPermId);
        sysDataPermMenuMapper.delete(new QueryWrapper<>(dataPermMenu));
        MobileEntryDataPerm mobileEntryDataPerm = new MobileEntryDataPerm();
        mobileEntryDataPerm.setDataPermId(dataPermId);
        mobileEntryDataPermMapper.delete(new QueryWrapper<>(mobileEntryDataPerm));
        return true;
    }

    @Override
    public List<SysDataPerm> getSysDataPermListWithRelation(SysDataPerm filter, String orderBy) {
        List<SysDataPerm> resultList = sysDataPermMapper.getSysDataPermList(filter, orderBy);
        buildRelationForDataList(resultList, MyRelationParam.full(), CollUtil.newHashSet("dataPermDeptList"));
        TokenData tokenData = TokenData.takeFromRequest();
        //下面添加需要排除的数据权限--（有的数据权限只有管理员能看到）
        List<String> idList =new ArrayList<>();
        idList.add("查看全部"); //基础数据部门
        if (!BooleanUtil.isTrue(tokenData.getIsAdmin())) {
            resultList = resultList.stream().filter(e -> !idList.contains(e.getDataPermName())).collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 缓存用户的权限资源
     * @param sessionId 会话Id。
     * @param userId    用户主键Id。
     * @param deptId    用户所属部门主键Id。
     */
    @Override
    public void putDataPermCache(String sessionId, Long userId, Long deptId) {
        Map<String, Map<Integer, String>> menuDataPermMap = getSysDataPermListByUserId(userId, deptId);
        if (menuDataPermMap.size() > 0) {
            String dataPermSessionKey = RedisKeyUtil.makeSessionDataPermIdKey(sessionId);
            RBucket<String> bucket = redissonClient.getBucket(dataPermSessionKey);
            bucket.set(JSON.toJSONString(menuDataPermMap),
                    applicationConfig.getSessionExpiredSeconds(), TimeUnit.SECONDS);
        }
    }

    /**
     * 将指定会话的数据权限集合从缓存中移除。
     *
     * @param sessionId 会话Id。
     */
    @Override
    public void removeDataPermCache(String sessionId) {
        String sessionPermKey = RedisKeyUtil.makeSessionDataPermIdKey(sessionId);
        redissonClient.getBucket(sessionPermKey).deleteAsync();
    }

    /**
     * 获取指定用户Id的数据权限列表。并基于menuId和权限规则类型进行了一级分组。
     *
     * @param userId 指定的用户Id。
     * @param deptId 用户所属部门主键Id。
     * @return 合并优化后的数据权限列表。返回格式为，Map<MenuId, Map<RuleType, DeptIdString>>。
     */
    @Override
    public Map<String, Map<Integer, String>> getSysDataPermListByUserId(Long userId, Long deptId) {
        List<SysDataPerm> dataPermList = sysDataPermMapper.getSysDataPermListByUserId(userId);
        dataPermList.forEach(dataPerm -> {
            if (CollUtil.isNotEmpty(dataPerm.getDataPermDeptList())) {
                Set<Long> deptIdSet = dataPerm.getDataPermDeptList().stream()
                        .map(SysDataPermDept::getDeptId).collect(Collectors.toSet());
                dataPerm.setDeptIdListString(StrUtil.join(",", deptIdSet));
            }
        });
        Map<String, List<SysDataPerm>> menuIdMap = new HashMap<>(4);
        for (SysDataPerm dataPerm : dataPermList) {
            if (CollUtil.isNotEmpty(dataPerm.getDataPermMenuList())
                    || CollUtil.isNotEmpty(dataPerm.getDataPermMobileEntryList())) {
                for (SysDataPermMenu dataPermMenu : dataPerm.getDataPermMenuList()) {
                    menuIdMap.computeIfAbsent(
                            dataPermMenu.getMenuId().toString(), k -> new LinkedList<>()).add(dataPerm);
                }
            } else {
                menuIdMap.computeIfAbsent(
                        ApplicationConstant.DATA_PERM_ALL_MENU_ID, k -> new LinkedList<>()).add(dataPerm);
            }
        }
        Map<String, Map<Integer, String>> menuResultMap = new HashMap<>(menuIdMap.size());
        for (Map.Entry<String, List<SysDataPerm>> entry : menuIdMap.entrySet()) {
            Map<Integer, String> resultMap = this.mergeAndOptimizeDataPermRule(entry.getValue(), deptId);
            menuResultMap.put(entry.getKey(), resultMap);
        }
        return menuResultMap;
    }

    @Override
    public List<SysDataPerm> getSysDataPermListByMenuId(Long menuId) {
        return sysDataPermMapper.getSysDataPermListByMenuId(menuId);
    }

    private Map<Integer, String> mergeAndOptimizeDataPermRule(List<SysDataPerm> dataPermList, Long deptId) {
        // 为了更方便进行后续的合并优化处理，这里再基于菜单Id和规则类型进行分组。ruleMap的key是规则类型。
        Map<Integer, List<SysDataPerm>> ruleMap =
                dataPermList.stream().collect(Collectors.groupingBy(SysDataPerm::getRuleType));
        Map<Integer, String> resultMap = new HashMap<>(ruleMap.size());
        // 如有有ALL存在，就可以直接退出了，没有必要在处理后续的规则了。
        if (ruleMap.containsKey(DataPermRuleType.TYPE_ALL)) {
            resultMap.put(DataPermRuleType.TYPE_ALL, "null");
            return resultMap;
        }
        // 这里优先合并最复杂的多部门及子部门场景。
        String deptIds = processMultiDeptAndChildren(ruleMap, deptId);
        if (deptIds != null) {
            resultMap.put(DataPermRuleType.TYPE_MULTI_DEPT_AND_CHILD_DEPT, deptIds);
        }
        // 合并当前部门及子部门的优化
        if (ruleMap.get(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT) != null) {
            // 需要与仅仅当前部门规则进行合并。
            ruleMap.remove(DataPermRuleType.TYPE_DEPT_ONLY);
            resultMap.put(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT, "null");
        }
        // 合并自定义部门了。
        deptIds = processMultiDept(ruleMap, deptId);
        if (deptIds != null) {
            resultMap.put(DataPermRuleType.TYPE_CUSTOM_DEPT_LIST, deptIds);
        }
        // 最后处理当前部门和当前用户。
        if (ruleMap.get(DataPermRuleType.TYPE_DEPT_ONLY) != null) {
            resultMap.put(DataPermRuleType.TYPE_DEPT_ONLY, "null");
        }
        if (ruleMap.get(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT_USERS) != null) {
            // 合并当前部门用户和当前用户
            ruleMap.remove(DataPermRuleType.TYPE_USER_ONLY);
            ruleMap.remove(DataPermRuleType.TYPE_DEPT_USERS);
            SysUser filter = new SysUser();
            filter.setDeptId(deptId);
            List<SysUser> userList = sysUserService.getSysUserList(filter, null);
            Set<Long> userIdSet = userList.stream().map(SysUser::getUserId).collect(Collectors.toSet());
            resultMap.put(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT_USERS, CollUtil.join(userIdSet, ","));
        }
        if (ruleMap.get(DataPermRuleType.TYPE_DEPT_USERS) != null) {
            SysUser filter = new SysUser();
            filter.setDeptId(deptId);
            List<SysUser> userList = sysUserService.getListByFilter(filter);
            Set<Long> userIdSet = userList.stream().map(SysUser::getUserId).collect(Collectors.toSet());
            // 合并仅当前用户
            ruleMap.remove(DataPermRuleType.TYPE_USER_ONLY);
            resultMap.put(DataPermRuleType.TYPE_DEPT_USERS, CollUtil.join(userIdSet, ","));
        }
        if (ruleMap.get(DataPermRuleType.TYPE_USER_ONLY) != null) {
            resultMap.put(DataPermRuleType.TYPE_USER_ONLY, "null");
        }
        return resultMap;
    }

    private String processMultiDeptAndChildren(Map<Integer, List<SysDataPerm>> ruleMap, Long deptId) {
        List<SysDataPerm> parentDeptList = ruleMap.get(DataPermRuleType.TYPE_MULTI_DEPT_AND_CHILD_DEPT);
        if (parentDeptList == null) {
            return null;
        }
        Set<Long> deptIdSet = new HashSet<>();
        for (SysDataPerm parentDept : parentDeptList) {
            deptIdSet.addAll(StrUtil.split(parentDept.getDeptIdListString(), ',')
                    .stream().map(Long::valueOf).collect(Collectors.toSet()));
        }
        // 在合并所有的多父部门Id之后，需要判断是否有本部门及子部门的规则。如果有，就继续合并。
        if (ruleMap.containsKey(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT)) {
            // 如果多父部门列表中包含当前部门，那么可以直接删除该规则了，如果没包含，就加入到多部门的DEPT_ID的IN LIST中。
            deptIdSet.add(deptId);
            ruleMap.remove(DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT);
        }
        // 需要与仅仅当前部门规则进行合并。
        if (ruleMap.containsKey(DataPermRuleType.TYPE_DEPT_ONLY) && deptIdSet.contains(deptId)) {
            ruleMap.remove(DataPermRuleType.TYPE_DEPT_ONLY);
        }
        return StrUtil.join(",", deptIdSet);
    }

    private String processMultiDept(Map<Integer, List<SysDataPerm>> ruleMap, Long deptId) {
        List<SysDataPerm> customDeptList = ruleMap.get(DataPermRuleType.TYPE_CUSTOM_DEPT_LIST);
        if (customDeptList == null) {
            return null;
        }
        Set<Long> deptIdSet = new HashSet<>();
        for (SysDataPerm customDept : customDeptList) {
            deptIdSet.addAll(StrUtil.split(customDept.getDeptIdListString(), ',')
                    .stream().map(Long::valueOf).collect(Collectors.toSet()));
        }
        if (ruleMap.containsKey(DataPermRuleType.TYPE_DEPT_ONLY)) {
            deptIdSet.add(deptId);
            ruleMap.remove(DataPermRuleType.TYPE_DEPT_ONLY);
        }
        return StrUtil.join(",", deptIdSet);
    }

    /**
     * 添加用户和数据权限之间的多对多关联关系。
     *
     * @param dataPermId 数据权限Id。
     * @param userIdSet  关联的用户Id列表。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addDataPermUserList(Long dataPermId, Set<Long> userIdSet) {
        for (Long userId : userIdSet) {
            SysDataPermUser dataPermUser = new SysDataPermUser();
            dataPermUser.setDataPermId(dataPermId);
            dataPermUser.setUserId(userId);
            sysDataPermUserMapper.insert(dataPermUser);
        }
    }

    /**
     * 移除用户和数据权限之间的多对多关联关系。
     *
     * @param dataPermId 数据权限主键Id。
     * @param userId     用户主键Id。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeDataPermUser(Long dataPermId, Long userId) {
        SysDataPermUser dataPermUser = new SysDataPermUser();
        dataPermUser.setDataPermId(dataPermId);
        dataPermUser.setUserId(userId);
        return sysDataPermUserMapper.delete(new QueryWrapper<>(dataPermUser)) == 1;
    }

    @Override
    public CallResult verifyRelatedData(SysDataPerm dataPerm, String deptIdListString, String menuIdListString) {
        JSONObject jsonObject = new JSONObject();
        if (dataPerm.getRuleType() == DataPermRuleType.TYPE_MULTI_DEPT_AND_CHILD_DEPT
                || dataPerm.getRuleType() == DataPermRuleType.TYPE_CUSTOM_DEPT_LIST) {
            if (StrUtil.isBlank(deptIdListString)) {
                return CallResult.error("数据验证失败，部门列表不能为空！");
            }
            Set<Long> deptIdSet = StrUtil.split(
                    deptIdListString, ",").stream().map(Long::valueOf).collect(Collectors.toSet());
            if (!sysDeptService.existAllPrimaryKeys(deptIdSet)) {
                return CallResult.error("数据验证失败，存在不合法的部门数据，请刷新后重试！");
            }
            jsonObject.put("deptIdSet", deptIdSet);
        }
        if (StrUtil.isNotBlank(menuIdListString)) {
            Set<Long> menuIdSet = StrUtil.split(
                    menuIdListString, ",").stream().map(Long::valueOf).collect(Collectors.toSet());
            if (!sysMenuService.existAllPrimaryKeys(menuIdSet)) {
                return CallResult.error("数据验证失败，存在不合法的菜单数据，请刷新后重试！");
            }
            jsonObject.put("menuIdSet", menuIdSet);
        }
        return CallResult.ok(jsonObject);
    }

    private void insertRelationData(SysDataPerm dataPerm, Set<Long> deptIdSet, Set<Long> menuIdSet, Set<Long> entryIdSet) {
        if (CollUtil.isNotEmpty(deptIdSet)) {
            for (Long deptId : deptIdSet) {
                SysDataPermDept dataPermDept = new SysDataPermDept();
                dataPermDept.setDataPermId(dataPerm.getDataPermId());
                dataPermDept.setDeptId(deptId);
                sysDataPermDeptMapper.insert(dataPermDept);
            }
        }
        if (CollUtil.isNotEmpty(menuIdSet)) {
            for (Long menuId : menuIdSet) {
                SysDataPermMenu dataPermMenu = new SysDataPermMenu();
                dataPermMenu.setDataPermId(dataPerm.getDataPermId());
                dataPermMenu.setMenuId(menuId);
                sysDataPermMenuMapper.insert(dataPermMenu);
            }
        }
        if (CollUtil.isNotEmpty(entryIdSet)) {
            for (Long entryId : entryIdSet) {
                MobileEntryDataPerm mobileEntryDataPerm = new MobileEntryDataPerm();
                mobileEntryDataPerm.setDataPermId(dataPerm.getDataPermId());
                mobileEntryDataPerm.setEntryId(entryId);
                mobileEntryDataPermMapper.insert(mobileEntryDataPerm);
            }
        }
    }

    private Map<String, List<SysDataPerm>>  getSysDataPermListWithMobileByUserId(Long userId) {
        List<SysDataPerm> dataPermList = sysDataPermMapper.getSysDataPermListWithMobileByUserId(userId);
        dataPermList.forEach(dataPerm -> {
            if (CollUtil.isNotEmpty(dataPerm.getDataPermDeptList())) {
                Set<Long> deptIdSet = dataPerm.getDataPermDeptList().stream()
                        .map(SysDataPermDept::getDeptId).collect(Collectors.toSet());
                dataPerm.setDeptIdListString(StrUtil.join(",", deptIdSet));
            }
        });
        Map<String, List<SysDataPerm>> mobileEntryIdMap = new HashMap<>(4);
        for (SysDataPerm dataPerm : dataPermList) {
            if (CollUtil.isNotEmpty(dataPerm.getDataPermMobileEntryList())) {
                for (MobileEntryDataPerm mobileEntryDataPerm : dataPerm.getDataPermMobileEntryList()) {
                    mobileEntryIdMap.computeIfAbsent(
                            mobileEntryDataPerm.getEntryId().toString(), k -> new LinkedList<>()).add(dataPerm);
                }
            } else {
                mobileEntryIdMap.computeIfAbsent(
                        ApplicationConstant.DATA_PERM_ALL_MENU_ID, k -> new LinkedList<>()).add(dataPerm);
            }
        }
        return mobileEntryIdMap;
    }

    /**
     * 获取指定用户的数据权限部门列表。
     * @param user 用户信息。
     */
    @Override
    public Map<String, Set<Long>> getDataPermByUserId(SysUser user) {
        if (user.getUserType() != null && user.getUserType() == SysUserType.TYPE_ADMIN) {
            Map<String, Set<Long>> dataPermData = new HashMap<>(2);
            dataPermData.put("dataDeptIdList", new HashSet<>());
            dataPermData.put("dataUserIdList", new HashSet<>());
            return dataPermData;
        }
        Long userId = user.getUserId();
        Long deptId = user.getDeptId();
        // 获取当前用户的数据权。
        List<SysDataPerm> sysDataPermListByUserId = sysDataPermMapper.getSysDataPermListByUserId(userId);
        Map<Integer, SysDataPerm> ruleTypeMap = new HashMap<>();
        for (SysDataPerm sysDataPerm : sysDataPermListByUserId) {
            ruleTypeMap.put(sysDataPerm.getRuleType(), sysDataPerm);
        }
        Set<Integer> ruleTypeSet = sysDataPermListByUserId.stream()
                .map(SysDataPerm::getRuleType).collect(Collectors.toSet());
        // 获取当前用户的数据权限部门列表。
        Set<Long> dataDeptIdList = new HashSet<>();
        if (!ruleTypeSet.contains(DataPermRuleType.TYPE_ALL)) {
            for (Integer ruleType : ruleTypeSet) {
                if (ruleType == DataPermRuleType.TYPE_DEPT_ONLY) {
                    // 仅查看所在部门
                    dataDeptIdList.add(deptId);
                } else if (ruleType == DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT) {
                    // 所在部门及子部门
                    List<Long> allChildDeptIdByParentIds = sysDeptService.getAllChildDeptIdByParentIds(Collections.singletonList(deptId));
                    dataDeptIdList.addAll(allChildDeptIdByParentIds);
                } else if (ruleType == DataPermRuleType.TYPE_MULTI_DEPT_AND_CHILD_DEPT) {
                    // TODO 多部门及子部门
                } else if (ruleType == DataPermRuleType.TYPE_CUSTOM_DEPT_LIST) {
                    // TODO 自定义部门列表
                    SysDataPerm sysDataPerm = ruleTypeMap.get(ruleType);
                    List<SysDataPermDept> dataPermDeptList = sysDataPerm.getDataPermDeptList();
                    for (SysDataPermDept sysDataPermDept : dataPermDeptList) {
                        dataDeptIdList.add(sysDataPermDept.getDeptId());
                    }
                }
            }
        }
        Set<Long> dataUserIdList = new HashSet<>();
        if (!ruleTypeSet.contains(DataPermRuleType.TYPE_ALL)) {
            for (Integer ruleType : ruleTypeSet) {
                if (ruleType == DataPermRuleType.TYPE_USER_ONLY) {
                    // 仅查看当前用户
                    dataUserIdList.add(userId);
                } else if (ruleType == DataPermRuleType.TYPE_DEPT_USERS) {
                    // 本部门所有用户
                    Set<Long> userIdListByDeptId = sysUserService.getUserIdListByDeptIdList(Collections.singletonList(deptId));
                    dataUserIdList.addAll(userIdListByDeptId);
                } else if (ruleType == DataPermRuleType.TYPE_DEPT_AND_CHILD_DEPT_USERS) {
                    // 本部门及子部门所有用户
                    List<Long> allChildDeptIdByParentIds = sysDeptService.getAllChildDeptIdByParentIds(Collections.singletonList(deptId));
                    Set<Long> userIdListByDeptId = sysUserService.getUserIdListByDeptIdList(allChildDeptIdByParentIds);
                    dataUserIdList.addAll(userIdListByDeptId);
                }
            }
        }
        Map<String, Set<Long>> dataPermData = new HashMap<>(2);
        dataPermData.put("dataDeptIdList", dataDeptIdList);
        dataPermData.put("dataUserIdList", dataUserIdList);
        return dataPermData;
    }
}
