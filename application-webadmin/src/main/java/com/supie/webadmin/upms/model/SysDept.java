package com.supie.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.*;
import com.supie.common.core.annotation.DeptFilterColumn;
import com.supie.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 部门管理实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_dept")
public class SysDept extends BaseModel {

    /**
     * 部门Id。
     */
    @DeptFilterColumn
    @TableId(value = "dept_id")
    private Long deptId;

    /**
     * 部门名称。
     */
    private String deptName;

    /**
     * 显示顺序。
     */
    private Integer showOrder;

    /**
     * 父部门Id。
     */
    private Long parentId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer deletedFlag;
}
