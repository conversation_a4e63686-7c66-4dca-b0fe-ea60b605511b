package com.supie.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supie.webadmin.upms.model.SysUser;
import com.supie.webadmin.upms.model.UserInfoMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserInfoMapMapper extends BaseMapper<UserInfoMap> {
    /**
     * 通过外部用户id查询对照表判断用户是否已经存在
     *
     * @param userId 外部用户id
     */
    SysUser queryOutUser(@Param("id") Long userId);
}
