package com.supie.webadmin.upms.service;

import com.alibaba.fastjson.JSONObject;
import com.supie.common.core.base.service.IBaseService;
import com.supie.common.core.object.CallResult;
import com.supie.webadmin.train.model.ExcelData;
import com.supie.webadmin.upms.model.SysUser;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户管理数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
public interface SysUserService extends IBaseService<SysUser, Long> {

    /**
     * 获取指定登录名的用户对象。
     *
     * @param loginName 指定登录用户名。
     * @return 用户对象。
     */
    SysUser getSysUserByLoginName(String loginName);


    /**
     * 获取指定用户id的用户对象。
     *
     * @param userId 指定登录用户id。
     * @return 用户对象。
     */
    SysUser getSysUserByUserId(Long userId);

    /**
     * 保存新增的用户对象。
     *
     * @param user          新增的用户对象。
     * @param roleIdSet     用户角色Id集合。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 新增后的用户对象。
     */
    SysUser saveNew(SysUser user, Set<Long> roleIdSet, Set<Long> dataPermIdSet);

    /**
     * 更新用户对象。
     *
     * @param user          更新的用户对象。
     * @param originalUser  原有的用户对象。
     * @param roleIdSet     用户角色Id列表。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 更新成功返回true，否则false。
     */
    boolean update(SysUser user, SysUser originalUser, Set<Long> roleIdSet, Set<Long> dataPermIdSet);

    @Transactional(rollbackFor = Exception.class)
    boolean updateTrainingAccount(SysUser user, SysUser originalUser,Long classId);

    /**
     * 修改用户密码。
     * @param userId  用户主键Id。
     * @param newPass 新密码。
     * @return 成功返回true，否则false。
     */
    boolean changePassword(Long userId, String newPass);

    @Transactional(rollbackFor = Exception.class)
    boolean changePasswordByMobile(String mobile, String newPassword);

    /**
     * 修改用户头像。
     *
     * @param userId  用户主键Id。
     * @param newHeadImage 新的头像信息。
     * @return 成功返回true，否则false。
     */
    boolean changeHeadImage(Long userId, String newHeadImage);

    /**
     * 删除指定数据。
     *
     * @param userId 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long userId);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getSysUserListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SysUser> getSysUserList(SysUser filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getSysUserList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SysUser> getSysUserListWithRelation(SysUser filter, String orderBy);

    List<SysUser> getStudentListByTaskId(SysUser filter, String orderBy,Long classId,Long taskId);

    List<SysUser> getStudentList(SysUser filter, String orderBy,Long taskId);

    /**
     * 获取指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByRoleId(Long roleId, SysUser filter, String orderBy);

    /**
     * 获取不属于指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    List<SysUser> getNotInSysUserListByRoleId(Long roleId, SysUser filter, String orderBy);

    /**
     * 获取指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy);

    /**
     * 获取不属于指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getNotInSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy);



    /**
     * 验证用户对象关联的数据是否都合法。
     *
     * @param sysUser         当前操作的对象。
     * @param originalSysUser 原有对象。
     * @param roleIds         逗号分隔的角色Id列表字符串。
     * @param dataPermIds     逗号分隔的数据权限Id列表字符串。
     * @return 验证结果。
     */
    CallResult verifyRelatedData(
            SysUser sysUser, SysUser originalSysUser, String roleIds, String dataPermIds);

    /**
     * 根据部门Id获取用户Id列表。
     *
     * @param deptIdList 部门Id。
     * @return 用户Id列表。
     */
    Set<Long> getUserIdListByDeptIdList(List<Long> deptIdList);

    Map<String, Integer> saveFromExcel(List<ExcelData> excelDataList, Long classId, Integer type);


    SysUser addStudent(SysUser user, Long classId,Integer type);


    SysUser registeredUser(String name, String password, String phone);
    /**
     * 获取指定电话号码的用户对象。
     *
     * @param mobile 指定登录用户名。
     * @return 用户对象。
     */
    SysUser getSysUserByMobile(String mobile);


    /**
     * sso单点登陆验证
     *
     * @param bss3SessionId sessionId
     * @param signTimestamp 登陆时间戳
     * @param signString 加密串
     */
    JSONObject verifySsoLogin(String bss3SessionId, String signTimestamp, String signString);
}
