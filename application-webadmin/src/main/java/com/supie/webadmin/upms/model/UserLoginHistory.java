package com.supie.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supie.common.core.annotation.DeptFilterColumn;
import com.supie.common.core.annotation.UserFilterColumn;
import com.supie.common.core.util.MyCommonUtil;
import lombok.Data;

import java.util.Date;

/**
 * 用户登录历史表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-06-20
 */
@Data
@TableName(value = "lmd_user_login_history")
public class UserLoginHistory {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 创建者ID。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改者ID。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 修改时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据所属人ID。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 用户id。
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 登录时间。
     */
    @TableField(value = "login_time")
    private Date loginTime;

    /**
     * 登录名称。
     */
    @TableField(value = "login_name")
    private String loginName;


    /**
     * 用户手机。
     */
    @TableField(value = "mobile")
    private String mobile;


    /**
     * 菜单列表。
     */
    @TableField(value = "menu_list")
    private String menuList;


    /**
     * 权限列表。
     */
    @TableField(value = "perm_code_list")
    private String permCodeList;

    /**
     * 显示名称。
     */
    @TableField(value = "show_name")
    private String showName;

    /**
     * 是否为管理员。
     */
    @TableField(value = "is_admin")
    private String isAdmin;

    /**
     * 登录ip。
     */
    @TableField(value = "login_ip")
    private String loginIp;

    /**
     * 设备类型。
     */
    @TableField(value = "device_type")
    private String deviceType;


    /**
     * 头像地址。
     */
    @TableField(value = "head_image_url")
    private String headImageUrl;

    /**
     * 角色列表。
     */
    @TableField(value = "role_id_list")
    private String roleIdList;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * loginTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String loginTimeStart;

    /**
     * loginTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String loginTimeEnd;

    /**
     * str_id LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
