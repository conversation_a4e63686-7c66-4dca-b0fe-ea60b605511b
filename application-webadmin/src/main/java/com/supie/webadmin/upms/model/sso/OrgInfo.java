package com.supie.webadmin.upms.model.sso;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 组织信息实体
 * @date 2025/5/15 10:09
 */
@Data
public class OrgInfo {
    /**
     * 组织标识
     */
    @JsonProperty("orgId")
    private Long orgId;

    /**
     * 组织名称
     */
    @JsonProperty("orgName")
    private String orgName;

    @JsonProperty("regionId")
    private Long regionId;

    @JsonProperty("orgCode")
    private String orgCode;

    /**
     * 上级组织标识，记录组织的直接管理上级标识
     */
    @JsonProperty("parentOrgId")
    private Long parentOrgId;

    /**
     * 组织级别
     */
    @JsonProperty("orgLevel")
    private Long orgLevel;

    @JsonProperty("lanId")
    private Long lanId;

    /**
     * 省份区域标识
     */
    @JsonProperty("province")
    private Long province;

    /**
     * 地市区域标识
     */
    @JsonProperty("city")
    private Long city;

    /**
     * 区县区域标识
     */
    @JsonProperty("county")
    private Long county;

    /**
     * 营销中心区域标识
     */
    @JsonProperty("marketCentre")
    private  Long marketCentre;
}
