<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.upms.dao.SysUserMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.upms.model.SysUser">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="head_image_url" jdbcType="VARCHAR" property="headImageUrl"/>
        <result column="user_status" jdbcType="INTEGER" property="userStatus"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="user_auth_info" jdbcType="VARCHAR" property="userAuthInfo"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
        <result column="account_type" jdbcType="INTEGER" property="accountType"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO li_sys_user
            (user_id,
            login_name,
            password,
            dept_id,
            show_name,
            user_type,
            head_image_url,
            user_status,
            email,
            mobile,
            user_auth_info,
            create_user_id,
            update_user_id,
            create_time,
            update_time,
            deleted_flag,
             account_type
             )
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.userId},
            #{item.loginName},
            #{item.password},
            #{item.deptId},
            #{item.showName},
            #{item.userType},
            #{item.headImageUrl},
            #{item.userStatus},
            #{item.email},
            #{item.mobile},
            #{item.userAuthInfo},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.createTime},
            #{item.updateTime},
            #{item.deletedFlag},
            #{item.accountType}
             )
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.upms.dao.SysUserMapper.inputFilterRef"/>
        AND li_sys_user.deleted_flag = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="sysUserFilter != null">
            <if test="sysUserFilter.loginName != null and sysUserFilter.loginName != ''">
                <bind name = "safeSysUserLoginName" value = "'%' + sysUserFilter.loginName + '%'" />
                AND li_sys_user.login_name LIKE #{safeSysUserLoginName}
            </if>
            <if test="sysUserFilter.deptId != null">
                AND (EXISTS (SELECT 1 FROM li_sys_dept_relation WHERE
                        li_sys_dept_relation.parent_dept_id = #{sysUserFilter.deptId}
                        AND li_sys_user.dept_id = li_sys_dept_relation.dept_id))
            </if>
            <if test="sysUserFilter.showName != null and sysUserFilter.showName != ''">
                <bind name = "safeSysUserShowName" value = "'%' + sysUserFilter.showName + '%'" />
                AND li_sys_user.show_name LIKE #{safeSysUserShowName}
            </if>
            <if test="sysUserFilter.userStatus != null">
                AND li_sys_user.user_status = #{sysUserFilter.userStatus}
            </if>
            <if test="sysUserFilter.createTimeStart != null and sysUserFilter.createTimeStart != ''">
                AND li_sys_user.create_time &gt;= #{sysUserFilter.createTimeStart}
            </if>
            <if test="sysUserFilter.createTimeEnd != null and sysUserFilter.createTimeEnd != ''">
                AND li_sys_user.create_time &lt;= #{sysUserFilter.createTimeEnd}
            </if>

            <if test="sysUserFilter.accountType != null and sysUserFilter.accountType != ''">
                AND li_sys_user.account_type &lt;= #{sysUserFilter.accountType}
            </if>
        </if>
    </sql>

    <select id="getSysUserList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.upms.model.SysUser">
        SELECT * FROM li_sys_user
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDeptIds" resultMap="BaseResultMap">
        SELECT * FROM li_sys_user
        <where>
            <if test="deptIds != null">
                AND (EXISTS (SELECT 1 FROM li_sys_dept_relation WHERE
                        li_sys_dept_relation.parent_dept_id IN
                        <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        AND li_sys_user.dept_id = li_sys_dept_relation.dept_id))
            </if>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByLoginNames" resultMap="BaseResultMap">
        SELECT * FROM li_sys_user
        <where>
            <if test="loginNames != null">
                AND li_sys_user.login_name IN
                <foreach collection="loginNames" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getUserIdListByRoleIds" resultType="java.lang.Long">
        SELECT
            DISTINCT li_sys_user.user_id
        FROM
            li_sys_user_role,
            li_sys_user
        <where>
            AND li_sys_user_role.role_id IN
            <foreach collection="roleIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND li_sys_user_role.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT
            li_sys_user.*
        FROM
            li_sys_user_role,
            li_sys_user
        <where>
            AND li_sys_user_role.role_id = #{roleId}
            AND li_sys_user_role.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT * FROM li_sys_user
        <where>
            NOT EXISTS (SELECT * FROM li_sys_user_role
                WHERE li_sys_user_role.role_id = #{roleId} AND li_sys_user_role.user_id = li_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT
            li_sys_user.*
        FROM
            li_sys_data_perm_user,
            li_sys_user
        <where>
            AND li_sys_data_perm_user.data_perm_id = #{dataPermId}
            AND li_sys_data_perm_user.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT * FROM li_sys_user
        <where>
            NOT EXISTS (SELECT * FROM li_sys_data_perm_user
                WHERE li_sys_data_perm_user.data_perm_id = #{dataPermId} AND li_sys_data_perm_user.user_id = li_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getUserIdListByDeptPostIds" resultType="java.lang.Long">
        SELECT
            DISTINCT li_sys_user.user_id
        FROM
            li_sys_user_post,
            li_sys_user
        <where>
            AND li_sys_user_post.dept_post_id IN
            <foreach collection="deptPostIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND li_sys_user_post.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDeptPostId" resultMap="BaseResultMap">
        SELECT
            li_sys_user.*
        FROM
            li_sys_user_post,
            li_sys_user
        <where>
            AND li_sys_user_post.dept_post_id = #{deptPostId}
            AND li_sys_user_post.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByDeptPostId" resultMap="BaseResultMap">
        SELECT * FROM li_sys_user
        <where>
            NOT EXISTS (SELECT * FROM li_sys_user_post
                WHERE li_sys_user_post.dept_post_id = #{deptPostId} AND li_sys_user_post.user_id = li_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getUserIdListByPostIds" resultType="java.lang.Long">
        SELECT
            DISTINCT li_sys_user.user_id
        FROM
            li_sys_user_post,
            li_sys_user
        <where>
            AND li_sys_user_post.post_id IN
            <foreach collection="postIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND li_sys_user_post.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByPostId" resultMap="BaseResultMap">
        SELECT
            li_sys_user.*
        FROM
            li_sys_user_post,
            li_sys_user
        <where>
            AND li_sys_user_post.post_id = #{postId}
            AND li_sys_user_post.user_id = li_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserByOpenId" resultMap="BaseResultMap">
        SELECT
            li_sys_user.*
        FROM
            li_sys_user_auth,
            li_sys_user
        WHERE
            li_sys_user_auth.source = #{source}
            AND li_sys_user_auth.open_id = #{openId}
            AND li_sys_user_auth.user_id = li_sys_user.user_id
    </select>
</mapper>
