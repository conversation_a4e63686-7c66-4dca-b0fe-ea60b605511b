<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.webadmin.upms.dao.SysUserMapper">
    <resultMap id="BaseResultMap" type="com.supie.webadmin.upms.model.SysUser">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="head_image_url" jdbcType="VARCHAR" property="headImageUrl"/>
        <result column="user_status" jdbcType="INTEGER" property="userStatus"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sys_user
            (user_id,
            login_name,
            password,
            dept_id,
            show_name,
            user_type,
            head_image_url,
            user_status,
            email,
            mobile,
            create_user_id,
            update_user_id,
            create_time,
            update_time,
            deleted_flag)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.userId},
            #{item.loginName},
            #{item.password},
            #{item.deptId},
            #{item.showName},
            #{item.userType},
            #{item.headImageUrl},
            #{item.userStatus},
            #{item.email},
            #{item.mobile},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.createTime},
            #{item.updateTime},
            #{item.deletedFlag})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.webadmin.upms.dao.SysUserMapper.inputFilterRef"/>
        AND sys_user.deleted_flag = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="sysUserFilter != null">
            <if test="sysUserFilter.loginName != null and sysUserFilter.loginName != ''">
                <bind name = "safeSysUserLoginName" value = "'%' + sysUserFilter.loginName + '%'" />
                AND sys_user.login_name LIKE #{safeSysUserLoginName}
            </if>
            <if test="sysUserFilter.deptId != null">
                AND (EXISTS (SELECT 1 FROM sys_dept_relation WHERE
                        sys_dept_relation.parent_dept_id = #{sysUserFilter.deptId}
                        AND sys_user.dept_id = sys_dept_relation.dept_id))
            </if>
            <if test="sysUserFilter.showName != null and sysUserFilter.showName != ''">
                <bind name = "safeSysUserShowName" value = "'%' + sysUserFilter.showName + '%'" />
                AND sys_user.show_name LIKE #{safeSysUserShowName}
            </if>
            <if test="sysUserFilter.userStatus != null">
                AND sys_user.user_status = #{sysUserFilter.userStatus}
            </if>
            <if test="sysUserFilter.createTimeStart != null and sysUserFilter.createTimeStart != ''">
                AND sys_user.create_time &gt;= #{sysUserFilter.createTimeStart}
            </if>
            <if test="sysUserFilter.createTimeEnd != null and sysUserFilter.createTimeEnd != ''">
                AND sys_user.create_time &lt;= #{sysUserFilter.createTimeEnd}
            </if>
        </if>
    </sql>

    <select id="getSysUserList" resultMap="BaseResultMap" parameterType="com.supie.webadmin.upms.model.SysUser">
        SELECT * FROM sys_user
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDeptIds" resultMap="BaseResultMap">
        SELECT * FROM sys_user
        <where>
            <if test="deptIds != null">
                AND (EXISTS (SELECT 1 FROM sys_dept_relation WHERE
                        sys_dept_relation.parent_dept_id IN
                        <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        AND sys_user.dept_id = sys_dept_relation.dept_id))
            </if>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByLoginNames" resultMap="BaseResultMap">
        SELECT * FROM sys_user
        <where>
            <if test="loginNames != null">
                AND sys_user.login_name IN
                <foreach collection="loginNames" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getUserIdListByRoleIds" resultType="java.lang.Long">
        SELECT
            DISTINCT sys_user.user_id
        FROM
            sys_user_role,
            sys_user
        <where>
            AND sys_user_role.role_id IN
            <foreach collection="roleIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND sys_user_role.user_id = sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT
            sys_user.*
        FROM
            sys_user_role,
            sys_user
        <where>
            AND sys_user_role.role_id = #{roleId}
            AND sys_user_role.user_id = sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT * FROM sys_user
        <where>
            NOT EXISTS (SELECT * FROM sys_user_role
                WHERE sys_user_role.role_id = #{roleId} AND sys_user_role.user_id = sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT
            sys_user.*
        FROM
            sys_data_perm_user,
            sys_user
        <where>
            AND sys_data_perm_user.data_perm_id = #{dataPermId}
            AND sys_data_perm_user.user_id = sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT * FROM sys_user
        <where>
            NOT EXISTS (SELECT * FROM sys_data_perm_user
                WHERE sys_data_perm_user.data_perm_id = #{dataPermId} AND sys_data_perm_user.user_id = sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNoDataPermissionUserList" resultType="com.supie.webadmin.upms.model.SysUser">
        SELECT * FROM sys_user
        <where>
            <include refid="filterRef"/>
        </where>
    </select>

    <select id="getNoDataPermissionUser" resultType="com.supie.webadmin.upms.model.SysUser">
        SELECT * FROM sys_user
        <where>
            <include refid="filterRef"/>
        </where>
    </select>

    <select id="getSysUserListFilterFoundation" resultType="com.supie.webadmin.upms.model.SysUser">
        SELECT * FROM sys_user
        <where>
            dept_id !='1787740798154969088'
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

</mapper>
