//package com.supie.webadmin.asynctask;
//
//import lombok.Data;
//
//import java.lang.reflect.Method;
//
//@Data
//public class TaskInfo {
//    /**
//     * 方法所属对象
//     */
//    private final Object target;
//    /**
//     * 被调用的方法
//     */
//    private final Method method;
//    /**
//     * 方法参数
//     */
//    private final Object[] args;
//    /**
//     * 所属队列名
//     */
//    private final String queueName;
//    /**
//     * 最大重试次数
//     */
//    private final int maxRetry;
//}
