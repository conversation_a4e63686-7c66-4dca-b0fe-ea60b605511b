package com.supie.webadmin.config;

import com.supie.webadmin.interceptor.ActuatorInterceptor;
import com.supie.webadmin.interceptor.AuthenticationInterceptor;
import com.supie.webadmin.interceptor.BugFixesInterceptor;
import com.supie.webadmin.interceptor.OpenApiInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 所有的项目拦截器都在这里集中配置
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AuthenticationInterceptor()).addPathPatterns("/admin/**");
        registry.addInterceptor(new OpenApiInterceptor()).addPathPatterns("/openapi/**");
        registry.addInterceptor(new ActuatorInterceptor()).addPathPatterns("/actuator/**");
        registry.addInterceptor(new BugFixesInterceptor()).addPathPatterns("/swagger-ui/**");
        registry.addInterceptor(new BugFixesInterceptor()).addPathPatterns("/.svn/**");
        registry.addInterceptor(new BugFixesInterceptor()).addPathPatterns("/actuator/**");
        registry.addInterceptor(new BugFixesInterceptor()).addPathPatterns("/data-processing/**");
        registry.addInterceptor(new BugFixesInterceptor()).addPathPatterns("/model-service/**");
    }

}
