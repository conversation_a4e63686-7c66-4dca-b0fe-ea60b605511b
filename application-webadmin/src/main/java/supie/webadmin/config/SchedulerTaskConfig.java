package supie.webadmin.config;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import supie.webadmin.app.dao.SchClusterNodeMapper;
import supie.webadmin.app.dao.SchContainerManagerMapper;
import supie.webadmin.app.dao.SchResourceInfoMapper;
import supie.webadmin.app.dao.SchTaskInfoMapper;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.SchNodeBasicMetricsService;
import supie.webadmin.app.service.SchTaskInfoService;
import supie.webadmin.app.service.impl.SchTaskMonitoringServiceImpl;
import supie.webadmin.app.util.ComposeGenerate;
import supie.webadmin.app.util.DockerJavaUtil;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 定时任务p
 * @date 2025/6/5 22:17
 */

@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "scheduling", name = "enabled", havingValue = "true")
public class SchedulerTaskConfig {



    @Resource
    private SchTaskInfoMapper schTaskInfoMapper;

    @Resource
    private SchTaskInfoService schTaskInfoService;

    @Resource
    private SchContainerManagerMapper schContainerManagerMapper;


    @Resource
    private SchNodeBasicMetricsService schNodeBasicMetricsService;
    @Autowired
    private DockerJavaUtil dockerJavaUtil;
    @Autowired
    private SchClusterNodeMapper schClusterNodeMapper;
    @Autowired
    private SchTaskMonitoringServiceImpl schTaskMonitoringService;
    @Autowired
    private IdGeneratorWrapper idGeneratorWrapper;

    //@Scheduled(cron = "*/30 * * * * ?")
    public void managementTimed() throws IOException {
        schNodeBasicMetricsService.collectNodeBasicInfo();
    }

    /**
     * 监控容器数据 定时采集容器数据 CPU 使用率 CPU 使用量  内存使用率 内存使用量 磁盘使用率 磁盘使用量 网络使用率 网络使用量 2d4c5522b971
     */
    @Scheduled(cron = "* 0/30 * * * ?")
    public void collectContainerData() {
        // 查询所有正在运行的任务，且关联的容器也在运行中
        LambdaQueryWrapper<SchTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchTaskInfo::getStatus, "running")
                .and(i -> i.eq(SchTaskInfo::getContainerStatus, "running"));
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.selectList(queryWrapper);
        for (SchTaskInfo item : schTaskInfos) {
            try {
                // 执行监控数据采集
              //  composeGenerate.containerMonitorCollect(item.getId());
            } catch (Exception e) {
                // 捕获异常，防止一个任务失败影响其他任务执行
                log.error("采集容器监控数据失败，任务ID：" + item.getId() + ", 错误信息：" + e.getMessage());
            }
        }
    }

    //@Scheduled(cron = "0/30 * * * * ?")
    //@Scheduled(cron = "0 0/30 * * * ?")
    public void collectContainerDatatest() {
        log.debug("<======================开始执行容器数据采集======================>");
        // 查询所有正在运行的任务，且关联的容器也在运行中
        List<SchContainerManager> schContainerManagerList = schContainerManagerMapper.selectList(
                new LambdaQueryWrapper<SchContainerManager>()
                        .eq(SchContainerManager::getContainerStatus, "running")
        );
        //提取节点列表
        List<Long> nodeIdList = schContainerManagerList.stream().map(SchContainerManager::getClusterNodeId).toList();
        List<SchClusterNode> schciusterNodeList = schClusterNodeMapper.selectList(new LambdaQueryWrapper<SchClusterNode>()
                .in(!schContainerManagerList.isEmpty(),SchClusterNode::getId, nodeIdList));
        Map<Long, SchClusterNode> schciusterNodeMap = schciusterNodeList.stream().collect(Collectors.toMap(SchClusterNode::getId, item -> item));
        List<SchTaskMonitoring> schTaskMonitoringList = new ArrayList<>();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        for (SchContainerManager item : schContainerManagerList) {
            try {
                // 执行监控数据采集
                SchClusterNode schClusterNode = schciusterNodeMap.get(item.getClusterNodeId());
                Map<String, Object> containerInfomap = dockerJavaUtil.getContainerInfo(schClusterNode.getResourceInfoId(), item.getContainerId());
                if (!containerInfomap.get("containerState").equals("running")) {
                    log.debug("容器异常"+item.getContainerId());
                    continue;
                }
                Map<String, Object> map = dockerJavaUtil.getMemoryStats(schClusterNode.getResourceInfoId(), item.getContainerId());
                log.debug("容器监控"+ JSONObject.toJSONString(map));
                SchTaskMonitoring schTaskMonitoring = new SchTaskMonitoring();
                schTaskMonitoring.setContainerId(item.getContainerId());
                Double cpuUsage = (Double) map.get("cpuUsage");
                Double  memoryMB = (Double) map.get("memoryMB");
                schTaskMonitoring.setCpuUsage(cpuUsage != null ? BigDecimal.valueOf(cpuUsage) : null);
                schTaskMonitoring.setMemoryUsage(memoryMB != null ? BigDecimal.valueOf(memoryMB) : null);

                schTaskMonitoring.setTs(timestamp);
                schTaskMonitoring.setId(idGeneratorWrapper.nextLongId());
                schTaskMonitoring.setIsDelete(1);
                schTaskMonitoringList.add(schTaskMonitoring);

            } catch (Exception e) {
                // 捕获异常，防止一个任务失败影响其他任务执行
                log.error("采集容器监控数据失败，任务ID：" + item.getId() + ", 错误信息：" + e.getMessage());
            }
        }
        if (!schTaskMonitoringList.isEmpty()) {
            schTaskMonitoringService.saveBatch(schTaskMonitoringList);
            log.debug("容器数据采集完成，添加数据{}条", schTaskMonitoringList.size());
        }
    }

    /**
     * 扩缩容监控30分钟采集一次
     */
    //@Scheduled(cron = "0 0/30 * * * ?")
    public void expandDecreaseMonitor() {
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.queryRunTask();
        schTaskInfoService.expandDecreaseMonitor(schTaskInfos);
    }

    /**
     * 每10秒检测一下任务状态 taskStatus running for containerStatus(running created restarting )
     *        taskStatus stop for containerStatus( paused)
     *        taskStatus failed for containerStatus(dead)
     */
    @Scheduled(cron = "0/10 * * * * ?")
    public void upDateTaskStatus() {
        schTaskInfoService.upDateTaskStatus();
    }

}
