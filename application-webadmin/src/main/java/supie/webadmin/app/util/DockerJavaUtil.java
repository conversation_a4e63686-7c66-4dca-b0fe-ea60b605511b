package supie.webadmin.app.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import com.alibaba.fastjson.JSONObject;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.async.ResultCallback;
import com.github.dockerjava.api.async.ResultCallbackTemplate;
import com.github.dockerjava.api.command.*;
import com.github.dockerjava.api.exception.DockerException;
import com.github.dockerjava.api.model.*;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientBuilder;
import com.github.dockerjava.core.DockerClientImpl;
import com.github.dockerjava.core.InvocationBuilder;
import com.github.dockerjava.core.command.LogContainerResultCallback;
import com.github.dockerjava.httpclient5.ApacheDockerHttpClient;
import com.github.dockerjava.transport.DockerHttpClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import supie.common.core.exception.MyRuntimeException;
import supie.webadmin.app.model.SchResourceInfo;
import supie.webadmin.app.service.impl.SchResourceInfoServiceImpl;

import java.io.Closeable;
import java.io.IOException;

import com.github.dockerjava.api.command.LogContainerCmd;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.rmi.server.LogStream;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Component
public class DockerJavaUtil {
    private static final Random random = new Random();
    private static final int PORT_MIN = 30000;
    private static final int PORT_RANGE = 5001;
    private static final int CONNECTION_TIMEOUT = 2000;
    @Resource
    private SchResourceInfoServiceImpl schResourceInfoService;


    /**
     * 初始化Docker Client 连接
     * @param schResourceInfoId 支援信息id
     * @return Docker Client
     */
    public DockerClient connect(Long schResourceInfoId) {
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schResourceInfoId);
        DefaultDockerClientConfig config = DefaultDockerClientConfig.
                createDefaultConfigBuilder()
                .withDockerTlsVerify(false)
                .withDockerHost("tcp://" + schResourceInfo.getHostIp() + ":" + "2375")
                .withApiVersion("1.39")
                .withRegistryUrl(null)
                .withDockerConfig("/root/.docker/config.json")
                .build();
        DockerHttpClient httpClient = new ApacheDockerHttpClient
                .Builder()
                .dockerHost(config.getDockerHost())
                .maxConnections(200)
                .connectionTimeout(Duration.ofSeconds(60))
                .responseTimeout(Duration.ofSeconds(90)).build();
        return DockerClientImpl.getInstance(config, httpClient);

    }

    /**
     * 创建容器
     */
    public String createServiceCmd(String containerName, String image, String innerPort, String exportPort, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {

            // 创建容器配置
            CreateContainerCmd containerCmd = dockerClient.createContainerCmd(image)
                    .withName(containerName)
                    .withHostConfig(new HostConfig()
//                            .withAutoRemove(true)  // 容器退出后自动删除
                                    .withPortBindings(PortBinding.parse(exportPort + ":" + innerPort))
                    )
                    .withExposedPorts(ExposedPort.parse(innerPort))
                    .withNetworkDisabled(false);
            log.debug("启动命令：" + containerCmd);

            // 创建容器
            CreateContainerResponse container = containerCmd.exec();
            String containerId = container.getId();

            // 启动容器
            dockerClient.startContainerCmd(container.getId()).exec();
            return containerId;

        } catch (IOException | DockerException e) {
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 删除容器
     */
    public void deleteContainer(String containerId, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {

            dockerClient.removeContainerCmd(containerId).exec();
        } catch (IOException | DockerException e) {
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 启动容器
     */
    public void startContainer(String containerId, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            dockerClient.startContainerCmd(containerId).exec();
        } catch (IOException | DockerException e) {
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 停止容器
     */
    public void stopContainer(String containerId, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            dockerClient.stopContainerCmd(containerId).exec();
        } catch (IOException | DockerException e) {
            log.error("容器停止失败{}",e.getMessage());
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 重启容器
     */
    public void restartContainer(String containerId, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            dockerClient.restartContainerCmd(containerId).exec();
        } catch (IOException | DockerException e) {
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 获取容器日志（非流式，直接返回）
     * 
     * @param containerIdOrName 容器ID或容器名称
     * @param lines 要获取的日志行数，默认为100行，设置为-1表示获取所有日志
     * @param schResourceInfoId 资源ID
     * @return 容器日志内容
     */
    public String getContainerLogs(String containerIdOrName, int lines, Long schResourceInfoId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            LogContainerCmd logCmd = dockerClient.logContainerCmd(containerIdOrName)
                    .withStdOut(true)
                    .withStdErr(true)
                    .withTimestamps(true);
            
            // 设置日志行数，如果lines为-1则获取所有日志
            if (lines > 0) {
                logCmd.withTail(lines);
            }
            
            final StringBuilder logStringBuilder = new StringBuilder();
            
            logCmd.exec(new LogContainerResultCallback() {
                @Override
                public void onNext(Frame item) {
                    String logLine = new String(item.getPayload()).trim();
                    logStringBuilder.append(logLine).append("\n");
                    super.onNext(item);
                }
            }).awaitCompletion();
            
            return logStringBuilder.toString();
        } catch (IOException | DockerException | InterruptedException e) {
            log.error("获取容器日志失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("获取容器日志失败: " + e.getMessage());
        }
    }

    /**
     * 流式获取容器日志（支持分页和过滤）
     * 
     * @param containerIdOrName 容器ID或容器名称
     * @param schResourceInfoId 资源ID
     * @param emitter 用于发送SSE事件的emitter
     * @param lines 要获取的日志行数，默认为100行，设置为-1表示获取所有日志
     * @param since 获取指定时间之后的日志（Unix时间戳，单位：秒）
     * @param until 获取指定时间之前的日志（Unix时间戳，单位：秒）
     * @param follow 是否持续跟踪日志
     */
    public void streamContainerLogs(String containerIdOrName, Long schResourceInfoId, SseEmitter emitter, 
                                   int lines, Long since, Long until, boolean follow) {
        DockerClient dockerClient = connect(schResourceInfoId);
        
        LogContainerCmd logCmd = dockerClient.logContainerCmd(containerIdOrName)
                .withStdOut(true)
                .withStdErr(true)
                .withTimestamps(true)
                .withFollowStream(follow);
        
        // 设置日志行数
        if (lines > 0) {
            logCmd.withTail(lines);
        }
        
        // 设置开始时间
        if (since != null && since > 0) {
            logCmd.withSince((int)since.longValue());
        }
        
        // 设置结束时间（Docker API不直接支持until，需要在回调中处理）
        final Long untilTimestamp = until;
        
        try {
            logCmd.exec(new LogContainerResultCallback() {
                @Override
                public void onNext(Frame item) {
                    try {
                        String logLine = new String(item.getPayload()).trim();
                        
                        // 如果设置了until参数，检查日志时间戳是否超过了until
                        if (untilTimestamp != null && untilTimestamp > 0) {
                            // 从日志行中提取时间戳（假设时间戳在日志行的开头）
                            try {
                                String timestampStr = logLine.substring(0, logLine.indexOf(" "));
                                long logTimestamp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'")
                                        .parse(timestampStr).getTime() / 1000;
                                
                                if (logTimestamp > untilTimestamp) {
                                    // 如果日志时间戳超过了until，则停止处理
                                    emitter.complete();
                                    this.onComplete();
                                    return;
                                }
                            } catch (Exception e) {
                                // 时间戳解析失败，继续处理
                                log.warn("解析日志时间戳失败: {}", e.getMessage());
                            }
                        }
                        
                        log.debug("日志行: {}", logLine);
                        emitter.send(logLine);
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                    super.onNext(item);
                }
                
                @Override
                public void onComplete() {
                    emitter.complete();
                    try {
                        dockerClient.close();
                    } catch (IOException e) {
                        log.warn("关闭Docker客户端失败: {}", e.getMessage());
                    }
                    super.onComplete();
                }
                
                @Override
                public void onError(Throwable throwable) {
                    emitter.completeWithError(throwable);
                    try {
                        dockerClient.close();
                    } catch (IOException e) {
                        log.warn("关闭Docker客户端失败: {}", e.getMessage());
                    }
                    super.onError(throwable);
                }
            }).awaitCompletion();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            emitter.completeWithError(e);
            try {
                dockerClient.close();
            } catch (IOException ex) {
                log.warn("关闭Docker客户端失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 简化版的流式获取容器日志方法（兼容原有接口）
     */


    public int getPort(String ip) {
        int attempts = 0;
        while (attempts < 50) {
            int port = PORT_MIN + random.nextInt(PORT_RANGE);
            if (isPortAvailable(ip, port)) {
                return port;
            }
            attempts++;
        }
        throw new MyRuntimeException("无法找到可用端口，请稍后重试");
    }

    private boolean isPortAvailable(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), CONNECTION_TIMEOUT);
            // 占用
            return false;
        } catch (IOException e) {
            // 未占用
            return true;
        }
    }

    /**
     * 获取容器内存占用
     *
     * @param containerId 容器ID
     * @return 占用内存（MB）
     */
    public Map<String, Object> getMemoryStats(Long schResourceInfoId, String containerId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            CountDownLatch latch = new CountDownLatch(1);
            Map<String, Object> usageMap = new HashMap<>();
            usageMap.put("containerId", containerId);
            usageMap.put("cpuUsage", 0.0);
            usageMap.put("memoryUsage", 0.0);
            usageMap.put("memoryMB", 0.0);

            dockerClient.statsCmd(containerId).withNoStream(true).exec(new ResultCallback<Statistics>() {
                @Override
                public void onStart(Closeable closeable) {}

                @Override
                public void onNext(Statistics stats) {
                    try {
//                        log.debug("容器统计监控：{}", JSONObject.toJSONString(stats));
                        // === 内存统计 ===
                        MemoryStatsConfig memStats = stats.getMemoryStats();
                        if (memStats != null && memStats.getUsage() != null && memStats.getLimit() != null) {
                            long usage = memStats.getUsage();
                            long limit = memStats.getLimit();
                            double memPercent = (double) usage / limit * 100.0;
                            double memMB = (double) usage / (1024 * 1024);

                            usageMap.put("memoryUsage", memPercent);
                            usageMap.put("memoryMB", memMB);
                        }

                        // === CPU 统计 ===
                        CpuStatsConfig cpu = stats.getCpuStats();
                        CpuStatsConfig precpu = stats.getPreCpuStats();
                        if (cpu != null && precpu != null &&
                                cpu.getCpuUsage() != null && precpu.getCpuUsage() != null &&
                                cpu.getSystemCpuUsage() != null && precpu.getSystemCpuUsage() != null) {

                            long cpuDelta = cpu.getCpuUsage().getTotalUsage() - precpu.getCpuUsage().getTotalUsage();
                            long sysDelta = cpu.getSystemCpuUsage() - precpu.getSystemCpuUsage();
                            int cpuCores = cpu.getCpuUsage().getPercpuUsage() != null ?
                                    cpu.getCpuUsage().getPercpuUsage().size() : 1;

                            if (sysDelta > 0 && cpuDelta > 0) {
                                double cpuPercent = ((double) cpuDelta / sysDelta) * cpuCores * 100;
                                usageMap.put("cpuUsage", cpuPercent);
                            }
                        }
                    } finally {
                        try {
                            close();
                        } catch (IOException e) {
                            log.error("关闭 stats 流失败", e);
                        }
                        latch.countDown();
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    latch.countDown();
                    log.error("获取容器资源信息失败", throwable);
                }

                @Override
                public void onComplete() {}

                @Override
                public void close() throws IOException {}
            });

            latch.await();
            return usageMap;

        } catch (IOException | InterruptedException | DockerException e) {
            throw new MyRuntimeException("获取容器资源信息失败", e);
        }
    }

    /**
     * @param containerId 容器ID
     * @Description: 获取容器的基本信息
     */
    public Map<String, Object> getContainerInfo(Long schResourceInfoId, String containerId) {
        try (DockerClient dockerClient = connect(schResourceInfoId)) {
            HashMap<String, Object> map = new HashMap<>();
            // 获取容器信息
            InspectContainerResponse containerInfo = dockerClient.inspectContainerCmd(containerId).exec();
            // 获取容器的名称
            map.put("containerName", containerInfo.getName());
            // 获取容器的镜像名称
            String image = containerInfo.getConfig().getImage();
            map.put("dockerImage", image);
            // 获取容器的端口信息
            Ports ports = containerInfo.getNetworkSettings().getPorts();
            map.put("ports", StrUtil.join(",", ports.getBindings().keySet()));
            // 获取容器的运行状态
            String containerState = containerInfo.getState().getStatus();
            map.put("containerState", containerState);
            // 获取容器的创建时间戳（以毫秒为单位）
            String createdTimeMillis = containerInfo.getCreated();
            map.put("created", formattedTimeZone(createdTimeMillis));
            log.debug("Container info: {}", JSONObject.toJSONString(map));
            return map;
        } catch (IOException | DockerException e) {
            throw new MyRuntimeException(e);
        }
    }

    /**
     * @param isoTimeMillis ISO8601格式的时间
     * @return {@code String } 时间
     * @Description: 格式化时间
     */
    private static String formattedTimeZone(String isoTimeMillis) {
        // 解析ISO 8601格式的字符串为Instant对象
        Instant instant = Instant.parse(isoTimeMillis);
        // 将Instant对象转换为ZonedDateTime对象（UTC时区）
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.of("UTC"));
        // 转换为本地时区（如果需要）
        ZonedDateTime localZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.systemDefault());
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化时间
        return localZonedDateTime.format(formatter);
    }


}
