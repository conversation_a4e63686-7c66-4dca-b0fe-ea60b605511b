package supie.webadmin.app.util;

import com.alibaba.fastjson.JSON;
import com.jcraft.jsch.JSchException;
import supie.common.core.util.RsaUtil;
import supie.webadmin.app.dto.RemoteTask;
import supie.webadmin.app.service.SchTaskInfoService;
import supie.webadmin.app.service.impl.SchTaskInfoServiceImpl;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/6/19 15:08
 */
public class Test {
    public static void main(String[] args) throws JSchException, IOException {
        RemoteTask test1004Local = new RemoteTask().setTaskName("test_1004_local")
                .setContainerMapPath("/data/applications/spss/exp_1787010173943808/trial_1139")
                .setContainerPath("/workspace")
                .setContainerPort("8080")
                .setCpuCore(1)
                .setToken("1343434")
                .setExport(List.of("11233"))
                .setGraphicSize("0");
        System.out.println(JSON.toJSONString(test1004Local));
    }
}
