package supie.webadmin.app.util;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jcraft.jsch.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import supie.common.core.constant.TaskStatusType;
import supie.common.core.exception.MyRuntimeException;
import supie.common.core.util.RsaUtil;
import supie.webadmin.app.dao.SchResourceInfoMapper;
import supie.webadmin.app.dao.SchTaskInfoMapper;
import supie.webadmin.app.model.SchTaskInfo;

import java.io.*;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.Properties;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * SSH连接工具类。
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class SshConnectionUtil {
    /**
     * 端口最小值40068-40085
     */
    private static final int PORT_MIN = 40000;
    /**
     * 端口范围
     */
    private static final int PORT_RANGE = 5001;
    /**
     * 连接超时时间
     */
    private static final int CONNECTION_TIMEOUT = 2000;

    /**
     * 最大重试执行次数笔名死循环
     */
    private static final int MAX_RETRY_ATTEMPTS = 50;
    private static final java.util.Random random = new Random();

    /**
     * 服务器密码解密私钥
     */
    @Value("${application.server.secretKey}")
    private String secretKey;
    @Resource
    private SchTaskInfoMapper schTaskInfoMapper;
    /**
     * 获取系统可以使用的开放端口
     * @param ip 主机IP
     * @return 端口号
     */
    public int getFreePort(String ip) {
        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            int port = PORT_MIN + random.nextInt(PORT_RANGE);
            if (this.isPortAvailable(ip, port)) {
                return port;
            }
            attempts++;
        }
        throw new MyRuntimeException("无法找到可用端口，请稍后重试");
    }

    /**
     *  Socket监听端口可用性 通过IP:端口 检测内否建立Socket 建立连接说明该端口已经存储
     * @param ip 主机IP
     * @param port 端口
     * @return boolean
     */
    private boolean isPortAvailable(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), CONNECTION_TIMEOUT);
            // 建立连接成功说明端口已经使用
            return false;
        } catch (IOException e) {
            return true;
        }
    }

    /**
     * 创建SSH连接（密码认证）。
     *
     * @param host     服务器IP
     * @param port     SSH端口
     * @param username 用户名
     * @param password 密码
     * @param timeout  超时时间（毫秒）
     * @return SSH连接
     * @throws JSchException 连接异常
     */
    public SshConnection createConnection(String host, int port, String username, 
                                        String password, int timeout) throws JSchException {
        JSch jsch = new JSch();
        Session session = jsch.getSession(username, host, port);
        String decodedString = URLDecoder.decode(password, StandardCharsets.UTF_8);
        String decrypt = RsaUtil.decrypt(decodedString, secretKey);
        log.info("SSH连接密码解密成功: {}", decrypt);
        session.setPassword(decrypt);
        // 配置连接属性
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        // 只使用密码认证，不尝试其他方式
        //config.put("PreferredAuthentications", "password");
        session.setConfig("PreferredAuthentications", "publickey,password");
        // 禁用Kerberos认证
        config.put("GSSAPIAuthentication", "no");
        config.put("UserKnownHostsFile", "/dev/null");
        config.put("ServerAliveInterval", "60"); // 每60秒发送一次保活包
        config.put("ServerAliveCountMax", "3");  // 最多允许丢失3个保活包
        config.put("TCPKeepAlive", "yes");       // 启用TCP保活
        session.setConfig(config);
        session.setTimeout(timeout);
        // 建立连接
        session.connect();

        log.info("SSH连接建立成功: {}@{}:{}", username, host, port);
        return new SshConnection(session, null);
    }

    /**
     * 创建SSH连接（私钥认证）。
     *
     * @param host           服务器IP
     * @param port           SSH端口
     * @param username       用户名
     * @param privateKeyPath 私钥路径
     * @param timeout        超时时间（毫秒）
     * @return SSH连接
     * @throws JSchException 连接异常
     */
    public SshConnection createConnectionWithPrivateKey(String host, int port, String username, 
                                                      String privateKeyPath, int timeout) throws JSchException {
        JSch jsch = new JSch();
        jsch.addIdentity(privateKeyPath);
        Session session = jsch.getSession(username, host, port);

        // 配置连接属性
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        config.put("UserKnownHostsFile", "/dev/null");
        session.setConfig(config);
        session.setTimeout(timeout);

        // 建立连接
        session.connect();
        log.info("SSH连接建立成功(私钥): {}@{}:{}", username, host, port);

        return new SshConnection(session, null);
    }

    /**
     * 执行命令。
     *
     * @param connection SSH连接
     * @param command    要执行的命令
     * @return 执行结果
     * @throws JSchException 执行异常
     * @throws IOException   IO异常
     */
    public String executeCommand(SshConnection connection, String command) throws JSchException, IOException {
        ChannelExec channelExec = (ChannelExec) connection.getSession().openChannel("exec");
        channelExec.setCommand(command);
        channelExec.setErrStream(System.err);
        // 获取输出流
        InputStream inputStream = channelExec.getInputStream();
        InputStream errorStream = channelExec.getErrStream();
        // 连接并执行
        channelExec.connect();
        log.info("SShConnectionUtl executeCommand  method  start   execute command: {}", command);
        // 读取输出
        StringBuilder output = new StringBuilder();
        StringBuilder errorOutput = new StringBuilder();
        byte[] buffer = new byte[1024];
        while (true) {
            // 读取标准输出
            while (inputStream.available() > 0) {
                int len = inputStream.read(buffer);
                if (len < 0) break;
                output.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
            }

            // 读取错误输出
            while (errorStream.available() > 0) {
                int len = errorStream.read(buffer);
                if (len < 0) break;
                errorOutput.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
            }

            // 检查命令是否执行完成
            if (channelExec.isClosed()) {
                if (inputStream.available() > 0 || errorStream.available() > 0) {
                    continue;
                }
                break;
            }

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 获取退出状态
        int exitStatus = channelExec.getExitStatus();
        channelExec.disconnect();

        String result =  output.toString();
        String error = errorOutput.toString();
        //log.info("命令执行完成, 退出状态: {}, 输出长度: {}, 错误长度: {}", exitStatus, result.length(), error.length());
        //log.error("错误输出: {}", error);
        if (exitStatus != 0 && !error.isEmpty()) {
            log.error("SshConnectionUtl executeCommand  method  execute fail reason: {}", error);
            throw new RuntimeException("命令执行失败: " + error);
        }

        return result;
    }

    /**
     * 执行命令。
     *
     * @param connection SSH连接
     * @param command    要执行的命令
     * @return 执行结果
     * @throws JSchException 执行异常
     * @throws IOException   IO异常
     */
    public String executeCommandCompose(SshConnection connection, String command, Long taskId) throws JSchException, IOException {
        log.info("Executing command: {}", command);
        Session session = connection.getSession();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand(command);
        
        // 获取输出流，使用与executeCommand相同的方式
        InputStream inputStream = channel.getInputStream();
        InputStream errorStream = channel.getErrStream();
        
        // 连接并执行
        channel.connect();
        
        // 读取输出，使用与executeCommand相同的方式
        StringBuilder output = new StringBuilder();
        StringBuilder errorOutput = new StringBuilder();
        byte[] buffer = new byte[1024];
        
        while (true) {
            // 读取标准输出
            while (inputStream.available() > 0) {
                int len = inputStream.read(buffer);
                if (len < 0) break;
                output.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
            }

            // 读取错误输出
            while (errorStream.available() > 0) {
                int len = errorStream.read(buffer);
                if (len < 0) break;
                errorOutput.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
            }

            // 检查命令是否执行完成
            if (channel.isClosed()) {
                if (inputStream.available() > 0 || errorStream.available() > 0) {
                    continue;
                }
                break;
            }

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 获取退出状态
        int exitStatus = channel.getExitStatus();
        String error = errorOutput.toString();
        String result = output.toString();
        
        log.info("命令执行完成, 退出状态: {}, 输出长度: {}, 错误长度: {}", 
                exitStatus, result.length(), error.length());
        
        // 1. 检查是否存在网络地址池重叠错误 继续执行命令
        if (error.contains("could not find an available, non-overlapping IPv4 address pool") ||
            error.contains("failed to allocate gateway") ||
            error.contains("pool overlaps with other one on this address space")) {
            log.warn("检测到Docker网络地址池重叠错误，尝试清理未使用的网络...");
            
            // 执行网络清理命令
            ChannelExec pruneChannel = (ChannelExec) session.openChannel("exec");
            pruneChannel.setCommand("docker network prune -f");
            InputStream pruneInputStream = pruneChannel.getInputStream();
            InputStream pruneErrorStream = pruneChannel.getErrStream();
            pruneChannel.connect();
            
            // 读取清理命令输出
            StringBuilder pruneOutput = new StringBuilder();
            StringBuilder pruneError = new StringBuilder();
            
            while (true) {
                // 读取标准输出
                while (pruneInputStream.available() > 0) {
                    int len = pruneInputStream.read(buffer);
                    if (len < 0) break;
                    pruneOutput.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
                }

                // 读取错误输出
                while (pruneErrorStream.available() > 0) {
                    int len = pruneErrorStream.read(buffer);
                    if (len < 0) break;
                    pruneError.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
                }

                // 检查命令是否执行完成
                if (pruneChannel.isClosed()) {
                    if (pruneInputStream.available() > 0 || pruneErrorStream.available() > 0) {
                        continue;
                    }
                    break;
                }

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 检查网络清理命令是否成功
            int pruneExitStatus = pruneChannel.getExitStatus();
            String pruneOutputStr = pruneOutput.toString();
            String pruneErrorStr = pruneError.toString();
            
            log.info("网络清理完成: 退出状态={}, 输出={}", pruneExitStatus, pruneOutputStr);
            if (pruneExitStatus != 0) {
                log.error("网络清理失败: {}", pruneErrorStr);
                // 更新任务状态为失败
                if (taskId != null) {
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskId)
                        .set(SchTaskInfo::getFailReason, "Docker网络清理失败: " + pruneErrorStr)
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus, TaskStatusType.FAILED)
                    );
                }
                pruneChannel.disconnect();
                throw new MyRuntimeException("Docker网络清理失败: " + pruneErrorStr);
            }
            
            pruneChannel.disconnect();
            
            // 添加延迟，确保网络清理完全生效
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 重新执行原始命令
            log.info("重新执行Docker Compose命令: {}", command);
            channel.disconnect();
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);
            inputStream = channel.getInputStream();
            errorStream = channel.getErrStream();
            channel.connect();
            
            // 重新读取输出
            output = new StringBuilder();
            errorOutput = new StringBuilder();
            
            while (true) {
                // 读取标准输出
                while (inputStream.available() > 0) {
                    int len = inputStream.read(buffer);
                    if (len < 0) break;
                    output.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
                }

                // 读取错误输出
                while (errorStream.available() > 0) {
                    int len = errorStream.read(buffer);
                    if (len < 0) break;
                    errorOutput.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
                }

                // 检查命令是否执行完成
                if (channel.isClosed()) {
                    if (inputStream.available() > 0 || errorStream.available() > 0) {
                        continue;
                    }
                    break;
                }

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 获取新的输出和退出状态
            result = output.toString();
            error = errorOutput.toString();
            exitStatus = channel.getExitStatus();
            
            // 检查清理后的命令是否仍然有网络池错误
            if (exitStatus != 0 && (error.contains("could not find an available, non-overlapping IPv4 address pool") ||
                error.contains("failed to allocate gateway") ||
                error.contains("pool overlaps with other one on this address space"))) {
                log.error("网络清理后仍然存在网络地址池问题，可能需要重启Docker服务");
                // 更新任务状态为失败
                if (taskId != null) {
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskId)
                        .set(SchTaskInfo::getFailReason, "网络清理后仍然存在网络地址池问题，可能需要重启Docker服务")
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus, TaskStatusType.FAILED)
                    );
                }
                throw new MyRuntimeException("网络清理后仍然存在网络地址池问题，可能需要重启Docker服务");
            }
        }
        
        // 2. 正常执行结果判定操作
        if (exitStatus != 0) {
            log.error("Command failed with exit status: {}", exitStatus);
            // 过滤掉常见的警告信息
            String filteredError = Arrays.stream(error.split("\n"))
                .filter(line -> !line.contains("Error loading config file: /root/.docker/config.json"))
                .filter(line -> !line.contains("Invalid auth configuration file"))
                .collect(Collectors.joining("\n"));
            
            if (filteredError.trim().isEmpty() && !result.trim().isEmpty()) {
                // 如果错误流为空但标准输出不为空，可能错误信息在标准输出中
                filteredError = result;
            }
            
            // 更新任务状态为失败
            if (taskId != null) {
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getFailReason, filteredError.isEmpty() ? "命令执行失败，退出状态: " + exitStatus : filteredError)
                    .set(SchTaskInfo::getUpdateTime, new Date())
                    .set(SchTaskInfo::getStatus, TaskStatusType.FAILED)
                );
            }
            
            throw new MyRuntimeException("命令执行失败: " + (filteredError.isEmpty() ? "退出状态: " + exitStatus : filteredError));
        } else if (!error.isEmpty()) {
            // 命令成功执行但有错误输出，过滤掉常见警告
            String filteredError = Arrays.stream(error.split("\n"))
                .filter(line -> !line.contains("Error loading config file: /root/.docker/config.json"))
                .filter(line -> !line.contains("Invalid auth configuration file"))
                .collect(Collectors.joining("\n"));
            
            // 检查过滤后是否还有错误信息
            if (!filteredError.trim().isEmpty()) {
                log.warn("Command succeeded but with warnings: {}", filteredError);
                // 检查是否是插值格式错误并提供帮助信息
                if (filteredError.contains("invalid interpolation format") && 
                    filteredError.contains("You may need to escape any $ with another $")) {
                    log.info("检测到$插值错误，请考虑在命令字符串中将$转义为$$");
                    // 更新任务状态为失败，因为插值错误通常会导致容器无法正常启动
                    if (taskId != null) {
                        schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getFailReason, filteredError)
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, TaskStatusType.FAILED)
                        );
                    }
                    
                    throw new MyRuntimeException("命令执行失败: " + filteredError);
                }
            } else {
                log.info("命令只产生了警告（已忽略）: {}", error);
            }
        }
        
        channel.disconnect();
        return result;
    }

    /**
     * 测试连接。
     *
     * @param host     服务器IP
     * @param port     SSH端口
     * @param username 用户名
     * @param password 密码
     * @param timeout  超时时间（毫秒）
     * @return 连接是否成功
     */
    public boolean testConnection(String host, int port, String username, String password, int timeout) {
        SshConnection connection = null;
        try {
            // 直接传递加密密码，让createConnection内部处理解密
            connection = this.createConnection(host, port, username, password, timeout);
            return true;
        } catch (Exception e) {
            log.warn("SSH连接测试失败: {}@{}:{}, 错误: {}", username, host, port, e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 使用ping命令测试网络连通性
     * @param hostIp 主机IP
     * @return 连通性结果
     */
    public boolean testPingConnection(String hostIp) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String pingCmd;

            if (os.contains("win")) {
                // Windows系统
                pingCmd = "ping -n 3 " + hostIp;
            } else {
                // Linux/Unix系统
                pingCmd = "ping -c 3 " + hostIp;
            }

            Process process = Runtime.getRuntime().exec(pingCmd);
            int exitCode = process.waitFor();

            return exitCode == 0;
        } catch (Exception e) {
            log.error("Ping测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用Socket测试telnet连接
     * @param hostIp 主机IP
     * @param port 端口号
     * @param timeout 超时时间(毫秒)
     * @return 连接结果
     */
    public boolean testTelnetConnection(String hostIp, Integer port, int timeout) {
        try (java.net.Socket socket = new java.net.Socket()) {
            socket.connect(new java.net.InetSocketAddress(hostIp, port), timeout);
            return socket.isConnected();
        } catch (Exception e) {
            log.debug("Telnet连接测试失败 {}:{} - {}", hostIp, port, e.getMessage());
            return false;
        }
    }

    /**
     * 测试连接（私钥认证）。
     *
     * @param host           服务器IP
     * @param port           SSH端口
     * @param username       用户名
     * @param privateKeyPath 私钥路径
     * @param timeout        超时时间（毫秒）
     * @return 连接是否成功
     */
    public boolean testConnectionWithPrivateKey(String host, int port, String username, 
                                              String privateKeyPath, int timeout) {
        SshConnection connection = null;
        try {
            connection = createConnectionWithPrivateKey(host, port, username, privateKeyPath, timeout);
            return true;
        } catch (Exception e) {
            log.warn("SSH连接测试失败(私钥): {}@{}:{}, 错误: {}", username, host, port, e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }
} 