package supie.webadmin.app.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import supie.common.core.exception.MyRuntimeException;
import supie.webadmin.app.dao.SchVirtualComputeCardTemplateMapper;
import supie.webadmin.app.model.SchComputeDevice;
import supie.webadmin.app.model.SchResourceInfo;
import supie.webadmin.app.model.SchVirtualComputeCardTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * VNPU管理工具类
 * 基于华为昇腾NPU虚拟化技术实现虚拟计算卡的管理
 * 支持创建、销毁、查看VNPU等功能
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VnpuManagementUtil {

    private final SshUtil sshUtil;
    private final SchVirtualComputeCardTemplateMapper templateMapper;

    /**
     * VNPU管理结果
     */
    public static class VnpuResult {
        private final boolean success;
        private final String message;
        private final Object data;

        private VnpuResult(boolean success, String message, Object data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

        public static VnpuResult success(String message) {
            return new VnpuResult(true, message, null);
        }

        public static VnpuResult success(String message, Object data) {
            return new VnpuResult(true, message, data);
        }

        public static VnpuResult failure(String message) {
            return new VnpuResult(false, message, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Object getData() { return data; }
    }

    /**
     * VNPU配置信息
     */
    public static class VnpuConfig {
        private final Integer deviceNumber;
        private final Integer chipId;
        private final Integer vnpuId;
        private final Integer vgroupId;
        private final String templateName;

        public VnpuConfig(Integer deviceNumber, Integer chipId, Integer vnpuId, 
                         Integer vgroupId, String templateName) {
            this.deviceNumber = deviceNumber;
            this.chipId = chipId;
            this.vnpuId = vnpuId;
            this.vgroupId = vgroupId;
            this.templateName = templateName;
        }

        // Getters
        public Integer getDeviceNumber() { return deviceNumber; }
        public Integer getChipId() { return chipId; }
        public Integer getVnpuId() { return vnpuId; }
        public Integer getVgroupId() { return vgroupId; }
        public String getTemplateName() { return templateName; }
    }

    /**
     * 查看VNPU详情
     *
     * @param computeDevice 计算设备
     * @param resourceInfo 资源信息
     * @return VNPU详情列表
     */
    public VnpuResult viewVnpuDetails(SchComputeDevice computeDevice, SchResourceInfo resourceInfo) {
        try {
            // 参数验证
            validateDeviceAndResource(computeDevice, resourceInfo);

            // 构建查看命令
            String viewCommand = buildViewCommand(computeDevice);
            log.debug("查看VNPU命令: {}", viewCommand);

            // 执行命令
            CommandExecutionResult result = executeRemoteCommand(resourceInfo, viewCommand);
            if (result.getExitCode() != 0) {
                return VnpuResult.failure("查看VNPU失败: " + result.getStdOutput());
            }

            // 解析结果
            List<Map<String, String>> vnpuList = parseVnpuDetails(result.getStdOutput());
            
            // 增强VNPU信息（添加模板详情）
            enhanceVnpuInfoWithTemplate(vnpuList);

            log.debug("解析返回结果: {}", JSON.toJSONString(vnpuList));
            return VnpuResult.success("查看VNPU成功", vnpuList);

        } catch (Exception e) {
            log.error("查看VNPU详情失败", e);
            return VnpuResult.failure("查看VNPU详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建VNPU
     *
     * @param computeDevice 计算设备
     * @param resourceInfo 资源信息
     * @param template 虚拟计算卡模板
     * @return 创建结果
     */
    public VnpuResult createVnpu(SchComputeDevice computeDevice, SchResourceInfo resourceInfo, 
                                SchVirtualComputeCardTemplate template) {
        try {
            // 参数验证
            validateDeviceAndResource(computeDevice, resourceInfo);
            validateTemplate(template);
            validateResourceStatus(resourceInfo, "active", "当前资源状态不支持切分");

            // 检查资源可用性
            VnpuResult availabilityCheck = checkResourceAvailability(computeDevice, resourceInfo, template);
            if (!availabilityCheck.isSuccess()) {
                return availabilityCheck;
            }

            // 计算VNPU配置
            VnpuConfig config = calculateVnpuConfig(computeDevice, resourceInfo, template);
            if (config == null) {
                return VnpuResult.failure("计算VNPU配置失败，可能是ID范围已满");
            }

            // 构建创建命令
            String createCommand = buildCreateCommand(config);
            log.debug("创建VNPU命令: {}", createCommand);

            // 执行命令
            CommandExecutionResult result = executeRemoteCommand(resourceInfo, createCommand);
            if (result.getExitCode() != 0) {
                return VnpuResult.failure("创建VNPU失败: " + result.getStdOutput());
            }

            // 解析执行结果
            VnpuOperationStatus status = parseOperationResult(result.getStdOutput());
            if (!status.isSuccess()) {
                return VnpuResult.failure("创建VNPU失败: " + status.getMessage());
            }

            return VnpuResult.success(status.getMessage(), config);

        } catch (Exception e) {
            log.error("创建VNPU失败", e);
            return VnpuResult.failure("创建VNPU失败: " + e.getMessage());
        }
    }

    /**
     * 销毁VNPU
     *
     * @param computeDevice 计算设备
     * @param resourceInfo 资源信息
     * @param vnpuId VNPU ID
     * @return 销毁结果
     */
    public VnpuResult destroyVnpu(SchComputeDevice computeDevice, SchResourceInfo resourceInfo, Integer vnpuId) {
        try {
            // 参数验证
            validateDeviceAndResource(computeDevice, resourceInfo);
            validateResourceStatus(resourceInfo, "active", "当前资源状态不支持销毁");
            
            if (vnpuId == null) {
                return VnpuResult.failure("VNPU ID不能为空");
            }

            // 构建销毁命令
            String destroyCommand = buildDestroyCommand(computeDevice, vnpuId);
            log.debug("销毁VNPU命令: {}", destroyCommand);

            // 执行命令
            CommandExecutionResult result = executeRemoteCommand(resourceInfo, destroyCommand);
            if (result.getExitCode() != 0) {
                return VnpuResult.failure("销毁VNPU失败: " + result.getStdError());
            }

            // 解析执行结果
            VnpuOperationStatus status = parseOperationResult(result.getStdOutput());
            if (!status.isSuccess()) {
                return VnpuResult.failure("销毁VNPU失败: " + status.getMessage());
            }

            return VnpuResult.success(status.getMessage());

        } catch (Exception e) {
            log.error("销毁VNPU失败", e);
            return VnpuResult.failure("销毁VNPU失败: " + e.getMessage());
        }
    }

    /**
     * 验证设备和资源信息
     */
    private void validateDeviceAndResource(SchComputeDevice computeDevice, SchResourceInfo resourceInfo) {
        if (computeDevice == null) {
            throw new MyRuntimeException("计算设备信息不能为空");
        }
        if (computeDevice.getDeviceNumber() == null) {
            throw new MyRuntimeException("设备编号不能为空");
        }
        if (resourceInfo == null) {
            throw new MyRuntimeException("资源信息不能为空");
        }
    }

    /**
     * 验证模板信息
     */
    private void validateTemplate(SchVirtualComputeCardTemplate template) {
        if (template == null) {
            throw new MyRuntimeException("虚拟计算卡模板不能为空");
        }
        if (template.getMemory() == null || template.getMemory() <= 0) {
            throw new MyRuntimeException("模板内存配置无效");
        }
    }

    /**
     * 验证资源状态
     */
    private void validateResourceStatus(SchResourceInfo resourceInfo, String requiredStatus, String errorMessage) {
        if (!requiredStatus.equals(resourceInfo.getStatus())) {
            throw new MyRuntimeException(errorMessage);
        }
    }

    /**
     * 检查资源可用性
     */
    private VnpuResult checkResourceAvailability(SchComputeDevice computeDevice, SchResourceInfo resourceInfo, 
                                                SchVirtualComputeCardTemplate template) {
        // 获取当前VNPU使用情况
        VnpuResult currentVnpus = viewVnpuDetails(computeDevice, resourceInfo);
        if (!currentVnpus.isSuccess()) {
            return VnpuResult.failure("无法获取当前VNPU状态: " + currentVnpus.getMessage());
        }

        @SuppressWarnings("unchecked")
        List<Map<String, String>> vnpuList = (List<Map<String, String>>) currentVnpus.getData();
        
        // 计算已使用内存
        int usedMemory = calculateUsedMemory(vnpuList);
        
        // 计算剩余内存
        int totalMemory = Math.toIntExact(computeDevice.getMemorySize());
        int freeMemory = totalMemory - usedMemory;
        
        // 检查内存是否足够（模板内存单位为MB，需要转换为MB进行比较）
        if (freeMemory < template.getMemory()) {
            return VnpuResult.failure(String.format("创建VNPU失败，剩余显存不足！当前剩余: %dMB，需要: %dMB", 
                                                   freeMemory, template.getMemory()));
        }

        return VnpuResult.success("资源检查通过");
    }

    /**
     * 计算已使用内存
     */
    private int calculateUsedMemory(List<Map<String, String>> vnpuList) {
        if (CollUtil.isEmpty(vnpuList)) {
            return 0;
        }

        List<String> templateNames = vnpuList.stream()
                .map(map -> map.get("Template Name"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(templateNames)) {
            return 0;
        }

        List<SchVirtualComputeCardTemplate> templates = templateMapper.selectList(
                new LambdaQueryWrapper<SchVirtualComputeCardTemplate>()
                        .in(SchVirtualComputeCardTemplate::getExample, templateNames)
        );

        return templates.stream()
                .mapToInt(SchVirtualComputeCardTemplate::getMemory)
                .sum();
    }

    /**
     * 计算VNPU配置
     */
    private VnpuConfig calculateVnpuConfig(SchComputeDevice computeDevice, SchResourceInfo resourceInfo,
                                          SchVirtualComputeCardTemplate template) {
        Integer deviceNumber = computeDevice.getDeviceNumber();
        Integer chipId = 0; // 默认为0
        
        // 获取当前已使用的VNPU ID和VGroup ID
        VnpuResult currentResult = viewVnpuDetails(computeDevice, resourceInfo);
        List<Integer> usedVnpuIds = new ArrayList<>();
        List<String> usedVgroupIds = new ArrayList<>();
        
        if (currentResult.isSuccess()) {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> vnpuList = (List<Map<String, String>>) currentResult.getData();
            usedVnpuIds = vnpuList.stream()
                    .map(map -> Integer.parseInt(map.get("Vnpu ID")))
                    .collect(Collectors.toList());
            usedVgroupIds = vnpuList.stream()
                    .map(map -> map.get("Vgroup ID"))
                    .collect(Collectors.toList());
        }

        // 根据产品类型计算ID范围和配置
        if (template.getProductType() == 1) {
            // 产品类型1：推理类产品
            int startId = deviceNumber * 16 + 100;
            int endId = deviceNumber * 16 + 115;
            
            Integer vnpuId = findAvailableId(usedVnpuIds, startId, endId);
            if (vnpuId == null) {
                return null;
            }
            
            return new VnpuConfig(deviceNumber, chipId, vnpuId, null, template.getExample());
        } else {
            // 其他产品类型：需要VGroup
            int startId = deviceNumber * 16 + 100;
            int endId = deviceNumber * 16 + 107;
            
            Integer vnpuId = findAvailableId(usedVnpuIds, startId, endId);
            if (vnpuId == null) {
                return null;
            }
            
            Integer vgroupId = findAvailableVgroupId(usedVgroupIds);
            if (vgroupId == null) {
                return null;
            }
            
            return new VnpuConfig(deviceNumber, chipId, vnpuId, vgroupId, template.getExample());
        }
    }

    /**
     * 查找可用的ID
     */
    private Integer findAvailableId(List<Integer> usedIds, int startId, int endId) {
        for (int i = startId; i <= endId; i++) {
            if (!usedIds.contains(i)) {
                return i;
            }
        }
        return null;
    }

    /**
     * 查找可用的VGroup ID
     */
    private Integer findAvailableVgroupId(List<String> usedVgroupIds) {
        for (int i = 0; i <= 3; i++) {
            if (!usedVgroupIds.contains(String.valueOf(i))) {
                return i;
            }
        }
        return null;
    }

    /**
     * 构建查看命令
     */
    private String buildViewCommand(SchComputeDevice computeDevice) {
        return String.format("npu-smi info -t info-vnpu -i %d -c %d", 
                           computeDevice.getDeviceNumber(), 0);
    }

    /**
     * 构建创建命令
     */
    private String buildCreateCommand(VnpuConfig config) {
        StringBuilder command = new StringBuilder();
        command.append("npu-smi set -t create-vnpu")
               .append(" -i ").append(config.getDeviceNumber())
               .append(" -c ").append(config.getChipId())
               .append(" -f ").append(config.getTemplateName())
               .append(" -v ").append(config.getVnpuId());
        
        if (config.getVgroupId() != null) {
            command.append(" -g ").append(config.getVgroupId());
        }
        
        return command.toString();
    }

    /**
     * 构建销毁命令
     */
    private String buildDestroyCommand(SchComputeDevice computeDevice, Integer vnpuId) {
        return String.format("npu-smi set -t destroy-vnpu -i %d -c %d -v %d", 
                           computeDevice.getDeviceNumber(), 0, vnpuId);
    }

    /**
     * 执行远程命令
     */
    private CommandExecutionResult executeRemoteCommand(SchResourceInfo resourceInfo, String command) {
        // 注意：这里假设所有服务器都是远程服务器
        // 在实际使用中可以根据 resourceInfo.getIsMasterHost() 来判断是否为本地服务器
        return sshUtil.executionCommand(resourceInfo.getId(), command);
    }

    /**
     * 增强VNPU信息（添加模板详情）
     */
    private void enhanceVnpuInfoWithTemplate(List<Map<String, String>> vnpuList) {
        for (Map<String, String> vnpuInfo : vnpuList) {
            String templateName = vnpuInfo.get("Template Name");
            if (templateName != null) {
                SchVirtualComputeCardTemplate template = templateMapper.selectOne(
                        new LambdaQueryWrapper<SchVirtualComputeCardTemplate>()
                                .eq(SchVirtualComputeCardTemplate::getExample, templateName)
                );
                if (template != null) {
                    vnpuInfo.put("Template", JSONObject.toJSONString(template));
                }
            }
        }
    }

    /**
     * 解析VNPU详情
     */
    private List<Map<String, String>> parseVnpuDetails(String output) {
        List<Map<String, String>> result = new ArrayList<>();
        
        if (output == null || output.trim().isEmpty()) {
            return result;
        }

        String[] lines = output.split("\n");

        // 查找表头行
        String headerLine = null;
        for (String line : lines) {
            if (line.trim().startsWith("|") && line.contains("Vnpu ID")) {
                headerLine = line.trim();
                break;
            }
        }

        if (headerLine == null) {
            log.warn("未找到VNPU表头行，可能没有VNPU或输出格式不正确");
            return result;
        }

        // 解析表头
        String[] headers = Arrays.stream(headerLine.split("\\|"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toArray(String[]::new);

        // 解析数据行
        for (String line : lines) {
            if (line.trim().startsWith("|") && !line.trim().equals(headerLine)) {
                String[] values = Arrays.stream(line.split("\\|"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .toArray(String[]::new);

                if (values.length == headers.length) {
                    Map<String, String> row = new HashMap<>();
                    for (int i = 0; i < headers.length; i++) {
                        row.put(headers[i], values[i]);
                    }
                    result.add(row);
                }
            }
        }

        return result;
    }

    /**
     * 解析操作结果
     */
    private VnpuOperationStatus parseOperationResult(String output) {
        if (output == null || output.trim().isEmpty()) {
            return new VnpuOperationStatus(false, "操作结果为空");
        }

        String[] lines = output.split("\n");
        String status = "";
        String message = "";

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("Status")) {
                String[] parts = line.split(":", 2);
                if (parts.length > 1) {
                    status = parts[1].trim();
                }
            } else if (line.startsWith("Message")) {
                String[] parts = line.split(":", 2);
                if (parts.length > 1) {
                    message = parts[1].trim();
                }
            }
        }

        boolean success = "OK".equals(status);
        return new VnpuOperationStatus(success, message);
    }

    /**
     * VNPU操作状态
     */
    private static class VnpuOperationStatus {
        private final boolean success;
        private final String message;

        public VnpuOperationStatus(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }
} 