<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchVirtualComputeCardTemplateMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchVirtualComputeCardTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="example" jdbcType="VARCHAR" property="example"/>
        <result column="ai_core_number" jdbcType="INTEGER" property="aiCoreNumber"/>
        <result column="memory" jdbcType="INTEGER" property="memory"/>
        <result column="ai_cpu" jdbcType="INTEGER" property="aiCpu"/>
        <result column="vpc" jdbcType="INTEGER" property="vpc"/>
        <result column="vdec" jdbcType="INTEGER" property="vdec"/>
        <result column="jpegd" jdbcType="INTEGER" property="jpegd"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_virtual_compute_card_template
            (id,
            str_id,
            is_delete,
            create_user_id,
            create_time,
            update_user_id,
            update_time,
            data_user_id,
            data_dept_id,
            product_type,
            example,
            ai_core_number,
            memory,
            ai_cpu,
            vpc,
            vdec,
            jpegd)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.isDelete},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.productType},
            #{item.example},
            #{item.aiCoreNumber},
            #{item.memory},
            #{item.aiCpu},
            #{item.vpc},
            #{item.vdec},
            #{item.jpegd})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchVirtualComputeCardTemplateMapper.inputFilterRef"/>
        AND sch_virtual_compute_card_template.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schVirtualComputeCardTemplateFilter != null">
            <if test="schVirtualComputeCardTemplateFilter.id != null">
                AND sch_virtual_compute_card_template.id = #{schVirtualComputeCardTemplateFilter.id}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.strId != null and schVirtualComputeCardTemplateFilter.strId != ''">
                <bind name = "safeSchVirtualComputeCardTemplateStrId" value = "'%' + schVirtualComputeCardTemplateFilter.strId + '%'" />
                AND sch_virtual_compute_card_template.str_id LIKE #{safeSchVirtualComputeCardTemplateStrId}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.createUserId != null">
                AND sch_virtual_compute_card_template.create_user_id = #{schVirtualComputeCardTemplateFilter.createUserId}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.createTimeStart != null and schVirtualComputeCardTemplateFilter.createTimeStart != ''">
                AND sch_virtual_compute_card_template.create_time &gt;= #{schVirtualComputeCardTemplateFilter.createTimeStart}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.createTimeEnd != null and schVirtualComputeCardTemplateFilter.createTimeEnd != ''">
                AND sch_virtual_compute_card_template.create_time &lt;= #{schVirtualComputeCardTemplateFilter.createTimeEnd}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.updateUserId != null">
                AND sch_virtual_compute_card_template.update_user_id = #{schVirtualComputeCardTemplateFilter.updateUserId}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.updateTimeStart != null and schVirtualComputeCardTemplateFilter.updateTimeStart != ''">
                AND sch_virtual_compute_card_template.update_time &gt;= #{schVirtualComputeCardTemplateFilter.updateTimeStart}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.updateTimeEnd != null and schVirtualComputeCardTemplateFilter.updateTimeEnd != ''">
                AND sch_virtual_compute_card_template.update_time &lt;= #{schVirtualComputeCardTemplateFilter.updateTimeEnd}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.dataUserId != null">
                AND sch_virtual_compute_card_template.data_user_id = #{schVirtualComputeCardTemplateFilter.dataUserId}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.dataDeptId != null">
                AND sch_virtual_compute_card_template.data_dept_id = #{schVirtualComputeCardTemplateFilter.dataDeptId}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.productType != null">
                AND sch_virtual_compute_card_template.product_type = #{schVirtualComputeCardTemplateFilter.productType}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.example != null and schVirtualComputeCardTemplateFilter.example != ''">
                <bind name = "safeSchVirtualComputeCardTemplateExample" value = "'%' + schVirtualComputeCardTemplateFilter.example + '%'" />
                AND sch_virtual_compute_card_template.example LIKE #{safeSchVirtualComputeCardTemplateExample}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.aiCoreNumber != null">
                AND sch_virtual_compute_card_template.ai_core_number = #{schVirtualComputeCardTemplateFilter.aiCoreNumber}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.memory != null ">
                AND sch_virtual_compute_card_template.memory = #{schVirtualComputeCardTemplateFilter.memory}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.aiCpu != null">
                AND sch_virtual_compute_card_template.ai_cpu = #{schVirtualComputeCardTemplateFilter.aiCpu}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.vpc != null">
                AND sch_virtual_compute_card_template.vpc = #{schVirtualComputeCardTemplateFilter.vpc}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.vdec != null">
                AND sch_virtual_compute_card_template.vdec = #{schVirtualComputeCardTemplateFilter.vdec}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.jpegd != null">
                AND sch_virtual_compute_card_template.jpegd = #{schVirtualComputeCardTemplateFilter.jpegd}
            </if>
            <if test="schVirtualComputeCardTemplateFilter.searchString != null and schVirtualComputeCardTemplateFilter.searchString != ''">
                <bind name = "safeSchVirtualComputeCardTemplateSearchString" value = "'%' + schVirtualComputeCardTemplateFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_virtual_compute_card_template.str_id,''), IFNULL(sch_virtual_compute_card_template.example,'')) LIKE #{safeSchVirtualComputeCardTemplateSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchVirtualComputeCardTemplateList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchVirtualComputeCardTemplate">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_virtual_compute_card_template
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_virtual_compute_card_template
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchVirtualComputeCardTemplateList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchVirtualComputeCardTemplate">
        SELECT * FROM sch_virtual_compute_card_template
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
