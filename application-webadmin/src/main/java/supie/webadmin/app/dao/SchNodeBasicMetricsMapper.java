package supie.webadmin.app.dao;

import org.apache.ibatis.annotations.MapKey;
import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchNodeBasicMetrics;
import org.apache.ibatis.annotations.Param;
import supie.webadmin.app.util.HardwareMonitoringData;

import java.util.*;

/**
 * 节点基础监控表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchNodeBasicMetricsMapper extends BaseDaoMapper<SchNodeBasicMetrics> {

    /**
     * 批量插入对象列表。
     *
     * @param schNodeBasicMetricsList 新增对象列表。
     */
    void insertList(List<SchNodeBasicMetrics> schNodeBasicMetricsList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schNodeBasicMetricsFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchNodeBasicMetrics> getGroupedSchNodeBasicMetricsList(
            @Param("schNodeBasicMetricsFilter") SchNodeBasicMetrics schNodeBasicMetricsFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schNodeBasicMetricsFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchNodeBasicMetrics> getSchNodeBasicMetricsList(
            @Param("schNodeBasicMetricsFilter") SchNodeBasicMetrics schNodeBasicMetricsFilter, @Param("orderBy") String orderBy);
    /*
    *获取平均cpu使用率
    * */
    Double getAvgCpuUsage();

    /*
    * 获取新的节点监控信息
    * */
    @MapKey("resource_name")
    List<Map<String, Object>> getSchNodeBasicMetrics();

    SchNodeBasicMetrics getLatestData(Long resourceId);

    SchNodeBasicMetrics getCpuMonitoringByResourceId(Long resourceId,Date ts);

    List<SchNodeBasicMetrics> statisticalIndicators(@Param("schNodeBasicMetricsFilter") SchNodeBasicMetrics schNodeBasicMetricsFilter,List<Long> resourceIdList);

    List<SchNodeBasicMetrics> getSchNodeBasicMetricsListByTs(@Param("resourceIdList") List<Long> resourceIdList);
}
