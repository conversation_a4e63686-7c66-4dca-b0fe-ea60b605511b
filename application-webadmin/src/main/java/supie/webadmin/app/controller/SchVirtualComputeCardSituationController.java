package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.service.impl.SchComputeDeviceServiceImpl;
import supie.webadmin.app.util.ComputingPowerManagement;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * vpu情况表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "vpu情况表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schVirtualComputeCardSituation")
public class SchVirtualComputeCardSituationController {

    @Autowired
    private SchVirtualComputeCardSituationService schVirtualComputeCardSituationService;
    @Autowired
    private SchComputeDeviceServiceImpl schComputeDeviceService;

    /**
     * 新增vpu情况表数据。
     *
     * @param schVirtualComputeCardSituationDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schVirtualComputeCardSituationDto.id",
            "schVirtualComputeCardSituationDto.searchString",
            "schVirtualComputeCardSituationDto.createTimeStart",
            "schVirtualComputeCardSituationDto.createTimeEnd",
            "schVirtualComputeCardSituationDto.updateTimeStart",
            "schVirtualComputeCardSituationDto.updateTimeEnd"})
    @SaCheckPermission("schVirtualComputeCardSituation.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchVirtualComputeCardSituationDto schVirtualComputeCardSituationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schVirtualComputeCardSituationDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchVirtualComputeCardSituation schVirtualComputeCardSituation = MyModelUtil.copyTo(schVirtualComputeCardSituationDto, SchVirtualComputeCardSituation.class);
        schVirtualComputeCardSituation = schVirtualComputeCardSituationService.saveNew(schVirtualComputeCardSituation);
        return ResponseResult.success(schVirtualComputeCardSituation.getId());
    }

    /**
     * 更新vpu情况表数据。
     *
     * @param schVirtualComputeCardSituationDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schVirtualComputeCardSituationDto.searchString",
            "schVirtualComputeCardSituationDto.createTimeStart",
            "schVirtualComputeCardSituationDto.createTimeEnd",
            "schVirtualComputeCardSituationDto.updateTimeStart",
            "schVirtualComputeCardSituationDto.updateTimeEnd"})
    @SaCheckPermission("schVirtualComputeCardSituation.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchVirtualComputeCardSituationDto schVirtualComputeCardSituationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schVirtualComputeCardSituationDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchVirtualComputeCardSituation schVirtualComputeCardSituation = MyModelUtil.copyTo(schVirtualComputeCardSituationDto, SchVirtualComputeCardSituation.class);
        SchVirtualComputeCardSituation originalSchVirtualComputeCardSituation = schVirtualComputeCardSituationService.getById(schVirtualComputeCardSituation.getId());
        if (originalSchVirtualComputeCardSituation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schVirtualComputeCardSituationService.update(schVirtualComputeCardSituation, originalSchVirtualComputeCardSituation)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除vpu情况表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schVirtualComputeCardSituation.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除vpu情况表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schVirtualComputeCardSituation.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的vpu情况表列表。
     *
     * @param schVirtualComputeCardSituationDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schVirtualComputeCardSituation.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchVirtualComputeCardSituationVo>> list(
            @MyRequestBody SchVirtualComputeCardSituationDto schVirtualComputeCardSituationDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchVirtualComputeCardSituation schVirtualComputeCardSituationFilter = MyModelUtil.copyTo(schVirtualComputeCardSituationDtoFilter, SchVirtualComputeCardSituation.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchVirtualComputeCardSituation.class);
        List<SchVirtualComputeCardSituation> schVirtualComputeCardSituationList =
                schVirtualComputeCardSituationService.getSchVirtualComputeCardSituationListWithRelation(schVirtualComputeCardSituationFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schVirtualComputeCardSituationList, SchVirtualComputeCardSituationVo.class));
    }

    /**
     * 分组列出符合过滤条件的vpu情况表列表。
     *
     * @param schVirtualComputeCardSituationDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schVirtualComputeCardSituation.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchVirtualComputeCardSituationVo>> listWithGroup(
            @MyRequestBody SchVirtualComputeCardSituationDto schVirtualComputeCardSituationDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchVirtualComputeCardSituation.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchVirtualComputeCardSituation.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchVirtualComputeCardSituation filter = MyModelUtil.copyTo(schVirtualComputeCardSituationDtoFilter, SchVirtualComputeCardSituation.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchVirtualComputeCardSituation> resultList = schVirtualComputeCardSituationService.getGroupedSchVirtualComputeCardSituationListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchVirtualComputeCardSituationVo.class));
    }

    /**
     * 查看指定vpu情况表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schVirtualComputeCardSituation.view")
    @GetMapping("/view")
    public ResponseResult<SchVirtualComputeCardSituationVo> view(@RequestParam Long id) {
        SchVirtualComputeCardSituation schVirtualComputeCardSituation = schVirtualComputeCardSituationService.getByIdWithRelation(id, MyRelationParam.full());
        if (schVirtualComputeCardSituation == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchVirtualComputeCardSituationVo schVirtualComputeCardSituationVo = MyModelUtil.copyTo(schVirtualComputeCardSituation, SchVirtualComputeCardSituationVo.class);
        return ResponseResult.success(schVirtualComputeCardSituationVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchVirtualComputeCardSituation originalSchVirtualComputeCardSituation = schVirtualComputeCardSituationService.getById(id);
        if (originalSchVirtualComputeCardSituation == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schVirtualComputeCardSituationService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 创建vnpu
     *
     * @param schVirtualComputeCardSituationDto 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @PostMapping("/createVNPU")
    public ResponseResult<String> create(@MyRequestBody SchVirtualComputeCardSituationDto schVirtualComputeCardSituationDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schVirtualComputeCardSituationDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchVirtualComputeCardSituation schVirtualComputeCardSituation = MyModelUtil.copyTo(schVirtualComputeCardSituationDto, SchVirtualComputeCardSituation.class);
        String data = schVirtualComputeCardSituationService.create(schVirtualComputeCardSituation);
        return ResponseResult.success(data);
    }

    /**
     * 查看vnpu情况。
     *
     * @param schComputeDeviceId 待对象的主键Id列表。
     * @return 应答结果对象。
     */
    @PostMapping("/viewVNPU")
    public ResponseResult<List<Map<String, String>>> viewVNPU(@MyRequestBody Long schComputeDeviceId) {
        SchComputeDevice schComputeDevice = schComputeDeviceService.getById(schComputeDeviceId);
        List<Map<String, String>> data = schVirtualComputeCardSituationService.viewVNPU(schComputeDevice);
        return ResponseResult.success(data);
    }

    /**
     * 销毁vnpu。
     *
     * @param memorySegmentationSituationId 待对象的主键Id列表。
     * @return 应答结果对象。
     */
    @PostMapping("/deleteVNPU")
    public ResponseResult<String> deleteVNPU(@MyRequestBody Long memorySegmentationSituationId) {
        SchVirtualComputeCardSituation schVirtualComputeCardSituation = schVirtualComputeCardSituationService.getById(memorySegmentationSituationId);
        String data = schVirtualComputeCardSituationService.deleteVNPU(schVirtualComputeCardSituation);
        return ResponseResult.success(data);
    }
}
