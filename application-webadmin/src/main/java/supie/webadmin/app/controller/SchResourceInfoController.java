package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchComputeDeviceMapper;
import supie.webadmin.app.dao.SchResourceInfoMapper;
import supie.webadmin.app.dao.SchResourcePoolMemberMapper;
import supie.webadmin.app.service.impl.SchComputeDeviceServiceImpl;
import supie.webadmin.app.util.*;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 服务资源信息表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "服务资源信息表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schResourceInfo")
public class SchResourceInfoController {

    @Autowired
    private SchResourceInfoService schResourceInfoService;
    @Resource
    private SchComputeDeviceMapper schComputeDeviceMapper;

    @Resource
    private SchResourcePoolMemberMapper schResourcePoolMemberMapper;

    @Resource
    private  SshConnectionUtil sshConnectionUtil;


    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;

    @Operation(summary = "connectionTest",description = "服务器连接测试")
    @PostMapping("/connectionTest")
    public ResponseResult<Object> connectionTest(@MyRequestBody SchResourceInfoDto schResourceInfoDto){
        // 使用ping和telnet进行服务器连通性检测
        boolean pingResult = sshConnectionUtil.testPingConnection(schResourceInfoDto.getHostIp());
        boolean telnetResult = false;
        
        if (pingResult && schResourceInfoDto.getPort() != null && schResourceInfoDto.getPort() > 0) {
            telnetResult = sshConnectionUtil.testTelnetConnection(schResourceInfoDto.getHostIp(), schResourceInfoDto.getPort(), 5000);
        }
        
        // 如果ping成功，则认为服务器可达
        if(pingResult){
            String message = "服务器可达";
            if (schResourceInfoDto.getPort() != null && schResourceInfoDto.getPort() > 0) {
                message += telnetResult ? "，端口连通正常" : "，但端口连接失败";
            }
            
            if(!Objects.isNull(schResourceInfoDto.getId())){
                return ResponseResult.success(schResourceInfoMapper.queryMsg(schResourceInfoDto.getId()) + " - " + message);
            }else{
                return ResponseResult.success(message);
            }
        }
        return ResponseResult.error(ErrorCodeEnum.OPERATION_FAILED, "服务器不可达");
    }
    



    @Operation(summary = "verifyServerMsg",description = "服务器注册信息校验")
    @PostMapping("/verifyServerMsg")
    public ResponseResult<SchResourceInfoVo> verifyServerMsg(@MyRequestBody SchResourceInfoDto schResourceInfoDto){
        if (MyCommonUtil.existBlankArgument(schResourceInfoDto.getHostIp())) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "主机IP不能为空");
        }
        // 验证必要的连接参数
        if (MyCommonUtil.existBlankArgument( schResourceInfoDto.getLoginName())) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "用户名不能为空");
        }

        if (MyCommonUtil.existBlankArgument( schResourceInfoDto.getPassword())){
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "密码不能同时为空");
        }
        try {
            ResponseResult<SchResourceInfoVo> connectionResult = schResourceInfoService.verifyServerConnection(schResourceInfoDto);
            if (!connectionResult.isSuccess()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "服务器连接验证失败: " + connectionResult.getMessage());
            }
            return ResponseResult.success( connectionResult.getData());
        } catch (Exception e) {
            log.error("服务器验证和部署过程中发生异常: {}", e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.OPERATION_FAILED, "服务器验证和部署失败: " + e.getMessage());
        }
    }




    /**
     * 新增服务资源信息表数据。
     *
     * @param schResourceInfoDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourceInfoDto.id",
            "schResourceInfoDto.searchString",
            "schResourceInfoDto.updateTimeStart",
            "schResourceInfoDto.updateTimeEnd",
            "schResourceInfoDto.createTimeStart",
            "schResourceInfoDto.createTimeEnd"})
    @SaCheckPermission("schResourceInfo.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchResourceInfoDto schResourceInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourceInfoDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourceInfo schResourceInfo = MyModelUtil.copyTo(schResourceInfoDto, SchResourceInfo.class);
        schResourceInfo = schResourceInfoService.saveNew(schResourceInfo);
        return ResponseResult.success(schResourceInfo.getId());
    }

    /**
     * 更新服务资源信息表数据。
     *
     * @param schResourceInfoDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourceInfoDto.searchString",
            "schResourceInfoDto.updateTimeStart",
            "schResourceInfoDto.updateTimeEnd",
            "schResourceInfoDto.createTimeStart",
            "schResourceInfoDto.createTimeEnd"})
    @SaCheckPermission("schResourceInfo.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchResourceInfoDto schResourceInfoDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourceInfoDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourceInfo schResourceInfo = MyModelUtil.copyTo(schResourceInfoDto, SchResourceInfo.class);
        SchResourceInfo originalSchResourceInfo = schResourceInfoService.getById(schResourceInfo.getId());
        if (originalSchResourceInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourceInfoService.update(schResourceInfo, originalSchResourceInfo)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        // 更新绑定关系
        schResourceInfoService.updateBindRelation(schResourceInfoDto.getPoolId(),  schResourceInfo.getId());
        //更新卡信息
        LambdaQueryWrapper<SchComputeDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchComputeDevice::getResourceId, schResourceInfo.getId())
                .eq(SchComputeDevice::getIsDelete,1);
        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(queryWrapper);
       schComputeDeviceList.forEach(device -> {
           device.setCardType(schResourceInfoDto.getCardType());
           device.setUpdateTime(new Date());
           device.setUpdateUserId(schResourceInfoDto.getUpdateUserId());
           device.setProductType(schResourceInfoDto.getProductType());
           schComputeDeviceMapper.updateById(device);
       });
        return ResponseResult.success();
    }

    /**
     * 删除服务资源信息表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourceInfo.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除服务资源信息表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourceInfo.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的服务资源信息表列表。
     *
     * @param schResourceInfoDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourceInfo.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchResourceInfoVo>>
    list(
            @MyRequestBody SchResourceInfoDto schResourceInfoDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourceInfo schResourceInfoFilter = MyModelUtil.copyTo(schResourceInfoDtoFilter, SchResourceInfo.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourceInfo.class);
        List<SchResourceInfo> schResourceInfoList =
                schResourceInfoService.getSchResourceInfoListWithRelation(schResourceInfoFilter, orderBy);
        List<SchResourceInfoVo> schResourceInfoVoList = new ArrayList<>();
        schResourceInfoList.forEach(schResourceInfo -> {
            List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(new LambdaQueryWrapper<SchComputeDevice>()
                    .eq(SchComputeDevice::getResourceId, schResourceInfo.getId()));
            if(schComputeDeviceList.isEmpty()){
                return;
            }
            SchResourceInfoVo schResourceInfoVo = MyModelUtil.copyTo(schResourceInfo, SchResourceInfoVo.class);
            schResourceInfoVo.setCardType(schComputeDeviceList.get(0).getCardType());
            schResourceInfoVo.setProductType(schComputeDeviceList.get(0).getProductType());
            schResourceInfoVoList.add(schResourceInfoVo);
        });
        return ResponseResult.success(MyPageUtil.makeResponseData(schResourceInfoVoList));
    }

    /**
     * 分组列出符合过滤条件的服务资源信息表列表。
     *
     * @param schResourceInfoDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourceInfo.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchResourceInfoVo>> listWithGroup(
            @MyRequestBody SchResourceInfoDto schResourceInfoDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourceInfo.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchResourceInfo.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourceInfo filter = MyModelUtil.copyTo(schResourceInfoDtoFilter, SchResourceInfo.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchResourceInfo> resultList = schResourceInfoService.getGroupedSchResourceInfoListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchResourceInfoVo.class));
    }

    /**
     * 查看指定服务资源信息表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schResourceInfo.view")
    @GetMapping("/view")
    public ResponseResult<SchResourceInfoVo> view(@RequestParam Long id) {
        SchResourceInfo schResourceInfo = schResourceInfoService.getByIdWithRelation(id, MyRelationParam.full());
        List<SchCardMonitor> schCardMonitorInfoByResource = schResourceInfoService.getSchCardMonitorInfoByResource(schResourceInfo);
        if (schResourceInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourceInfoVo schResourceInfoVo = MyModelUtil.copyTo(schResourceInfo, SchResourceInfoVo.class);
        return ResponseResult.success(schResourceInfoVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchResourceInfo originalSchResourceInfo = schResourceInfoService.getById(id);
        if (originalSchResourceInfo == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourceInfoService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        //   关联关系删除
        schResourcePoolMemberMapper.deleteRelation(id);
        // 删除卡的关联关系
        //查出节点下所有的卡
        List<SchComputeDevice> deviceInfoList = schResourceInfoService.getDeviceInfoByResource(originalSchResourceInfo);
        List<Long> deviceIdList = deviceInfoList.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
        if(deviceInfoList == null){
            errorMessage = "计算卡id为空";
            log.info(errorMessage);
            return ResponseResult.success();
        }
        //根据卡id删除卡
        schComputeDeviceMapper.delete(new LambdaUpdateWrapper<SchComputeDevice>()
                .in(SchComputeDevice::getId,deviceIdList));
        return ResponseResult.success();
    }

    /**
     * 指标统计接口：统计资源总个数，全部cpu总核心数量，全部计算卡内存总数，以及资源的分配情况：空闲，占用，异常。
     *
     * @param
     * @return 应答结果map，包含统计结果详情。
     */
    @Operation(summary = "指标统计接口")
    @GetMapping("/getResourceStatistics")
    public ResponseResult<HashMap> resourceStatistic() {
        HashMap<String,Object> resourceStatistics= schResourceInfoService.getResourceStatistics();
//        if (resourceStatistics == null || resourceStatistics.isEmpty()) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
        return ResponseResult.success(resourceStatistics);
    }

    @Operation(summary = "agent检测")
    @PostMapping("/agentCheck")
    public ResponseResult<Map<String,  Object>> agentCheck(@MyRequestBody JSONObject agentConfig) {
        //String errorMessage = "";
        if(agentConfig == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        //调用方法，返回cpu，gpu，npu查询信息的信息
        Map<String, Object> resourceInfo = new HashMap<>();
        if(agentConfig.getString("cpuPort") != null && agentConfig.getString("cpuPort") != "" && agentConfig.getString("hostIp") != null && agentConfig.getString("hostIp") != ""){
            resourceInfo = schResourceInfoService.getResources(agentConfig);
        }
        //返回结果
        return ResponseResult.success(resourceInfo);
    }



    /*
     *最新历史记录信息查询以节点id查询
     * @param
     * @return
     * */
    @Operation(summary = "最新历史记录信息查询")
    @PostMapping("/agentHistoryCheck")
    public ResponseResult<List<String>> agentHistoryCheck(@MyRequestBody SchResourceInfoDto schResourceInfoDto) {
        if(schResourceInfoDto == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        //调用方法，返回节点，各个计算卡查询信息的信息
        SchResourceInfo schResourceInfo = MyModelUtil.copyTo(schResourceInfoDto, SchResourceInfo.class);
        List<String> resourceInfosHistory = schResourceInfoService.getHistoryInfos(schResourceInfo);

        return ResponseResult.success(resourceInfosHistory);
    }


    @Operation(summary = "新增节点和计算卡数据")
    @PostMapping("/upschResourchInfoWithComputeDeviceInfo")
    public ResponseResult<Void> upschResourchInfoWithComputeDeviceInfo(@MyRequestBody JSONObject agentConfig) {
        String errorMessage = "";
        if(agentConfig == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        //调用方法，返回cpu，gpu，npu查询信息的信息
        List<Map<String, Object>> resourceInfo = new ArrayList<>();
        //resourceInfo = schResourceInfoService.getResources(agentConfig);
        if(!resourceInfo.isEmpty()){
            //存储计算卡数据
            schResourceInfoService.saveComputeDeviceInfoV2(resourceInfo,agentConfig);
        }else {
            errorMessage = "获取计算卡数据为空，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST,errorMessage);
        }
        //返回结果
        return ResponseResult.success();
    }

    @Operation(summary = "根据节点id查询卡信息")
    @PostMapping("/getComputeDeviceInfoByNodeId")
    public ResponseResult<List<Map<String, Object>>> getComputeDeviceInfoByNodeId(@MyRequestBody Long id) {
        String errorMessage = "";
        if(id == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(id);
        if(schResourceInfo == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        //调用方法npu查询信息的信息
        List<Map<String, Object>> computeDeviceInfo = schResourceInfoService.getComputeDeviceInfoByNodeId(schResourceInfo);
        //返回结果
        return ResponseResult.success(computeDeviceInfo);
    }

    /**
     * 主机抽样情况
     *
     * @return 应答结果对象，包含对象详情。
     */
    @PostMapping("/hostSampling")
    public ResponseResult<Map<String, Object>> hostSampling(
            @MyRequestBody Long resourceInfo,
            @MyRequestBody String tsStart,
            @MyRequestBody String tsEnd,
            @MyRequestBody String type,
            @MyRequestBody Integer interval,
            @MyRequestBody Long resourcePoolId) {
//        if(resourceInfo ==null || tsStart ==null || tsEnd ==null || interval ==null){
//            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
//        }
        return ResponseResult.success(schResourceInfoService.statisticalIndicators(resourceInfo,tsStart,tsEnd,type,interval,resourcePoolId));
    }

    /**
     * 指标统计接口：任务总数，全部cpu总核心数量，全部计算卡内存总数，以及资源的分配情况：空闲，占用，异常。
     *
     * @param
     * @return 应答结果map，包含统计结果详情。
     */
    @Operation(summary = "资源总量查询")
    @GetMapping("/resourceTotal")
    public ResponseResult<Map<String, Object>> resourceTotal() {
        Map<String, Object> resourceStatistics=  schResourceInfoService.resourceTotal();
//        if (resourceStatistics == null || resourceStatistics.isEmpty()) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
        return ResponseResult.success(resourceStatistics);
    }


    /**
     * 主机监控情况
     *
     * @return 应答结果对象，包含对象详情。
     */
//    @PostMapping("/hostMonitoring")
//    public ResponseResult<List<Map<String,Object>>> hostMonitoring(
//            @MyRequestBody SchResourceInfoDto schResourceInfoDto,
//            @MyRequestBody MyOrderParam orderParam,
//            @MyRequestBody MyPageParam pageParam) {
//        if (pageParam != null) {
//            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
//        }
//        SchResourceInfo schResourceInfoFilter = MyModelUtil.copyTo(schResourceInfoDto, SchResourceInfo.class);
//        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourceInfo.class);
//        return ResponseResult.success(schResourceInfoService.monitoringMetrics(schResourceInfoFilter,orderBy));
//    }

    @Operation(summary = "根据id获取节点信息")//基础信息
    @PostMapping("/nodeInfo")
    public ResponseResult<Map<String, Object>> nodeInfo(@MyRequestBody Long schResourceInfoId) {
        if(schResourceInfoId == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schResourceInfoId);
        if(schResourceInfo == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        HashMap<String, Object> map = new HashMap<>();
        List<SchComputeDevice> deviceInfoByResource = schResourceInfoService.getDeviceInfoByResource(schResourceInfo);
        SchResourceInfoVo schResourceInfoVo = MyModelUtil.copyTo(schResourceInfo, SchResourceInfoVo.class);
        //MyModelUtil.
        map.put("schResourceInfo", schResourceInfoVo);
        map.put("deviceInfoList", deviceInfoByResource);
        return ResponseResult.success(map);
    }

    @Operation(summary = "节点实时监控数据")//实时监控数据
    @PostMapping("/ComputeDeviceInfos")
    public ResponseResult<SchResourceInfoVo> ComputeDeviceInfos(@MyRequestBody Long schResourceInfoId) {
        if(schResourceInfoId == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schResourceInfoId);
        if(schResourceInfo == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        List<SchComputeDevice> deviceInfoByResource = schResourceInfoService.getDeviceInfoByResource(schResourceInfo);
        List<SchCardMonitor> deviceMonitoring = schResourceInfoService.getSchCardMonitorInfoByResource(schResourceInfo);
        SchNodeBasicMetrics nodeBasicMetrics = schResourceInfoService.getSchNodeBasicMetricsInfoByResource(schResourceInfo);
//        if(deviceMonitoring == null || nodeBasicMetrics == null){
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
        schResourceInfo.setSchNodeBasicMetrics(nodeBasicMetrics);
        schResourceInfo.setSchCardMonitorList(deviceMonitoring);
        schResourceInfo.setSchComputeDeviceList(deviceInfoByResource);
        SchResourceInfoVo schResourceInfoVo = MyModelUtil.copyTo(schResourceInfo, SchResourceInfoVo.class);
        return ResponseResult.success(schResourceInfoVo);
    }
    @Operation(summary = "节点任务统计")//运行使用指标
    @PostMapping("/shResourceInfoTast")
    public ResponseResult<Map<String, Object>> shResourceInfoTast(@MyRequestBody Long schResourceInfoId) {
        if(schResourceInfoId==null){
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(schResourceInfoId);
        if(schResourceInfo == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(schResourceInfoService.getResourceTaskStats(schResourceInfo));
    }

    /**
     * 根据资源池id查找主机。
     *
     * @param poolId 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourceInfo.view")
    @PostMapping("/queryResourceInfoByPoolId")
    public ResponseResult<MyPageData<SchResourceInfoVo>>queryResourceInfoByPoolId(
            @MyRequestBody Long poolId,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        List<SchResourceInfo> schResourceInfoList =
                schResourceInfoService.getSchResourceInfoListByPoolId(poolId);
        return ResponseResult.success(MyPageUtil.makeResponseData(schResourceInfoList, SchResourceInfoVo.class));
    }
}
