package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.experimental.Accessors;
import supie.common.core.annotation.DeptFilterColumn;
import supie.common.core.annotation.RelationOneToOne;
import supie.common.core.annotation.UserFilterColumn;
import supie.common.core.util.MyCommonUtil;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 任务表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Accessors(chain = true)
@Data
@TableName(value = "sch_task_info")
public class SchTaskInfo {

    /**
     * 任务需要的资源类型 physical-物理卡|vnpu-显卡|non-不需要
     */
    @TableField(value = "need_resource")
    private  String needResource;


    /**
     * 远程任务调用数据json 对象
     */
    @TableField("remote_task_json")
    private  String remoteTaskJson;

    /**
     * 远程任务类型
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 容器id 定时检测任务时候使用
     */
    @TableField(exist = false)
    private String containerId;

    /**
     * 容器管理表主键id
     */
    @TableField(exist = false)
    private  Long containerMangerId;

    /**
     * 容器退出码(exited(0)容器正常退出 exited(非零) 容器异常 )
     */
    @TableField("exit_code")
    private  String exitCode;

    /**
     * 任务失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 扩缩容状态
     */
    @TableField(exist = false)
    private  String statusED ;

    /**
     * 扩容计划信息表
     */
    @TableField(exist = false)
    private SchScalePlan scalePlan;

    /**
     * 当前任务执行扩缩容计划ID
     */
    @TableField("scale_plan_id")
    private  Long scalePlanId;

    /**
     * 字典表主键id
     */
    @TableField("dict_id")
    private Long dictId;



    /**
     *  镜像表关联数据
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "taskImageId",
            slaveIdField ="id",
            slaveModelClass = SchTaskImage.class
    )
    private SchTaskImage  taskImage;

    /**
     * 构建资源池信息
     */
    @RelationOneToOne(
            masterIdField = "poolId",
            slaveIdField ="id",
            slaveModelClass = SchResourcePool.class
    )
    @TableField(exist = false)
    private  SchResourcePool schResourcePool;

    /**
     * 服务资源信息
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "resourceId",
            slaveIdField ="id",
            slaveModelClass = SchResourceInfo.class
    )
    private SchResourceInfo schResourceInfo;


    /**
     * 显卡信息
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "computeDeviceId",
            slaveIdField ="id",
            slaveModelClass = SchComputeDevice.class
    )
    private  SchComputeDevice schComputeDevice;

    /**
     *  卡切分信息
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "partitionId",
            slaveIdField ="id",
            slaveModelClass = SchVirtualComputeCardSituation.class
    )
    private SchVirtualComputeCardSituation partition;


    /**
     * 审批信息记录
     */
    @TableField(exist = false)
    @RelationOneToOne(
            masterIdField = "id",
            slaveIdField = "taskId",
            slaveModelClass = SchTaskApproval.class
    )
    private SchTaskApproval schTaskApproval;

    /**
     * 启动任务成功之后的容器名
     */
    @TableField("container_name")
    private String containerName;

    /**
     * 容器状态 容器健康状态(running运行中、 paused暂停、restarting重启中、 exited(非零) 容器运行异常 / exited (0)正常退出表示任务正常结束、dead死亡） fail 重启失败  restarting：容器正在重启过程中 created：容器已经被创建，但尚未启动
     */
    @TableField("container_status")
    private String containerStatus;

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 任务名称。
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务状态（（枚举：pending初始状态、queued任务排队中、starting启动中、[stopping停止中、running运行中、restarting重启中 异步操作返回描述]、stop任务已经暂停、finished任务已经完成、failed代表此次任务执行操作失败）
     */
    @TableField(value = "status")
    private String status;

    /**
     * 优先级（1-10,默认0）。
     */
    @TableField(value = "task_priority")
    private Integer taskPriority;

    /**
     * 所需显存（MB）。
     */
    @TableField(value = "graphic_needed_mb")
    private Integer graphicNeededMb;

    /**
     * 所需内存（MB）。
     */
    @TableField(value = "memory_needed_mb")
    private Integer memoryNeededMb;

    /**
     * 所需CPU核。
     */
    @TableField(value = "cpu_need")
    private String cpuNeed;

    /**
     * 指定使用的资源池（可为空，系统自动选择）。
     */
    @TableField(value = "pool_id")
    private Long poolId;

    /**
     * 资源ID(不选资源池的时候后端自动分配节点，选了池是该池下的某个资源ID，实际运行)。
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 镜像表id。
     */
    @TableField(value = "task_image_id")
    private Long taskImageId;

    /**
     * 计算卡ID(如果需要算力)。
     */
    @TableField(value = "compute_device_id")
    private String computeDeviceId;

    /**
     * 资源ID数组 可以是计算卡·ID 列表 vnpuID 列表
     *
     */
    @TableField(exist = false)
    private List<Long> resourceIds;

    /**
     * 资源切分ID。
     */
    @TableField(value = "partition_id")
    private String partitionId;

    /**
     * 执行的命令或脚本（由用户提供，如运行什么程序）。
     */
    @TableField(value = "run_command")
    private String runCommand;

    /**
     * 环境变量键。
     */
    @TableField(value = "env_config")
    private String envConfig;

    /**
     * 资源释放策略（1自动 2手动）。
     */
    @TableField(value = "release_policy")
    private Integer releasePolicy;

    /**
     * 任务开始时间。
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 任务结束时间。
     */
    @TableField(value = "end_tiem")
    private Date endTiem;

    /**
     * 预计执行时间（分钟）。
     */
    @TableField(value = "estimat_time")
    private Date estimatTime;

    /**
     * 调度策略（1优先级调度2短作业优先3紧急抢占）。
     */
    @TableField(value = "scheduling_policies")
    private Integer schedulingPolicies;

    /**
     * 审批状态（通过未通过）  pending待定审核、approved批准、rejected拒绝
     */
    @TableField(value = "approve_state")
    private String approveState;

    /**
     * 是否允许被抢占(1 是，-1 否)。
     */
    @TableField(value = "allow_preemption")
    private Integer allowPreemption;


    /**
     * cpu信息。
     */
    @TableField(exist = false)
    private SchNodeBasicMetrics schNodeBasicMetrics;

    /**
     * npu信息。
     */
    @TableField(exist = false)
    private SchCardMonitor schCardMonitor;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * startTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String startTimeStart;

    /**
     * startTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String startTimeEnd;

    /**
     * endTiem 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String endTiemStart;

    /**
     * endTiem 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String endTiemEnd;

    /**
     * estimatTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String estimatTimeStart;

    /**
     * estimatTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String estimatTimeEnd;

    /**
     * str_id / task_name / status / cpu_need / run_command / env_config / approve_state LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
