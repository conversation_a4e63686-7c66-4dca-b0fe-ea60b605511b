package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;
import supie.webadmin.app.service.SchTaskTemplateService;

import java.util.Date;

/**
 * 新建任务模板表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
@TableName(value = "sch_task_template")
public class SchTaskTemplate {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 模板名称。
     */
    @TableField(value = "template_name")
    private String templateName;

    /**
     * 模板描述。
     */
    @TableField(value = "template_desc")
    private String templateDesc;

    /**
     * 启动命令。
     */
    @TableField(value = "run_command")
    private String runCommand;

    /**
     * 环境变量键。
     */
    @TableField(value = "env_config")
    private String envConfig;

    /**
     * 任务模板配置
     */
    @TableField(value = "template_config_json")
    private String templateConfigJson;

    /**
     * 镜像表ID。
     */
    @TableField(value = "image_id")
    private Long imageId;

    /**
     * 镜像信息。
     */
    @RelationOneToOne(slaveIdField = "id",masterIdField = "imageId", slaveModelClass = SchTaskImage.class)
    @TableField(exist = false)
    private SchTaskImage schTaskImage;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * str_id / template_name / template_desc / run_command / env_config LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
