package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 容器管理实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Accessors(chain = true)
@Data
@TableName(value = "sch_container_manager")
@Component
public class SchContainerManager {

   /**
    * 镜像名称
    */
   @TableField("image_name")
   private  String imageName;


 /**
  * 容器内部挂载路径
  */
    @TableField("container_path")
    private String containerPath;
 /**
  * 需要资源类型(physical  vnpu)
  */
 @TableField("resource_type")
 private String resourceType;
 /**
  * 启动命令
  */
 @TableField("start_command")
 private String startCommand;
 /**
  * 环境变量
  */
 @TableField("environment")
 private String environment;
 /**
  * 镜像版本信息
  */
 @TableField("image_version")
 private String imageVersion;
 /**
  * 服务器挂载路径
  */
 @TableField("server_mount_path")
 private String serverMountPath;

    /**
     * 容器内部端口
     */
    @TableField("inner_port")
    private  String innerPort;

    /**
     * 暴露端口
     */
    @TableField("export_port")
    private  String exportPort;


    /**
     * pod_monitor_id。
     */
    @TableField("pod_monitor_id")
    private  Long podMonitorId;

    /**
     * 容器管理主键id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 集群节点id。
     */
    @TableField(value = "cluster_node_id")
    private Long clusterNodeId;

    /**
     * 任务镜像id。
     */
    @TableField(value = "task_image_id")
    private Long taskImageId;

    /**
     * 任务信息id。
     */
    @TableField(value = "task_info_id")
    private Long taskInfoId;

    /**
     * 容器名称。
     */
    @TableField(value = "container_name")
    private String containerName;

    /**
     * 容器状态(running stopped)。
     */
    @TableField(value = "container_status")
    private String containerStatus;

    /**
     * 容器id。
     */
    @TableField(value = "container_id")
    private String containerId;

    /**
     * 容器创建时间。
     */
    @TableField(value = "container_create_time")
    private Date containerCreateTime;

    /**
     * 容器运行时间(启动到结束时间)。
     */
    @TableField(value = "container_run_time")
    private String containerRunTime;

    /**
     * 容器操作(start stop restart)。
     */
    @TableField(value = "container_operation")
    private String containerOperation;

    /**
     * 容器操作时间。
     */
    @TableField(value = "container_operation_time")
    private Date containerOperationTime;

    /**
     * 字符编号。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * containerCreateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String containerCreateTimeStart;

    /**
     * containerCreateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String containerCreateTimeEnd;

    /**
     * containerOperationTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String containerOperationTimeStart;

    /**
     * containerOperationTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String containerOperationTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * container_name / container_status / container_id / container_run_time / container_operation LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
