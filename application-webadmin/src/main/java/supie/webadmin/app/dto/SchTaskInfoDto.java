package supie.webadmin.app.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import supie.webadmin.app.model.SchTaskInfo;

import java.util.Date;
import java.util.List;

/**
 * 任务表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "任务表Dto对象")
@Data
public class SchTaskInfoDto {
    /**
     * 需要的资源 NPU  物理卡
     */
    @Schema(description = "需要的资源(physical-物理卡|vnpu-显卡|non-不需要)",example = "physical")
    @NotNull(message = "数据验证失败，需要的资源 NPU  物理卡不能为空！")
    private  String needResource;


    /**
     * 字典表主键id
     */
    @Schema(description = "字典表主键id")
    private Long dictId;


    /**
     * 抢占信息列表
     */
    @Schema(description = "抢占信息列表")
    private List<Long> occupyList;



    /**
     * 容器状态 容器健康状态(running运行中、 paused暂停、restarting重启中、 exited 异常退出 / exited (0)正常退出、dead死亡）
     */
    @Schema(description = "容器状态 容器健康状态(running运行中、 paused暂停、restarting重启中、 exited 异常退出 / exited (0)正常退出、dead死亡）")
    private String containerStatus;
    /**
     * 启动任务成功之后的容器名
     */
    @Schema(description = "启动任务成功之后的容器名")
    private String containerName;

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 更新时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新时间。可支持等于操作符的列表数据过滤。")
    private Date updateTime;

    /**
     * 创建时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建时间。可支持等于操作符的列表数据过滤。")
    private Date createTime;

    /**
     * 创建人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建人。可支持等于操作符的列表数据过滤。")
    private Long createUserId;

    /**
     * 更新人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新人。可支持等于操作符的列表数据过滤。")
    private Long updateUserId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 任务名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务名称。可支持等于操作符的列表数据过滤。")
    private String taskName;

    /**
     * 任务状态（（枚举：pending待审批、queued排队中、running运行中、finished完成、failed失败、cancelled取消、rejected已拒绝）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务状态（（枚举：pending待审批、queued排队中、running运行中、finished完成、failed失败、cancelled取消、rejected已拒绝）。可支持等于操作符的列表数据过滤。")
    private String status;

    /**
     * 优先级（1-10,默认0）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "优先级（1-10,默认0）。可支持等于操作符的列表数据过滤。")
    private Integer taskPriority;

    /**
     * 所需显存（MB）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "所需显存（MB）。可支持等于操作符的列表数据过滤。")
    private Integer graphicNeededMb;

    /**
     * 所需内存（MB）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "所需内存（MB）。可支持等于操作符的列表数据过滤。")
    private Integer memoryNeededMb;

    /**
     * 所需CPU核。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "所需CPU核。可支持等于操作符的列表数据过滤。")
    private String cpuNeed;

    /**
     * 指定使用的资源池（可为空，系统自动选择）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "指定使用的资源池（可为空，系统自动选择）。可支持等于操作符的列表数据过滤。")
    private Long poolId;

    /**
     * 资源ID(不选资源池的时候后端自动分配节点，选了池是该池下的某个资源ID，实际运行)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源ID(不选资源池的时候后端自动分配节点，选了池是该池下的某个资源ID，实际运行)。可支持等于操作符的列表数据过滤。")
    private Long resourceId;

    /**
     * 镜像表id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "镜像表id。可支持等于操作符的列表数据过滤。")
    private Long taskImageId;

    /**
     * 计算卡ID(如果需要算力)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "计算卡ID(如果需要算力)。可支持等于操作符的列表数据过滤。")
    private String computeDeviceId;


    /**
     * 资源ID数组 可以是计算卡·ID 列表 vnpuID 列表
     *
     */
    @Schema(description = "资源ID数组 可以是计算卡·ID 列表 vnpuID 计算列表")
    private List<Long> resourceIds;

    /**
     * 资源切分ID。  vnpuId VCC_ID 不是主键id
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "vnpuId VCC_ID 不是主键id")
    private String partitionId;

    /**
     * 执行的命令或脚本（由用户提供，如运行什么程序）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "执行的命令或脚本（由用户提供，如运行什么程序）。可支持等于操作符的列表数据过滤。")
    private String runCommand;

    /**
     * 环境变量键。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "环境变量键。可支持等于操作符的列表数据过滤。")
    private String envConfig;

    /**
     * 资源释放策略（1自动 2手动）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源释放策略（1自动 2手动）。可支持等于操作符的列表数据过滤。")
    private Integer releasePolicy;

    /**
     * 任务开始时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务开始时间。可支持等于操作符的列表数据过滤。")
    private Date startTime;

    /**
     * 任务结束时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务结束时间。可支持等于操作符的列表数据过滤。")
    private Date endTiem;

    /**
     * 预计执行时间（分钟）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "预计执行时间（分钟）。可支持等于操作符的列表数据过滤。")
    private Date estimatTime;

    /**
     * 调度策略（1优先级调度2短作业优先3紧急抢占）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "调度策略（1优先级调度2短作业优先3紧急抢占）。可支持等于操作符的列表数据过滤。")
    private Integer schedulingPolicies;

    /**
     * 当前任务执行扩缩容计划ID
     */
    @Schema(description = "当前任务执行扩缩容计划ID")
    private  Long scalePlanId;

    /**
     * 审批状态（通过未通过）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "审批状态（通过未通过）。可支持等于操作符的列表数据过滤。")
    private String approveState;

    /**
     * 是否允许被抢占(1 是，-1 否)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "是否允许被抢占(1 是，-1 否)。可支持等于操作符的列表数据过滤。")
    private Integer allowPreemption;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * startTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "startTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String startTimeStart;

    /**
     * startTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "startTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String startTimeEnd;

    /**
     * endTiem 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "endTiem 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String endTiemStart;

    /**
     * endTiem 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "endTiem 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String endTiemEnd;

    /**
     * estimatTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "estimatTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String estimatTimeStart;

    /**
     * estimatTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "estimatTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String estimatTimeEnd;

    /**
     * str_id / task_name / status / cpu_need / run_command / env_config / approve_state LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
