package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 集群管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "集群管理表Dto对象")
@Data
public class SchClusterManagerDto {
    /**
     * 状态 ('Active', 'Terminating','Pending', 'Failed')
     */
    @Schema(description = "状态 ('Active', 'Terminating','Pending', 'Failed')")
    private  String status;

    /**
     * 集群管理表主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群管理表主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，集群管理表主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 集群名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群名称。可支持等于操作符的列表数据过滤。")
    private String clusterName;

    /**
     * 集群类型。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群类型。可支持等于操作符的列表数据过滤。")
    private String clusterType;

    /**
     * 集群状态。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群状态。可支持等于操作符的列表数据过滤。")
    private String clusterStatus;

    /**
     * 集群描述。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群描述。可支持等于操作符的列表数据过滤。")
    private String clusterDescription;

    /**
     * 集群标识。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群标识。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，集群标识不能为空！", groups = {UpdateGroup.class})
    private String clusterMark;

    /**
     * 字符编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符编号。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * clusterMark 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "clusterMark 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String clusterMarkStart;

    /**
     * clusterMark 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "clusterMark 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String clusterMarkEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * cluster_name / cluster_type / cluster_status / cluster_description LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
