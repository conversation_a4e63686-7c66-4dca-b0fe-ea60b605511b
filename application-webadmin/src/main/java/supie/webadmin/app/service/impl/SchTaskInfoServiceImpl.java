package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.dockerjava.api.command.InspectContainerResponse;
import com.jcraft.jsch.JSchException;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import supie.common.core.constant.DockerType;
import supie.common.core.constant.ErrorCodeEnum;
import supie.common.core.constant.TaskStatusType;
import supie.common.core.exception.MyRuntimeException;
import supie.common.core.object.ResponseResult;
import supie.common.core.object.TokenData;
import supie.common.core.util.MyModelUtil;
import supie.webadmin.app.dto.RemoteTask;
import supie.webadmin.app.dto.ResourceAllocation;
import supie.webadmin.app.dto.SchTaskInfoDto;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.app.util.*;
import supie.webadmin.app.vo.RemoteTaskVo;
import supie.webadmin.app.vo.SchTaskInfoVo;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.Base64;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 任务表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schTaskInfoService")
public class SchTaskInfoServiceImpl extends BaseService<SchTaskInfo, Long> implements SchTaskInfoService {



    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchTaskInfoMapper schTaskInfoMapper;

    @Resource
    SshConnectionUtil sshConnectionUtil;

    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;

    @Resource
    private ComposeGenerate composeGenerate;

    @Resource
    private SchVirtualComputeCardSituationMapper schVirtualComputeCardSituationMappers;

    @Resource
    private SchTaskApprovalMapper schTaskApprovalMapper;
    @Autowired
    private SchNodeBasicMetricsService schNodeBasicMetricsService;
    @Autowired
    private SchCardMonitorService schCardMonitorService;

    @Resource
    private SchComputeDeviceMapper schComputeDeviceMapper;

    @Resource
    private SchTaskImageMapper schTaskImageMapper;

    @Resource
    private VnpuMonitorUtil vnpuMonitorUtil;

    @Value("${application.composeBasePath}")
    private String mountPath;

    @Resource(name = "poolExecutor")
    private ExecutorService poolExecutor;

    @Resource
    private SchScalePlanMapper schScalePlanMapper;

    @Resource
    private SchTaskMonitoringMapper schTaskMonitoringMapper;

    @Resource
    private SchContainerManagerMapper schContainerManagerMapper;

    @Value("${nginx.mount-path}")
    private String nginxConfig;



    @Resource
    private DockerJavaUtil dockerJavaUtil;

    @Resource
    private  SchBusinessDictMapper schBusinessDictMapper;

    /**
     * nginx 链接地址
     */
    @Value("${nginx.gateway-url}")
    private  String linkUrl;


    /**
     * nginx 代理配置文件路径 用于构建存储代理配置文件
     */
    @Value("${nginx.proxy-conf}")
    private  String proxyConf;


    /**
     * 审核任务
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @return 返回审核任务结果集
     */
    @Override
    public SchTaskApproval auditTask(String taskId, @NotNull String status) {
        schTaskApprovalMapper.update(
                new LambdaUpdateWrapper<SchTaskApproval>()
                        .eq(SchTaskApproval::getTaskId, Long.valueOf(taskId))
                        .set(SchTaskApproval::getDecisionTime, new Date())
                        .set(SchTaskApproval::getUpdateTime, new Date())
                        .set(SchTaskApproval::getStatus, status)

        );
        //  更新任务表 1930928779136339968
        schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, Long.valueOf(taskId))
                .set(SchTaskInfo::getUpdateTime, new Date())
                .set(SchTaskInfo::getUpdateUserId, Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                .set(SchTaskInfo::getApproveState, status)

        );
        return schTaskApprovalMapper.selectOne(new LambdaQueryWrapper<SchTaskApproval>().eq(SchTaskApproval::getTaskId, Long.valueOf(taskId)));
    }

    /**
     * 构建服务器的 资源池、资源、计算卡、切分ID
     *
     * @return 返回资源信息集合
     */
    @Override
    public List<RootNode> relationResource() {
        // 树型结果关系 资源池-->资源-->计算卡-->计算卡切分结果
        List<ResourceTree> deviceSliceList = schResourceInfoMapper.queryDeviceSlice();  // 包含设备、切片信息
        List<ResourceTree> resourcePoolList = schResourceInfoMapper.queryResourcePool();  // 包含资源、资源池信息
        List<SchTaskInfo> taskList = schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .isNotNull(SchTaskInfo::getPartitionId));
        //   TODO 当前 partitionId 是JSON 数组
        Map<String, List<SchTaskInfo>> taskMap = taskList.stream().collect(Collectors.groupingBy(SchTaskInfo::getPartitionId));

        // 2 合并所有数据，并按 poolId 分组，用于构建 RootNode
        Map<Long, List<ResourceTree>> rootGroup = Stream.concat(
                        deviceSliceList.stream(),
                        resourcePoolList.stream()
                ).filter(tree -> tree.getPoolId() != null)
                .collect(Collectors.groupingBy(ResourceTree::getPoolId));

        List<RootNode> treeResult = new ArrayList<>();

        // 3 构建完整树形结构
        rootGroup.forEach((poolId, rootTrees) -> {
            if (rootTrees.isEmpty()) {
                return;
            }
            // 3.1 创建 RootNode
            RootNode rootNode = new RootNode();
            rootNode.setPoolId(poolId);
            rootNode.setPoolName(rootTrees.get(0).getPoolName());

            // 3.2 按 resourceId 分组，构建 ResourceNode
            Map<Long, List<ResourceTree>> resourceGroup = rootTrees.stream()
                    .filter(tree -> tree.getResourceId() != null)
                    .collect(Collectors.groupingBy(ResourceTree::getResourceId));

            // / 3.2 构建 ResourceNode 结点列表
            List<ResourceNode> resourceNodes = new ArrayList<>();

            resourceGroup.forEach((resourceId, resourceTrees) -> {
                ResourceNode resourceNode = new ResourceNode();
                resourceNode.setResourceId(resourceId);
                if (!resourceTrees.isEmpty()) {
                    resourceNode.setResourceName(resourceTrees.get(0).getResourceName());
                }
                //  获取当前 资源所关的 设备、切片信息
                List<ResourceTree> currentDeviceSlices = deviceSliceList
                        .stream()
                        .filter(t ->
                                Objects.equals(t.getResourceId(), resourceId))
                        .toList();
                // 3.3 按 deviceId 分组，构建 DeviceNode
                Map<Long, List<ResourceTree>> deviceGroup = currentDeviceSlices
                        .stream()
                        .filter(tree -> tree.getDeviceId() != null)
                        .collect(Collectors.groupingBy(ResourceTree::getDeviceId));
                // 构建   ResourceNode 孩子结点列表  列表 DeviceNode
                List<DeviceNode> deviceNodes = new ArrayList<>();
                deviceGroup.forEach((deviceId, deviceTrees) -> {
                    DeviceNode deviceNode = new DeviceNode();
                    deviceNode.setDeviceId(deviceId);
                    if (!deviceTrees.isEmpty()) {
                        deviceNode.setDeviceName(deviceTrees.get(0).getModelNumber());
                    }
                    // 3.4 构建 DeviceNode 孩子节点列表 SliceNode
                    List<SliceNode> sliceNodes = deviceTrees.stream()
                            .filter(t -> t.getSliceId() != null && t.getVnpuId() != null)
                            .map(t -> {
                                SliceNode sliceNode = new SliceNode();
                                sliceNode.setSliceId(t.getSliceId());
                                sliceNode.setVnpuId(t.getVnpuId());
                                sliceNode.setSchTaskInfoList(taskMap.get(t.getVnpuId()));
                                return sliceNode;
                            })
                            .distinct()
                            .collect(Collectors.toList());
                    deviceNode.setChildrenSlice(sliceNodes);
                    deviceNodes.add(deviceNode);
                });

                resourceNode.setChildrenDevice(deviceNodes);
                resourceNodes.add(resourceNode);
            });
            rootNode.setChildrenResource(resourceNodes);
            treeResult.add(rootNode);
        });
        return treeResult;

    }

    /**
     * 停止任务
     *
     * @param taskId 任务id
     */
    @Override
    public SchTaskInfo stopTask(Long taskId) {
//        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Objects.requireNonNull(taskId, "任务id不能为空"));
//        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        SchTaskInfo resource = schResourceInfoMapper.queryResourceId(taskId);
        if(Objects.isNull(resource)){
            throw new MyRuntimeException("当前任务数据不存在");
        }
        CompletableFuture.runAsync(
                ()->{
                    dockerJavaUtil.stopContainer(resource.getContainerId(),resource.getResourceId());
                }
        ,poolExecutor)
                .thenAccept(result -> {
                    log.info("任务停止成功: {}", taskId);
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getStatus, TaskStatusType.STOPPED)
                            .set(SchTaskInfo::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_PAUSED)
                            .set(SchTaskInfo::getUpdateTime, new Date()));
                })
                .exceptionally(throwable -> {
            log.error("任务id{} 任务停止失败  : {}",taskId, throwable.getMessage(), throwable);
            throw new MyRuntimeException("任务停止失败");
        });
        return schTaskInfoMapper.selectById(taskId).setStatus(TaskStatusType.STOPPING);

       /* SshConnection connection = null;
        try {
            connection = sshConnectionUtil.createConnection(schResourceInfo.getHostIp(), schResourceInfo.getPort(),
                    schResourceInfo.getLoginName(), schResourceInfo.getPassword(), 30000);
            String result = sshConnectionUtil.executeCommand(connection, "docker stop " + Objects.requireNonNull(schTaskInfo.getContainerName(), "当前任务不存在"));
            if (schTaskInfo.getContainerName().equals(result.trim())) {
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskId)
                        .set(SchTaskInfo::getStatus, "stop")
                        .set(SchTaskInfo::getContainerStatus, "paused"));
                
                // 删除对应的远程nginx配置文件
                //this.removeNginxConfigFile(taskId, connection);
                
                // 重新加载nginx配置
                try {
                    sshConnectionUtil.executeCommand(connection, "docker exec code_proxy_nginx nginx -s reload");
                    log.info("nginx配置重新加载完成（任务停止）");
                } catch (Exception e) {
                    log.warn("重新加载nginx配置失败: {}", e.getMessage());
                }
                
                return schTaskInfoMapper.selectById(taskId);
            } else {
                throw new MyRuntimeException("任务停止失败");
            }
        } catch (JSchException | IOException e) {
            log.error("任务停止失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("任务停止失败");
        } finally {
            if (connection != null) {
                connection.close();
            }
        }*/
    }

    /**
     * 定时更新任务数据状态
     */
    @Override
    public void upDateTaskStatus() {
        List<SchTaskInfo> info = schTaskInfoMapper.checkTaskList();
        if (CollUtil.isNotEmpty(info)) {
            info.forEach(item -> {
                try {
                    // 获取容器状态信息
                    InspectContainerResponse exec = dockerJavaUtil.connect(item.getResourceId())
                            .inspectContainerCmd(item.getContainerId())
                            .exec();
                    String status = exec.getState().getStatus();
                    Long exitCodeLong = Optional.ofNullable(exec.getState().getExitCodeLong()).orElse(0L);
                    String logs = "";
                    if(exitCodeLong != 0L){
                      logs = dockerJavaUtil.getContainerLogs(item.getContainerName(), 1000,item.getResourceId())+item.getFailReason();
                    }
                    String exitCode = "exited(" + exitCodeLong.intValue() + ")";
                    log.info("定时更新任务状态 ContainerStatus: {} ContainerExitCode: {}", status, exitCode);
                    // 更新任务和容器状态
                    this.updateTaskAndContainerStatus(item, status, exitCode,logs);
                } catch (Exception e) {
                    log.error("获取容器状态失败 containerId: {} - {}", item.getContainerId(), e.getMessage());
                }
            });
        }
    }

    /**
     * @param item     数据项
     * @param status   容器状态
     * @param exitCode 状态码
     */
    private void updateTaskAndContainerStatus(SchTaskInfo item, String status, String exitCode,String logs) {
        LambdaUpdateWrapper<SchTaskInfo> taskUpdateWrapper = new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, item.getId())
                .set(SchTaskInfo::getExitCode, exitCode)
                .set(SchTaskInfo::getContainerStatus, status)
                .set(SchTaskInfo::getFailReason, logs)
                .set(SchTaskInfo::getUpdateTime, new Date());

        LambdaUpdateWrapper<SchContainerManager> containerUpdateWrapper = new LambdaUpdateWrapper<SchContainerManager>()
                .eq(SchContainerManager::getId, item.getContainerMangerId())
                .set(SchContainerManager::getContainerStatus, status)
                .set(SchContainerManager::getUpdateTime, new Date());

        // 根据容器状态更新任务状态
        if (DockerType.DOCKER_CONTAINER_STATUS_RUNNING.equals(status) ||
                DockerType.DOCKER_CONTAINER_STATUS_CREATED.equals(status) ||
                DockerType.DOCKER_CONTAINER_STATUS_RESTARTING.equals(status)) {
            log.info("更新任务状态为 running，当前 containerStatus: {}", status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, TaskStatusType.RUNNING);
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_RUNNING);
        } else if (DockerType.DOCKER_CONTAINER_STATUS_PAUSED.equals(status)) {
            log.info("更新任务状态为 stop，当前 containerStatus: {}", status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, TaskStatusType.STOPPED);
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_PAUSED);
        } else if (DockerType.DOCKER_CONTAINER_STATUS_DEAD.equals(status)) {
            log.info("更新任务状态为 failed，当前 containerStatus: {}", status);
            taskUpdateWrapper.set(SchTaskInfo::getStatus, TaskStatusType.FAILED);
            containerUpdateWrapper.set(SchContainerManager::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_FAILED);
        } else if ("exited".equals(status)) {
            // 容器已退出，根据退出码判断任务结果
            if ("exited(0)".equals(exitCode)) {
                log.info("容器正常退出，更新任务状态为 finished，containerStatus: {}, exitCode: {}", status, exitCode);
                taskUpdateWrapper.set(SchTaskInfo::getStatus, TaskStatusType.FINISHED);
                containerUpdateWrapper.set(SchContainerManager::getContainerStatus, exitCode);
            } else {
                log.info("容器异常退出，更新任务状态为 failed，containerStatus: {}, exitCode: {}", status, exitCode);
                taskUpdateWrapper.set(SchTaskInfo::getStatus, TaskStatusType.FAILED);
                containerUpdateWrapper.set(SchContainerManager::getContainerStatus,  exitCode);
            }
        }
        schTaskInfoMapper.update(taskUpdateWrapper);
        schContainerManagerMapper.update(containerUpdateWrapper);
    }

    /**
     * 扩缩容正在执行的容器任务
     *
     * @param schTaskInfos 正在执行的仁列表
     */
    @Override
    public Map<String, List<SchTaskInfo>> expandDecreaseMonitor(List<SchTaskInfo> schTaskInfos) {
        // 循环 正在执行的容器任务
        // 记录缩容扩容队列信息
        List<SchTaskInfo> expandQueue = new ArrayList<>();
        List<SchTaskInfo> decreaseQueue = new ArrayList<>();
        schTaskInfos.forEach(item -> {
            Long scalePlanId = item.getScalePlanId();
            //  查询CPU   内存信息 使用率 去匹配对应的扩缩容计划
            SchScalePlan plan = schScalePlanMapper.selectById(scalePlanId);
            //  获取当前任务的监控数据
            SchTaskMonitoring monitor = schTaskMonitoringMapper
                    .selectOne(new LambdaQueryWrapper<SchTaskMonitoring>()
                            .eq(SchTaskMonitoring::getTaskId, item.getId())
                            .orderByDesc(SchTaskMonitoring::getCreateTime)
                            .last("limit 1")
                    );
            boolean resultCEx = monitor.getCpuUsage().compareTo(new BigDecimal(plan.getCpuThresholdEx())) > 0;
            boolean resultMEx = monitor.getMemRate().compareTo(new BigDecimal(plan.getMemoryThresholdEx())) > 0;
            if (resultCEx && resultMEx) {
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                item.setScalePlan(schScalePlan);
                expandQueue.add(item);
            }
            // 缩容队列
            boolean resultCDc = monitor.getCpuUsage().compareTo(new BigDecimal(plan.getCpuThresholdDc())) < 0;
            boolean resultMDC = monitor.getMemRate().compareTo(new BigDecimal(plan.getMemoryThresholdDc())) < 0;
            if (resultCDc && resultMDC) {
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                item.setScalePlan(schScalePlan);
                decreaseQueue.add(item);
            }
        });
        Map<String, List<SchTaskInfo>> map = new HashMap<>();
        map.put("expandQueue", expandQueue);
        map.put("decreaseQueue", decreaseQueue);
        return map;
    }


    /**
     * 构建可用资源VNPU 资源信息
     *
     * @param map
     */
    public void buildAvailableResource(List<SchTaskInfo> map) {
        for (SchTaskInfo item : map) {
            SchScalePlan scalePlan = item.getScalePlan();
            JSONObject jsonObject = JSON.parseObject(scalePlan.getResourceConfig());
            String poolId = jsonObject.getString("poolId");
            String resourceInfoId = jsonObject.getString("resourceInfoId");
            String computeDeviceId = jsonObject.getString("computeDeviceId");
            String vccId = jsonObject.getString("vccId");
            // 选卡删除自己在运行的卡
            List<SchTaskInfo> verify = schTaskInfoMapper.usageQuery(item.getId(), computeDeviceId, vccId);

            if (verify.isEmpty()) {
                //构建模板镜像信息执行
                List<SchContainerManager> container = schContainerManagerMapper
                        .selectList(new LambdaQueryWrapper<SchContainerManager>()
                                .eq(SchContainerManager::getTaskInfoId, item.getId()));
                this.stopTask(item.getId());
                SchScalePlan schScalePlan = schScalePlanMapper.selectById(item.getScalePlanId());
                //composeGenerate.buildRemoteCommonTemplate(List.of(container.get(Integer.parseInt(vccId))),item,resource);

            } else {
                List<Integer> resource = this.availableResource(Math.toIntExact(scalePlan.getExpandMemoryNum()));

            }
        }
    }

    /**
     * 构建空闲可用VNPU 信息列表
     *
     * @param graphicSize 需要显存大小
     * @return 返回可用信息列表
     */
    public List<Integer> availableResource(Integer graphicSize) {
        // 查询所有可用的计算卡 满足内存需要
        List<SchComputeDevice> devices = schComputeDeviceMapper.selectList(
                new LambdaQueryWrapper<SchComputeDevice>()
                        .ge(SchComputeDevice::getMemorySize, graphicSize
                        ));
        List<Long> computerId = devices.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
        // 查询满足算力需求的在运行的卡
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
                        .eq(SchTaskInfo::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_RUNNING)
                        .in(SchTaskInfo::getComputeDeviceId, computerId)
        );
        if (schTaskInfos.isEmpty()) {
            throw new MyRuntimeException("没有可运行的任务");
        }
        List<Long> runCom = new ArrayList<>();
        List<Long> runPartition = new ArrayList<>();
        schTaskInfos.forEach(i->{
            //  抽取在运行物理卡
            if("physical".equals(i.getNeedResource()) &&  StringUtils.isNotBlank(i.getComputeDeviceId())){
                List<Long> pysical = JSON.parseArray(i.getComputeDeviceId(), Long.class);
                runCom.addAll(pysical);
            }
            // 抽取在运行VNPU信息
            if("vnpu".equals(i.getNeedResource()) &&  StringUtils.isNotBlank(i.getPartitionId())  ){
                List<Long> vnpu = JSON.parseArray(i.getPartitionId(), Long.class);
                runPartition.addAll(vnpu);
            }
        });
        return schTaskInfoMapper.queryAvaliable(runCom, runPartition);
    }

   

    /**
     *   执行docker-compose up -d
     * @param remoteTask 任务调度
     * @return 任务信息返回结果
     */
    public RemoteTaskVo remoteTask(RemoteTask remoteTask) {
        // 验证基本参数
         this.validateRemoteTask(remoteTask);
        // 准备资源信息
        ResourceAllocation allocation = this.allocateResources(remoteTask);
        // 创建或获取镜像记录
        Long imageId = this.getOrCreateTaskImage(remoteTask);
        // 创建任务记录
        SchTaskInfo taskInfo = this.createTaskInfo(remoteTask, allocation, imageId);
        // 设置挂载路径
        if (StringUtils.isEmpty(remoteTask.getContainerMapPath())) {
            remoteTask.setContainerMapPath(mountPath + "/" + taskInfo.getId());
        }
        // 异步执行任务
         this.executeTaskAsync(remoteTask, taskInfo, allocation);
        // 返回任务信息


        String visitLink = "";

        if (StringUtils.isNotEmpty(remoteTask.getProxyType()) && "jupyter".equals(remoteTask.getProxyType())) {
            // http://192.168.1.1:8080/code12344545/lab?token=74849dndjfndsjrmdgffdv
            String token = remoteTask.getToken() != null ? remoteTask.getToken() : "";
            visitLink = linkUrl + "code" + taskInfo.getId() + "/lab?token=" + token;
        } else if (StringUtils.isNotEmpty(remoteTask.getProxyType()) && "vscode".equals(remoteTask.getProxyType())) {
            // http://192.168.1.1:8080/code12345454
            visitLink = linkUrl + "code" + taskInfo.getId() + "/";
        }else if (StringUtils.isNotEmpty(remoteTask.getProxyType()) && "simple".equals(remoteTask.getProxyType())) {
            // http://192.168.1.1:8080/code12345454
            visitLink = linkUrl + "simple" + taskInfo.getId() + "/";
        }

        assert taskInfo != null;
        return new RemoteTaskVo()
                .setTaskName(remoteTask.getTaskName())
                .setPorts(allocation.getPorts())
                .setTaskId(taskInfo.getId().toString())
                .setImageName(remoteTask.getImageName())
                .setStatus(TaskStatusType.STARTING)
                .setVisitLink(visitLink)
                .setMapPath(remoteTask.getContainerMapPath());
    }

    /**
     *   参数验证
     * @param remoteTask 远程任务信息
     */
    private void validateRemoteTask(RemoteTask remoteTask) {
        if (!"non".equals(remoteTask.getNeedResource()) &&
                (remoteTask.getGraphicSize() == null || !remoteTask.getGraphicSize().matches("\\d+"))) {
            throw new IllegalArgumentException("显存大小必须为正整数");
        }
    }

    /**
     *  构建不同任务算力分配  non 不需要算力资源  physical 物理卡资源 vnpu 代表需要npu 资源
     * @param remoteTask 任务信息列表
     * @return  资源分配结果
     */
    private ResourceAllocation allocateResources(RemoteTask remoteTask) {
        ResourceAllocation allocation = new ResourceAllocation();
        if (!"non".equals(remoteTask.getNeedResource())) {
            // 需要GPU/NPU资源的情况
            this.allocateComputeResources(remoteTask, allocation);
        } else {
            // 不需要特殊资源的情况
            this.allocateBasicResources(remoteTask, allocation);
        }
        // 设置端口信息
        remoteTask.setExport(allocation.getPorts());
        return allocation;
    }


    /**
     *   避免重复创建 镜像
     * @param remoteTask 任务基础信息
     * @return imageId
     */
    private Long getOrCreateTaskImage(RemoteTask remoteTask) {
        List<SchTaskImage> images = schTaskImageMapper.selectList(
                new LambdaQueryWrapper<SchTaskImage>()
                        .and(
                                wrapper -> wrapper
                                .eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                .or(remoteTask.getImageVersion() != null,
                                        i ->
                                                i.eq(SchTaskImage::getImageName, remoteTask.getImageName())
                                                .eq(SchTaskImage::getImageVersion, remoteTask.getImageVersion()))
                        )
        );
        if (images.isEmpty()) {
            SchTaskImage schTaskImage = new SchTaskImage()
                    .setId(idGenerator.nextLongId())
                    .setImageVersion(remoteTask.getImageVersion())
                    .setImageName(remoteTask.getImageName())
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setIsDelete(GlobalDeletedFlag.NORMAL);
            schTaskImageMapper.insert(schTaskImage);
            return schTaskImage.getId();
        } else {
            return images.get(0).getId();
        }
    }

    /**
     * 创建存储任务信息
     * @param remoteTask 构建任务信息传输对象
     * @param allocation 资源分配解果
     * @param imageId 镜像id
     * @return 构建任务信息
     */
    private SchTaskInfo createTaskInfo(RemoteTask remoteTask, ResourceAllocation allocation, Long imageId) {
        Integer graphicSize = remoteTask.getGraphicSize() != null ? Integer.parseInt(remoteTask.getGraphicSize()) : 0;
        Integer mem = remoteTask.getMemorySize() != null ? Integer.valueOf(remoteTask.getMemorySize()) : null;
        String env = remoteTask.getEnvironment() != null ? JSON.toJSONString(remoteTask.getEnvironment()) : null;
        String command = remoteTask.getCommand() != null ? JSON.toJSONString(remoteTask.getCommand()) : null;
        SchBusinessDict dict = schBusinessDictMapper.selectOne(new LambdaQueryWrapper<SchBusinessDict>().orderByDesc(SchBusinessDict::getCreateTime).last("limit 1"));
        SchTaskInfo taskInfo = new SchTaskInfo()
                .setId(idGenerator.nextLongId())
                .setUpdateTime(new Date())
                .setCreateTime(new Date())
                .setGraphicNeededMb(graphicSize)
                .setMemoryNeededMb(mem)
                .setRunCommand(command)
                .setEnvConfig(env)
                .setApproveState("approved")
                .setStatus(TaskStatusType.STARTING)
                .setTaskName(remoteTask.getTaskName())
                .setTaskType("external")//外部任务
                .setRemoteTaskJson(JSON.toJSONString(remoteTask))
                .setComputeDeviceId(String.valueOf(allocation.getComputeDevice() != null ? allocation.getComputeDevice().getId() : null))
                .setPoolId(allocation.getPoolId())
                .setDictId(dict.getId())//
                .setIsDelete(GlobalDeletedFlag.NORMAL)
                .setPartitionId(String.valueOf(allocation.getPartitionId() != null ? Long.valueOf(allocation.getPartitionId()) : null))
                .setResourceId(allocation.getResourceInfo().getId())
                // 存储远程任务参数JSON 任务类型
                .setTaskImageId(imageId);
        schTaskInfoMapper.insert(taskInfo);
        return taskInfo;
    }

    /**
     *   异步执行任务
     * @param remoteTask 远程任务信息
     * @param taskInfo 当前存储的数据库任务信息
     * @param allocation   当前分配资源
     */
    private void executeTaskAsync(RemoteTask remoteTask, SchTaskInfo taskInfo, ResourceAllocation allocation) {
        CompletableFuture.runAsync(() -> {
            try {
                byte[] bytes = composeGenerate.buildRemoteTaskTemplate(
                        allocation.getPartitionId() != null ? List.of(allocation.getPartitionId()) : List.of(0),
                        remoteTask,
                        allocation.getPorts(),
                        Map.of("taskId", taskInfo.getId())
                );
                composeGenerate.remoteTask(allocation.getResourceInfo(), taskInfo.getId(), bytes, remoteTask);
            } catch (Exception e) {
                log.error("任务执行失败: {}", e.getMessage(), e);
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, taskInfo.getId())
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getFailReason, e.getMessage())
                        .set(SchTaskInfo::getStatus, TaskStatusType.FAILED));
            }
        }, poolExecutor);
    }


    /**
     *  执行资源分配 需要VNPU GPU  需要获取vccid 才能执行
     * @param remoteTask  任务信息传输对象
     * @param allocation 分配资源信息对象
     */
    private void allocateComputeResources(RemoteTask remoteTask, ResourceAllocation allocation) {
        log.info("SchTaskInfoService  allocateComputeResources  method  execute start: current task need compute resource ");
        int graphicSizeValue = Integer.parseInt(remoteTask.getGraphicSize());
        // 查找满足显存要求的计算设备
        List<SchComputeDevice> devices = schComputeDeviceMapper.selectList(
                new LambdaQueryWrapper<SchComputeDevice>()
                        .ge(SchComputeDevice::getMemorySize, graphicSizeValue)
        );
        if (devices.isEmpty()) {
            throw new MyRuntimeException("没有满足条件的资源");
        }
        // 提取计算设备ID
        List<Long> computerId = devices.stream().map(SchComputeDevice::getId).filter(Objects::nonNull).toList();
        // 收集正在运行任务的卡  任务态 running  容器态 running restarting paused created
        List<Long> runCard = schTaskInfoMapper.collectRunTask(computerId);
        // 提取可用VNPU ID
        List<Integer> available = schComputeDeviceMapper.queryAvailableVnpu(runCard);
        if (available.isEmpty()) {
            throw new MyRuntimeException("没有可用的算力卡资源");
        }
        // 随机选择一个可用VNPU
        Integer partitionId = available.get(new Random().nextInt(available.size()));
        allocation.setPartitionId(partitionId);
        // 获取资源信息和计算设备
        SchResourceInfo resourceInfo = schResourceInfoMapper.queryAvaliable(partitionId);
        SchComputeDevice computeDevice = schComputeDeviceMapper.queryDevice(partitionId, resourceInfo.getId());
        allocation.setResourceInfo(resourceInfo);
        allocation.setComputeDevice(computeDevice);
        // 获取资源池ID
        List<Long> poolIds = schResourceInfoMapper.queryPoolId(resourceInfo.getId());
        allocation.setPoolId(poolIds.isEmpty() ? 0L : poolIds.get(new Random().nextInt(poolIds.size())));
        // 分配端口
        this.allocatePorts(remoteTask, allocation);
    }


    /**
     * 分配基本资源(不需要GPU/NPU) 只需要服务器空闲即可
     * @param remoteTask 远程调用任务数据对象
     * @param allocation 分配资源信息传输对象
     */
    private void allocateBasicResources(RemoteTask remoteTask, ResourceAllocation allocation) {
        log.info("SchTaskInfoService  allocateBasicResources  method  execute start: current task don't need compute resource ");
        // 获取可用服务器资源
        List<SchResourceInfo> infos = schResourceInfoMapper.selectList(null);
        if (infos.isEmpty()) {
            throw new MyRuntimeException("没有可用服务器资源");
        }
        // 随机选择一个服务器
        int index = new Random().nextInt(infos.size());
        SchResourceInfo resourceInfo = infos.get(index);
        allocation.setResourceInfo(resourceInfo);
        // 获取资源池ID
        List<Long> poolIds = schResourceInfoMapper.queryPoolId(resourceInfo.getId());
        allocation.setPoolId(poolIds.isEmpty() ? 0L : poolIds.get(new Random().nextInt(poolIds.size())));
        // 分配端口
        this.allocatePorts(remoteTask, allocation);
    }

    /**
     * 执行端口分配
     * @param remoteTask 远程任务数据对象
     * @param allocation 资源分配传输对象
     */
    private void allocatePorts(RemoteTask remoteTask, ResourceAllocation allocation) {
        List<String> ports = new ArrayList<>();
        String hostIp = allocation.getResourceInfo().getHostIp();
        if (Boolean.TRUE.equals(remoteTask.getMultiplePort())) {
            // 分配多个端口
            for (int i = 0; i < remoteTask.getNeedPortNum(); i++) {
                ports.add(String.valueOf(sshConnectionUtil.getFreePort(hostIp)));
            }
        } else {
            // 分配单个端口
            ports.add(String.valueOf(sshConnectionUtil.getFreePort(hostIp)));
        }
        allocation.setPorts(ports);
        remoteTask.setExport(ports);//携带传输
    }


    /**
     *  构建nginx config配置文件
     * @param resourceId 资源id
     * @param taskId 信息列表
     * @param export 存储信息
     * @throws JSchException
     */
    @Override
    public void nginxProxy(Long resourceId, Long taskId, String export, String serviceType) throws JSchException, IOException {
        log.info("nginxProxy build start, taskId: {}, resourceId: {}, serviceType: {}", taskId, resourceId, serviceType);
        SchResourceInfo info = schResourceInfoMapper.selectById(resourceId);
        try {
            // 使用网络别名构建nginx配置，不需要SSH连接
            String codePrefix = "code" + taskId;
            String networkAlias= "code-"+taskId;
            String nginxConfig = this.buildCodeServerProxyConfig(codePrefix, networkAlias, export, serviceType);
            // 将配置写入本地文件，供后续部署使用
            String configFileName = taskId + ".conf";
            Path configPath = Paths.get(proxyConf, configFileName);
            // 确保目录存在
            Files.createDirectories(configPath.getParent());
            
            // 写入nginx配置文件
            Files.writeString(configPath, nginxConfig,
                       StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

            // 如果nginx容器在本地，可以直接使用ProcessBuilder
            this.reloadNginxConfig();
            log.info("成功生成nginx配置文件, taskId: {}, 文件路径: {}, 网络别名: {}, 端口: {}", 
                    taskId, configPath.toString(), networkAlias, export);
            log.debug("nginx配置内容: \n{}", nginxConfig);
        } catch (Exception e) {
            log.error("生成nginx配置失败, taskId: {}, 错误: {}", taskId, e.getMessage(), e);
            throw new MyRuntimeException("生成nginx配置失败: " + e.getMessage());
        }
    }





    
    /**
     * 构建 Jupyter Lab 代理配置，支持 token 参数传递
     * http://192.168.0.9:40084/code2748719/lab?token=14545bdhdh2iuhfwierh
     *
     * @param codePrefix  代码前缀，例如：code12344545
     * @param networkAlias 网络名称或容器名称
     * @param port        容器映射端口 容器内部端口
     * @return nginx 配置模板
     */
    public String buildNginxJupyterProxyConfig(String codePrefix, String networkAlias, String port) {
        return String.format(
                """
                        # Jupyter Lab 代理配置 - 直接使用proxy_pass到容器
                        location ~ ^/%s/lab {
                            proxy_pass http://%s:%s;
                            rewrite ^/%s/lab(.*)$ /lab$1 break;
                        }
                        
                        # Jupyter API 代理
                        location ~ ^/%s/api {
                            proxy_pass http://%s:%s;
                            rewrite ^/%s/api(.*)$ /api$1 break;
                        }
                        """,
                codePrefix, networkAlias, port, codePrefix,
                codePrefix, networkAlias, port, codePrefix
        );
    }

    /**
     * 构建代码服务器代理配置
     *
     * @param codePrefix   代码前缀，例如：code12344545
     * @param networkAlias Docker网络别名，例如：code-12344545
     * @param port         容器映射端口
     * @param serviceType  服务类型
     * @return nginx配置模板
     */
    public String buildCodeServerProxyConfig(String codePrefix, String networkAlias, String port, String serviceType) {
        String location = "";
        
        if ("jupyter".equalsIgnoreCase(serviceType)) {
            location = this.buildNginxJupyterProxyConfig(codePrefix, networkAlias, port);
        } else if ("vscode".equalsIgnoreCase(serviceType)) {
            // VS Code Server 配置 - 直接使用proxy_pass到容器，不使用upstream
            location = String.format(
                    """
                            # VS Code Server 代理配置
                            location ~ ^/%s/ {
                                proxy_pass http://%s:%s;
                                rewrite ^/%s/(.*)$ /$1 break;
                            }
                            """,
                    codePrefix, networkAlias, port, codePrefix
            );
        } else if ("simple".equalsIgnoreCase(serviceType)) {
            // 默认通用配置 - 直接使用proxy_pass到容器，不使用upstream
            String numberStr = codePrefix.substring("code".length());
            String simple = "simple" + numberStr;
            location = String.format(
                    """
                            # Simple Service 代理配置
                            location ~ ^/%s/ {
                                proxy_pass http://%s:%s;
                                rewrite ^/%s/(.*)$ /$1 break;
                            }
                            """,
                    simple, networkAlias, port, simple
            );
        }
        return location;
    }

    /**
     * 使用ProcessBuilder重新加载nginx配置
     * 适用于nginx容器与Java应用在同一台服务器的情况
     */
    private void reloadNginxConfig() {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "docker", "exec", "code_proxy_nginx", "nginx", "-s", "reload"
            );
            
            // 设置工作目录（可选）
            // processBuilder.directory(new File("/path/to/working/directory"));
            
            // 合并错误流和输出流
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            // 等待命令执行完成
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                log.info("nginx配置重新加载成功");
            } else {
                log.error("nginx配置重新加载失败，退出码: {}", exitCode);
                throw new MyRuntimeException("nginx配置重新加载失败");
            }
            
        } catch (IOException | InterruptedException e) {
            log.error("执行nginx重新加载命令失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("nginx配置重新加载失败: " + e.getMessage());
        }
    }
    

    /**
     * 运行任务批量停止
     *
     * @param idList 运行型任务列表
     * @return 任务信息列表
     */
    @Override
    public List<SchTaskInfo> batchStop(List<String> idList) {
        List<Long> id = idList.stream().filter(i -> !StringUtils.isEmpty(i)).map(Long::valueOf).toList();
        if (id.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        List<SchTaskInfo> queryR = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, id)
                        .eq(SchTaskInfo::getApproveState, "approved")
                        .eq(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
        );
        if (queryR.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        queryR.forEach(item -> {
            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, item.getId())
                    .set(SchTaskInfo::getStatus, "stopping")
                    .set(SchTaskInfo::getUpdateTime, new Date()));
        });
        CompletableFuture.runAsync(() -> {
            queryR.forEach(item -> {
                try {
                    //TODO 任务执行失败状态回退问题
                    this.stopTask(item.getId());
                } catch (Exception e) {
                    log.warn("任务停止失败");
                }
            });
        }, poolExecutor)
                .exceptionally(throwable -> {
                    log.error("任务停止异常", throwable);
                    return null;
                })
        ;
        return List.of();
    }

    /**
     * 重启失败任务   重启失败任务
     *
     * @param taskId 任务id
     */
    @Override
    public SchTaskInfo restartTask(Long taskId) {
        SchTaskInfo resource = schResourceInfoMapper.queryResourceId(taskId);
        SchTaskInfo  task = schTaskInfoMapper.selectById(taskId);
        if (Objects.isNull(resource)) {
            throw new MyRuntimeException("当前任务数据不存在");
        }
        CompletableFuture.runAsync(() -> {
            try {
                if (StringUtils.isBlank(resource.getContainerName()) && "external".equals(task.getTaskType())) {
                    log.info("容器名称为空，重新启动外部任务......");
                    RemoteTask remoteTask = JSON.parseObject(task.getRemoteTaskJson(), RemoteTask.class);
                    this.remoteTask(remoteTask);
                } else if(StringUtils.isBlank(resource.getContainerName()) && "inner".equals(task.getTaskType())){
                    log.info("容器名称为空，重新启动内部任务......");
                    // TODO 第一次启动失败则重新构建模板  远程任务单独构建 启动
                     this.startTask(taskId);
                }else {
                    log.info("容器名称不为空，重新启动容器......");
                    dockerJavaUtil.restartContainer(resource.getContainerId(), resource.getResourceId());
                }
            } catch (Exception e) {
                log.error("任务重启过程中发生异常: taskId={}, error={}", taskId, e.getMessage(), e);
                throw new MyRuntimeException("任务重启失败: " + e.getMessage());
            }
        }, poolExecutor).thenAccept(result -> {
            log.info("任务重启成功: {}", taskId);
            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getUpdateTime, new Date())
                    .set(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
                    .set(SchTaskInfo::getContainerStatus, DockerType.DOCKER_CONTAINER_STATUS_RUNNING));
        })
        .exceptionally(throwable -> {
            log.error("任务重启失败: {}", throwable.getMessage(), throwable);
            // 更新任务状态为失败
            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                    .eq(SchTaskInfo::getId, taskId)
                    .set(SchTaskInfo::getUpdateTime, new Date())
                    .set(SchTaskInfo::getStatus, TaskStatusType.FAILED)
                    .set(SchTaskInfo::getFailReason, throwable.getMessage()));
            return null;
        });

        return schTaskInfoMapper.selectById(taskId).setStatus(TaskStatusType.RESTARTING);
    }




    /**
     * 批量启动任务
     *
     * @param idList 任务列表 status
     * @return 启动数据结果
     */
    @Override
    public List<SchTaskInfo> batchStart(List<String> idList) {
        List<Long> id = idList.stream().filter(i -> !StringUtils.isEmpty(i)).map(Long::valueOf).toList();
        if (id.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        List<SchTaskInfo> queryR = schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, id)
                        .eq(SchTaskInfo::getApproveState, "approved")
                        .eq(SchTaskInfo::getStatus, "pending")
        );
        if (queryR.isEmpty()) {
            throw new MyRuntimeException("任务不存在");
        }
        CompletableFuture.runAsync(() -> {
            // 批量循环执行任务启动
            queryR.forEach(item -> {
                try {
                    // 保证发生异常任务继续执行
                    this.startBatchTask(item.getId());
                } catch (Exception e) {
                    // 异常跟新当前任务启动状态
                    // 记录具体异常类型和详细信息
                    log.error("任务[{}]启动失败: {}", item.getId(), e.getMessage(), e);
                    schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, item.getId())
                            .set(SchTaskInfo::getFailReason,e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, TaskStatusType.FAILED));

                }
            });
        }, poolExecutor);
        queryR.forEach(
                item -> schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getId, item.getId())
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus, TaskStatusType.STARTING)
                )
        );
        return List.of();
    }

    public SchTaskInfo startBatchTask(Long taskId) {
        // 获取审核通过任务信息
        SchTaskInfo schTaskInfo = schResourceInfoMapper.approval(taskId);
        if (Objects.isNull(schTaskInfo)) {
            throw new MyRuntimeException("当前任务审核没有通过");
        }
        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        Objects.requireNonNull(schResourceInfo, "当前任务对应的资源不存在");
        SchVirtualComputeCardSituation car = schVirtualComputeCardSituationMappers
                .selectById(Objects.requireNonNull(schTaskInfo.getPartitionId(), "切分卡信息不存在"));
        List<Integer> vnpuList = List.of(car.getVccId());
        if (vnpuList.isEmpty()) {
            vnpuList = schTaskInfoMapper.queryInfoVnpu(taskId);
        }
        try {
            // 压缩包类型 线解压缩上传构建 挂载数据
            return composeGenerate.ordinaryStart(schResourceInfo, vnpuList, schTaskInfo.getTaskImageId(), taskId);
        } catch (IOException e) {
            log.error("startTask: {}", e.getMessage());
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchTaskInfo> mapper() {
        return schTaskInfoMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchTaskInfo saveNew(SchTaskInfo schTaskInfo) {
        if (StringUtils.isEmpty(schTaskInfo.getApproveState())) {
            schTaskInfo.setApproveState("pending");
        }
        if (StringUtils.isEmpty(schTaskInfo.getApproveState())) {
            schTaskInfo.setStatus("pending");
        }
        // 存储计算卡类型
        if("physical".equals(schTaskInfo.getNeedResource()) && !schTaskInfo.getResourceIds().isEmpty()){
            // 存储多物理卡
            schTaskInfo.setComputeDeviceId(JSON.toJSONString(schTaskInfo.getResourceIds()));
        }
        // 存储多计算卡
        if("vnpu".equals(schTaskInfo.getNeedResource()) && !schTaskInfo.getResourceIds().isEmpty()){
            schTaskInfo.setPartitionId(JSON.toJSONString(schTaskInfo.getResourceIds()));
        }
        schTaskInfo.setTaskType("inner");
        schTaskInfoMapper.insert(this.buildDefaultValue(schTaskInfo));
        // 构建审核关系
        schTaskApprovalMapper.insert(
                new SchTaskApproval()
                        .setId(idGenerator.nextLongId())
                        .setTaskId(schTaskInfo.getId())
                        .setStatus("pending")
                        .setTaskId(schTaskInfo.getId())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setIsDelete(GlobalDeletedFlag.NORMAL)
                        .setCreateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                        .setUpdateUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
                        .setDataDeptId(Objects.requireNonNull(TokenData.takeFromRequest()).getDeptId())
                        .setDataUserId(Objects.requireNonNull(TokenData.takeFromRequest()).getUserId())
        );


        return schTaskInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchTaskInfo> schTaskInfoList) {
        if (CollUtil.isNotEmpty(schTaskInfoList)) {
            schTaskInfoList.forEach(this::buildDefaultValue);
            schTaskInfoMapper.insertList(schTaskInfoList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchTaskInfo schTaskInfo, SchTaskInfo originalSchTaskInfo) {
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchTaskInfo> uw = this.createUpdateQueryForNullValue(schTaskInfo, schTaskInfo.getId());
        return schTaskInfoMapper.update(schTaskInfo, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schTaskInfoMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchTaskInfo> getSchTaskInfoList(SchTaskInfo filter, String orderBy) {
        return schTaskInfoMapper.getSchTaskInfoList(filter, orderBy);
    }

    @Override
    public List<SchTaskInfo> getSchTaskInfoListWithRelation(SchTaskInfo filter, String orderBy) {
        List<SchTaskInfo> resultList = schTaskInfoMapper.getSchTaskInfoList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }


    /**
     * 启动任务 目前支持单机多卡
     *
     * @param taskId 任务表主键id
     * @return 启动结果数据
     */
    @Override
    public SchTaskInfo startTask(Long taskId) {
        // 获取审核通过任务信息
        SchTaskInfo schTaskInfo = schResourceInfoMapper.approval(taskId);
        if (Objects.isNull(schTaskInfo)) {
            throw new MyRuntimeException("当前任务审核没有通过");
        }
        // 构建任务排队队列 不为空说明在排队
        SchTaskInfo verify = this.memoryQueue(schTaskInfo.getId(), schTaskInfo.getPartitionId());
        if (!Objects.isNull(verify)) {
            return verify;
        }

        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        Objects.requireNonNull(schResourceInfo, "当前任务对应的资源不存在");
        List<Integer> vnpuList = List.of(schTaskInfo.getPartitionId().intValue());
        if (vnpuList.isEmpty()) {
            vnpuList = schTaskInfoMapper.queryInfoVnpu(taskId);
        }
        try {
            return composeGenerate.ordinaryStart(schResourceInfo, vnpuList, schTaskInfo.getTaskImageId(), taskId);
        } catch (IOException e) {
            log.error("startTask: {}", e.getMessage());
            throw new MyRuntimeException(e);
        }
    }

    /**
     * 构建内部任务所需资源
     * @param needResource 需要的资源(physical-物理卡|vnpu-显卡|non-不需要
     */
    public void buildInnerTaskResource(String needResource) {
    }

    /**
     * 抢占调度
     *
     * @param schTaskInfoDto 任务信息
     * @return 返回当前任务数据
     */
    @Override
    public SchTaskInfo priorityDispatch(SchTaskInfoDto schTaskInfoDto) {
        // 1. 参数验证和任务检查
        SchTaskInfo targetTask = validateAndGetTargetTask(schTaskInfoDto);
        List<Long> occupyTaskIds = validateOccupyList(schTaskInfoDto.getOccupyList());
        
        // 2. 查询需要抢占的运行中任务
        List<SchTaskInfo> runningTasks = getRunningTasksToPreempt(occupyTaskIds);
        
        // 3. 更新目标任务状态为启动中
        updateTaskStatus(schTaskInfoDto.getId().toString(), TaskStatusType.STARTING, null);
        
        // 4. 异步执行抢占调度流程
        executePreemptionAsync(targetTask, runningTasks, schTaskInfoDto);
        
        // 5. 立即返回当前任务状态（异步处理中）
        return schTaskInfoMapper.selectById(schTaskInfoDto.getId());
    }
    
    /**
     * 验证并获取目标任务
     */
    private SchTaskInfo validateAndGetTargetTask(SchTaskInfoDto schTaskInfoDto) {
        SchTaskInfo targetTask = schResourceInfoMapper.approval(schTaskInfoDto.getId());
        if (targetTask == null) {
            throw new MyRuntimeException("当前任务审核未通过");
        }
        return targetTask;
    }
    
    /**
     * 验证抢占任务列表
     */
    private List<Long> validateOccupyList(List<Long> occupyList) {
        if (occupyList == null || occupyList.isEmpty()) {
            throw new MyRuntimeException("抢占任务列表不能为空");
        }
        return occupyList.stream().filter(Objects::nonNull).toList();
    }
    
    /**
     * 获取需要抢占的运行中任务
     */
    private List<SchTaskInfo> getRunningTasksToPreempt(List<Long> occupyTaskIds) {
        return schTaskInfoMapper.selectList(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .in(SchTaskInfo::getId, occupyTaskIds)
                        .eq(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
        );
    }
    
    /**
     * 异步执行抢占调度流程
     */
    private void executePreemptionAsync(SchTaskInfo targetTask, List<SchTaskInfo> runningTasks, SchTaskInfoDto schTaskInfoDto) {
        CompletableFuture
                .supplyAsync(() -> {
                    try {
                        // 1. 并行停止被抢占的任务
                        stopPreemptedTasks(runningTasks);
                        
                        // 2. 收集资源信息
                        PreemptionResources resources = collectPreemptionResources(runningTasks, schTaskInfoDto.getResourceId());
                        
                        // 3. 启动目标任务
                        return startTargetTask(targetTask, resources, schTaskInfoDto.getId());
                        
                    } catch (Exception e) {
                        log.error("抢占调度过程发生异常: taskId={}, error={}", schTaskInfoDto.getId(), e.getMessage(), e);
                        throw new MyRuntimeException("抢占调度失败: " + e.getMessage());
                    }
                }, poolExecutor)
                .thenAccept(result -> {
                    if (result != null) {
                        // 成功回调处理
                        this.handlePreemptionSuccess(result);
                    }
                })
                .exceptionally(ex -> {
                    // 异常处理
                    this.handlePreemptionFailure(schTaskInfoDto.getId(), ex);
                    return null;
                });
    }
    
    /**
     * 并行停止被抢占的任务
     */
    private void stopPreemptedTasks(List<SchTaskInfo> runningTasks) {
        List<CompletableFuture<Void>> stopFutures = runningTasks.stream()
                .map(task -> CompletableFuture.runAsync(() -> {
                    try {
                        this.stopTask(task.getId());
                        log.info("成功停止被抢占任务: {}", task.getId());
                    } catch (Exception e) {
                        log.error("停止被抢占任务失败: taskId={}, error={}", task.getId(), e.getMessage(), e);
                    }
                }, poolExecutor))
                .toList();
        
        // 等待所有停止任务完成
        CompletableFuture.allOf(stopFutures.toArray(new CompletableFuture[0])).join();
        log.info("所有被抢占任务已停止，共{}个任务", runningTasks.size());
    }
    
    /**
     * 收集抢占资源信息
     */
    private PreemptionResources collectPreemptionResources(List<SchTaskInfo> runningTasks, Long resourceId) {
        // 获取资源信息
        SchResourceInfo resourceInfo = schResourceInfoMapper.selectById(resourceId);
        if (resourceInfo == null) {
            throw new MyRuntimeException("任务关联的资源不存在");
        }
        
        // 获取计算设备和切分信息
        List<Long> resourceIds = runningTasks.stream()
                .map(SchTaskInfo::getResourceId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        
        List<SchComputeDevice> computeDevices = schComputeDeviceMapper.selectList(
                new LambdaQueryWrapper<SchComputeDevice>()
                        .in(!resourceIds.isEmpty(), SchComputeDevice::getResourceId, resourceIds));
        
        List<Long> deviceIds = computeDevices.stream()
                .map(SchComputeDevice::getId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        
        List<SchVirtualComputeCardSituation> virtualSituations = schVirtualComputeCardSituationMappers.selectList(
                new LambdaQueryWrapper<SchVirtualComputeCardSituation>()
                        .in(!deviceIds.isEmpty(), SchVirtualComputeCardSituation::getComputeDeviceId, deviceIds));
        
        List<Integer> vccIds = virtualSituations.stream()
                .map(SchVirtualComputeCardSituation::getVccId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        
        return new PreemptionResources(resourceInfo, vccIds);
    }
    
    /**
     * 启动目标任务
     */
    private SchTaskInfo startTargetTask(SchTaskInfo targetTask, PreemptionResources resources, Long taskId) {
        try {
            SchTaskInfo result = composeGenerate.generateCompose(
                    resources.getResourceInfo(), 
                    resources.getVccIds(), 
                    targetTask.getTaskImageId(), 
                    taskId
            );
            
            // 更新任务状态为运行中
            updateTaskStatus(taskId.toString(), TaskStatusType.RUNNING, null);
            
            log.info("抢占调度成功启动目标任务: {}", taskId);
            return result;
            
        } catch (IOException e) {
            log.error("启动目标任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new MyRuntimeException("启动任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理抢占成功回调
     */
    private void handlePreemptionSuccess(SchTaskInfo result) {
        log.info("抢占任务异步处理完成，任务状态: {}", result.getStatus());
        try {
            // 可以添加监控数据采集等后续处理
            // composeGenerate.containerMonitorCollect(result.getId());
        } catch (Exception e) {
            log.warn("启动监控数据采集失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理抢占失败回调
     */
    private void handlePreemptionFailure(Long taskId, Throwable ex) {
        log.error("抢占任务失败: taskId={}, error={}", taskId, ex.getMessage(), ex);
        updateTaskStatus(taskId.toString(), TaskStatusType.FAILED, ex.getMessage());
    }
    
    /**
     * 抢占资源信息封装类
     */
    private static class PreemptionResources {
        private final SchResourceInfo resourceInfo;
        private final List<Integer> vccIds;
        
        public PreemptionResources(SchResourceInfo resourceInfo, List<Integer> vccIds) {
            this.resourceInfo = resourceInfo;
            this.vccIds = vccIds;
        }
        
        public SchResourceInfo getResourceInfo() { return resourceInfo; }
        public List<Integer> getVccIds() { return vccIds; }
    }

    /**
     * 构建排队任务队列 构建单机多卡支持 判断当前卡是不是在执行中
     *
     * @param taskId      任务ID
     * @param partitionId 切分卡ID [123,344,555,555]
     */
    public SchTaskInfo memoryQueue(Long taskId, String partitionId) {
        List<SchTaskInfo> running = schTaskInfoMapper.selectList(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
        );
        List<Long>  currentVnpu = JSON.parseArray(partitionId, Long.class);
        //List<Long> list = running.stream().map(SchTaskInfo::getPartitionId).toList();
         running.forEach(i->{
             currentVnpu.forEach(j->{

             });

         });
        if (list.contains(partitionId)) {
            log.warn("memoryQueue....切分卡正在运行中...进入排队队列");
            schTaskInfoMapper
                    .update(new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId).set(SchTaskInfo::getStatus, TaskStatusType.QUEUED)
                    );
            return schTaskInfoMapper.selectById(taskId);
        }
        return null;
    }


    /**
     * 内存调度 抢占调度
     *
     * @param taskId      任务ID
     * @param partitionId 分卡ID
     */
    public SchTaskInfo memoryDispatch(Long taskId, Long partitionId) {
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(taskId);
        List<SchTaskInfo> running = schTaskInfoMapper.selectList(new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, TaskStatusType.RUNNING)
        );
        for (SchTaskInfo item : running) {
            if (item.getPartition() == schTaskInfo.getPartition() && item.getTaskPriority() > schTaskInfo.getTaskPriority()) {
                schTaskInfoMapper
                        .update(new LambdaUpdateWrapper<SchTaskInfo>()
                                .eq(SchTaskInfo::getId, taskId)
                                .set(SchTaskInfo::getStatus, TaskStatusType.QUEUED)
                        );
                return schTaskInfoMapper.selectById(taskId);
            }
        }
        return null;
    }


    @Override
    public List<SchTaskInfo> getGroupedSchTaskInfoListWithRelation(
            SchTaskInfo filter, String groupSelect, String groupBy, String orderBy) {
        List<SchTaskInfo> resultList =
                schTaskInfoMapper.getGroupedSchTaskInfoList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchTaskInfo buildDefaultValue(SchTaskInfo schTaskInfo) {
        if (schTaskInfo.getId() == null) {
            schTaskInfo.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schTaskInfo.setCreateUserId(tokenData.getUserId());
        schTaskInfo.setCreateTime(new Date());
        schTaskInfo.setUpdateTime(new Date());
        schTaskInfo.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schTaskInfo;
    }

    /*
     * 获取容器数量
     * */
    @Override
    public Integer countByContainerName() {
        return schTaskInfoMapper.countByContainerName();
    }


    /**
     * 任务资源占用
     */
    @Override
    public List<SchTaskInfo> taskOccupyResource(Long resourceInfoId, Long resourcePoolId) {
        List<Long> resourceInfoIdList = new ArrayList<>();
        if (resourcePoolId != null) {
            //查询资源池下所有主机
            List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.queryResourceInfoByPoolId(resourcePoolId);
            resourceInfoIdList = schResourceInfoList.stream().map(SchResourceInfo::getId).toList();
        } else if (resourceInfoId != null) {
            resourceInfoIdList.add(resourceInfoId);
        } else {
            resourceInfoIdList = schResourceInfoMapper.selectList(null).stream().map(SchResourceInfo::getId).toList();
        }
        if (resourceInfoIdList.isEmpty()) {
            return new ArrayList<>();
        }
        //查询运行中的任务
        List<SchTaskInfo> schTaskInfos = schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getStatus, "running")
                .in(SchTaskInfo::getResourceId, resourceInfoIdList));
        //提取资源id
        List<Long> resourceIds = schTaskInfos.stream().map(SchTaskInfo::getResourceId).toList();
        //提取显卡id
        List<Long> computeDeviceIds = schTaskInfos.stream().map(SchTaskInfo::getComputeDeviceId).toList();
        Map<Long, SchCardMonitor> schCardMonitorToComputeDeviceIdMap = new HashMap<>();
        Map<Long, SchNodeBasicMetrics> schNodeBasicMetricsToResourceIdMap = new HashMap<>();
        //存入监控信息
        for (SchTaskInfo schTaskInfo : schTaskInfos) {
            if (schTaskInfo.getResourceId() == null) {
                continue;
            }
            //获取最新时间
            SchNodeBasicMetrics newSchNodeBasicMetrics = schNodeBasicMetricsService.getLatestData(schTaskInfo.getResourceId());
            if (schTaskInfo.getComputeDeviceId() != null) {
                //查询显卡监控
                SchCardMonitor schCardMonitor = schCardMonitorService.getNpuMonitoringByComputeDeviceId(schTaskInfo.getComputeDeviceId(), newSchNodeBasicMetrics.getTs());
                schTaskInfo.setSchCardMonitor(schCardMonitor);
//                //提取显卡id转为map
//                schCardMonitorToComputeDeviceIdMap = schCardMonitorList.stream().collect(Collectors.toMap(SchCardMonitor::getComputeDeviceId, Function.identity()));
            }
            if (schTaskInfo.getResourceId() != null) {
                //查询cpu监控
                SchNodeBasicMetrics schNodeBasicMetrics = schNodeBasicMetricsService.getCpuMonitoringByResourceId(schTaskInfo.getResourceId(), newSchNodeBasicMetrics.getTs());
                schTaskInfo.setSchNodeBasicMetrics(schNodeBasicMetrics);
                //提取主机id转为map
//                schNodeBasicMetricsToResourceIdMap = schNodeBasicMetricsList.stream().collect(Collectors.toMap(SchNodeBasicMetrics::getResourceId, Function.identity()));
            }
//            schTaskInfo.setSchCardMonitor(schCardMonitorToComputeDeviceIdMap.get(schTaskInfo.getComputeDeviceId()));
//            schTaskInfo.setSchNodeBasicMetrics(schNodeBasicMetricsToResourceIdMap.get(schTaskInfo.getResourceId()));
        }

        return schTaskInfos;

    }

    /*
     * 获取执行任务的节点数量
     * */
    @Override
    public Integer getNodeNumber() {
        return schTaskInfoMapper.getNodeNumber();
    }

    /*
     * 获取不同状态的任务数量
     * */
    @Override
    public List<Map<String, Object>> getTaskStatusCount() {
        return schTaskInfoMapper.getTaskStatusCount();
    }


    /**
     * 更新数据状态接口
     * @param taskId 任务ID
     * @param status 当前任务状态
     * @param failReason 失败原因
     */
    private void updateTaskStatus(String taskId, String status, String failReason) {
        LambdaUpdateWrapper<SchTaskInfo> updateWrapper = new LambdaUpdateWrapper<SchTaskInfo>()
                .eq(SchTaskInfo::getId, taskId)
                .set(SchTaskInfo::getStatus, status)
                .set(SchTaskInfo::getUpdateTime, new Date());
        
        if (failReason != null) {
            updateWrapper.set(SchTaskInfo::getFailReason, failReason);
        }
        this.update(updateWrapper);
    }

    /**
     * 执行异步更新数据
     * @param taskId 任务id
     * @param originalStatus 初始状态
     * @param operationName 操作名
     * @param taskSupplier 函数接口执行任务
     * @return 结果
     */
    private <T> CompletableFuture<T> executeTaskAsync(String taskId, String originalStatus, 
                                                     String operationName, 
                                                   Supplier<T> taskSupplier) {
        return CompletableFuture
                .supplyAsync(taskSupplier, poolExecutor)
                .exceptionally(ex -> {
                    log.error("任务{}异常: taskId={}, error={}", operationName, taskId, ex.getMessage(), ex);
                    // 恢复原状态并记录错误
                    this.updateTaskStatus(taskId, originalStatus, ex.getMessage());
                    return null;
                });
    }

    /**
     *  统一任务处理操作
     * @param taskId 任务id
     * @param operationStatus 操作状态
     * @param operationName 操作名称
     * @param isRemote 远程调用还是本地调用
     * @param taskSupplier 供给者函数接口
     * @return result
     */
    private <T> ResponseResult<T> handleTaskOperation(String taskId, String operationStatus,
                                                      String operationName, boolean isRemote,
                                                     Supplier<?> taskSupplier) {
        // 参数校验
        if (taskId == null || taskId.trim().isEmpty()) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST, "任务ID不能为空");
        }

        // 查询任务信息
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(taskId));
        if (schTaskInfo == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "任务不存在");
        }

        String originalStatus = schTaskInfo.getStatus();
        
        // 更新状态为操作中
        this.updateTaskStatus(taskId, operationStatus, null);

        // 异步执行任务操作
        this.executeTaskAsync(taskId, originalStatus, operationName, taskSupplier);

        // 返回结果
        if (isRemote) {
            SchTaskInfoVo result = MyModelUtil.copyTo(schTaskInfoMapper.queryStatus(Long.valueOf(taskId)), SchTaskInfoVo.class);
            return (ResponseResult<T>) ResponseResult.success(result);
        } else {
            SchTaskInfo result = schTaskInfoMapper.selectById(Long.valueOf(taskId));
            return (ResponseResult<T>) ResponseResult.success(result);
        }
    }

    /**
     * 任务开始
     * @param taskId 任务ID
     * @return 信息数据
     */
    @Override
    public SchTaskInfo handleStartTask(String taskId) {
        ResponseResult<SchTaskInfo> result = handleTaskOperation(taskId, TaskStatusType.STARTING, "启动", false,
                () -> this.startTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }

    /**
     *  远程任务开始
     * @param taskId 任务ID
     * @return 任务信息
     */
    @Override
    public SchTaskInfoVo handleRemoteStartTask(String taskId) {
        ResponseResult<SchTaskInfoVo> result = handleTaskOperation(taskId, TaskStatusType.STARTING, "远程启动", true,
                () -> this.startTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }

    /**
     * 重启任务
     * @param taskId 任务ID
     * @return 任务信息
     */
    @Override
    public SchTaskInfo handleRestartTask(String taskId) {
        ResponseResult<SchTaskInfo> result = handleTaskOperation(taskId, TaskStatusType.RESTARTING, "重启", false,
                () -> this.restartTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }


    /**
     * 远程任务重启
     * @param taskId 任务ID
     * @return 任务信息
     */
    @Override
    public SchTaskInfoVo handleRemoteRestartTask(String taskId) {
        ResponseResult<SchTaskInfoVo> result = handleTaskOperation(taskId, TaskStatusType.RESTARTING, "远程重启", true,
                () -> this.restartTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }

    /**
     * 停止任务
     * @param taskId 任务ID
     * @return 任务信息
     */
    @Override
    public SchTaskInfo handleStopTask(String taskId) {
        ResponseResult<SchTaskInfo> result = handleTaskOperation(taskId, TaskStatusType.STOPPING, "停止", false,
                () -> this.stopTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }

    /**
     * 远程任务处理
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Override
    public SchTaskInfo handleRemoteStopTask(String taskId) {

        ResponseResult<SchTaskInfo> result = handleTaskOperation(taskId, TaskStatusType.STOPPING, TaskStatusType.STOPPING, true,
                () -> this.stopTask(Long.valueOf(taskId)));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new MyRuntimeException(result.getErrorMessage());
    }
}
