package supie.webadmin.app.service;

import com.jcraft.jsch.JSchException;
import jakarta.validation.constraints.NotNull;
import supie.webadmin.app.dto.RemoteTask;
import supie.webadmin.app.dto.SchTaskInfoDto;
import supie.webadmin.app.model.*;
import supie.common.core.base.service.IBaseService;
import supie.webadmin.app.vo.RemoteTaskVo;
import supie.webadmin.app.vo.SchTaskInfoVo;

import java.io.IOException;
import java.util.*;

/**
 * 任务表数据操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface SchTaskInfoService extends IBaseService<SchTaskInfo, Long> {

    /**
     * 保存新增对象。
     *
     * @param schTaskInfo 新增对象。
     * @return 返回新增对象。
     */
    SchTaskInfo saveNew(SchTaskInfo schTaskInfo);

    /**
     * 利用数据库的insertList语法，批量插入对象列表。
     *
     * @param schTaskInfoList 新增对象列表。
     */
    void saveNewBatch(List<SchTaskInfo> schTaskInfoList);

    /**
     * 更新数据对象。
     *
     * @param schTaskInfo         更新的对象。
     * @param originalSchTaskInfo 原有数据对象。
     * @return 成功返回true，否则false。
     */
    boolean update(SchTaskInfo schTaskInfo, SchTaskInfo originalSchTaskInfo);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getSchTaskInfoListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SchTaskInfo> getSchTaskInfoList(SchTaskInfo filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getSchTaskInfoList)，以便获取更好的查询性能。
     *
     * @param filter 主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SchTaskInfo> getSchTaskInfoListWithRelation(SchTaskInfo filter, String orderBy);

    /**
     * 获取分组过滤后的数据查询结果，以及关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     *
     * @param filter      过滤对象。
     * @param groupSelect 分组显示列表参数。位于SQL语句SELECT的后面。
     * @param groupBy     分组参数。位于SQL语句的GROUP BY后面。
     * @param orderBy     排序字符串，ORDER BY从句的参数。
     * @return 分组过滤结果集。
     */
    List<SchTaskInfo> getGroupedSchTaskInfoListWithRelation(
            SchTaskInfo filter, String groupSelect, String groupBy, String orderBy);

    /**
     *  启动任务
     * @param taskId 任务表主键id
     * @return 启动结果数据
     */
    SchTaskInfo startTask(Long taskId);

    /**
     * 重启失败任务
     *
     * @param taskId 任务id
     */
    SchTaskInfo restartTask( Long taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务id
     */
    SchTaskInfo stopTask( Long taskId);

    /*
    * 统计容器数量（通过容器名统计）
    * */
    Integer countByContainerName();

    /**
     * 构建服务器的 资源池、资源、计算卡、切分ID
     * @return 返回资源信息集合
     */
    List<RootNode> relationResource();

    /*
    * 查询任务占用资源，选择运行中状态
    * @return 返回任务占用资源
    * */
    Integer getNodeNumber();

    /*
    * 查询各种状态的任务数量
    * */
    List<Map<String, Object>> getTaskStatusCount();


    /**
     * 审核任务
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @return 返回审核任务结果集
     */
    SchTaskApproval auditTask(String taskId, @NotNull String status);


    /**
     *  抢占调度
     * @param
     * @return 返回当前任务数据
     */
    SchTaskInfo priorityDispatch(SchTaskInfoDto schTaskInfoDto);

    List<SchTaskInfo> taskOccupyResource(Long resourceInfoId,Long resourcePoolId);


    /**
     * 批量启动任务
     * @param idList 任务列表
     * @return 启动数据结果
     */
    List<SchTaskInfo> batchStart(List<String> idList);


    /**
     *  构建nginx  配置
     * @param resourceId 资源id
     * @param taskId 信息列表
     * @param export 存储信息
     */
    void nginxProxy(Long resourceId, Long taskId, String export, String serviceType) throws JSchException, IOException;

    /**
     * 运行任务批量停止
     * @param idList 运行型任务列表
     * @return 任务信息列表
     */
    List<SchTaskInfo> batchStop(List<String> idList);

    /**
     * 远程任务调度
     * @param remoteTask 任务调度
     */
    RemoteTaskVo remoteTask(RemoteTask remoteTask);

    /**
     * 扩缩容正在执行的容器任务
     * @param schTaskInfos 正在执行的仁列表
     */
    Map<String,List<SchTaskInfo>> expandDecreaseMonitor(List<SchTaskInfo> schTaskInfos);

    /**
     * 定时更新任务数据状态
     */
    void upDateTaskStatus();




    /**
     * 统一的任务启动操作
     * @param taskId 任务ID
     * @return 任务信息
     */
    SchTaskInfo handleStartTask(String taskId);

    /**
     * 统一的远程任务启动操作
     * @param taskId 任务ID
     * @return 任务信息VO
     */
    SchTaskInfoVo handleRemoteStartTask(String taskId);

    /**
     * 统一的任务重启操作
     * @param taskId 任务ID
     * @return 任务信息
     */
    SchTaskInfo handleRestartTask(String taskId);

    /**
     * 统一的远程任务重启操作
     * @param taskId 任务ID
     * @return 任务信息VO
     */
    SchTaskInfoVo handleRemoteRestartTask(String taskId);

    /**
     * 统一的任务停止操作
     * @param taskId 任务ID
     * @return 任务信息
     */
    SchTaskInfo handleStopTask(String taskId);

    /**
     * 统一的远程任务停止操作
     * @param taskId 任务ID
     * @return 任务信息
     */
    SchTaskInfo handleRemoteStopTask(String taskId);
}
