package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jcraft.jsch.*;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import supie.common.core.annotation.MyDataSource;
import supie.common.core.exception.MyRuntimeException;
import supie.common.core.util.RsaUtil;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.dto.SchResourceInfoDto;
import supie.webadmin.app.util.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.constant.ErrorCodeEnum;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.object.ResponseResult;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.app.vo.SchResourceInfoVo;
import supie.webadmin.config.DataSourceType;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务资源信息表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schResourceInfoService")
@MyDataSource(DataSourceType.MAIN)
public class SchResourceInfoServiceImpl extends BaseService<SchResourceInfo, Long> implements SchResourceInfoService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchResourceInfoMapper schResourceInfoMapper;
    @Autowired
    private SchResourceInfoService schResourceInfoService;
    @Autowired
    private SchTaskInfoService schTaskInfoService;
    @Autowired
    private SchNodeBasicMetricsService schNodeBasicMetricsService;
    @Autowired
    private SchComputeDeviceService schComputeDeviceService;
    @Autowired
    private SchCardMonitorService schCardMonitorService;
    @Autowired
    private SchResourcePoolMapper schResourcePoolMapper;
    @Autowired
    private SchComputeDeviceMapper schComputeDeviceMapper;
    @Autowired
    private SchCardMonitorMapper schCardMonitorMapper;
    @Autowired
    private SchTaskInfoMapper schTaskInfoMapper;
    @Autowired
    private SchVirtualComputeCardSituationMapper schVirtualComputeCardSituationMapper;

    @Resource
    private CpuMonitoringInformationToolUtil cpuMonitoringInformationToolUtil;
    @Resource
    private GpuMonitoringInformationToolUtil gpuMonitoringInformationToolUtil;

    @Resource
    private NpuToolUtil npuToolUtil;

    @Resource
    private SchResourcePoolMemberMapper resourcePoolMemberMapper;
    @Resource
    private SshUtil sshUtil;

    /**
     * 服务器密码解密私钥
     */
    @Value("${application.server.secretKey}")
    private String secretKey;


    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchResourceInfo> mapper() {
        return schResourceInfoMapper;
    }

    /**
     * 验证服务器系统版本号，信息信息, cpu核心数 总内存，显卡数 显卡内存
     *
     * @param schResourceInfoDto 服务器连接信息
     * @return 构建的验证信息
     */
    @Override
    public ResponseResult<SchResourceInfoVo> verifyServerConnection(SchResourceInfoDto schResourceInfoDto) {
        try {
            SshConfig sshConfig = new SshConfig();
            sshConfig.setHost(schResourceInfoDto.getHostIp());//************
            //sshConfig.setHost("*************");//************
            sshConfig.setPort(schResourceInfoDto.getPort());//22
            //sshConfig.setPort(40086);//22
            sshConfig.setUsername(schResourceInfoDto.getLoginName());//root
           // sshConfig.setUsername("root");//roo
            sshConfig.setPassword(schResourceInfoDto.getPassword());//aliyun20011013!
           // sshConfig.setPassword("Ascend12!@");//aliyun20011013!
            log.info("开始测试SSH连接: {}@{}:{}", sshConfig.getUsername(), sshConfig.getHost(), sshConfig.getPort());
            // 执行SSH连接测试 - 优先使用脚本方法，失败时回退到命令方法
            ServerInfo serverInfo = getServerInfoWithFallback(sshConfig);
            //SystemInfoCollector.collectSystemInfo();
            SchResourceInfoVo svo = new SchResourceInfoVo();
            svo.setCpuCoreCount(serverInfo.getCpuCoreCount());
            svo.setMemoryCapacity(serverInfo.getTotalMemory().replace("MB", ""));
            svo.setGpuCount(serverInfo.getGpuCount());
            svo.setSystemInfo(serverInfo.getSystemInfo());
            svo.setSystemVersion(serverInfo.getSystemVersion());
            svo.setUsedMemory(serverInfo.getUsedMemory().replace("MB", ""));
            this.prometheus(schResourceInfoDto.getHostIp(),serverInfo,schResourceInfoDto.getComputeDevicePort());
            svo.setGraphicsMemory(serverInfo.getGpuMemory());
            if (serverInfo.isConnected()) {
                svo.setStatus("active");
                log.info("SSH连接测试成功，系统信息: {}", serverInfo.getSystemInfo());
                return ResponseResult.success(svo);
            } else {
                log.warn("SSH连接测试失败: {}", serverInfo.getErrorMessage());
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "连接测试失败");
            }

        } catch (Exception e) {
            log.error("服务器连接测试异常: {}", e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.OPERATION_FAILED, "连接测试异常");
        }
    }

    /**
     *  使用 prometheus 采集数据指标
     * @param ip ip 地址
     * @param serverInfo 服务信息结果
     * @param port 端口
     */
    public void prometheus(String ip,ServerInfo serverInfo,String port) {
        String url = String.format("http://%s:%s/metrics", ip, port);        List<Map<String, String>> npuData = npuToolUtil.npuInformationss(url);
        AtomicReference<Integer> usedMem= new AtomicReference<>(0);
        AtomicReference<Integer> availableMem= new AtomicReference<>(0);
        AtomicReference<Integer> gricphMem= new AtomicReference<>(0);
        npuData.forEach(
                item -> {
                   item.forEach(
                           (key, value) -> {
                              if("usedMemMemory".equals(key)){
                                  usedMem.updateAndGet(v -> v + Integer.parseInt(value));
                              }
                              if("fbFree".equals(key)){
                                  availableMem.updateAndGet(v -> v + Integer.parseInt(value));
                              }
                              if("fbTotal".equals(key)){
                                  gricphMem.updateAndGet(v -> v + Integer.parseInt(value));
                              }
                           }
                   );
                }
        );
        serverInfo.setGpuMemory(String.valueOf(gricphMem.get()));
        serverInfo.setUsedMemory(String.valueOf(usedMem.get()));
        serverInfo.setAvailableMemory(String.valueOf(availableMem.get()));
    }

    /**
     * 获取服务器信息，带回退机制
     * 优先使用脚本上传方式，失败时自动回退到内联命令方式
     *
     * @param sshConfig SSH连接配置
     * @return 服务器信息
     */
    private ServerInfo getServerInfoWithFallback(SshConfig sshConfig) {
        ServerInfo serverInfo;

        try {
            // 优先尝试脚本上传方式
            log.info("尝试使用脚本上传方式获取服务器信息");
            serverInfo = sshUtil.getServerInfoByScript(sshConfig);
            // 如果连接成功且获取到了基本信息，则返回结果
            if (serverInfo.isConnected() &&
                    serverInfo.getSystemInfo() != null &&
                    !"Unknown".equals(serverInfo.getSystemInfo())) {
                log.info("脚本上传方式获取服务器信息成功");
                return serverInfo;
            }

        } catch (Exception e) {
            log.warn("脚本上传方式失败，: {}", e.getMessage());
            throw  new MyRuntimeException(e.getMessage());
        }



        // 如果所有方式都失败，返回失败的服务器信息
        serverInfo = new ServerInfo();
        serverInfo.setConnected(false);
        serverInfo.setErrorMessage("所有获取服务器信息的方式都失败");
        return serverInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchResourceInfo saveNew(SchResourceInfo schResourceInfo) {
        schResourceInfoMapper.insert(this.buildDefaultValue(schResourceInfo));
        //构建关联关系
        resourcePoolMemberMapper.insert(new SchResourcePoolMember()
                .setId(idGenerator.nextLongId())
                .setResourceId(schResourceInfo.getId())
                .setPoolId(schResourceInfo.getPoolId())
                .setUpdateTime(new Date())
                .setCreateTime(new Date())
        );
        return schResourceInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchResourceInfo> schResourceInfoList) {
        if (CollUtil.isNotEmpty(schResourceInfoList)) {
            schResourceInfoList.forEach(this::buildDefaultValue);
            schResourceInfoMapper.insertList(schResourceInfoList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchResourceInfo schResourceInfo, SchResourceInfo originalSchResourceInfo) {
        schResourceInfo.setCreateUserId(originalSchResourceInfo.getCreateUserId());
        schResourceInfo.setCreateTime(originalSchResourceInfo.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchResourceInfo> uw = this.createUpdateQueryForNullValue(schResourceInfo, schResourceInfo.getId());
        boolean flag = schResourceInfoMapper.update(schResourceInfo, uw) == 1;
        // 更新绑定关系 Todo
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schResourceInfoMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchResourceInfo> getSchResourceInfoList(SchResourceInfo filter, String orderBy) {
        return schResourceInfoMapper.getSchResourceInfoList(filter, orderBy);
    }

    /**
     * 更新资源池和资源池关联的资源信息
     *
     * @param poolId 当前资源池id resource_pool_id
     * @param id     当前资源id resource_info_id
     */
    @Override
    public void updateBindRelation(Long poolId, Long id) {
        // 更新资源池关系
        SchResourcePoolMember sch = resourcePoolMemberMapper.queryRelation(id);
        if (sch.getPoolId() != null && !Objects.equals(poolId, sch.getPoolId())) {
            // 更新数据关系
            sch.setPoolId(poolId)
                    .setUpdateTime(new Date());
            resourcePoolMemberMapper.update(sch,
                    new LambdaUpdateWrapper<SchResourcePoolMember>().
                            eq(SchResourcePoolMember::getId, sch.getId()
                            )
            );
        }

    }

    @Override
    public List<SchResourceInfo> getSchResourceInfoListWithRelation(SchResourceInfo filter, String orderBy) {
        List<SchResourceInfo> resultList = schResourceInfoMapper.getSchResourceInfoList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        monitoringMetrics(resultList);
        return resultList;
    }

    @Override
    public List<SchResourceInfo> getGroupedSchResourceInfoListWithRelation(
            SchResourceInfo filter, String groupSelect, String groupBy, String orderBy) {
        List<SchResourceInfo> resultList =
                schResourceInfoMapper.getGroupedSchResourceInfoList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchResourceInfo buildDefaultValue(SchResourceInfo schResourceInfo) {
        if (schResourceInfo.getId() == null) {
            schResourceInfo.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schResourceInfo.setCreateUserId(tokenData.getUserId());
        schResourceInfo.setCreateTime(new Date());
        schResourceInfo.setUpdateTime(new Date());
        schResourceInfo.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schResourceInfo;
    }

    /**
     * 获取服务器各种不同资源的总量
     *
     * @return 统计结果集。
     */
    @Override
    public HashMap<String, Object> getResourceStatistics() {
        HashMap<String, Long> statsFromMapper = schResourceInfoMapper.getResourceStatistics();

        // 如果 mapper 返回 null，创建空的 HashMap 避免 NPE
        HashMap<String, Long> safeStatsFromMapper = (statsFromMapper != null) ? statsFromMapper : new HashMap<>();

        // 转换为支持多种类型的 Map
        HashMap<String, Object> resourceStatistics = new HashMap<>(safeStatsFromMapper);

        //
        Long resourceTotal = statsFromMapper.get("resourceTotal");
        // 获取并安全处理各统计值
        Long virtualResoureCount = Optional.ofNullable(schComputeDeviceService.getVirtualResoureCount())
                .map(Long::valueOf)
                .orElse(0L);
//wenti
        Integer NodeNumber = Optional.ofNullable(schTaskInfoService.getNodeNumber())
                .orElse(0);
        Integer resourceUsedNumber = schResourceInfoService.getResourceUsedNumber();
        Double resourceRateUsage = resourceTotal == 0 ? 0 : (resourceUsedNumber * 100.0 / resourceTotal);

        //Double resourceRateUsage = resourceTotal == 0 ? 0 : (virtualResoureCount * 100.0 / resourceTotal);
//        Integer containerCount = Optional.ofNullable(schTaskInfoService.countByContainerName())
//                .orElse(0);


        // 放入结果
        resourceStatistics.put("virtualResoureCount", virtualResoureCount);
        resourceStatistics.put("resourceRateUsage", resourceRateUsage); // 放入 Double
        //resourceStatistics.put("containerCount", containerCount);
        //存放任务的执行情况
        List<Map<String, Object>> taskStatusCount = schTaskInfoService.getTaskStatusCount();
        resourceStatistics.put("taskStatusCount", taskStatusCount);
        LambdaQueryWrapper<SchResourcePool> queryWrapper = new LambdaQueryWrapper<>();
        Long total = schResourcePoolMapper.selectCount(queryWrapper);
        resourceStatistics.put("totalPool", total);
        return resourceStatistics;
    }


    /**
     * 连接远程主机
     *
     * @param resourceInfoId 资源服务对象id
     */
    @Override
    public Session SshConnector(Long resourceInfoId) {
        SchResourceInfo resourceInfo = schResourceInfoService.getById(resourceInfoId);
        if (resourceInfo == null) {
            throw new RuntimeException("远程主机不存在");
        }
        Session session;
        try {
            JSch jsch = new JSch();
            String hostIp = resourceInfo.getHostIp();
            JSONObject configObject = JSON.parseObject(resourceInfo.getConnectConfigJson());
            String username = configObject.getString("username");
            Integer port = Integer.parseInt(configObject.getString("port"));
            session = jsch.getSession(username, hostIp, port);

            if (configObject.containsKey("password") && !configObject.getString("password").isEmpty()) {
                String password = configObject.getString("password");
                session.setPassword(password);
            }
            //跳过ssh主机密钥验证，直接连接远程服务器
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(1000 * 10);
        } catch (JSchException e) {
            log.error("连接时发生错误!", e);
            throw new RuntimeException();
        }
        return session;
    }

    /*
     * 在远程主机上执行命令,将结果打印成日志
     * */
    public void execution(Long resourceInfoId, String command) {
        Session session = SshConnector(resourceInfoId);
        try {
            Channel channel = session.openChannel("exec");
            ((ChannelExec) channel).setCommand(command);
            // 设置日志记录器来记录标准错误流
            OutputStream errStream = new OutputStream() {
                private final StringBuilder lineBuffer = new StringBuilder();

                @Override
                public void write(int b) {
                    char c = (char) b;
                    lineBuffer.append(c);
                    if (c == '\n' || lineBuffer.length() >= 1024) {
                        // 当遇到换行或缓冲区达到一定大小时，输出日志
                        log.error(lineBuffer.toString());
                        // 清空缓冲区
                        lineBuffer.setLength(0);
                    }
                }

                @Override
                public void close() throws IOException {
                    super.close();
                    // 在流关闭时，确保缓冲区内剩余的内容也被记录
                    if (lineBuffer.length() > 0) {
                        log.error(lineBuffer.toString());
                        lineBuffer.setLength(0);
                    }
                }
            };
            ((ChannelExec) channel).setErrStream(errStream);

            InputStream in = channel.getInputStream();
            channel.connect();
            byte[] tmp = new byte[1024];
            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) {
                        break;
                    }
                    log.info(new String(tmp, 0, i));
                }
                if (channel.isClosed()) {
                    if (in.available() > 0) {
                        continue;
                    }
                    log.info("exit-status: " + channel.getExitStatus());
                    break;
                }
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error(String.valueOf(e));
                }
            }
            channel.disconnect();
        } catch (JSchException | IOException e) {
            log.error(String.valueOf(e));
            throw new RuntimeException(e);
        } finally {
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 在远程服务器上执行命令并返回结果
     *
     * @param resourceInfoId 远程主机ID
     * @param command        要执行的命令
     * @return 命令执行结果
     */
    public String executeCommand(Long resourceInfoId, String command) {
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(resourceInfoId);
        if (schResourceInfo == null) {
            throw new MyRuntimeException("远程主机不存在");
        }

        Session session = null;
        ChannelExec channel = null;
        try {
            session = SshConnector(resourceInfoId);
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            channel.setOutputStream(outputStream);
            channel.setErrStream(outputStream);

            channel.connect();

            while (channel.isConnected()) {
                Thread.sleep(100);
            }
            return outputStream.toString(String.valueOf(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("执行远程命令失败: " + command, e);
            throw new MyRuntimeException("执行远程命令失败: " + e.getMessage());
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 在远程服务器上执行命令并返回结果。
     *
     * @param resourceInfoId 远程主机ID
     * @param command        要执行的命令
     * @return 命令执行结果字符串
     * @throws MyRuntimeException 执行失败时抛出异常
     */
    public String executeCommands(Long resourceInfoId, String command) {
        SchResourceInfo schResourceInfo = schResourceInfoService.getById(resourceInfoId);
        if (schResourceInfo == null) {
            throw new MyRuntimeException("远程主机不存在");
        }

        Session session = null;
        ChannelExec channel = null;
        try {
            // 建立SSH连接
            session = SshConnector(resourceInfoId);

            // 打开执行通道并设置命令
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);

            // 设置输入输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            channel.setOutputStream(outputStream);
            channel.setErrStream(outputStream); // 合并标准错误流

            // 设置连接超时时间（单位：毫秒）
            int connectTimeout = 10_000; // 最多等待10秒
            channel.connect(connectTimeout);

            // 等待命令执行完成
            long startTime = System.currentTimeMillis();
            while (channel.isConnected()) {
                Thread.sleep(100);
                if (System.currentTimeMillis() - startTime > connectTimeout) {
                    throw new MyRuntimeException("命令执行超时: " + command);
                }
            }

            // 返回UTF-8解码后的结果
            return outputStream.toString(StandardCharsets.UTF_8.name());

        } catch (JSchException | InterruptedException e) {
            log.error("执行远程命令失败: {}", command, e);
            throw new MyRuntimeException("执行远程命令失败: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("IO异常: {}", e.getMessage(), e);
            throw new MyRuntimeException("执行远程命令时发生IO错误", e);
        } finally {
            // 安全关闭资源
            if (channel != null && !channel.isClosed()) {
                channel.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }


    /**
     * 安全执行命令并返回结果
     */
    private String executeSafeCommand(Long resourceId, String command) {
        try {
            return executeCommand(resourceId, command).trim();
        } catch (Exception e) {
            log.warn("执行命令失败: {}", command, e);
            return null;
        }
    }

    /**
     * 获取历史信息：分别获取节点的历史信息和计算卡的历史信息
     *
     * @param schResourceInfo
     */
    @Override
    public List<String> getHistoryInfos(SchResourceInfo schResourceInfo) {
        List<String> historyInfos = new ArrayList<>();
        Long resourceId = schResourceInfo.getId();

        // 获取节点最新监控数据
        LambdaQueryWrapper<SchNodeBasicMetrics> nodeMetricsWrapper = new LambdaQueryWrapper<>();
        nodeMetricsWrapper.eq(SchNodeBasicMetrics::getResourceId, resourceId)
                .orderByDesc(SchNodeBasicMetrics::getCreateTime)
                .last("LIMIT 1");
        SchNodeBasicMetrics latestNodeMetric = schNodeBasicMetricsService.getOne(nodeMetricsWrapper);
        if (latestNodeMetric != null) {
            historyInfos.add(JSON.toJSONString(latestNodeMetric));
        }

        // 获取该节点下所有计算卡 ID
        List<Long> computeDeviceIds = schComputeDeviceService.list(new LambdaQueryWrapper<SchComputeDevice>()
                        .eq(SchComputeDevice::getResourceId, resourceId))
                .stream()
                .map(SchComputeDevice::getId)
                .toList();

        // 批量查询每个计算卡的最新一条监控记录
        if (!computeDeviceIds.isEmpty()) {
            List<SchCardMonitor> latestDeviceMetrics = schCardMonitorService.list(
                    new LambdaQueryWrapper<SchCardMonitor>()
                            .in(SchCardMonitor::getComputeDeviceId, computeDeviceIds)
                            .orderByDesc(SchCardMonitor::getCreateTime)
                            .last("LIMIT " + computeDeviceIds.size())
            );

            // 使用 Map 去重，确保每个 computeDeviceId 只保留最新的一个记录
            Map<Long, SchCardMonitor> uniqueMetrics = new LinkedHashMap<>();
            for (SchCardMonitor metric : latestDeviceMetrics) {
                uniqueMetrics.putIfAbsent(metric.getComputeDeviceId(), metric);
            }


            historyInfos.addAll(
                    uniqueMetrics.values().stream()
                            .filter(Objects::nonNull)
                            .map(JSON::toJSONString)
                            .toList()
            );
        }

        return historyInfos;
    }

    @Override
    public List<String> getNowInfos(SchResourceInfoDto schResourceInfoDto) {
        String hostId = schResourceInfoDto.getHostIp();
        if (hostId != null) {

        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getResource(AgentConfig agentConfig) {
        List<Map<String, Object>> informations = new ArrayList<>();
        String cpuUrl = "http://" + agentConfig.getHost() + ":" + agentConfig.getCpuPort() + "/metrics";
        String compteDeviceUrl = "http://" + agentConfig.getHost() + ":" + agentConfig.getComputeDevicePort() + "/metrics";

        try {
            Map<String, Double> cpuInformations = cpuMonitoringInformationToolUtil.getCpuMonitoringInformationTool(cpuUrl);
            Map<String, Object> gpuInformations = gpuMonitoringInformationToolUtil.getGpuInformation(compteDeviceUrl);
            List<Map<String, String>> npuInformations = npuToolUtil.npuInformationss(compteDeviceUrl);
            // 转换类型保证泛型兼容
            Map<String, Object> convertedCpuMetrics = new HashMap<>(cpuInformations);
            informations.add(convertedCpuMetrics);
            informations.add(gpuInformations);
            List<Map<String, Object>> npuList = new ArrayList<>();
            if (npuInformations != null) {
                for (Map<String, String> item : npuInformations) {
                    Map<String, Object> convertedItem = new HashMap<>();
                    for (Map.Entry<String, String> entry : item.entrySet()) {
                        convertedItem.put(entry.getKey(), entry.getValue());
                    }
                    npuList.add(convertedItem);
                }
            }
            informations.addAll(npuList);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return informations;
    }


    @Override
    public List<Map<String, Object>> getResources(JSONObject agentConfig) {
        List<Map<String, Object>> informations = new ArrayList<>();
        //String cpuUrl = "http://" + agentConfig.getHost() + ":" + agentConfig.getCpuPort() + "/metrics";
        String cpuUrl = "http://" + agentConfig.getString("hostIp") + ":" + agentConfig.getString("cpuPort") + "/metrics";
        String compteDeviceUrl = "http://" + agentConfig.getString("hostIp") + ":" + agentConfig.getString("computeDevicePort") + "/metrics";

        try {
            Map<String, Double> cpuInformations = cpuMonitoringInformationToolUtil.getCpuMonitoringInformationTool(cpuUrl);
            Map<String, Object> gpuInformations = gpuMonitoringInformationToolUtil.getGpuInformation(compteDeviceUrl);
            List<Map<String, String>> npuInformations = npuToolUtil.npuInformationss(compteDeviceUrl);
            // 转换类型保证泛型兼容
            Map<String, Object> convertedCpuMetrics = new HashMap<>(cpuInformations);
            informations.add(convertedCpuMetrics);
            informations.add(gpuInformations);

            List<Map<String, Object>> npuList = new ArrayList<>();
            if (npuInformations != null) {
                for (Map<String, String> item : npuInformations) {
                    Map<String, Object> convertedItem = new HashMap<>();
                    if (item != null) {
                        for (Map.Entry<String, String> entry : item.entrySet()) {
                            String key = entry.getKey();
                            String value = entry.getValue();
                            convertedItem.put(key, value); // 值保留为 String 类型，保证兼容性
                        }
                    }
                    npuList.add(convertedItem);
                }
            }
            informations.addAll(npuList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return informations;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override//
    public void saveComputeDeviceInfo(List<Map<String, Object>> resourceInfo, ServerInfo serverInfo, AgentConfig agentConfig) {
        SchResourceInfo schResourceInfo = new SchResourceInfo();
        schResourceInfo.setId(idGenerator.nextLongId());
        schResourceInfo.setCreateTime(new Date());
        schResourceInfo.setUpdateTime(new Date());
        TokenData tokenData = TokenData.takeFromRequest();
        schResourceInfo.setCreateUserId(tokenData.getUserId());
        schResourceInfo.setIsDelete(GlobalDeletedFlag.NORMAL);

        schResourceInfo.setHostIp(agentConfig.getHost());
        schResourceInfo.setPort(Integer.parseInt(agentConfig.getCpuPort()));
        schResourceInfo.setUuid(UUID.randomUUID().toString());
        schResourceInfo.setPassword(agentConfig.getPassword());
        schResourceInfo.setLoginName(agentConfig.getUsername());
        //schResourceInfo.setResourceType();//缺少资源名
        schResourceInfo.setCpuCoreCount(serverInfo.getCpuCoreCount());
        schResourceInfo.setSystemInfo(serverInfo.getSystemInfo());
        //schResourceInfo.setResourceType();//缺少资源类型
        schResourceInfo.setMemoryCapacity(serverInfo.getTotalMemory());
        //schResourceInfo.setGpuCount(serverInfo.getGpuCount());
        //schResourceInfo.setAvailableMemory(serverInfo.getUsedMemory());
        schResourceInfo.setSystemVersion(serverInfo.getSystemVersion());
        schResourceInfo.setUsedMemory(serverInfo.getUsedMemory());
        if (schResourceInfo.getResourceType() == "GPU") {
            schResourceInfo.setGpuMemory(serverInfo.getGpuMemory());//GPU
        } else if (schResourceInfo.getResourceType() == "NPU") {
            schResourceInfo.setGraphicsMemory(serverInfo.getGpuMemory());//NPU
        }
        schResourceInfo.setStatus(serverInfo.isConnected() ? "active" : "error");
        schResourceInfoService.save(schResourceInfo);
        //存储计算卡信息
        List<Map<String, Object>> serialNumber = resourceInfo.stream()
                .filter(m -> m.get("serialNumber") != null)
                .toList();
        List<SchComputeDevice> schComputeDeviceList = new ArrayList<>();
        for (Map<String, Object> map : serialNumber) {
            SchComputeDevice schComputeDevice = new SchComputeDevice();
            schComputeDevice.setId(idGenerator.nextLongId());
            schComputeDevice.setDeviceNumber(Integer.parseInt(String.valueOf(map.get("serialNumber"))));
            schComputeDevice.setUpdateTime(new Date());
            schComputeDevice.setCreateTime(new Date());
            schComputeDevice.setCreateUserId(tokenData.getUserId());
            schComputeDevice.setDeviceName(String.valueOf(map.get("modelName")));
            //schComputeDevice.setDeviceType();
            //schComputeDevice.setArchitecture();
            schComputeDevice.setIsDelete(GlobalDeletedFlag.NORMAL);
            schComputeDevice.setStatus(String.valueOf(map.get("healthStatus")));
            //schComputeDevice.setCudaNumber();
            //schComputeDevice.setDoublePrecision();
            schComputeDevice.setMemorySize(Integer.parseInt(String.valueOf(map.get("fbTotal"))));
            //schComputeDevice.setMemoryType();
            //schComputeDevice.setModelNumber();
            schComputeDevice.setResourceId(schResourceInfo.getId());
            //schComputeDevice.setSinglePrecision();
            //schComputeDevice.setSemiPrecision();
//            schComputeDevice.setTensorNumber();
//            schComputeDevice.setTotalCompute();
            schComputeDeviceList.add(schComputeDevice);
        }
        schComputeDeviceService.saveNewBatch(schComputeDeviceList);
    }

    /*
     *
     * */
    @Override
    public void registerResourceInfo(SchResourceInfo schResourceInfo) {
        if (schResourceInfo.getHostIp() == null || schResourceInfo.getHostIp().trim().isEmpty()) {
            throw new MyRuntimeException("请填写节点IP");
        }
        if (schResourceInfo.getPort() == null || schResourceInfo.getPort() == 0) {
            throw new MyRuntimeException("请填写节点端口");
        }

        if (schResourceInfo.getId() == null || schResourceInfo.getId() == 0) {
            schResourceInfo.setId(idGenerator.nextLongId());
        }
        schResourceInfo.setCreateTime(new Date());
        schResourceInfo.setUpdateTime(new Date());
        TokenData tokenData = TokenData.takeFromRequest();
        assert tokenData != null;
        schResourceInfo.setCreateUserId(tokenData.getUserId());
        schResourceInfo.setIsDelete(GlobalDeletedFlag.NORMAL);
        //需要调用方法从节点查询数据
        SshConfig sshConfig = new SshConfig();
        //前端传入的参数
        sshConfig.setHost(schResourceInfo.getHostIp());
        sshConfig.setPort(schResourceInfo.getPort());
        sshConfig.setUsername(schResourceInfo.getLoginName());
        sshConfig.setPassword(schResourceInfo.getPassword());
        // TODO 脚本上传
        /*ServerInfo serverInfo = sshUtil.getServerInfo(sshConfig);
        schResourceInfo.setMemoryCapacity(serverInfo.getTotalMemory());
        schResourceInfo.setGraphicsMemory(serverInfo.getGpuMemory());
        schResourceInfo.setCpuCoreCount(serverInfo.getCpuCoreCount());
        schResourceInfo.setStatus(serverInfo.isConnected() ? "active" : "error");
        //保存节点信息
        schResourceInfoService.save(schResourceInfo);*/
        //保存计算卡信息
        //SchComputeDevice schComputeDevice = new SchComputeDevice();


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveComputeDeviceInfoV2(List<Map<String, Object>> resourceInfo, JSONObject agentConfig) {
        SchResourceInfo schResourceInfo = new SchResourceInfo();
        List<Map<String, Object>> serialNumber = resourceInfo.stream()
                .filter(m -> m.get("serialNumber") != null)
                .collect(Collectors.toList());

        schResourceInfo.setId(idGenerator.nextLongId());
        schResourceInfo.setCreateTime(new Date());
        schResourceInfo.setCpuPort(agentConfig.getString("cpuPort"));
        schResourceInfo.setComputeDevicePort(agentConfig.getString("computeDevicePort"));
        schResourceInfo.setUpdateTime(new Date());
        //schResourceInfo.setDiskCapacity(resourceInfo. get(0).get("diskCapacity").toString());
        schResourceInfo.setDiskCapacity(getValueByListMap(resourceInfo, "diskSum").toString());
        TokenData tokenData = TokenData.takeFromRequest();
        schResourceInfo.setCreateUserId(tokenData.getUserId());
        schResourceInfo.setIsDelete(GlobalDeletedFlag.NORMAL);

        schResourceInfo.setHostIp(agentConfig.getString("hostIp"));
        schResourceInfo.setPort(Integer.parseInt(agentConfig.getString("cpuPort")));
        schResourceInfo.setUuid(UUID.randomUUID().toString());
        schResourceInfo.setPassword(agentConfig.getString("password"));
        schResourceInfo.setLoginName(agentConfig.getString("loginName"));
        schResourceInfo.setResourceName(agentConfig.getString("resourceName"));//资源名
        schResourceInfo.setCpuCoreCount(agentConfig.getString("cpuCoreCount"));
        schResourceInfo.setSystemInfo(agentConfig.getString("systemInfo"));
        schResourceInfo.setResourceType(agentConfig.getString("resourceType"));//资源类型
        schResourceInfo.setMemoryCapacity(agentConfig.getString("memoryCapacity"));
        schResourceInfo.setGpuCount(String.valueOf(serialNumber.size()));
        schResourceInfo.setAvailableMemory(agentConfig.getString("availableMemory"));
        schResourceInfo.setSystemVersion(agentConfig.getString("systemVersion"));
        schResourceInfo.setUsedMemory(agentConfig.getString("usedMemory"));
        JSONObject jsonObject = schResourceInfoService.addDeviceInfo(agentConfig.getString("productType"));
        schResourceInfo.setGpuMemory(agentConfig.getString("graphicsMemory"));
        if ("GPU".equals(jsonObject.getString("deviceType"))) {
            schResourceInfo.setGpuMemory(agentConfig.getString("graphicsMemory"));//GPU
        } else if ("NPU".equals(jsonObject.getString("deviceType"))) {
            schResourceInfo.setGraphicsMemory(agentConfig.getString("graphicsMemory"));//NPU
        }
        schResourceInfo.setStatus(agentConfig.getString("status"));
        schResourceInfoService.save(schResourceInfo);
        //存储计算卡信息

        Map<Long, SchComputeDevice> schComputeDeviceMap = new HashMap<>();

        for (Map<String, Object> map : serialNumber) {

            SchComputeDevice schComputeDevice = new SchComputeDevice();
            schComputeDevice.setId(idGenerator.nextLongId());
            schComputeDevice.setProductType(jsonObject.getInteger("productType"));
            schComputeDevice.setDeviceNumber(Integer.parseInt(String.valueOf(map.get("serialNumber"))));
            schComputeDevice.setUpdateTime(new Date());
            schComputeDevice.setCreateTime(new Date());
            schComputeDevice.setCreateUserId(tokenData.getUserId());
            schComputeDevice.setDeviceName(String.valueOf(map.get("modelName")));
            schComputeDevice.setDeviceType(jsonObject.getString("deviceType"));
            schComputeDevice.setArchitecture(agentConfig.getString("architecture"));
            schComputeDevice.setIsDelete(GlobalDeletedFlag.NORMAL);
            schComputeDevice.setStatus(String.valueOf(map.get("healthStatus")));
            //schComputeDevice.setCudaNumber();
            schComputeDevice.setDoublePrecision(BigDecimal.valueOf(jsonObject.getDouble("doublePrecision") != null ? jsonObject.getDouble("doublePrecision") : 0.0));
            schComputeDevice.setMemorySize(Integer.parseInt(String.valueOf(map.get("fbTotal"))));

            schComputeDevice.setResourceId(schResourceInfo.getId());
            schComputeDevice.setSinglePrecision(BigDecimal.valueOf(jsonObject.getDouble("singlePrecision") != null ? jsonObject.getDouble("singlePrecision") : 0.0));
            schComputeDevice.setSemiPrecision(BigDecimal.valueOf(jsonObject.getDouble("semiPrecision") != null ? jsonObject.getDouble("semiPrecision") : 0.0));
            schComputeDeviceMap.put(schComputeDevice.getId(), schComputeDevice);
        }
        List<SchComputeDevice> schComputeDeviceList = new ArrayList<>(schComputeDeviceMap.values());
        schComputeDeviceService.saveNewBatch(schComputeDeviceList);
    }

    public Object getValueByListMap(List<Map<String, Object>> resourceInfo, String fieldName) {
        int size = resourceInfo.size();
        if (size == 0) {

        }
        for (Map<String, Object> item : resourceInfo) {
            if (item.containsKey(fieldName)) {
                return item.get(fieldName); // 返回第一个匹配的字段值
            }
        }
        return null;
    }

    @Override
    public JSONObject addDeviceInfo(String productType) {
        if (productType == null) {
            // 可选择抛出异常、记录日志或返回错误信息
            log.error("productType类型不能为空");
            throw new MyRuntimeException();
        }
        JSONObject jsonObject = new JSONObject();
        switch (productType) {
            case "910A":
                jsonObject.put("deviceType", "npu");
                jsonObject.put("semiPrecision", "256");
                jsonObject.put("singlePrecision", "");
                jsonObject.put("doublePrecision", "");
                jsonObject.put("productType", "1");
                break;
            case "910B":
                jsonObject.put("deviceType", "npu");
                jsonObject.put("semiPrecision", "320");
                jsonObject.put("singlePrecision", "128");
                jsonObject.put("doublePrecision", "");
                jsonObject.put("productType", "1");
                break;
            case "H100":
                jsonObject.put("deviceType", "gpu");
                jsonObject.put("semiPrecision", "1979");
                jsonObject.put("singlePrecision", "");
                jsonObject.put("doublePrecision", "67");
                jsonObject.put("productType", "3");
                break;
            default:
                // 处理未识别的属性
                break;
        }
        return jsonObject;
    }

    @Override
    public List<Map<String, Object>> getComputeDeviceInfoByNodeId(SchResourceInfo schResourceInfo) {
        List<Map<String, Object>> informations = new ArrayList<>();
        String hostIp = schResourceInfo.getHostIp();
        //String cpuPort = schResourceInfo.getCpuPort();
        String computeDevicePort = schResourceInfo.getComputeDevicePort();

        String compteDeviceUrl = "http://" + hostIp + ":" + computeDevicePort + "/metrics";
        //String compteDeviceUrl = "http://*************:10011/metrics";
        try {
            List<Map<String, String>> npuInformations = npuToolUtil.npuInformationss(compteDeviceUrl);
            Map<String, Object> gpuInformations = gpuMonitoringInformationToolUtil.getGpuInformation(compteDeviceUrl);
            informations.add(gpuInformations);
            List<Map<String, Object>> npuList = new ArrayList<>();
            if (npuInformations != null) {
                for (Map<String, String> item : npuInformations) {
                    Map<String, Object> convertedItem = new HashMap<>();
                    for (Map.Entry<String, String> entry : item.entrySet()) {
                        convertedItem.put(entry.getKey(), entry.getValue());
                    }
                    npuList.add(convertedItem);
                }
            }
            informations.addAll(npuList);
        } catch (IOException e) {
            log.error("出现io错误" + e.getMessage(), e);
        }
        return informations;
    }

    @Override
    public List<SchResourceInfo> getAllList() {
        return schResourceInfoMapper.selectList(null);
    }

    /**
     * 统计指标
     *
     * @return resultList 结果列表
     */
    @Override
    public Map<String, Object> statisticalIndicators(Long resourceId, String tsStart, String tsEnd, String type, Integer interval, Long resourcePoolId) {
        Map<String, Object> map = new HashMap<>();
        List<Long> resourceIdList = new ArrayList<>();
        if (resourcePoolId != null) {
            //查询资源池下所有主机
            List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.queryResourceInfoByPoolId(resourcePoolId);
            resourceIdList = schResourceInfoList.stream().map(SchResourceInfo::getId).toList();
        } else if (resourceId != null) {
            resourceIdList.add(resourceId);
        } else {
            resourceIdList = schResourceInfoMapper.selectList(null).stream().map(SchResourceInfo::getId).toList();
        }
        if (resourceIdList.isEmpty()) {
            return map;
        }
        if (tsStart == null) {
            // 默认24小时前
            LocalDateTime twentyFourHoursAgo = LocalDateTime.now().minus(24, ChronoUnit.HOURS);
//            tsStart = DateUtil.formatDateTime(twentyFourHoursAgo); // 使用Hutool格式化日期
            tsStart = String.valueOf(twentyFourHoursAgo);
        }
        if (tsEnd == null) {
            // 默认当前时间
            tsEnd = String.valueOf(LocalDateTime.now());
        }
        if (interval == null) {
            interval = 60;
        }

        //设置筛选
        SchCardMonitor filter = new SchCardMonitor();
        filter.setResourceId(resourceId);
        filter.setTsStart(tsStart);
        filter.setTsEnd(tsEnd);
        SchNodeBasicMetrics schNodeBasicMetrics = new SchNodeBasicMetrics();
        schNodeBasicMetrics.setResourceId(resourceId);
        schNodeBasicMetrics.setTsStartStart(tsStart);
        schNodeBasicMetrics.setTsEndEnd(tsEnd);
        //查询Cpu监控
        List<SchNodeBasicMetrics> schNodeBasicMetricsList = schNodeBasicMetricsService.getSchNodeBasicMetricsListByFilter(schNodeBasicMetrics, resourceIdList);
        //查询Npu监控
        List<SchCardMonitor> schCardMonitorList = schCardMonitorService.getSchCardMonitorListByFilter(filter, resourceIdList);
        ;
        //根据时间间隔获取数据
        List<SchCardMonitor> schCardMonitorLists = new ArrayList<>();
        List<SchNodeBasicMetrics> schNodeBasicMetricsLists = new ArrayList<>();
        // 存储分组后的 CPU 和 NPU 数据
        Map<String, List<SchNodeBasicMetrics>> groupedCpuData = new LinkedHashMap<>();
        Map<String, List<SchCardMonitor>> groupedNpuData = new LinkedHashMap<>();
        // 按时间范围预分组
// 按时间范围分组 CPU 数据，并将 key 设置为时间间隔的中间时间
        Integer finalInterval = interval;
        Map<LocalDateTime, List<SchNodeBasicMetrics>> cpuDataByTimeRange = schNodeBasicMetricsList.stream()
                .collect(Collectors.groupingBy(data -> {
                    LocalDateTime tsLocal = data.getTsLocal().truncatedTo(ChronoUnit.MINUTES); // 截断到分钟
                    int minute = tsLocal.getMinute();
                    int intervalStartMinute = (minute / finalInterval) * finalInterval; // 计算当前间隔的起始分钟
                    LocalDateTime intervalStart = tsLocal.withMinute(intervalStartMinute); // 当前间隔的起始时间
                    LocalDateTime midTime = intervalStart.plusMinutes(finalInterval / 2); // 计算中间时间
                    return midTime;
                }));

// 按时间范围分组 NPU 数据，并将 key 设置为时间间隔的中间时间
        Map<LocalDateTime, List<SchCardMonitor>> npuDataByTimeRange = schCardMonitorList.stream()
                .collect(Collectors.groupingBy(data -> {
                    LocalDateTime tsLocal = data.getTsLocal().truncatedTo(ChronoUnit.MINUTES); // 截断到分钟
                    int minute = tsLocal.getMinute();
                    int intervalStartMinute = (minute / finalInterval) * finalInterval; // 计算当前间隔的起始分钟
                    LocalDateTime intervalStart = tsLocal.withMinute(intervalStartMinute); // 当前间隔的起始时间
                    LocalDateTime midTime = intervalStart.plusMinutes(finalInterval / 2); // 计算中间时间
                    return midTime;
                }));
        //遍历map
        for (Map.Entry<LocalDateTime, List<SchNodeBasicMetrics>> entry : cpuDataByTimeRange.entrySet()) {
            //判断是获取什么值
//            if("average".equals(type)){
//                //计算所有字段的平均值
//                SchNodeBasicMetrics cpuMetrics = calculateCPUAverage(entry.getValue());
//                cpuMetrics.setTs(Timestamp.valueOf(entry.getKey()));
//                schNodeBasicMetricsLists.add(cpuMetrics);
//            }else if("max".equals(type)){
//                SchNodeBasicMetrics cpuMetrics = calculateCPUMaximum(entry.getValue());
//                cpuMetrics.setTs(Timestamp.valueOf(entry.getKey()));
//                schNodeBasicMetricsLists.add(cpuMetrics);
//            }else if("min".equals(type)){
//                SchNodeBasicMetrics cpuMetrics = calculateCPUMinimum(entry.getValue());
//                cpuMetrics.setTs(Timestamp.valueOf(entry.getKey()));
//                schNodeBasicMetricsLists.add(cpuMetrics);
//            }else if("sum".equals(type)){
//                SchNodeBasicMetrics cpuMetrics = calculateCPUSum(entry.getValue());
//                cpuMetrics.setTs(Timestamp.valueOf(entry.getKey()));
//                schNodeBasicMetricsLists.add(cpuMetrics);
//            }else{
//                schNodeBasicMetricsLists.addAll(entry.getValue());
//            }
            SchNodeBasicMetrics cpuMetrics = calculateCPUAverage(entry.getValue());
            cpuMetrics.setTs(Timestamp.valueOf(entry.getKey()));
            schNodeBasicMetricsLists.add(cpuMetrics);
//            schNodeBasicMetricsLists.addAll(entry.getValue());
        }
        for (Map.Entry<LocalDateTime, List<SchCardMonitor>> entry : npuDataByTimeRange.entrySet()) {
//            if("average".equals(type)){
//                //计算所有字段的平均值
//                List<SchCardMonitor> list = calculateNPUAverage(entry.getValue());
//                for (SchCardMonitor schCardMonitor : list){
//                    schCardMonitor.setTs(Timestamp.valueOf(entry.getKey()));
//                }
//                schCardMonitorLists.addAll(list);
//            }else if("max".equals(type)){
//                List<SchCardMonitor> list = calculateNPUMaximum(entry.getValue());
//                for (SchCardMonitor schCardMonitor : list){
//                    schCardMonitor.setTs(Timestamp.valueOf(entry.getKey()));
//                }
//                schCardMonitorLists.addAll(list);
//            }else if("min".equals(type)){
//                List<SchCardMonitor> list = calculateNPUMinimum(entry.getValue());
//                for (SchCardMonitor schCardMonitor : list){
//                    schCardMonitor.setTs(Timestamp.valueOf(entry.getKey()));
//                }
//                schCardMonitorLists.addAll(list);
//            }else if("sum".equals(type)){
//                List<SchCardMonitor> list = calculateNPUSum(entry.getValue());
//                for (SchCardMonitor schCardMonitor : list){
//                    schCardMonitor.setTs(Timestamp.valueOf(entry.getKey()));
//                }
//                schCardMonitorLists.addAll(list);
//            }else{
//                schCardMonitorLists.addAll(entry.getValue());
//            }
            List<SchCardMonitor> list = calculateNPUAverage(entry.getValue());
            for (SchCardMonitor schCardMonitor : list) {
                schCardMonitor.setTs(Timestamp.valueOf(entry.getKey()));
            }
            schCardMonitorLists.addAll(list);
        }

        //根据显卡序号分组
        Map<Integer, List<SchCardMonitor>> schCardMonitorMap = schCardMonitorLists.stream()
                .collect(Collectors.groupingBy(SchCardMonitor::getSerialNumber));
        //修改key值类型为string
        Map<String, List<SchCardMonitor>> schCardMonitorMaps = new HashMap<>();
        for (Map.Entry<Integer, List<SchCardMonitor>> entry : schCardMonitorMap.entrySet()) {
            schCardMonitorMaps.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        map.put("NPU", schCardMonitorMaps);
        map.put("CPU", schNodeBasicMetricsLists);

        return map;
    }

    /**
     * 计算CPU平均值。
     *
     * @param dataList dataList
     * @return 应答结果对象，包含查询结果集。
     */
    @Override
    public SchNodeBasicMetrics calculateCPUAverage(List<SchNodeBasicMetrics> dataList) {
        SchNodeBasicMetrics schNodeBasicMetrics = new SchNodeBasicMetrics();
        if (dataList == null || dataList.isEmpty()) {
            return schNodeBasicMetrics; // 如果列表为空，返回 null
        }
        // 定义累加变量
        BigDecimal cpuUtilizationSum = BigDecimal.ZERO;
        BigDecimal overallCpuUsageSum = BigDecimal.ZERO;
        BigDecimal memoryUtilizationSum = BigDecimal.ZERO;
        BigDecimal videoUtilizationSum = BigDecimal.ZERO;
        double networkSpeedSum = 0.0;
        double availableMemorySum = 0.0;
        double diskIoWrittenAndReadSumSum = 0.0;
        double nowThreadSum = 0.0;
        double processMemoryUsageSum = 0.0;
        double processMemoryAmountSum = 0.0;
        double diskUsedSum = 0.0;
        double diskUtilizationSum = 0.0;


        // 遍历列表，累加每个字段的值
        for (SchNodeBasicMetrics data : dataList) {
            cpuUtilizationSum = cpuUtilizationSum.add(Optional.ofNullable(data.getCpuUtilization()).orElse(BigDecimal.ZERO));
            overallCpuUsageSum = overallCpuUsageSum.add(Optional.ofNullable(data.getOverallCpuUsage()).orElse(BigDecimal.ZERO));
            memoryUtilizationSum = memoryUtilizationSum.add(Optional.ofNullable(data.getMemoryUtilization()).orElse(BigDecimal.ZERO));
            videoUtilizationSum = videoUtilizationSum.add(Optional.ofNullable(data.getVideoUtilization()).orElse(BigDecimal.ZERO));
            networkSpeedSum += Optional.ofNullable(data.getNetworkSpeedSum()).orElse(0.0);
            availableMemorySum += Optional.ofNullable(data.getAvailableMemory()).orElse(0.0);
            diskIoWrittenAndReadSumSum += Optional.ofNullable(data.getDiskIoWrittenAndReadSum()).orElse(0.0);
            nowThreadSum += Optional.ofNullable(data.getNowThread()).orElse(0.0);
            processMemoryUsageSum += Optional.ofNullable(data.getProcessMemoryUsage()).orElse(0.0);
            processMemoryAmountSum += Optional.ofNullable(data.getProcessMemoryAmount()).orElse(0.0);
            diskUsedSum += Optional.ofNullable(data.getDiskUsedSum()).orElse(0.0);
            diskUtilizationSum += Optional.ofNullable(data.getDiskUtilization()).orElse(0.0);


        }

        // 计算平均值
        int size = dataList.size();
        // 计算中间索引
        int middleIndex = size / 2;
        // 获取中间数据的时间戳
        schNodeBasicMetrics.setTs(dataList.get(middleIndex).getTs());
//        schNodeBasicMetrics.setRemoteHostId(dataList.get(0).getRemoteHostId());
        schNodeBasicMetrics.setCpuUtilization(cpuUtilizationSum.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
        schNodeBasicMetrics.setOverallCpuUsage(overallCpuUsageSum.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
        schNodeBasicMetrics.setMemoryUtilization(memoryUtilizationSum.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
        schNodeBasicMetrics.setVideoUtilization(videoUtilizationSum.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
        schNodeBasicMetrics.setNetworkSpeedSum(networkSpeedSum / size);
        schNodeBasicMetrics.setAvailableMemory(availableMemorySum / size);
        schNodeBasicMetrics.setDiskIoWrittenAndReadSum(diskIoWrittenAndReadSumSum / size);
        schNodeBasicMetrics.setNowThread(nowThreadSum / size);
        schNodeBasicMetrics.setProcessMemoryUsage(processMemoryUsageSum / size);
        schNodeBasicMetrics.setProcessMemoryAmount(processMemoryAmountSum / size);
        schNodeBasicMetrics.setDiskUsedSum(diskUsedSum / size);
        schNodeBasicMetrics.setDiskUtilization(diskUtilizationSum / size);

        return schNodeBasicMetrics;
    }

    /**
     * 计算每个NPU平均值。
     *
     * @param dataList dataList
     * @return 应答结果对象，包含查询结果集。
     */
    @Override
    public List<SchCardMonitor> calculateNPUAverage(List<SchCardMonitor> dataList) {
        List<SchCardMonitor> schCardMonitorList = new ArrayList<>();
        //根据卡片序号分组
        Map<Integer, List<SchCardMonitor>> schCardMonitorMap = dataList.stream()
                .collect(Collectors.groupingBy(SchCardMonitor::getSerialNumber));
        //遍历分别计算每张卡的数据
        for (Map.Entry<Integer, List<SchCardMonitor>> entry : schCardMonitorMap.entrySet()) {
            // 定义累加变量
//            BigDecimal memoryUtilSum = BigDecimal.ZERO;
            Long memoryTotalSum = 0L;
            Long memoryUsedSum = 0L;

            // 遍历列表，累加每个字段的值
            for (SchCardMonitor data : entry.getValue()) {
//                memoryUtilSum = memoryUtilSum.add(data.getMemoryUtil());
                memoryTotalSum += data.getHbTotal();
                memoryUsedSum += data.getHbUsed();
            }

            SchCardMonitor schCardMonitors = new SchCardMonitor();
            // 计算平均值
            int size = entry.getValue().size();
            // 计算中间索引
            int middleIndex = size / 2;
            double memoryTotalAverage = memoryTotalSum / size;
            double memoryUsedAverage = memoryUsedSum / size;
            double memoryUtil = memoryUsedAverage * 100.0f / memoryTotalAverage;
            schCardMonitors.setMemoryUtil(BigDecimal.valueOf(memoryUtil));
            schCardMonitors.setTs(entry.getValue().get(middleIndex).getTs());
            schCardMonitors.setMemoryTotal(memoryTotalAverage);
            schCardMonitors.setMemoryUsed(memoryUsedAverage);
            schCardMonitors.setSerialNumber(entry.getKey());

            schCardMonitorList.add(schCardMonitors);

        }
        return schCardMonitorList;
    }

    @Override
    public Map<String, Object> resourceTotal() {
        HashMap<String, Object> resourceStatistics = new HashMap<>();
        HashMap<String, Long> resourceStatistic = schResourceInfoMapper.getResourceStatistics();
        resourceStatistics.put("resourceStatistic", resourceStatistic);
        List<Map<String, Object>> taskStatusCount = schTaskInfoService.getTaskStatusCount();
        int taskTotalCount = taskStatusCount.stream()
                .mapToInt(maps -> {
                    Number count = (Number) maps.get("count");
                    return count != null ? count.intValue() : 0; // 默认值设为 0
                })
                .sum();
        resourceStatistics.put("taskTotalCount", taskTotalCount);
//        List<Map<String, Object>> resourceStatusGroup = schResourceInfoService.getResourceStatusGroup();
//        resourceStatistics.put("resourceStatusGroup", resourceStatusGroup);
        //计算卡状态统计 -- 先查询出还在用的资源id，然后查询对应的id，最后查询任务表，看排队和运行状态的任务 --占用
        List<Map<String, Object>> mapList = schResourceInfoMapper.queryComputeDeviceTask();
        //查询全部卡的数量
        //去重因为被切而多次收集的卡

        List<SchComputeDevice> schComputeDeviceList = schResourceInfoMapper.queryComputeDeviceAll();
        int size = schComputeDeviceList.size(); //全部卡的数量
        List<Map<String, Object>> runingTaskStatus = mapList.stream()
                .filter(i -> "running".equals(i.get("taskStatus")))
                .toList();
        List<Long> distinctDeviceIdsRuning = runingTaskStatus.stream()
                .map(map -> Long.parseLong(String.valueOf(map.get("computeDeviceId"))))// 转换为 Long
                .filter(Objects::nonNull)                      // 过滤 null
                .distinct()                                    // 去重
                .collect(Collectors.toList());
        List<Map<String, Object>> queuedTaskStatus = mapList.stream()
                .filter(i -> "queued".equals(i.get("taskStatus")))
                .toList();
        List<Long> distinctDeviceIdsQueued = queuedTaskStatus.stream()
                .map(map -> Long.parseLong(String.valueOf(map.get("computeDeviceId"))))// 转换为 Long
                .filter(Objects::nonNull)                      // 过滤 null
                .distinct()                                    // 去重
                .collect(Collectors.toList());
        int used = distinctDeviceIdsRuning.size() + distinctDeviceIdsQueued.size();
        int free = size - used;
        resourceStatistics.put("usedComputeDevice", used);
        resourceStatistics.put("totalComputeDevice", size);
        resourceStatistics.put("freeComputeDevice", free);
        return resourceStatistics;
    }


    /**
     * 监控统计
     *
     * @return resultList 结果列表
     */
    @Override
    public List<SchResourceInfo> monitoringMetrics(List<SchResourceInfo> schResourceInfoList) {
//        List<Map<String, Object>> resultList = new ArrayList<>();
        //查询主机
//        List<SchResourceInfo> schResourceInfoList = schResourceInfoMapper.getSchResourceInfoList(schResourceInfoFiler, orderBy);
        //提取主机id
        List<Long> schResourceInfoIdList = schResourceInfoList.stream().map(SchResourceInfo::getId).collect(Collectors.toList());
        if (schResourceInfoIdList.isEmpty()) {
            return schResourceInfoList;
        }
        //查询最近20分钟内cpu数据
        List<SchNodeBasicMetrics> schNodeBasicMetricsList = schNodeBasicMetricsService.getRecentData(schResourceInfoIdList);
        //根据主机id分组
        Map<Long, List<SchNodeBasicMetrics>> schNodeBasicMetricsMap = schNodeBasicMetricsList.stream()
                .collect(Collectors.groupingBy(SchNodeBasicMetrics::getResourceId));
        //查询出最新Gpu的数据
        List<SchCardMonitor> schCardMonitorList = schCardMonitorService.getSchCardMonitorListByidList(schResourceInfoIdList);
        //根据主机id分组
        Map<Long, List<SchCardMonitor>> schCardMonitorMap = schCardMonitorList.stream()
                .collect(Collectors.groupingBy(SchCardMonitor::getResourceId));
        //遍历remoteHostList
        for (SchResourceInfo schResourceInfo : schResourceInfoList) {
            Map<String, Object> map = new HashMap<>();
            List<SchCardMonitor> schCardMonitor = schCardMonitorMap.get(schResourceInfo.getId());
            //统计显存占用平均值
            if (schCardMonitor == null) {
                map.put("fbUsedUtil", 0);
            } else {
                Long memoryTotalSum = schCardMonitor.stream().mapToLong(SchCardMonitor::getHbTotal).sum() / schCardMonitor.size();
                Long memoryUsedSum = schCardMonitor.stream().mapToLong(SchCardMonitor::getHbUsed).sum() / schCardMonitor.size();
                Long fbUsedUtil = memoryUsedSum / memoryTotalSum;
                map.put("fbUsedUtil", fbUsedUtil);
            }
            List<SchNodeBasicMetrics> schNodeBasicMetrics = new ArrayList<>();
            schNodeBasicMetrics = schNodeBasicMetricsMap.get(schResourceInfo.getId());
            map.put("CPUData", schNodeBasicMetrics);
            map.put("hostName", schResourceInfo.getResourceName());
//            map.put("hardwareAcceleratorModel",schResourceInfo.getHardwareAcceleratorModel());
            map.put("resourceInfoId", schResourceInfo.getId());
            schResourceInfo.setMonitoringStatistics(map);
        }
        return schResourceInfoList;
    }

    @Override
    public List<SchResourceInfo> getSchResourceInfoListByPoolId(Long poolId) {
        List<SchResourceInfo> resultList = schResourceInfoMapper.queryResourceInfoByPoolId(poolId);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        monitoringMetrics(resultList);
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<Map<String, Object>> getResourceStatusGroup() {
        List<Map<String, Object>> resourceStatusGroup = schResourceInfoMapper.getResourceStatusGroup();
        return resourceStatusGroup;
    }

    public List<SchComputeDevice> getDeviceInfoByResource(SchResourceInfo schResourceInfo) {
        Long id = schResourceInfo.getId();
        LambdaQueryWrapper<SchComputeDevice> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SchComputeDevice::getResourceId, id);
        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(lambdaQueryWrapper);
        if (schComputeDeviceList == null) {
            throw new RuntimeException("没有找到对应的设备信息");
        }
        return schComputeDeviceList;
    }

    @Override
    public List<SchCardMonitor> getSchCardMonitorInfoByResource(SchResourceInfo schResourceInfo) {
        Long id = schResourceInfo.getId();
        List<SchCardMonitor> schCardMonitorList = schCardMonitorService.getSchCardMonitorListByResourceId(id);
        if (schCardMonitorList == null) {
            throw new RuntimeException("没有找到对应的设备信息");
        }

        return schCardMonitorList;
    }

    @Override
    public SchNodeBasicMetrics getSchNodeBasicMetricsInfoByResource(SchResourceInfo schResourceInfo) {
        Long id = schResourceInfo.getId();
        SchNodeBasicMetrics latestData = schNodeBasicMetricsService.getLatestData(id);
        return latestData;
    }

    @Override
    public List<Map<String, Object>> getResourceAggregationStats(List<Long> resourceIds) {
        List<Map<String, Object>> resourceAggregationStats = schResourceInfoMapper.getResourceAggregationStats(resourceIds);
        return resourceAggregationStats;
    }

    @Override
    public Map<String, Object> getResourceTaskStats(SchResourceInfo schResourceInfo) {
        //查询任务列表
        Long resourceId = schResourceInfo.getId();
        Map<String, Object> map = new HashMap<>();
        //查询任务列表
        List<SchTaskInfo> schTaskInfoList = schTaskInfoMapper.selectList(new LambdaQueryWrapper<SchTaskInfo>()
                .in(SchTaskInfo::getResourceId, resourceId));
        //筛选初始化的pending任务状态
        List<SchTaskInfo> pendingSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("pending"))
                .toList();
        //筛选初始化的queued任务状态
        List<SchTaskInfo> queuedSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("queued"))
                .toList();
        //筛选初始化的starting任务状态
        List<SchTaskInfo> startingSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("starting"))
                .toList();
        //筛选初始化的stop任务状态
        List<SchTaskInfo> stopSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("starting"))
                .toList();
        //筛选进行中得任务
        List<SchTaskInfo> runSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("running"))
                .toList();
        //筛选完成任务
        List<SchTaskInfo> finishedSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("finished"))
                .toList();
        //筛选失败得任务
        List<SchTaskInfo> failedSchTaskInfoList = schTaskInfoList.stream()
                .filter(i -> i.getStatus().equals("failed"))
                .toList();
        map.put("pendingTask", pendingSchTaskInfoList.size());
        map.put("queuedTask", queuedSchTaskInfoList.size());
        map.put("startingTask", startingSchTaskInfoList.size());
        map.put("stopTask", stopSchTaskInfoList.size());
        map.put("runningTask", runSchTaskInfoList.size());
        map.put("finishedTask", finishedSchTaskInfoList.size());
        map.put("failedTask", failedSchTaskInfoList.size());
        map.put("totalTask", schTaskInfoList.size());
        return map;
    }

    /*
     * 计算已使用的资源数量：任务占用+虚拟切分
     * */
//    @Override
//    public Integer getResourceUsedNumber() {
//        //获取被任务占用的资源数量
//        LambdaQueryWrapper<SchTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.select(SchTaskInfo::getComputeDeviceId)
//                .eq(SchTaskInfo::getIsDelete, 1)
//                .isNotNull(SchTaskInfo::getComputeDeviceId)
//                .groupBy(SchTaskInfo::getComputeDeviceId);
//        List<Map<String, Object>> schTaskInfoMap = schTaskInfoMapper.selectMaps(queryWrapper);
//        List<Long> computeDeviceId = schTaskInfoMap.stream().map(map -> (Long) map.get("compute_device_id")).distinct().toList();
//        LambdaQueryWrapper<SchComputeDevice> computeDeviceLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        computeDeviceLambdaQueryWrapper.select()
//                        .eq(SchComputeDevice::getIsDelete, 1)
//                                .in(SchComputeDevice::getId, computeDeviceId);
//        List<SchComputeDevice> schComputeDeviceList = schComputeDeviceMapper.selectList(computeDeviceLambdaQueryWrapper);
//        List<Long> resourceIdUsed = schComputeDeviceList.stream().map(SchComputeDevice::getResourceId).distinct().toList();
//       // int usedNumber = resourceIdUsed.size();
//        //切分数量统计 -- 从切分情况表里查看哪些卡被切分
//        LambdaQueryWrapper<SchVirtualComputeCardSituation> schVirtualComputeCardSituationQueryWrapper = new LambdaQueryWrapper<>();
//        schVirtualComputeCardSituationQueryWrapper.select(SchVirtualComputeCardSituation::getComputeDeviceId)
//                .eq(SchVirtualComputeCardSituation::getIsDelete,1)
//                .isNotNull(SchVirtualComputeCardSituation::getComputeDeviceId)
//                .groupBy(SchVirtualComputeCardSituation::getComputeDeviceId);
//        List<Map<String, Object>> schVirtualComputeCardSituationMap = schVirtualComputeCardSituationMapper.selectMaps(schVirtualComputeCardSituationQueryWrapper);
//        List<Long> computeDeviceIds = schVirtualComputeCardSituationMap.stream().map(map -> (Long) map.get("compute_device_id")).distinct().toList();
//        computeDeviceLambdaQueryWrapper.clear();
//        computeDeviceLambdaQueryWrapper.select()
//                .eq(SchComputeDevice::getIsDelete, 1)
//                .in(SchComputeDevice::getId, computeDeviceIds);
//        List<SchComputeDevice> schComputeDeviceList1 = schComputeDeviceMapper.selectList(computeDeviceLambdaQueryWrapper);
//        List<Long> resourceIdVirtual = schComputeDeviceList1.stream().map(SchComputeDevice::getResourceId).distinct().toList();
//
//        //去重，避免资源重复计数
//        Set<Long> resourceIdSet = new HashSet<>();
//        resourceIdSet.addAll(resourceIdUsed);
//        resourceIdSet.addAll(resourceIdVirtual);
//        int number = resourceIdSet.size();
//        return number;
//    }
    @Override
    public Integer getResourceUsedNumber() {
        // 获取被任务占用的 compute_device_id
        List<Long> usedDeviceIds = schTaskInfoMapper.selectObjs(
                new LambdaQueryWrapper<SchTaskInfo>()
                        .eq(SchTaskInfo::getIsDelete, 1)
                        .isNotNull(SchTaskInfo::getComputeDeviceId)
                        .select(SchTaskInfo::getComputeDeviceId)
                        .groupBy(SchTaskInfo::getComputeDeviceId)
        ).stream().map(obj -> (Long) obj).collect(Collectors.toList());

        // 获取虚拟切分的 compute_device_id
        List<Long> virtualDeviceIds = schVirtualComputeCardSituationMapper.selectObjs(
                new LambdaQueryWrapper<SchVirtualComputeCardSituation>()
                        .eq(SchVirtualComputeCardSituation::getIsDelete, 1)
                        .isNotNull(SchVirtualComputeCardSituation::getComputeDeviceId)
                        .select(SchVirtualComputeCardSituation::getComputeDeviceId)
                        .groupBy(SchVirtualComputeCardSituation::getComputeDeviceId)
        ).stream().map(obj -> (Long) obj).collect(Collectors.toList());

        // 合并并去重
        Set<Long> mergedDeviceIds = new HashSet<>(usedDeviceIds);
        mergedDeviceIds.addAll(virtualDeviceIds);

        if (mergedDeviceIds.isEmpty()) {
            return 0;
        }

        // 查询 sch_compute_device 中关联的 resource_id（去重）
        List<Long> resourceIds = schComputeDeviceMapper.selectList(new LambdaQueryWrapper<SchComputeDevice>()
                        .eq(SchComputeDevice::getIsDelete, 1)
                        .in(SchComputeDevice::getId, mergedDeviceIds))
                .stream()
                .map(SchComputeDevice::getResourceId)
                .distinct()
                .toList();

        return resourceIds.size();
    }
}
