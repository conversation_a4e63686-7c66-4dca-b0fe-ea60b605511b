spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 数据库链接 [主数据源]
      main:
        url: ***************************************************************************************************************************
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
      # 默认生成的操作日志数据源配置。
      operation-log:
        url: ***************************************************************************************************************************
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
      # 默认生成的全局编码字典数据源配置。
      global-dict:
        url: ***************************************************************************************************************************
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
      # 默认生成的工作流及在线表单数据源配置。
      common-flow-online:
        url: ***************************************************************************************************************************
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
      # 默认生成的统计打印模块的数据源配置。
      common-report:
        url: ***************************************************************************************************************************
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
#      tdengine:
#        driver-class-name: com.taosdata.jdbc.rs.RestfulDriver
#        url: jdbc:TAOS-RS://*************:30057/large_model_dev_train_test
#        username: root
#        password: 2bz2wuidrkwi
      clickhome:
        driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
        url: jdbc:ch://*************:30054/large_model_dev_train_test
        username: root
        password: eW2dZ9dM8pZ7jS9wB7qY8oI0m
      driverClassName: com.mysql.cj.jdbc.Driver
      name: application-webadmin
      initialSize: 10
      minIdle: 10
      maxActive: 100
      remove-abandoned: true  # 开启连接泄漏检测
      remove-abandoned-timeout: 300  # 超过300秒未关闭的连接会被回收
      log-abandoned: true  # 记录泄漏日志      maxWait: 60000
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      maxOpenPreparedStatements: 20
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      filters: stat,wall
      useGlobalDataSourceStat: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*,/actuator/*"
      stat-view-servlet:
        enabled: true
        urlPattern: /druid/*
        resetEnable: true
  redis:
    host: *************
    port: 30051
    password: dQrtU2SmycrWWf3zhpzQfw0tS
    database: 1


application:
  # Jwt令牌加密的签名值。该值的长度要超过10个字符(过短会报错)。
  tokenSigningKey: LargeModelDev-signing-key-supiedt
  # Jwt令牌在Http Header中的键名称。
  tokenHeaderKey: Authorization
  # Jwt令牌刷新后在Http Header中的键名称。
  refreshedTokenHeaderKey: RefreshedToken
  # Jwt令牌过期时间(毫秒)。
  expiration: 72000000
  # 初始化密码。
  defaultUserPassword: 123456
  # 缺省的文件上传根目录。
  uploadFileBaseDir: ./zz-resource/upload-files/app
  # 跨域的IP(http://*************:8086)白名单列表，多个IP之间逗号分隔(* 表示全部信任，空白表示禁用跨域信任)。
  credentialIpList: "*"
  # Session的用户和数据权限在Redis中的过期时间(秒)。
  sessionExpiredSeconds: 86400
  # 是否排他登录。
  excludeLogin: false

# 这里仅仅是一个第三方配置的示例，如果没有接入斯三方系统，
# 这里的配置项也不会影响到系统的行为，如果觉得多余，也可以手动删除。
third-party:
  # 第三方系统接入的用户鉴权配置。
  auth:
    - appCode: orange-forms-default
      # 访问第三方系统接口的URL前缀，橙单会根据功能添加接口路径的其余部分，
      # 比如获取用户Token的接口 http://localhost:8083/orangePluginTest/getTokenData
      baseUrl: http://localhost:8083/orangePlugin
      # 第三方返回的用户Token数据的缓存过期时长，单位秒。
      # 如果为0，则不缓存，每次涉及第三方的请求，都会发出http请求，交由第三方验证，这样对系统性能会有影响。
      tokenExpiredSeconds: 60
      # 第三方返回的权限数据的缓存过期时长，单位秒。
      permExpiredSeconds: 86400

# 这里仅仅是一个第三方配置的示例，如果没有接入斯三方系统，
# 这里的配置项也不会影响到系统的行为，如果觉得多余，也可以手动删除。
common-ext:
  urlPrefix: /admin/commonext
  # 这里可以配置多个第三方应用，这里的应用数量，通常会和上面third-party.auth的配置数量一致。
  apps:
    # 应用唯一编码，尽量不要使用中文。
    - appCode: orange-forms-default
      # 业务组件的数据源配置。
      bizWidgetDatasources:
        # 组件的类型，多个类型之间可以逗号分隔。
        - types: upms_user,upms_dept
          # 组件获取列表数据的接口地址。
          listUrl: http://localhost:8083/orangePlugin/listBizWidgetData
          # 组件获取详情数据的接口地址。
          viewUrl: http://localhost:8083/orangePlugin/viewBizWidgetData

sequence:
  # Snowflake 分布式Id生成算法所需的WorkNode参数值。
  snowflakeWorkNode: 1

# 存储session数据的Redis，所有服务均需要，因此放到公共配置中。
# 根据实际情况，该Redis也可以用于存储其他数据。
common-redis:
  # redisson的配置。每个服务可以自己的配置文件中覆盖此选项。
  redisson:
    # 如果该值为false，系统将不会创建RedissionClient的bean。
    enabled: true
    # mode的可用值为，single/cluster/sentinel/master-slave
    mode: single
    # single: 单机模式
    #   address: redis://localhost:6379
    # cluster: 集群模式
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # sentinel:
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # master-slave:
    #   每个节点逗号分隔，第一个为主节点，其余为从节点。同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    address: redis://*************:30051
    # 链接超时，单位毫秒。
    timeout: 6000
    # 单位毫秒。分布式锁的超时检测时长。
    # 如果一次锁内操作超该毫秒数，或在释放锁之前异常退出，Redis会在该时长之后主动删除该锁使用的key。
    lockWatchdogTimeout: 60000
    # redis 密码，空可以不填。
    password: dQrtU2SmycrWWf3zhpzQfw0tS
    pool:
      # 连接池数量。
      poolSize: 20
      # 连接池中最小空闲数量。
      minIdle: 5

common-report:
  # 注意不要以反斜杠(/)结尾。
  urlPrefix: /admin/report
  # 如果为false，报表模块的所有Controller中的接口将不能使用。
  operationEnabled: true
  # 该配置项仅当打印模板中，打印图片字段时，才会需要。
  # 这里的url配置只是一个示例，并不能保证开箱即用，代码示例和说明可参考common-report模块
  # example包内的ReportExampleController中的代码和详细说明。
  imageDownloadUrl: "http://localhost:8082/admin/report/example/downloadDirectly"
  # 该配置用于报表部分的数据权限过滤功能。
  # 当前数据权限需要获取指定部门Ids的所有下级子部门Ids的时候，会调用该接口。
  dataPermAllChildrenDeptIdUrl: "http://localhost:8082/admin/upms/sysDept/listAllChildDeptIdByParentIds"
  # 当前服务是否为可视化后台服务。
  isVisualization: false
  # 下面的url列表，请保持反斜杠(/)结尾。
  viewUrlList:
    - ${common-report.urlPrefix}/reportOperation/listData/

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

minio:
  enabled: true
  endpoint: http://*************:30052
  accessKey: minioadmin
  secretKey: eW2dZ9dM8pZ7jS9wB7qY8oI0m
  bucketName: large-model-dev-train-test

#python端的配置（java连接python）
python:
  appCode: 10001
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCyNHpsiI0v41HwWO7DVqQaVUBkjVG86/XhUcS4E8lPZOKPEnd9QQUBImGXWYVXrbVggRp0XBeKVgqAhqZ6FjeaGpbdCCPH/3LbLeG+MOHlQ9BC0qm1HzYoOoEvUPU8+X0vM+rCopfn4xsL5lURRnFW7xwHQSrHCfMxLQRJwNsvswIDAQAB
  privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALI0emyIjS/jUfBY7sNWpBpVQGSNUbzr9eFRxLgTyU9k4o8Sd31BBQEiYZdZhVettWCBGnRcF4pWCoCGpnoWN5oalt0II8f/ctst4b4w4eVD0ELSqbUfNig6gS9Q9Tz5fS8z6sKil+fjGwvmVRFGcVbvHAdBKscJ8zEtBEnA2y+zAgMBAAECgYBJxUOwzk50NoxjDM26e5FvfT8s4PunU57z6Z+f/EDn5wGK7MAeT948e/keaGa0xypAivFingT4tWmfjx43iB73sJ32bkPChVDvv19F0Pn++DSgNF0wbytHsbzkiQTqoXsAcGRMMhvJg4bh6aXbbQoK7a4yBPwuwHzfg3kAEsbJcQJBAO01U9vBE9FyXmmvi0sSGHxv6G+syhGN1Vqy9hw9ZF4ih4i3fG/HaH93J9m5OgqbTtAQu+ob4rocjN3G3u6vnDcCQQDAUoqf19+Odlo6QBPU9fDhFYChjf/UAYYj3yYvBiGBimIcpwLgvWcRzpqJlPrkvxCPSOVJmbesS+AS/s3OpuJlAkEArRM97pfAKBA0xxQopEu9xf25MNJ4VbqRM65kWFKLCwxSq/SFPOHYYzjDwN6fdOHA7ZtoIPbbxUPTnHXgb3N+nQJABgoRhFd5E7YvuyiL9uIKQGc2dNxKrlOl7LZzMoLUxoab8h5kfr4z4GFGhQ+CmXBOitFVMex70hNH5BDQUa736QJBANmbXlR2gr2go5SXYw9U2RKit6UbtPgvAGh045ITOvrhYoS8bu0xgLRnCJ7xi56vpeT5HgOWt9a1nUqFbsNtOX8=
  enabled: true
  authorizationKey: udgKwkK7V7F10mdwP0iB3tLxtTGcJOtrDjD3Mz2nY4P3idlTLfpamuVCBDWLBet4gYk
  baseUrl: http://*************:30066
  trainBaseUrl: http://*************:30065
  # 数据处理共享文件夹
  dataProcessShareFolder: /data/applications/lmd-python/volumes/lmd-py-data
  # 数据处理（数据工程）接口地址
  dataProcessBaesUrl: http://*************:30064
  # 数据洞察接口地址
  dataInsightBaseUrl: http://************:30102
  # 语言检测
  languageDetectionUrl: xxxxxx

#java本地程序地址
java:
  baseUrl: http://*************:30069

#社区地址
communityAddress:
  enabled: true
  appCode: 1001
  appSecret: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCVrL17WWksp4im/JsgHYi2DYSbpbgFrsYyK9+nYN/7L3U0Lij5d2hvt/xwJFXpLacSjft9q/eOhx0icd8mlJVrie934PLyVT8MG0xBcHlP7xNRRJYA0/yjvcDe33yTlwCyngPX2d3Oh+tX0eSwwj3CurNKQEuDAvbpH5NXgGNX6wIDAQAB
  baseUrl: http://**************:30113/openapi/v1/DataSynchronization   #社区接口地址
  syncModelBasicUrl: http://**************:30113/openapi/v1/DataSynchronization/getBaseModelListData

#文件存放位置管理
fileLocationManagement:
  modelPosition: /data/applications/lmd-formal/backend/BaseModels/   # 基础模型存放位置
  jarPosition: /data/applications/lmd-formal/train/dev/java/ # jar包存放位置
  chunkSize: 104857600    #每个分片下载的大小 100MB
  tempDir:  ./tempDir/  #临时存储分片的目录
  finalFilePath: ./finalFilePath/ #合并后的文件路径

thread:
  pool:
    corePoolSize: 5
    maxPoolSize: 50
    queueCapacity: 300
    keepAliveSeconds: 300
    threadNamePrefix: "thread-pool-"

OpenAi:
  url: https://openaiapi.aidb.site/v1/chat/completions

SysInfo:
  localhostIp: *************
  port: 30069

# 定时器是否开启
scheduling:
  enabled: false

# 语音转换相关
tts:
  rawPath: ./models/raw/

#easy-es:
#  # 基础配置项
#  enable: true
#  address: *************:30053
#  schema: http
#  username: elastic
#  password: 9dcinqq0wq9qil3osb5u4jnz

UEditor:
  filePath: ./UEditor/

##文件转化服务
#kkfileview:
#  url: http://localhost:30060/

# 开放API
OpenApiUrl:
  rerankApiBaseUrl: http://localhost:30100
  embeddingApiBaseUrl: http://localhost:30101

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: ${application.sessionExpiredSeconds}
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 同一个账号最多登录数量。
  max-login-count: 5
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 配置 Sa-Token 单独使用的 Redis 连接
  alone-redis:
    # Redis数据库索引（默认为0）
    database: 0
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 30051
    # Redis服务器连接密码（默认为空）
    password: dQrtU2SmycrWWf3zhpzQfw0tS
    # 连接超时时间
    timeout: 10s
  is-read-header: true
  is-read-cookie: true


#video-directory:
#  directoryPath: D:\project\java\company\LargeModelDev1\application-webadmin\directory\
#  merge: \merge\
#  convert: \convert\
#  vp8FileName: \vp8\
#  zipPath: \pep-be\zipPath\
video-directory:
  directoryPath: /pep-be/director/
  merge: /merge/
  convert: /convert/
  vp8FileName: /vp8/
  zipPath: /pep-be/zipPath/

  # 短信验证码配置
aliyunSms:
  accessKeyId:  LTAI5tEBXjSFxo3MU6SmJfGw
  accessKeySecret: ******************************
  # 签名名称
  signName: 数派大模型培训平台
  # 模板ID
  templateId:
    # 登录
    LOGIN: SMS_475300460

codeServer:
  imageId: sha256:6fcb3370d3cdd557ce56d59be244fd60c398a7084bb0514ad6d4574ca69aba09
  nginxPath: /data/applications/lmd-formal/backend-cs/codeServerProxy/nginx.conf
  name: proxy-cs



yoloService:
  baseModelsPath: /data/applications/lmd-formal/backend/BaseModels/CVModels/

imageConfig:
  ascendLlama: ************:30061/supie/lmd-train:v1.2
  ascendMultiTrain: ************:30061/supie/lmd-train:v1.3
  mindIe300iDuo: 9a8bd1ec41c3
  mindIe910a: ************:30061/supie/mindie:v1.1
  mindIe910b: ************:30061/supie-lmd:mindie-910b
  mindIet71: ************:30061/supie-lmd:mindie-2.0.T3
  nvidiaLlama: ************:5000/lmd/lmd-base:v1.0.5
  yolo: ************:30061/supie/lmd:yolov1
  jupyter: be0733a2c87f
  codeServer: b743b885d22c
  nvidiaVllm: xxxxx
  ascendVllm: xxxx
  jupyter910a: ************:30061/supie/jupyter:v1-aarch
  jupyter910b: ************:30061/supie/jupyter:v1-aarch
  codeServer910a: ************:30061/supie/coder:C8.R2
  codeServer910b: ************:30061/supie/coder:C8.R2

# SSO单点登录配置
sso:
  server-url: http://112.115.191.209:31910/admin/upms/sso/redirect
  server-user-url: http://112.115.191.209:31910/admin/upms/sso/validateAndGetUser
  server-logout-url: http://112.115.191.209:31910/admin/upms/login/doLogout
  appName: 一体机培训平台
  app-code: app_zhai_2068374951830462
  app-secret: kXGAzGblLszllwZ3BulYE9VgoE0lgYXX
  timeout-seconds: 300

