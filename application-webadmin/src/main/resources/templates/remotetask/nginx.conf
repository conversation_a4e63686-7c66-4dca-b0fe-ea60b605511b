user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;
    # 代理配置
    proxy_connect_timeout       30s;
    proxy_send_timeout          60s;
    proxy_read_timeout          60s;
    proxy_buffering             off;
    proxy_request_buffering     off;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    # Include other server configurations (if any)
    include /etc/nginx/conf.d/*.conf;
    
    # Main server block for reverse proxy
    server {
        listen 80;
        server_name localhost;
        
	  # 包含所有代理配置文件（在server块中include）
        include /etc/nginx/proxyconf/*.conf;
        
        # Debug location for testing
        location /debug {
            return 200 "Nginx is working! Configs loaded successfully.";
            add_header Content-Type text/plain;
        }
        
        # Default location (optional)
        location / {
            return 404 "No matching location found. Please check your URL path.";
            add_header Content-Type text/plain;
        }
    }
}
