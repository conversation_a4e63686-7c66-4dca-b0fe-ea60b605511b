services:
  nginx-proxy:
    image: nginx
    container_name: code_proxy_nginx
    ports:
      - "40084:80"
    volumes:
       - /data/applications/lmd-formal/backend/nginx/proxyconf: 
       - /data/applications/lmd-formal/backend/nginx/nginx.conf:/etc/nginx/nginx.conf
       - /data/applications/lmd-formal/backend/nginx/logs:/var/log/nginx
    networks:
      code-network:
        aliases:
          - nginx-host
    logging:  # 添加容器日志配置
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
networks:
  code-network:
    external: true  # 使用外部网络
