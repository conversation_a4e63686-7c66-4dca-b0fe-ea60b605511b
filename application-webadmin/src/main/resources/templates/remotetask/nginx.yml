services:
  nginx-proxy:
    image: nginx:alpine
    container_name: code_proxy_nginx
    ports:
      - "40084:80"
    volumes:
       # 挂载主配置文件
       - /data/applications/lmd-formal/backend/nginx/nginx.conf:/etc/nginx/nginx.conf
       # 挂载代理配置目录到标准位置
       - /data/applications/lmd-formal/backend/nginx/proxyconf:/etc/nginx/conf.d
       # 挂载日志目录
       - /data/applications/lmd-formal/backend/nginx/logs:/var/log/nginx
    networks:
      code-network:
        aliases:
          - nginx-host
    logging: 
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped
networks:
  code-network:
    external: true  # 使用外部网络(base