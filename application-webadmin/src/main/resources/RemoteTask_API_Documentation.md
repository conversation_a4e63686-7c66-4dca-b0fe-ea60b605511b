# RemoteTask 远程任务接口文档

## 接口概述

**接口名称：** 远程任务启动接口  
**接口路径：** `/admin/app/schTaskInfo/remoteTask`  
**请求方法：** POST  
**接口描述：** 用于创建和启动远程容器任务的接口  
**权限要求：** 无需权限验证（@SaIgnore）

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 请求体参数 (RemoteTask)

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskName | String | ✓ | 容器任务名称 | "推理训练任务" |
| graphicSize | String | ✓ | 显存大小(默认单位MB) | "16384" |
| cpuCore | Integer | ✗ | CPU核数 | 4 |
| memorySize | String | ✗ | 内存大小(默认单位MB) | "8192" |
| needResource | String | ✓ | 需要的资源类型 | "physical" |
| containerPort | String | ✓ | 容器内部端口 | "8080" |
| imageName | String | ✓ | 镜像名称 | "oder:ubuntu-python3.10-910-cann8.0" |
| imageVersion | String | ✗ | 镜像版本号 | "1.0.0" |
| environment | Map<String,String> | ✗ | 环境变量 | {"TZ": "Asia/Shanghai"} |
| command | String | ✗ | 启动命令 | "python train.py" |
| containerPath | String | ✗ | 容器内部挂载路径 | "/containerPath/user" |
| containerMapPath | String | ✗ | 容器卷映射路径 | "/serverPath/user" |
| multiplePort | Boolean | ✗ | 是否需要多个端口 | true |
| needPortNum | Integer | ✗ | 需要端口数 | 1 |
| volumeMounts | List<String> | ✗ | 容器卷挂载列表 | ["/data/models:/models"] |
| restart | String | ✗ | 重启策略 | "always" |
| proxyType | String | ✗ | 代理类型 | "vscode" |

### 参数详细说明

#### needResource 资源类型枚举
- `physical` - 物理卡
- `vnpu` - 显卡
- `non` - 不需要

#### proxyType 代理类型枚举
- `jupyter` - Jupyter 代理
- `vscode` - VSCode 代理  
- `simple` - 普通代理

#### restart 重启策略
- `always` - 总是重启
- `unless-stopped` - 除非手动停止
- `on-failure` - 失败时重启
- `no` - 不重启

## 请求示例

### 基础请求示例
```json
{
  "remoteTask": {
    "taskName": "nggxin-prxoy",
    "graphicSize": "0",
    "cpuCore": 1,
    "needResource": "non",
    "containerPort": "9000",
    "imageName": "quay.io/supiedt/coder:ubuntu-python3.10-910-cann8.0.rc2.beta1",
    "multiplePort": false,
    "needPortNum": 1,
    "memorySize": 1024
  }
}

```

### 完整请求示例
```json
{
  "taskName": "深度学习推理任务",
  "graphicSize": "24576",
  "cpuCore": 8,
  "memorySize": "16384",
  "needResource": "physical",
  "containerPort": "8888",
  "imageName": "oder:ubuntu-python3.10-910-cann8.0",
  "imageVersion": "2.0.0",
  "environment": {
    "TZ": "Asia/Shanghai",
    "ASCEND_VERSION": "ascend910",
    "CUDA_VISIBLE_DEVICES": "0"
  },
  "command": "python -m jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root",
  "containerPath": "/workspace",
  "containerMapPath": "/data/workspace",
  "multiplePort": false,
  "needPortNum": 1,
  "volumeMounts": [
    "/data/models:/models",
    "/data/datasets:/datasets"
  ],
  "restart": "unless-stopped",
  "proxyType": "jupyter"
}
```

## 响应结果

### 成功响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "taskId": "12345678967877",
     "ports": [
        "12344",
        "34566"
     ],
    "status": "created",
    "mapPath": "task_123456789", 
    "visitLink": "http://*************:40084/code12345678967877/"
  }
}
```

### 失败响应
```json
{
  "success": false,
  "code": "500",
  "message": "任务创建失败：资源不足",
  "data": null
}
```

## 使用示例

### cURL 请求示例
```bash
curl -X POST \
  'http://your-domain/admin/app/schTaskInfo/remoteTask' \
  -H 'Content-Type: application/json' \
  -d '{
    "taskName": "AI训练任务",
    "graphicSize": "16384",
    "cpuCore": 4,
    "memorySize": "8192",
    "needResource": "physical",
    "containerPort": "8080",
    "imageName": "oder:ubuntu-python3.10-910-cann8.0",
    "imageVersion": "1.0.0"
  }'
```

## 注意事项

1. **必填参数验证**：确保所有标记为必填的参数都已提供，否则会返回参数验证失败错误

2. **资源规格限制**：
   - 显存大小 (graphicSize) 和内存大小 (memorySize) 需要根据实际硬件资源进行配置
   - CPU核数不能超过系统可用核数

3. **镜像要求**：
   - 确保指定的镜像名称和版本在容器仓库中存在
   - 镜像应包含任务运行所需的依赖环境

4. **端口配置**：
   - 容器端口需要与应用实际监听端口一致
   - 避免端口冲突

5. **卷挂载**：
   - volumeMounts 格式为 "宿主机路径:容器路径"
   - 确保宿主机路径存在且有相应权限

6. **环境变量**：
   - 环境变量值应为字符串类型
   - 特殊字符需要适当转义

7. **命令执行**：
   - command 参数应为完整的可执行命令
   - 长时间运行的任务建议使用后台运行方式

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证失败 | 检查必填参数是否提供，参数格式是否正确 |
| 500 | 服务内部错误 | 联系系统管理员检查服务状态 |
| 1001 | 资源不足 | 等待资源释放或选择其他资源池 |
| 1002 | 镜像不存在 | 检查镜像名称和版本是否正确 |
| 1003 | 端口被占用 | 更换端口或等待端口释放 |

---

# 远程任务状态查询接口

## 接口概述

**接口名称：** 远程任务状态查询接口  
**接口路径：** `/admin/app/schTaskInfo/remoteTaskStatus`  
**请求方法：** POST  
**接口描述：** 用于批量查询远程任务状态的接口  
**权限要求：** 无需权限验证（@SaIgnore）

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 请求体参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| taskIdList | List<String> | ✓ | 任务ID列表 | ["123456789", "987654321"] |

## 请求示例

```json
[
  "123456789",
  "987654321", 
  "555666777"
]
```

## 响应结果

### 成功响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "123456789",
      "taskName": "AI训练任务",
      "status": "running",
      "failedReason": "容器异常退出",
      "approveState": "pending"
    },
    {
       "id": "1234567892",
       "taskName": "AI训练任务",
       "status": "running",
       "failedReason": "容器异常退出3",
       "approveState": "pending"
    }
  ]
}
```

### 失败响应
```json
{
  "success": false,
  "code": "400",
  "message": "参数验证失败：任务ID列表不能为空",
  "data": null
}
```

## 使用示例

### cURL 请求示例
```bash
curl -X POST \
  'http://your-domain/admin/app/schTaskInfo/remoteTaskStatus' \
  -H 'Content-Type: application/json' \
  -d '["123456789", "987654321"]'
```

## 注意事项

1. **任务ID格式**：确保提供的任务ID格式正确（字符串类型）
2. **批量查询限制**：建议单次查询的任务ID数量不超过100个
3. **不存在的任务**：对于不存在的任务ID，响应中不会包含对应的数据

---

# 容器日志查询接口

## 接口概述

**接口名称：** 容器日志查询接口  
**接口路径：** `/admin/app/schContainerManager/containerLogsText`  
**请求方法：** GET  
**接口描述：** 用于获取容器日志内容的接口（非流式）  
**权限要求：** 无需权限验证（@SaIgnore）

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 查询参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | String | ✓ | 任务表主键ID | "123456789" |
| lines | Integer | ✗ | 日志行数，默认100行 | 100 |

## 请求示例

```
GET /admin/app/schContainerManager/containerLogsText?id=123456789&lines=200
```

## 响应结果

### 成功响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": "2025-01-01 10:00:01 INFO Starting application...\n2025-01-01 10:00:02 INFO Loading configuration...\n2025-01-01 10:00:03 INFO Application started successfully.\n2025-01-01 10:00:04 DEBUG Processing request..."
}
```

### 任务启动失败响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": "任务启动失败--资源不足，无法分配所需的GPU资源"
}
```

### 失败响应
```json
{
  "success": false,
  "code": "500",
  "message": "数据不存在，请核对后重试！",
  "data": null
}
```

## 使用示例

### cURL 请求示例
```bash
# 获取默认100行日志
curl -X GET 'http://your-domain/admin/app/schContainerManager/containerLogsText?id=123456789'

# 获取指定行数日志
curl -X GET 'http://your-domain/admin/app/schContainerManager/containerLogsText?id=123456789&lines=500'
```

## 注意事项

1. **任务状态检查**：接口会先检查任务状态，如果任务启动失败会返回失败原因
2. **容器存在性**：只有成功创建并启动的容器才能获取日志
3. **日志行数限制**：建议单次获取的日志行数不超过1000行，避免响应过大
4. **资源权限**：确保有对应容器的访问权限
5. **实时性**：该接口获取的是当前时刻的历史日志，不是实时流式日志

