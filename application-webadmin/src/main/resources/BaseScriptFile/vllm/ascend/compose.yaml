services:
  ascend_vllm:
    image: ${IMAGE}
    container_name: ${CONTAINER_NAME}
    env_file: .env
    shm_size: 16G
    privileged: true
    environment:
      - ASCEND_RT_VISIBLE_DEVICES=${DEVICES_NUMS_IDS}
      - TZ=Asia/Shanghai
    volumes:
      - ${MODEL_PATH}:/workspace/model
      - /etc/localtime:/etc/localtime
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /etc/ascend_install.info:/etc/ascend_install.info
      - /var/log/npu/:/usr/slog
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    devices:
      - /dev/davinci0:/dev/davinci0
      - /dev/davinci1:/dev/davinci1
      - /dev/davinci2:/dev/davinci2
      - /dev/davinci3:/dev/davinci3
      - /dev/davinci4:/dev/davinci4
      - /dev/davinci5:/dev/davinci5
      - /dev/davinci6:/dev/davinci6
      - /dev/davinci7:/dev/davinci7
      - /dev/davinci_manager:/dev/davinci_manager
      - /dev/devmm_svm:/dev/devmm_svm
      - /dev/hisi_hdc:/dev/hisi_hdc
    ports:
      - ${API_SERVER_PORT}:8000
    working_dir: /workspace/vllm-ascend
    command: ["vllm","serve","/workspace/model","--served-model-name","${MODEL_NAME}","--gpu-memory-utilization","0.9","--max-model-len","16384","--tensor-parallel-size","${WORLD_SIZE}","--dtype","bfloat16","--trust_remote_code"]
