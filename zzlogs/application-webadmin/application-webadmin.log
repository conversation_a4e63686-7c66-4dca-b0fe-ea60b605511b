[INFO ] [2025-07-01 09:23:35] T:[] S:[] U:[] [background-preinit] ==> HV000001: Hibernate Validator 8.0.1.Final
[INFO ] [2025-07-01 09:23:35] T:[] S:[] U:[] [main] ==> Starting WebAdminApplication using Java 17.0.4 with PID 22404 (D:\gy_sch_be\application-webadmin\target\classes started by Superhero in D:\gy_sch_be)
[INFO ] [2025-07-01 09:23:35] T:[] S:[] U:[] [main] ==> The following 1 profile is active: "dev"
[INFO ] [2025-07-01 09:23:42] T:[] S:[] U:[] [main] ==> Multiple Spring Data modules found, entering strict repository configuration mode
[INFO ] [2025-07-01 09:23:42] T:[] S:[] U:[] [main] ==> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[INFO ] [2025-07-01 09:23:42] T:[] S:[] U:[] [main] ==> Finished Spring Data repository scanning in 88 ms. Found 0 Redis repository interfaces.
[INFO ] [2025-07-01 09:23:45] T:[] S:[] U:[] [main] ==> Bean 'commonWebMvcConfig' of type [supie.common.core.config.CommonWebMvcConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Tomcat initialized with port(s): 8085 (http)
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Initializing ProtocolHandler ["http-nio-8085"]
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Starting service [Tomcat]
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Starting Servlet engine: [Apache Tomcat/10.1.16]
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Initializing Spring embedded WebApplicationContext
[INFO ] [2025-07-01 09:23:49] T:[] S:[] U:[] [main] ==> Root WebApplicationContext: initialization completed in 13228 ms
[ERROR] [2025-07-01 09:23:50] T:[] S:[] U:[] [main] ==> For security constraints with URL pattern [/*] only the HTTP methods [TRACE HEAD DELETE SEARCH PROPFIND COPY PUT PATCH] are covered. All other methods are uncovered.
[INFO ] [2025-07-01 09:23:51] T:[] S:[] U:[] [main] ==> Redisson 3.15.4
[INFO ] [2025-07-01 09:23:52] T:[] S:[] U:[] [redisson-netty-2-10] ==> 1 connections initialized for /*************:40051
[INFO ] [2025-07-01 09:23:52] T:[] S:[] U:[] [redisson-netty-2-12] ==> 5 connections initialized for /*************:40051
[INFO ] [2025-07-01 09:23:58] T:[] S:[] U:[] [main] ==> {dataSource-1,application-webadmin} inited
[INFO ] [2025-07-01 09:24:03] T:[] S:[] U:[] [main] ==> {dataSource-2,application-webadmin} inited
[INFO ] [2025-07-01 09:24:07] T:[] S:[] U:[] [main] ==> {dataSource-3,application-webadmin} inited
[INFO ] [2025-07-01 09:24:11] T:[] S:[] U:[] [main] ==> {dataSource-4,application-webadmin} inited
[INFO ] [2025-07-01 09:24:16] T:[] S:[] U:[] [main] ==> {dataSource-5,application-webadmin} inited
[INFO ] [2025-07-01 09:24:16] T:[] S:[] U:[] [main] ==> {dataSource-6,application-webadmin} inited
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermDept".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermDept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermMenu".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDataPermUser".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDataPermUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysDeptRelation".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysRoleMenu".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserPost".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.webadmin.upms.model.SysUserRole".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.webadmin.upms.model.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryDataPerm".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryDataPerm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.mobile.model.MobileEntryRole".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.common.mobile.model.MobileEntryRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.online.model.OnlineColumnRule".
[WARN ] [2025-07-01 09:24:20] T:[] S:[] U:[] [main] ==> class supie.common.online.model.OnlineColumnRule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:21] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.flow.model.FlowTaskExt".
[WARN ] [2025-07-01 09:24:21] T:[] S:[] U:[] [main] ==> class supie.common.flow.model.FlowTaskExt ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[WARN ] [2025-07-01 09:24:22] T:[] S:[] U:[] [main] ==> Can not find table primary key in Class: "supie.common.report.model.ReportTenantDataset".
[WARN ] [2025-07-01 09:24:22] T:[] S:[] U:[] [main] ==> class supie.common.report.model.ReportTenantDataset ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[INFO ] [2025-07-01 09:24:25] T:[] S:[] U:[] [main] ==> 创建任务线程池: 核心线程数=8, 最大线程数=16
[INFO ] [2025-07-01 09:24:37] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-07-01 09:24:37] T:[] S:[] U:[] [main] ==> No deployment resources were found for autodeployment
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> Found 3 Engine Configurators in total:
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-07-01 09:24:38] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Found 1 auto-discoverable Process Engine Configurator
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Found 1 Engine Configurators in total:
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Executing beforeInit() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:39] T:[] S:[] U:[] [main] ==> Executing configure() of class supie.common.flow.config.CustomEngineConfigurator (priority:0)
[INFO ] [2025-07-01 09:24:40] T:[] S:[] U:[] [main] ==> INFO: An older version of the XSD is specified in one or more changelog's <databaseChangeLog> header. This can lead to unexpected outcomes. If a specific XSD is not required, please replace all XSD version references with "-latest". Learn more at https://docs.liquibase.com
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> Reading from gy_sch_dev.FLW_EV_DATABASECHANGELOG
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> Changelog query completed.
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> EventRegistryEngine default created
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> IdmEngine default created
[INFO ] [2025-07-01 09:24:41] T:[] S:[] U:[] [main] ==> ProcessEngine default created
[INFO ] [2025-07-01 09:24:42] T:[] S:[] U:[] [main] ==> Total of v5 deployments found: 0
[INFO ] [2025-07-01 09:24:57] T:[] S:[] U:[] [main] ==> Exposing 14 endpoint(s) beneath base path '/actuator'
[INFO ] [2025-07-01 09:24:57] T:[] S:[] U:[] [main] ==> Starting ProtocolHandler ["http-nio-8085"]
[INFO ] [2025-07-01 09:24:57] T:[] S:[] U:[] [main] ==> Tomcat started on port(s): 8085 (http) with context path ''
[INFO ] [2025-07-01 09:24:58] T:[] S:[] U:[] [main] ==> Started WebAdminApplication in 84.935 seconds (process running for 90.438)
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:25:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:25:21] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:25:21] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:25:21] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:25:21] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:25:42] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:25:42] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:25:42] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:25:42] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:26:03] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:26:03] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:26:03] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:26:03] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:26:24] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:26:24] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:26:24] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:26:25] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:26:46] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:26:46] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:26:46] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:26:46] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:27:07] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:27:07] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:27:07] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:27:07] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:27:28] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:27:28] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:27:28] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:27:28] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:27:49] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:27:49] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:27:49] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:27:49] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:28:10] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:28:10] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:28:10] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:28:10] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:28:31] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:28:31] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:28:31] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:28:31] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:28:52] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:28:52] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:28:52] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:28:52] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:29:13] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:29:13] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:29:13] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:29:13] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:29:34] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:29:34] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:29:34] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:29:34] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:29:56] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:29:56] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:29:56] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:29:56] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:30:17] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:30:17] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:30:17] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:30:17] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:30:38] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:30:38] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:30:38] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:30:38] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:30:59] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:30:59] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:30:59] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:30:59] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 09:31:20.587(Timestamp)
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,remote_task_json,task_type,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (status = ? AND (container_status = ?))
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: running(String), running(String)
[DEBUG] [2025-07-01 09:31:20] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:31:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:31:51] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:31:51] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:31:51] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:31:51] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:32:12] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:32:12] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:32:12] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:32:12] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:32:33] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:32:33] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:32:33] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:32:33] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:32:54] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:32:54] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:32:54] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:32:54] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:33:15] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:33:15] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:33:15] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:33:15] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:33:36] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:33:36] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:33:36] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:33:36] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:33:57] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:33:57] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:33:57] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:33:57] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:34:18] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:34:18] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:34:18] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:34:18] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:34:39] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:34:39] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:34:39] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:34:39] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:35:01] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:35:01] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:35:01] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:35:01] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:35:22] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:35:22] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:35:22] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:35:22] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:35:43] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:35:43] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:35:43] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:35:43] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:36:04] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:36:04] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:36:04] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:36:04] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:36:25] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:36:25] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:36:25] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:36:25] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:36:46] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:36:46] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:36:46] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:36:46] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:37:07] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:37:07] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:37:07] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:37:07] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:37:28] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:37:28] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:37:28] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:37:29] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:37:50] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:37:50] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 09:37:50] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 09:37:50.098(Timestamp)
[DEBUG] [2025-07-01 09:37:50] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:38:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:38:21] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:38:21] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:38:21] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:38:21] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:38:42] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:38:42] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:38:42] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:38:42] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:39:03] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:39:03] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:39:03] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:39:03] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:39:24] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:39:24] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:39:24] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:39:24] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:39:45] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:39:45] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:39:45] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:39:45] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:40:06] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:40:06] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:40:06] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:40:06] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:40:27] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:40:27] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:40:27] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:40:27] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:40:48] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:40:48] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:40:48] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:40:49] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:41:10] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:41:10] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:41:10] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:41:10] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:41:31] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:41:31] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:41:31] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:41:31] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:41:52] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:41:52] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:41:52] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:41:52] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:42:13] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:42:13] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:42:13] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:42:13] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:42:34] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:42:34] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:42:34] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:42:34] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:42:55] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:42:55] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:42:55] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:42:55] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:43:16] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:43:16] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:43:16] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:43:16] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:43:38] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:43:38] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:43:38] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:43:38] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:43:59] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:43:59] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:43:59] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:43:59] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:44:20] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:44:20] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 09:44:20] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 09:44:20.213(Timestamp)
[DEBUG] [2025-07-01 09:44:20] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:44:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:44:51] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:44:51] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:44:51] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:44:51] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:45:12] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:45:12] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:45:12] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:45:12] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:45:33] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:45:33] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:45:33] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:45:33] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:45:54] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:45:54] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:45:54] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:45:54] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:46:15] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:46:15] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:46:15] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:46:15] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:46:26] T:[] S:[] U:[] [http-nio-8085-exec-1] ==> Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] [2025-07-01 09:46:26] T:[] S:[] U:[] [http-nio-8085-exec-1] ==> Initializing Servlet 'dispatcherServlet'
[INFO ] [2025-07-01 09:46:26] T:[] S:[] U:[] [http-nio-8085-exec-1] ==> Completed initialization in 5 ms
[INFO ] [2025-07-01 09:46:27] T:[a7846941abec4a7ca5c78e237e02e70d] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:46:27] T:[a7846941abec4a7ca5c78e237e02e70d] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:46:27] T:[a7846941abec4a7ca5c78e237e02e70d] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:46:27] T:[a7846941abec4a7ca5c78e237e02e70d] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 35
[INFO ] [2025-07-01 09:46:27] T:[a7846941abec4a7ca5c78e237e02e70d] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=231ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1937715585370558464,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1939861313492619264,"showOrder":1,"updateTime":1751333953000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"},{"$ref":"$.data[34]"}],"success":true}
[ERROR] [2025-07-01 09:46:36] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:46:36] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:46:36] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:46:36] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> 开始请求，url=/admin/upms/sysMenu/update, reqData={"sysMenuDto":{"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1}}
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 1932002304458756096(Long)
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 1937715588415623169(Long)
[DEBUG] [2025-07-01 09:46:41] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:46:42] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: UPDATE sys_menu SET data_belong_type=?, deleted_flag=?, parent_id=?, menu_name=?, menu_type=?, form_router_name=?, show_order=?, extra_data=?, create_user_id=?, create_time=?, update_user_id=?, update_time=?, online_form_id=?,online_menu_perm_type=?,report_page_id=?,online_flow_entry_id=?,icon=? WHERE menu_id=?
[DEBUG] [2025-07-01 09:46:42] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: business(String), 1(Integer), 1937715588415623169(Long), 镜像管理(String), 1(Integer), image-management(String), 1(Integer), {"bindType":0,"permCodeList":[]}(String), 1923195822141345792(Long), 2025-06-09 17:10:19.0(Timestamp), 1921773426524033024(Long), 2025-07-01 09:46:42.051(Timestamp), null, null, null, null, null, 1932002304458756096(Long)
[DEBUG] [2025-07-01 09:46:42] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==    Updates: 1
[INFO ] [2025-07-01 09:46:42] T:[658b143b5ca247f18f106d1191d0691f] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> 请求完成, url=/admin/upms/sysMenu/update，elapse=594ms, respData={"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}
[INFO ] [2025-07-01 09:46:42] T:[d6e81c67473b4358bcd35a02f9e540d8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:46:42] T:[d6e81c67473b4358bcd35a02f9e540d8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:46:42] T:[d6e81c67473b4358bcd35a02f9e540d8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:46:42] T:[] S:[] U:[] [flowable-task-Executor-1] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:46:42] T:[] S:[] U:[] [flowable-task-Executor-1] ==> ==> Parameters: 1939863196697366528(Long), (String), 15(Integer), application-webadmin(String), supie.webadmin.upms.controller.SysMenuController(String), supie.webadmin.upms.controller.SysMenuController.update(String), Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd(String), 658b143b5ca247f18f106d1191d0691f(String), 595(Long), POST(String), /admin/upms/sysMenu/update(String), {"sysMenuDto":{"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1}}(String), {"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}(String), ***************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-07-01 09:46:41.666(Timestamp)
[DEBUG] [2025-07-01 09:46:42] T:[d6e81c67473b4358bcd35a02f9e540d8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 35
[INFO ] [2025-07-01 09:46:42] T:[d6e81c67473b4358bcd35a02f9e540d8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=47ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1937715585370558464,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"},{"$ref":"$.data[34]"}],"success":true}
[DEBUG] [2025-07-01 09:46:42] T:[] S:[] U:[] [flowable-task-Executor-1] ==> <==    Updates: 1
[ERROR] [2025-07-01 09:46:57] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:46:57] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:46:57] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:46:57] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:00] T:[4c758c62d9034627b9f9089c582d7ebc] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> 开始请求，url=/admin/upms/sysDept/list, reqData={}
[DEBUG] [2025-07-01 09:47:00] T:[4c758c62d9034627b9f9089c582d7ebc] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT * FROM sys_dept WHERE sys_dept.deleted_flag = 1 ORDER BY sys_dept.dept_id DESC
[DEBUG] [2025-07-01 09:47:00] T:[4c758c62d9034627b9f9089c582d7ebc] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: 
[INFO ] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 开始请求，url=/admin/upms/sysUser/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"sysUserDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-07-01 09:47:00] T:[4c758c62d9034627b9f9089c582d7ebc] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:00] T:[4c758c62d9034627b9f9089c582d7ebc] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> 请求完成, url=/admin/upms/sysDept/list，elapse=70ms, respData={"data":{"dataList":[{"createTime":1747584000000,"createUserId":1923195822141345792,"deptId":1923195822141345795,"deptName":"公司总部","showOrder":1,"updateTime":1747584000000,"updateUserId":1923195822141345792}],"totalCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT count(0) FROM sys_user WHERE sys_user.deleted_flag = 1
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT * FROM sys_user WHERE sys_user.deleted_flag = 1 ORDER BY sys_user.create_time DESC LIMIT ?
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT dept_id,dept_name,show_order,parent_id,deleted_flag,create_user_id,update_user_id,create_time,update_time FROM sys_dept WHERE deleted_flag=1 AND (dept_id IN (?))
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: 1923195822141345795(Long)
[DEBUG] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:00] T:[1e0df6c1e9ef4024aafb1ee7798d68ca] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 请求完成, url=/admin/upms/sysUser/list，elapse=431ms, respData={"data":{"dataList":[{"createTime":1747584000000,"createUserId":1923195822141345792,"deptId":1923195822141345795,"deptIdDictMap":{"name":"公司总部","id":1923195822141345795},"loginName":"admin","showName":"管理员","updateTime":1747584000000,"updateUserId":1923195822141345792,"userId":1921773426524033024,"userStatus":0,"userStatusDictMap":{"name":"正常状态","id":0},"userType":0,"userTypeDictMap":{"name":"管理员","id":0}}],"totalCount":1},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> 开始请求，url=/admin/app/schResourceInfo/getResourceStatistics, reqData={}
[INFO ] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> 开始请求，url=/admin/app/schResourceInfo/list, reqData={}
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT COALESCE(COUNT(*), 0) AS 'resourceTotal', COALESCE(SUM( CASE WHEN cpu_core_count != 'Unknown' AND cpu_core_count REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(cpu_core_count AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'cpuCoreTotal', COALESCE(SUM( CASE WHEN memory_capacity != 'Unknown' AND memory_capacity REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(memory_capacity AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'memoryTotal', COALESCE(SUM( CASE WHEN graphics_memory REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(graphics_memory AS DECIMAL(20,2)) ELSE 0 END ), 0) AS 'gpuMemoryTotal', COALESCE(SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END), 0) AS 'offlineCount', COALESCE(SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END), 0) AS 'activeCount', COALESCE(SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END), 0) AS 'maintenanceCount', COALESCE(SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END), 0) AS 'errorCount' FROM sch_resource_info WHERE is_delete = 1;
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 
[INFO ] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> 开始请求，url=/admin/app/schResourceInfo/hostSampling, reqData={}
[INFO ] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 开始请求，url=/admin/app/schNodeBasicMetrics/resourceSort, reqData={}
[INFO ] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> 开始请求，url=/admin/app/schTaskInfo/taskOccupyResource, reqData={}
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT * FROM sch_resource_info WHERE sch_resource_info.is_delete = 1 ORDER BY sch_resource_info.id DESC
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT COUNT(*) AS total_count FROM sch_compute_device d WHERE EXISTS ( SELECT 1 FROM sch_virtual_compute_card_situation s WHERE s.compute_device_id = d.id AND s.is_delete = 1 ) AND d.is_delete = 1
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT t1.* FROM sch_node_basic_metrics t1 JOIN ( SELECT resource_id, MAX(id) AS max_id FROM sch_node_basic_metrics WHERE is_delete = 1 GROUP BY resource_id ) t2 ON t1.resource_id = t2.resource_id AND t1.id = t2.max_id WHERE t1.is_delete = 1 ORDER BY t1.overall_cpu_usage DESC, t1.memory_utilization DESC;
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT COUNT(DISTINCT resource_id) AS count FROM sch_task_info WHERE is_delete = 1 and status = 'running'
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id IN ( ? , ? ) AND ts >= now() - INTERVAL 60 MINUTE; ORDER BY ts DESC, resource_id DESC
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: 1939582665866874880(Long), 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT id,remote_task_json,task_type,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (status = ? AND resource_id IN (?,?))
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: running(String), 1935744652716019712(Long), 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==>  Preparing: SELECT ts AS tsLocal,* FROM sch_node_basic_metrics WHERE resource_id IN ? AND ts >= ? AND ts <= ? ORDER BY ts Asc
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT compute_device_id FROM sch_task_info WHERE is_delete=1 AND (is_delete = ? AND compute_device_id IS NOT NULL) GROUP BY compute_device_id
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 1(Integer)
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==> Parameters: [1935744652716019712, 1939582665866874880](ListN), 2025-06-30T09:47:01.477660500(String), 2025-07-01T09:47:01.477660500(String)
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT t.* FROM sch_card_monitor t JOIN ( SELECT resource_id, max(ts) AS latest_ts FROM sch_card_monitor WHERE resource_id IN ( ? , ? ) GROUP BY resource_id ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: 1939582665866874880(Long), 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==>  Preparing: SELECT ts AS tsLocal,* FROM sch_card_monitor WHERE resource_id in ? AND ts >= ? AND ts <= ? ORDER BY ts Asc
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> ==> Parameters: [1935744652716019712, 1939582665866874880](ListN), 2025-06-30T09:47:01.477660500(String), 2025-07-01T09:47:01.477660500(String)
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT compute_device_id FROM sch_virtual_compute_card_situation WHERE is_delete=1 AND (is_delete = ? AND compute_device_id IS NOT NULL) GROUP BY compute_device_id
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 1(Integer)
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? ORDER BY ts DESC LiMIT 1;
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (is_delete = ? AND id IN (?,?))
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 1(Integer), 1935744654603456512(Long), 1935744654603456513(Long)
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT * FROM sch_card_monitor WHERE compute_device_id =? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-07-01 09:47:01] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: 1935744654603456513(Long), 2025-06-27 15:33:03.103(Timestamp)
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT s.status, COALESCE(t.count, 0) AS count FROM ( SELECT 'running' AS status UNION ALL SELECT 'queued' UNION ALL SELECT 'starting' UNION ALL SELECT 'pending' UNION ALL SELECT 'stop' UNION ALL SELECT 'finished' UNION ALL SELECT 'failed' ) s LEFT JOIN ( SELECT status, COUNT(*) AS count FROM sch_task_info WHERE is_delete = 1 AND status IN ('pending', 'queued', 'starting', 'running', 'stop', 'finished', 'failed') GROUP BY status ) t ON s.status = t.status ORDER BY FIELD(s.status, 'running', 'queued', 'starting', 'pending', 'stop', 'finished', 'failed' )
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 39
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE is_delete=1
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 2
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 7
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==>  Preparing: SELECT COUNT( * ) AS total FROM sch_resource_pool WHERE is_delete=1
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> ==> Parameters: 
[INFO ] [2025-07-01 09:47:01] T:[d11d7553201d430ca0e4617a8b427343] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 请求完成, url=/admin/app/schNodeBasicMetrics/resourceSort，elapse=340ms, respData={"data":[{"cpuUsage":3.322445,"memoryUtil":13.006848,"resourceId":1935744652716019712,"resourceName":"资源1001","videoUtil":0.000000,"timestamp":"2025-06-27T15:33:03.103"}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"}],"success":true}
[DEBUG] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:01] T:[908ed34a358048958b48006910854bc8] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-8] ==> 请求完成, url=/admin/app/schResourceInfo/getResourceStatistics，elapse=429ms, respData={"data":{"resourceTotal":2,"activeCount":2,"resourceRateUsage":50.0,"offlineCount":0,"totalPool":1,"memoryTotal":1546441.00,"taskStatusCount":[{"count":1,"status":"running"},{"count":0,"status":"queued"},{"count":0,"status":"starting"},{"count":0,"status":"pending"},{"count":0,"status":"stop"},{"count":38,"status":"finished"},{"count":11,"status":"failed"}],"gpuMemoryTotal":524288.00,"cpuCoreTotal":384.00,"virtualResoureCount":2,"maintenanceCount":0,"errorCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-07-01 09:47:02] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> <==      Total: 0
[INFO ] [2025-07-01 09:47:02] T:[661b42e17a1a44b69713fbfd21020239] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-4] ==> 请求完成, url=/admin/app/schResourceInfo/hostSampling，elapse=617ms, respData={"data":{"NPU":{},"CPU":[]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-07-01 09:47:02] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:02] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==>  Preparing: SELECT * FROM sch_node_basic_metrics WHERE resource_id = ? AND ts = ? ORDER BY ts DESC
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 8
[DEBUG] [2025-07-01 09:47:02] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> ==> Parameters: 1935744652716019712(Long), 2025-06-27 15:33:03.103(Timestamp)
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:47:02] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT id,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,resource_id,status,total_compute,device_type,model_number,device_name,memory_size,semi_precision,single_precision,double_precision,cuda_number,tensor_number,architecture,product_type,device_number,card_type,memory_type FROM sch_compute_device WHERE is_delete=1 AND (resource_id = ?)
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 8
[INFO ] [2025-07-01 09:47:02] T:[4082f02879b5470e977b65d2ddee45ee] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-10] ==> 请求完成, url=/admin/app/schTaskInfo/taskOccupyResource，elapse=742ms, respData={"data":[{"approveState":"approved","computeDeviceId":1935744654603456513,"containerName":"1939620731843252224-remotetask-1","containerStatus":"running","createTime":1751276594000,"dictId":1932726533772808192,"exitCode":"exited(0)","failReason":"","graphicNeededMb":1679,"id":1939620731843252224,"isDelete":1,"memoryNeededMb":1678,"partitionId":118,"poolId":1935744808710574080,"remoteTaskJson":"{\"containerMapPath\":\"/serverPath/user\",\"containerPath\":\"/containerPath/user\",\"containerPort\":\"9000\",\"cpuCore\":4,\"graphicSize\":\"1679\",\"imageName\":\"quay.io/supiedt/coder:ubuntu-python3.10-910-cann8.0.rc2.beta1\",\"memorySize\":\"1678\",\"multiplePort\":false,\"needPortNum\":1,\"needResource\":\"physical\",\"proxyType\":\"vscode\",\"restart\":\"always\",\"taskName\":\"code-proxy\"}","resourceId":1935744652716019712,"schCardMonitor":{"cardUtilization":0.00,"computeDeviceId":1935744654603456513,"createUserId":1923195822141345792,"hbTotal":32768,"hbUsed":0,"hbUtil":"0.0","id":1938500808945438721,"isDelete":1,"monitorType":1,"resourceId":1935744652716019712,"serialNumber":1,"temp":39,"ts":1751009583103,"updateUserId":1923195822141345792},"schNodeBasicMetrics":{"availableMemory":672648.890625,"cpuUtilization":0.000000,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"diskIoWrittenAndReadSum":4127495.6982421875,"diskUsedSum":1.2276155500681596E9,"diskUtilization":10.224043974257757,"id":1938500809552670722,"isDelete":1,"memoryUtilization":13.006848,"networkSpeedSum":1.25E9,"nowThread":8.0,"overallCpuUsage":3.322445,"processMemoryAmount":27.14453125,"processMemoryUsage":2.2377221540611645,"resourceId":1935744652716019712,"systemMemoryUtilization":0.0,"ts":1751009583103,"videoUtilization":0.000000},"status":"running","taskImageId":1933866164367134720,"taskName":"code-proxy","taskType":"external","updateTime":1751334102000}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"}],"success":true}
[INFO ] [2025-07-01 09:47:02] T:[d066bda7d23f4c5d843f37e09d208dec] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> 请求完成, url=/admin/app/schResourceInfo/list，elapse=797ms, respData={"data":{"dataList":[{"cardType":"昇腾 910A","computeDevicePort":"10011","cpuCoreCount":"192","cpuPort":"10012","createTime":1750352464000,"createUserId":1921773426524033024,"dataDeptId":1923195822141345795,"dataUserId":1921773426524033024,"graphicsMemory":"262144","hostIp":"************","id":1935744652716019712,"loginName":"root","memoryCapacity":"773220.5","monitoringStatistics":{"hostName":"资源1001","fbUsedUtil":0,"resourceInfoId":1935744652716019712},"password":"CUYd6btTyjDudtf8W5pq+MmlhdM6phU46tw3aXZkFLpglt7khxr7c5eL2gYCFlqIm4WU7QQ+bTaW7EmGEyGMpiWyF8ssCnngNUGaIcUl368DFjQGDimBdLYpIswyGC/MxWHxPsCZrHsllCMzIe/S2lYzc2xOy+fCe31eF2gUk98=","port":40086,"productType":1,"resourceName":"资源1001","resourceType":"","status":"active","systemInfo":"Linux","systemVersion":"EulerOS 2.0 (SP8)","updateTime":1750352464000,"updateUserId":1921773426524033024,"usedMemory":"96552.0"}]},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[ERROR] [2025-07-01 09:47:18] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:47:18] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:47:18] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:47:18] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:20] T:[c61d4188c4ed43098d4a26efb0a79097] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-5] ==> 开始请求，url=/admin/upms/sysDept/list, reqData={}
[INFO ] [2025-07-01 09:47:20] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 开始请求，url=/admin/upms/sysUser/list, reqData={"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}],"sysUserDtoFilter":{},"pageParam":{"count":true,"pageNum":1,"pageSize":10}}
[DEBUG] [2025-07-01 09:47:20] T:[c61d4188c4ed43098d4a26efb0a79097] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-5] ==> ==>  Preparing: SELECT * FROM sys_dept WHERE sys_dept.deleted_flag = 1 ORDER BY sys_dept.dept_id DESC
[DEBUG] [2025-07-01 09:47:20] T:[c61d4188c4ed43098d4a26efb0a79097] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-5] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT count(0) FROM sys_user WHERE sys_user.deleted_flag = 1
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:47:21] T:[c61d4188c4ed43098d4a26efb0a79097] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-5] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:21] T:[c61d4188c4ed43098d4a26efb0a79097] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-5] ==> 请求完成, url=/admin/upms/sysDept/list，elapse=54ms, respData={"data":{"dataList":[{"createTime":1747584000000,"createUserId":1923195822141345792,"deptId":1923195822141345795,"deptName":"公司总部","showOrder":1,"updateTime":1747584000000,"updateUserId":1923195822141345792}],"totalCount":0},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT * FROM sys_user WHERE sys_user.deleted_flag = 1 ORDER BY sys_user.create_time DESC LIMIT ?
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: 10(Integer)
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT dept_id,dept_name,show_order,parent_id,deleted_flag,create_user_id,update_user_id,create_time,update_time FROM sys_dept WHERE deleted_flag=1 AND (dept_id IN (?))
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: 1923195822141345795(Long)
[DEBUG] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 1
[INFO ] [2025-07-01 09:47:21] T:[9bf7bf56533f4a6a99c5502888576511] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 请求完成, url=/admin/upms/sysUser/list，elapse=156ms, respData={"data":{"dataList":[{"createTime":1747584000000,"createUserId":1923195822141345792,"deptId":1923195822141345795,"deptIdDictMap":{"name":"公司总部","id":1923195822141345795},"loginName":"admin","showName":"管理员","updateTime":1747584000000,"updateUserId":1923195822141345792,"userId":1921773426524033024,"userStatus":0,"userStatusDictMap":{"name":"正常状态","id":0},"userType":0,"userTypeDictMap":{"name":"管理员","id":0}}],"totalCount":1},"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":{"$ref":"$.data"},"success":true}
[INFO ] [2025-07-01 09:47:23] T:[e417e1aafaf644e9baccfdb56e9bff17] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:47:23] T:[e417e1aafaf644e9baccfdb56e9bff17] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:47:23] T:[e417e1aafaf644e9baccfdb56e9bff17] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:47:23] T:[e417e1aafaf644e9baccfdb56e9bff17] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> <==      Total: 35
[INFO ] [2025-07-01 09:47:23] T:[e417e1aafaf644e9baccfdb56e9bff17] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-9] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=187ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1937715585370558464,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"},{"$ref":"$.data[34]"}],"success":true}
[ERROR] [2025-07-01 09:47:39] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:47:39] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:47:39] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:47:40] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:47:49] T:[] S:[] U:[] [http-nio-8085-exec-4] ==> DataAccessException exception from URL [/admin/upms/sysMenu/delete]
org.springframework.data.redis.RedisSystemException: Redis exception
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:256)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:969)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:826)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:54)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:284)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:382)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:54)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:50)
	at cn.dev33.satoken.dao.SaTokenDaoRedisFastjson.get(SaTokenDaoRedisFastjson.java:101)
	at cn.dev33.satoken.stp.StpLogic.getLoginIdNotHandle(StpLogic.java:1094)
	at cn.dev33.satoken.stp.StpLogic.getLoginIdDefaultNull(StpLogic.java:1023)
	at cn.dev33.satoken.stp.StpLogic.isLogin(StpLogic.java:905)
	at cn.dev33.satoken.stp.StpUtil.isLogin(StpUtil.java:315)
	at supie.common.satoken.util.SaTokenUtil.handleAuthIntercept(SaTokenUtil.java:123)
	at supie.webadmin.interceptor.AuthenticationInterceptor.preHandle(AuthenticationInterceptor.java:64)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1076)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset
	at io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:967)
	... 75 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
[INFO ] [2025-07-01 09:47:50] T:[ffdab2d25647453f8b16b4cf6f25d861] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:47:50] T:[ffdab2d25647453f8b16b4cf6f25d861] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:47:50] T:[ffdab2d25647453f8b16b4cf6f25d861] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:47:50] T:[ffdab2d25647453f8b16b4cf6f25d861] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> <==      Total: 35
[INFO ] [2025-07-01 09:47:50] T:[ffdab2d25647453f8b16b4cf6f25d861] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-3] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=49ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1937715585370558464,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"},{"$ref":"$.data[34]"}],"success":true}
[INFO ] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 开始请求，url=/admin/upms/sysMenu/delete, reqData={"menuId":1937715585370558464}
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1937715585370558464(Long)
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT COUNT( * ) AS total FROM sys_menu WHERE parent_id=?
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1937715585370558464(Long)
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT sys_data_perm.* FROM sys_data_perm, sys_data_perm_menu WHERE sys_data_perm.data_perm_id = sys_data_perm_menu.data_perm_id AND sys_data_perm_menu.menu_id = ?
[DEBUG] [2025-07-01 09:47:58] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1937715585370558464(Long)
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: DELETE FROM sys_menu WHERE (menu_id = ?)
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1937715585370558464(Long)
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==    Updates: 1
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: DELETE FROM sys_role_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1937715585370558464(Long)
[DEBUG] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==    Updates: 0
[INFO ] [2025-07-01 09:47:59] T:[729d746bd2aa441692544021df7e1505] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 请求完成, url=/admin/upms/sysMenu/delete，elapse=396ms, respData={"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}
[INFO ] [2025-07-01 09:47:59] T:[88817e3ef66e44d3a14741578ba88ef0] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:47:59] T:[88817e3ef66e44d3a14741578ba88ef0] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:47:59] T:[88817e3ef66e44d3a14741578ba88ef0] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:47:59] T:[] S:[] U:[] [flowable-task-Executor-2] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:47:59] T:[] S:[] U:[] [flowable-task-Executor-2] ==> ==> Parameters: 1939863520677990400(Long), (String), 20(Integer), application-webadmin(String), supie.webadmin.upms.controller.SysMenuController(String), supie.webadmin.upms.controller.SysMenuController.delete(String), Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd(String), 729d746bd2aa441692544021df7e1505(String), 396(Long), POST(String), /admin/upms/sysMenu/delete(String), {"menuId":1937715585370558464}(String), {"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}(String), ***************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-07-01 09:47:58.908(Timestamp)
[DEBUG] [2025-07-01 09:47:59] T:[88817e3ef66e44d3a14741578ba88ef0] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 34
[INFO ] [2025-07-01 09:47:59] T:[88817e3ef66e44d3a14741578ba88ef0] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=49ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"}],"success":true}
[DEBUG] [2025-07-01 09:47:59] T:[] S:[] U:[] [flowable-task-Executor-2] ==> <==    Updates: 1
[ERROR] [2025-07-01 09:48:01] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:48:01] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:48:01] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:48:01] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 开始请求，url=/admin/upms/sysMenu/add, reqData={"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6}}
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1001(Long)
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1001(Long)
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==>  Preparing: INSERT INTO sys_menu ( menu_id, data_belong_type, deleted_flag, parent_id, menu_name, menu_type, form_router_name, show_order, extra_data, create_user_id, create_time, update_user_id, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> ==> Parameters: 1939863543046213632(Long), business(String), 1(Integer), 1001(Long), 容器管理(String), 1(Integer), container-manager(String), 6(Integer), {"bindType":0,"permCodeList":[]}(String), 1921773426524033024(Long), 2025-07-01 09:48:04.241(Timestamp), 1921773426524033024(Long), 2025-07-01 09:48:04.241(Timestamp)
[DEBUG] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> <==    Updates: 1
[INFO ] [2025-07-01 09:48:04] T:[32acae632a9a4e9099d3e5e8fea77b8a] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-2] ==> 请求完成, url=/admin/upms/sysMenu/add，elapse=367ms, respData={"data":1939863543046213632,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1939863543046213632,"success":true}
[DEBUG] [2025-07-01 09:48:04] T:[] S:[] U:[] [flowable-task-Executor-3] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:48:04] T:[] S:[] U:[] [flowable-task-Executor-3] ==> ==> Parameters: 1939863542559674368(Long), (String), 10(Integer), application-webadmin(String), supie.webadmin.upms.controller.SysMenuController(String), supie.webadmin.upms.controller.SysMenuController.add(String), Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd(String), 32acae632a9a4e9099d3e5e8fea77b8a(String), 368(Long), POST(String), /admin/upms/sysMenu/add(String), {"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6}}(String), {"data":1939863543046213632,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1939863543046213632,"success":true}(String), ***************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-07-01 09:48:04.125(Timestamp)
[INFO ] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 开始请求，url=/admin/upms/sysMenu/update, reqData={"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1939863543046213632,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6}}
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: 1939863543046213632(Long)
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: 1937715588415623169(Long)
[DEBUG] [2025-07-01 09:48:04] T:[] S:[] U:[] [flowable-task-Executor-3] ==> <==    Updates: 1
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==>  Preparing: UPDATE sys_menu SET data_belong_type=?, parent_id=?, menu_name=?, menu_type=?, form_router_name=?, show_order=?, extra_data=?, create_user_id=?, create_time=?, update_user_id=?, update_time=?, deleted_flag=?,online_form_id=?,online_menu_perm_type=?,report_page_id=?,online_flow_entry_id=?,icon=? WHERE menu_id=?
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> ==> Parameters: business(String), 1937715588415623169(Long), 容器管理(String), 1(Integer), container-manager(String), 6(Integer), {"bindType":0,"permCodeList":[]}(String), 1921773426524033024(Long), 2025-07-01 09:48:04.0(Timestamp), 1921773426524033024(Long), 2025-07-01 09:48:04.68(Timestamp), null, null, null, null, null, null, 1939863543046213632(Long)
[DEBUG] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> <==    Updates: 1
[INFO ] [2025-07-01 09:48:04] T:[8c4d81e6370e4b838a3c20773b3bc8a1] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-6] ==> 请求完成, url=/admin/upms/sysMenu/update，elapse=280ms, respData={"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}
[INFO ] [2025-07-01 09:48:04] T:[124847dfe06c4744a5ec9b3439113d56] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:48:04] T:[124847dfe06c4744a5ec9b3439113d56] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:48:04] T:[124847dfe06c4744a5ec9b3439113d56] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:48:04] T:[] S:[] U:[] [flowable-task-Executor-4] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:48:04] T:[] S:[] U:[] [flowable-task-Executor-4] ==> ==> Parameters: 1939863544396779520(Long), (String), 15(Integer), application-webadmin(String), supie.webadmin.upms.controller.SysMenuController(String), supie.webadmin.upms.controller.SysMenuController.update(String), Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd(String), 8c4d81e6370e4b838a3c20773b3bc8a1(String), 280(Long), POST(String), /admin/upms/sysMenu/update(String), {"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1939863543046213632,"menuName":"容器管理","menuType":1,"parentId":1937715588415623169,"showOrder":6}}(String), {"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","success":true}(String), ***************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-07-01 09:48:04.563(Timestamp)
[DEBUG] [2025-07-01 09:48:04] T:[124847dfe06c4744a5ec9b3439113d56] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> <==      Total: 34
[INFO ] [2025-07-01 09:48:04] T:[124847dfe06c4744a5ec9b3439113d56] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=57ms, respData={"data":[{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"}],"success":true}
[DEBUG] [2025-07-01 09:48:05] T:[] S:[] U:[] [flowable-task-Executor-4] ==> <==    Updates: 1
[ERROR] [2025-07-01 09:48:22] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:48:22] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:48:22] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:48:22] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:48:43] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:48:43] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:48:43] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:48:43] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:49:04] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:49:04] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:49:04] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:49:04] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[INFO ] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> 开始请求，url=/admin/upms/sysMenu/add, reqData={"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6}}
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==> Parameters: 1001(Long)
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==>  Preparing: SELECT menu_id,data_belong_type,deleted_flag,parent_id,menu_name,menu_type,form_router_name,online_form_id,online_menu_perm_type,report_page_id,online_flow_entry_id,show_order,icon,extra_data,create_user_id,create_time,update_user_id,update_time FROM sys_menu WHERE menu_id=?
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==> Parameters: 1001(Long)
[DEBUG] [2025-07-01 09:49:05] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:49:28] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:49:36] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:49:36] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:49:37] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==>  Preparing: INSERT INTO sys_menu ( menu_id, data_belong_type, deleted_flag, parent_id, menu_name, menu_type, form_router_name, show_order, extra_data, create_user_id, create_time, update_user_id, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:49:37] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> ==> Parameters: 1939863800287072256(Long), business(String), 1(Integer), 1001(Long), 容器管理(String), 1(Integer), container-manager(String), 6(Integer), {"bindType":0,"permCodeList":[]}(String), 1921773426524033024(Long), 2025-07-01 09:49:11.098(Timestamp), 1921773426524033024(Long), 2025-07-01 09:49:11.098(Timestamp)
[DEBUG] [2025-07-01 09:49:37] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[DEBUG] [2025-07-01 09:49:37] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> <==    Updates: 1
[ERROR] [2025-07-01 09:50:51] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:50:51] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:50:51] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:50:51] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[WARN ] [2025-07-01 09:50:51] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> 耗时较长的请求完成警告, url=/admin/upms/sysMenu/add，elapse=106493ms reqData={"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6}} respData={"data":1939863800287072256,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1939863800287072256,"success":true}
[INFO ] [2025-07-01 09:50:51] T:[9342687704b641c2bbca3e03bf9a79a4] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-1] ==> 请求完成, url=/admin/upms/sysMenu/add，elapse=106493ms, respData={"data":1939863800287072256,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1939863800287072256,"success":true}
[DEBUG] [2025-07-01 09:50:51] T:[] S:[] U:[] [flowable-task-Executor-5] ==> ==>  Preparing: INSERT INTO zz_sys_operation_log ( log_id, description, operation_type, service_name, api_class, api_method, session_id, trace_id, elapse, request_method, request_url, request_arguments, response_result, request_ip, success, operator_id, operator_name, operation_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[DEBUG] [2025-07-01 09:50:51] T:[] S:[] U:[] [flowable-task-Executor-5] ==> ==> Parameters: 1939863799460794368(Long), (String), 10(Integer), application-webadmin(String), supie.webadmin.upms.controller.SysMenuController(String), supie.webadmin.upms.controller.SysMenuController.add(String), Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd(String), 9342687704b641c2bbca3e03bf9a79a4(String), 106504(Long), POST(String), /admin/upms/sysMenu/add(String), {"sysMenuDto":{"dataBelongType":"business","extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6}}(String), {"data":1939863800287072256,"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":1939863800287072256,"success":true}(String), ***************(String), true(Boolean), 1921773426524033024(Long), admin(String), 2025-07-01 09:49:05.375(Timestamp)
[DEBUG] [2025-07-01 09:50:52] T:[] S:[] U:[] [flowable-task-Executor-5] ==> <==    Updates: 1
[ERROR] [2025-07-01 09:51:12] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:51:12] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:51:12] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:51:13] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:51:34] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:51:34] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:51:34] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:51:34] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:51:55] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:51:55] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 09:51:55] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 09:51:55.263(Timestamp)
[DEBUG] [2025-07-01 09:51:55] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:52:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:52:21] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:52:21] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:52:21] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:52:21] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:52:42] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:52:42] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:52:42] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:52:42] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:53:03] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:53:03] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:53:03] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:53:03] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:53:24] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:53:24] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:53:24] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:53:24] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:53:45] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:53:45] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:53:45] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:53:45] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:54:06] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:54:06] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:54:06] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:54:06] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:54:27] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:54:27] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:54:27] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:54:27] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:54:39] T:[] S:[] U:[] [http-nio-8085-exec-6] ==> DataAccessException exception from URL [/admin/upms/sysMenu/list]
org.springframework.data.redis.RedisSystemException: Redis exception
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:256)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:969)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:826)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:54)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:284)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:382)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:54)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:50)
	at cn.dev33.satoken.dao.SaTokenDaoRedisFastjson.get(SaTokenDaoRedisFastjson.java:101)
	at cn.dev33.satoken.stp.StpLogic.getLoginIdNotHandle(StpLogic.java:1094)
	at cn.dev33.satoken.stp.StpLogic.getLoginIdDefaultNull(StpLogic.java:1023)
	at cn.dev33.satoken.stp.StpLogic.isLogin(StpLogic.java:905)
	at cn.dev33.satoken.stp.StpUtil.isLogin(StpUtil.java:315)
	at supie.common.satoken.util.SaTokenUtil.handleAuthIntercept(SaTokenUtil.java:123)
	at supie.webadmin.interceptor.AuthenticationInterceptor.preHandle(AuthenticationInterceptor.java:64)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1076)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset
	at io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:967)
	... 75 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
[INFO ] [2025-07-01 09:54:44] T:[eddb68b3f3cc4c2aa9d778b685141f07] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> 开始请求，url=/admin/upms/sysMenu/list, reqData={"sysMenuDtoFilter":{"dataBelongType":"business"},"orderParam":[{"asc":false,"dateAggregateBy":"","fieldName":"createTime"}]}
[DEBUG] [2025-07-01 09:54:44] T:[eddb68b3f3cc4c2aa9d778b685141f07] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> ==>  Preparing: SELECT * FROM sys_menu WHERE sys_menu.data_belong_type = ? AND sys_menu.deleted_flag = 1 ORDER BY sys_menu.create_time DESC
[DEBUG] [2025-07-01 09:54:44] T:[eddb68b3f3cc4c2aa9d778b685141f07] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> ==> Parameters: business(String)
[DEBUG] [2025-07-01 09:54:44] T:[eddb68b3f3cc4c2aa9d778b685141f07] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> <==      Total: 35
[INFO ] [2025-07-01 09:54:44] T:[eddb68b3f3cc4c2aa9d778b685141f07] S:[Authorization:login:token-session:fbc3121f-32ad-4ce0-8583-137a594983dd] U:[1921773426524033024] [http-nio-8085-exec-7] ==> 请求完成, url=/admin/upms/sysMenu/list，elapse=58ms, respData={"data":[{"createTime":1751334551000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-manager","menuId":1939863800287072256,"menuName":"容器管理","menuType":1,"parentId":1001,"showOrder":6,"updateTime":1751334551000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"container-resource","menuId":1937715588415623169,"menuName":"容器资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":5,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager","menuId":1937715588415623168,"menuName":"Service服务","menuType":0,"parentId":1937715588415623169,"showOrder":11,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor","menuId":1937715586846953472,"menuName":"Pod容器组监控","menuType":0,"parentId":1937715588415623169,"showOrder":9,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager-index","menuId":1937715586842759169,"menuName":"Ingress路由管理","menuType":1,"parentId":1937715586842759168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"ingress-manager","menuId":1937715586842759168,"menuName":"Ingress路由","menuType":0,"parentId":1937715588415623169,"showOrder":10,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"pod-monitor-index","menuId":1937715586825981952,"menuName":"Pod容器组监控","menuType":1,"parentId":1937715586846953472,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822372000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-manager-index","menuId":1937715586800816128,"menuName":"Service服务管理","menuType":1,"parentId":1937715588415623168,"showOrder":1,"updateTime":1750822374000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterLabel-manager","menuId":1937715583382458368,"menuName":"集群标签管理","menuType":1,"parentId":1937715588415623169,"showOrder":4,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"card-running","menuId":1937715583416012800,"menuName":"显卡运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"storageVolume-manager","menuId":1937715583432790016,"menuName":"存储卷管理","menuType":1,"parentId":1937715588415623169,"showOrder":5,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"cluster-manager","menuId":1937715583436984320,"menuName":"集群管理","menuType":1,"parentId":1937715588415623169,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNode-manager","menuId":1937715583617339392,"menuName":"集群节点管理","menuType":1,"parentId":1937715588415623169,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager","menuId":1937715585328615424,"menuName":"部署管理","menuType":0,"parentId":1937715588415623169,"showOrder":8,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"deployment-manager-index","menuId":1937715585357975552,"menuName":"部署管理","menuType":1,"parentId":1937715585328615424,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager","menuId":1937715585370558465,"menuName":"命名空间","menuType":0,"parentId":1937715588415623169,"showOrder":7,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1750822371000,"createUserId":1921773426524033024,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"clusterNamespace-manager-index","menuId":1937715585370558466,"menuName":"命名空间管理","menuType":1,"parentId":1937715585370558465,"showOrder":1,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002347219685377,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002347219685376,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"resource-pool\",\"permCodeList\":[]}","formRouterName":"resource-pool","menuId":1932002344703102979,"menuName":"资源池管理","menuType":0,"parentId":1867406109141110785,"showOrder":2,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460229000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"resource-pool-index","menuId":1932002344703102978,"menuName":"资源池管理","menuType":1,"parentId":1932002344703102979,"showOrder":1,"updateTime":1749460230000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-scheduling","menuId":1932002304458756098,"menuName":"任务调度","menuType":0,"parentId":1867406109141110785,"showOrder":4,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-monitor","menuId":1932002304458756097,"menuName":"任务运行监控","menuType":1,"parentId":1932002304458756098,"showOrder":2,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"image-management","menuId":1932002304458756096,"menuName":"镜像管理","menuType":1,"parentId":1937715588415623169,"showOrder":1,"updateTime":1751334402000,"updateUserId":1921773426524033024},{"createTime":1749460219000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"task-template","menuId":1932002304152571904,"menuName":"任务模板管理","menuType":1,"parentId":1932002304458756098,"showOrder":1,"updateTime":1750822372000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"service-resource\",\"permCodeList\":[]}","formRouterName":"service-resource","menuId":1932002301208170496,"menuName":"服务资源管理","menuType":0,"parentId":1867406109141110785,"showOrder":3,"updateTime":1750822373000,"updateUserId":1921773426524033024},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"overview","menuId":1932002300935540736,"menuName":"总览","menuType":1,"parentId":1867406109141110785,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1749460218000,"createUserId":1923195822141345792,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"service-resource-index","menuId":1932002301136867329,"menuName":"服务资源管理","menuType":1,"parentId":1932002301208170496,"showOrder":1,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"formRouterName\":\"index\",\"permCodeList\":[]}","formRouterName":"index","menuId":1867406109141110785,"menuName":"业务系统","menuType":0,"parentId":1001,"showOrder":1,"updateTime":1748341360000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"systemManage","menuId":1867406109774450688,"menuName":"系统管理","menuType":0,"parentId":1001,"showOrder":2,"updateTime":1749460219000,"updateUserId":1923195822141345792},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysMenu","menuId":1867406109371797504,"menuName":"菜单管理","menuType":1,"parentId":1867406109774450688,"showOrder":4,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDept","menuId":1867406109346631683,"menuName":"部门管理","menuType":1,"parentId":1867406109774450688,"showOrder":2,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysUser","menuId":1867406109346631682,"menuName":"用户管理","menuType":1,"parentId":1867406109774450688,"showOrder":1,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysDataPerm","menuId":1867406109346631681,"menuName":"数据权限管理","menuType":1,"parentId":1867406109774450688,"showOrder":5,"updateTime":1734059286000,"updateUserId":1742014705053995008},{"createTime":1734059286000,"createUserId":1742014705053995008,"dataBelongType":"business","deletedFlag":1,"extraData":"{\"bindType\":0,\"permCodeList\":[]}","formRouterName":"formSysRole","menuId":1867406109346631680,"menuName":"角色管理","menuType":1,"parentId":1867406109774450688,"showOrder":3,"updateTime":1734059286000,"updateUserId":1742014705053995008}],"errorCode":"NO-ERROR","errorMessage":"NO-MESSAGE","message":[{"$ref":"$.data[0]"},{"$ref":"$.data[1]"},{"$ref":"$.data[2]"},{"$ref":"$.data[3]"},{"$ref":"$.data[4]"},{"$ref":"$.data[5]"},{"$ref":"$.data[6]"},{"$ref":"$.data[7]"},{"$ref":"$.data[8]"},{"$ref":"$.data[9]"},{"$ref":"$.data[10]"},{"$ref":"$.data[11]"},{"$ref":"$.data[12]"},{"$ref":"$.data[13]"},{"$ref":"$.data[14]"},{"$ref":"$.data[15]"},{"$ref":"$.data[16]"},{"$ref":"$.data[17]"},{"$ref":"$.data[18]"},{"$ref":"$.data[19]"},{"$ref":"$.data[20]"},{"$ref":"$.data[21]"},{"$ref":"$.data[22]"},{"$ref":"$.data[23]"},{"$ref":"$.data[24]"},{"$ref":"$.data[25]"},{"$ref":"$.data[26]"},{"$ref":"$.data[27]"},{"$ref":"$.data[28]"},{"$ref":"$.data[29]"},{"$ref":"$.data[30]"},{"$ref":"$.data[31]"},{"$ref":"$.data[32]"},{"$ref":"$.data[33]"},{"$ref":"$.data[34]"}],"success":true}
[ERROR] [2025-07-01 09:54:49] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:54:49] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:54:49] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:54:49] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:55:10] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:55:10] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:55:10] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:55:10] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:55:31] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:55:31] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:55:31] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:55:31] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:55:52] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:55:52] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:55:52] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:55:52] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:56:13] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:56:13] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:56:13] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:56:13] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:56:34] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:56:34] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:56:34] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:56:34] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:56:55] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:56:55] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:56:55] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:56:55] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:57:16] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:57:16] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:57:16] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:57:16] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:57:38] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:57:38] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:57:38] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:57:38] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:57:59] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:57:59] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:57:59] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 09:57:59] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:58:20] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:58:20] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 09:58:20] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 09:58:20.354(Timestamp)
[DEBUG] [2025-07-01 09:58:20] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:58:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:58:51] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:58:51] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:58:51] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:58:51] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:59:12] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:59:12] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:59:12] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:59:12] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:59:33] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:59:33] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:59:33] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:59:33] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 09:59:54] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 09:59:54] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 09:59:54] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 09:59:54] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:00:15] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:00:15] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:00:15] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:00:15] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:00:36] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:00:36] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:00:36] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:00:36] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:00:57] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:00:57] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:00:57] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:00:57] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:01:18] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:01:18] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:01:18] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:01:18] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:01:40] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:01:40] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:01:40] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:01:40] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:02:01] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:02:01] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:02:01] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:02:01] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:02:22] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:02:22] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:02:22] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:02:22] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:02:43] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:02:43] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:02:43] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:02:43] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:03:04] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:03:04] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:03:04] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:03:04] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:03:25] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:03:25] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:03:25] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:03:25] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:03:46] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:03:46] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:03:46] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:03:46] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:04:07] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:04:07] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:04:07] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:04:08] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:04:29] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:04:29] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:04:29] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:04:29] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,remote_task_json,task_type,exit_code,fail_reason,scale_plan_id,dict_id,container_name,container_status,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,task_name,status,task_priority,graphic_needed_mb,memory_needed_mb,cpu_need,pool_id,resource_id,task_image_id,compute_device_id,partition_id,run_command,env_config,release_policy,start_time,end_tiem,estimat_time,scheduling_policies,approve_state,allow_preemption FROM sch_task_info WHERE is_delete=1 AND (status = ? AND (container_status = ?))
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: running(String), running(String)
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 10:04:50.266(Timestamp)
[DEBUG] [2025-07-01 10:04:50] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:05:00] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:05:21] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:05:21] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:05:21] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:05:21] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:05:42] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:05:42] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:05:42] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:05:42] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:06:03] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:06:03] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:06:03] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:06:03] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:06:24] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:06:24] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:06:24] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:06:24] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:06:45] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 78c18ece4168 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:06:45] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:06:45] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:06:45] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:07:06] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ad3385bdf8c9 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:07:06] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:07:06] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:07:06] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:07:27] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1029a7f907db - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:07:27] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:07:27] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:07:27] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:07:48] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: c90cb2b15952 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:07:48] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:07:48] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:07:48] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:08:10] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 287aa21b0871 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:08:10] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:08:10] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:08:10] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:08:31] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 7d299afd8ea8 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:08:31] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:08:31] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:08:31] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:08:52] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: b4aced013ac6 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:08:52] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:08:52] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:08:52] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:09:13] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 906ffcb49eb1 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:09:13] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:09:13] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:09:13] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:09:34] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 868c589a5223 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:09:34] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:09:34] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:09:34] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:09:55] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 4e3626ef4720 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:09:55] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:09:55] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:09:55] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:10:16] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 83d25996598c - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:10:16] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:10:16] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:10:16] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:10:37] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 737dd37a6b37 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:10:37] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:10:37] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:10:37] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:10:58] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 1f666d30939e - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:10:58] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:10:58] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1939582665866874880(Long)
[DEBUG] [2025-07-01 10:10:59] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:11:20] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 55547a4811a0 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://*************:2375 [/*************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:11:20] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,process_definition_id,process_instance_id,task_key,task_id,timeout_hours,handle_way,default_assignee,error_message,status,exec_time,create_time,update_time FROM zz_flow_task_timeout_job WHERE (status = ? AND exec_time <= ?)
[DEBUG] [2025-07-01 10:11:20] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 0(Integer), 2025-07-01 10:11:20.109(Timestamp)
[DEBUG] [2025-07-01 10:11:20] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 0
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT ti.id, ti.create_time, ti.task_name, ti.container_name, ti.container_status, ti.resource_id, cm.container_id, cm.id AS containerManagerId FROM sch_task_info ti JOIN sch_container_manager cm ON ti.id = cm.task_info_id WHERE ti.is_delete = 1 AND ti.status != 'finished' AND ti.container_name IS NOT NULL AND cm.is_delete = 1 LIMIT 50
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 18
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:11:30] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:11:51] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: ef9006b5e56a - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:11:51] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:11:51] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:11:51] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:12:12] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: f6ce8ec67263 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:12:12] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:12:12] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:12:12] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:12:33] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 2d1601c46398 - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:12:33] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:12:33] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:12:33] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
[ERROR] [2025-07-01 10:12:54] T:[] S:[] U:[] [scheduling-1] ==> 获取容器状态失败 containerId: 989f981f01fe - org.apache.hc.client5.http.HttpHostConnectException: Connect to http://************:2375 [/************] failed: Connection timed out: no further information
[DEBUG] [2025-07-01 10:12:54] T:[] S:[] U:[] [scheduling-1] ==> ==>  Preparing: SELECT id,systemInfo,system_version,gpu_count,gpu_memory,used_memory,available_memory,str_id,update_time,create_time,create_user_id,update_user_id,data_user_id,data_dept_id,is_delete,uuid,resource_name,host_ip,port,login_name,password,resource_type,status,graphics_memory,memory_capacity,disk_capacity,cpu_port,compute_device_port,cpu_core_count,connect_config_json,compute_device_id FROM sch_resource_info WHERE id=? AND is_delete=1
[DEBUG] [2025-07-01 10:12:54] T:[] S:[] U:[] [scheduling-1] ==> ==> Parameters: 1935744652716019712(Long)
[DEBUG] [2025-07-01 10:12:54] T:[] S:[] U:[] [scheduling-1] ==> <==      Total: 1
