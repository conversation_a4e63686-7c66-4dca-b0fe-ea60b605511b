package com.supie.common.satoken.config;

import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

//@Primary
public class MyLettuceConnectionFactory extends LettuceConnectionFactory {

    public MyLettuceConnectionFactory(RedisStandaloneConfiguration standaloneConfig, LettuceClientConfiguration clientConfig) {
        super(standaloneConfig, clientConfig);
    }

}
