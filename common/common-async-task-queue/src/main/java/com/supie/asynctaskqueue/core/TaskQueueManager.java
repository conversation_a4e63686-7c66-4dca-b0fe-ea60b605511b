package com.supie.asynctaskqueue.core;

import com.supie.asynctaskqueue.TaskType;
import com.supie.asynctaskqueue.config.AsyncTaskQueueConfig;
import com.supie.asynctaskqueue.exception.QueueFullException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.*;

/**
 * 队列管理器
 */
@Slf4j
@Component
public class TaskQueueManager {

    private final ConcurrentMap<TaskType, BlockingQueue<TaskInfo>> queues = new ConcurrentHashMap<>();
    private final Map<TaskType, ThreadPoolExecutor> threadPoolExecutorMap = new ConcurrentHashMap<>();

    /**
     * 初始化各任务线程池
     */
    @PostConstruct
    public void initThreadPoolExecutor() {
        for (TaskType taskType : TaskType.values()) {
            threadPoolExecutorMap.put(taskType, AsyncTaskQueueConfig.createPool(taskType));
        }
    }

    /**
     * 添加任务
     * @param task task
     * @throws QueueFullException 如果队列已满，则抛出此异常
     */
    public void addTask(TaskInfo task) throws QueueFullException {
        TaskType taskType = task.getTaskType();
        BlockingQueue<TaskInfo> queue = queues.computeIfAbsent(
                taskType,
                k -> new PriorityBlockingQueue<>(taskType.getQueueCapacity())
        );
        if (!queue.offer(task)) {
            throw new QueueFullException("Queue " + task.getTaskType() + " is full");
        }
        tryStartProcessing(taskType);
    }

    /**
     * 尝试启动处理
     */
    private void tryStartProcessing(TaskType taskType) {
        if (hasAvailableCapacity(taskType)) {
            ThreadPoolExecutor executor = threadPoolExecutorMap.get(taskType);
            executor.execute(() -> {
                processTasks(taskType);
            });
        }
    }

    /**
     * 检查是否有可用的容量
     * @return boolean
     */
    private boolean hasAvailableCapacity(TaskType taskType) {
        ThreadPoolExecutor executor = threadPoolExecutorMap.get(taskType);
        // 检查当前活动线程数是否小于最大线程数
        boolean hasAvailableCapacity = executor.getActiveCount() < executor.getMaximumPoolSize();
        // 检查队列剩余容量是否大于0
        boolean hasRemainingCapacity = executor.getQueue().remainingCapacity() > 0;
        return hasAvailableCapacity;
    }

    /**
     * 处理任务
     */
    private void processTasks(TaskType taskType) {
        try {
            TaskInfo task = getNextTask(taskType);
            while (task != null) {
                processSingleTask(task);
                task = getNextTask(taskType);
            }
        } finally {
            checkPendingTasks(taskType);
        }
    }

    /**
     * 处理单个任务
     * @param task task
     */
    private void processSingleTask(TaskInfo task) {
        try {
            Object result = task.execute();
            handleSuccess(task, result);
        } catch (Exception ex) {
            handleFailure(task, ex);
        }
    }

    /**
     * 处理失败
     * @param task task
     * @param ex ex
     */
    private void handleFailure(TaskInfo task, Exception ex) {
//        throw new MyRuntimeException(ex);
        log.error("Task failed: {}", ex.getMessage());
    }

    /**
     * 处理成功
     * @param task task
     * @param result result
     */
    private void handleSuccess(TaskInfo task, Object result) {
    }

    /**
     * 检查是否有待处理的任务，有则重新启动处理
     */
    private void checkPendingTasks(TaskType taskType) {
        if (hasPendingTasks(taskType)) {
            ThreadPoolExecutor executor = threadPoolExecutorMap.get(taskType);
            executor.execute(() -> {
                processTasks(taskType);
            });
        }
    }

    /**
     * 检查是否有待处理的任务
     * @return boolean
     */
    private boolean hasPendingTasks(TaskType taskType) {
        BlockingQueue<TaskInfo> taskInfos = queues.get(taskType);
        return taskInfos != null && !taskInfos.isEmpty();
    }

    /**
     * 获取下一个待处理的任务
     * @return TaskInfo
     */
    private TaskInfo getNextTask(TaskType taskType) {
        BlockingQueue<TaskInfo> taskInfos = queues.get(taskType);
        if (taskInfos != null && !taskInfos.isEmpty()) {
            return taskInfos.poll();
        }
        return null;
    }

}
