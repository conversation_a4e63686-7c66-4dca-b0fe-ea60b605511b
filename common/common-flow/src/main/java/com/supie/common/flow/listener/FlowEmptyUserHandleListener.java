package com.supie.common.flow.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.supie.common.core.util.ApplicationContextHolder;
import com.supie.common.flow.constant.FlowApprovalType;
import com.supie.common.flow.exception.FlowEmptyUserException;
import com.supie.common.flow.model.FlowTaskComment;
import com.supie.common.flow.model.FlowTaskExt;
import com.supie.common.flow.object.FlowUserTaskExtData;
import com.supie.common.flow.service.FlowApiService;
import com.supie.common.flow.service.FlowTaskExtService;
import com.supie.common.flow.vo.FlowUserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

import java.util.List;

/**
 * 空审批人处理监听器。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Slf4j
public class FlowEmptyUserHandleListener implements TaskListener {

    private final transient FlowTaskExtService flowTaskExtService =
            ApplicationContextHolder.getBean(FlowTaskExtService.class);
    private final transient FlowApiService flowApiService =
            ApplicationContextHolder.getBean(FlowApiService.class);

    @Override
    public void notify(DelegateTask t) {
        FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(
                t.getProcessDefinitionId(), t.getTaskDefinitionKey());
        Task task = flowApiService.getTaskById(t.getId());
        List<FlowUserInfoVo> userInfoVoList =
                flowTaskExtService.getCandidateUserInfoList(t.getProcessInstanceId(), flowTaskExt, task,
                        flowApiService.isMultiInstanceTask(t.getProcessDefinitionId(), t.getTaskDefinitionKey()), false);
        if (CollUtil.isNotEmpty(userInfoVoList)) {
            return;
        }
        FlowUserTaskExtData taskExtData =
                JSON.parseObject(flowTaskExt.getExtraDataJson(), FlowUserTaskExtData.class);
        if (StrUtil.isBlank(taskExtData.getEmptyUserHandleWay())) {
            return;
        }
        switch (taskExtData.getEmptyUserHandleWay()) {
            case FlowUserTaskExtData.EMPTY_USER_TO_ASSIGNEE:
                t.setAssignee(taskExtData.getEmptyUserToAssignee());
                break;
            case FlowUserTaskExtData.EMPTY_USER_AUTO_REJECT:
            case FlowUserTaskExtData.EMPTY_USER_AUTO_COMPLETE:
                FlowTaskComment comment = new FlowTaskComment();
                comment.fillWith(task);
                if (taskExtData.getEmptyUserHandleWay().equals(FlowUserTaskExtData.EMPTY_USER_AUTO_REJECT)) {
                    comment.setApprovalType(FlowApprovalType.EMPTY_USER_AUTO_REJECT);
                    comment.setTaskComment("空审批人自动回退审批。");
                    throw new FlowEmptyUserException(JSON.toJSONString(comment));
                } else {
                    comment.setApprovalType(FlowApprovalType.EMPTY_USER_AUTO_COMPLETE);
                    comment.setTaskComment("空审批人自动跳过审批。");
                    flowApiService.completeTask(task, comment, null, null);
                }
                break;
            default:
                break;
        }
    }
}
