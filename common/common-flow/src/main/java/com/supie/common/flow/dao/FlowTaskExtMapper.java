package com.supie.common.flow.dao;

import com.supie.common.core.base.dao.BaseDaoMapper;
import com.supie.common.flow.model.FlowTaskExt;

import java.util.List;

/**
 * 流程任务扩展数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
public interface FlowTaskExtMapper extends BaseDaoMapper<FlowTaskExt> {

    /**
     * 批量插入流程任务扩展信息列表。
     *
     * @param flowTaskExtList 流程任务扩展信息列表。
     */
    void insertList(List<FlowTaskExt> flowTaskExtList);
}
