package supie.common.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import supie.common.core.annotation.MyDataSourceResolver;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.base.service.BaseService;
import supie.common.core.constant.ApplicationConstant;
import supie.common.core.util.DefaultDataSourceResolver;
import supie.common.core.util.MyCommonUtil;
import supie.common.flow.dao.FlowAutoVariableLogMapper;
import supie.common.flow.model.FlowAutoVariableLog;
import supie.common.flow.service.FlowAutoVariableLogService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@MyDataSourceResolver(
        resolver = DefaultDataSourceResolver.class,
        intArg = ApplicationConstant.COMMON_FLOW_AND_ONLINE_DATASOURCE_TYPE)
@Service("flowAutoVariableLogService")
public class FlowAutoVariableLogServiceImpl extends BaseService<FlowAutoVariableLog, Long> implements FlowAutoVariableLogService {

    @Autowired
    private FlowAutoVariableLogMapper flowAutoVariableLogMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;

    @Override
    protected BaseDaoMapper<FlowAutoVariableLog> mapper() {
        return flowAutoVariableLogMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNew(FlowAutoVariableLog o) {
        o.setId(idGenerator.nextLongId());
        o.setTraceId(MyCommonUtil.getTraceId());
        o.setCreateTime(new Date());
        flowAutoVariableLogMapper.insert(o);
    }

    @Override
    public FlowAutoVariableLog getAutoVariableByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<FlowAutoVariableLog> qw = new LambdaQueryWrapper<>();
        qw.eq(FlowAutoVariableLog::getProcessInstanceId, processInstanceId);
        return flowAutoVariableLogMapper.selectOne(qw);
    }
    
    @Override
    public void deleteByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<FlowAutoVariableLog> qw = new LambdaQueryWrapper<>();
        qw.eq(FlowAutoVariableLog::getProcessInstanceId, processInstanceId);
        flowAutoVariableLogMapper.delete(qw);
    }
}
