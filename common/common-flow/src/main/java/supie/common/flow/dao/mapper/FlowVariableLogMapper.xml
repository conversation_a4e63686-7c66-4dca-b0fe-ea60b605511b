<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.flow.dao.FlowVariableLogMapper">
    <resultMap id="BaseResultMap" type="supie.common.flow.model.FlowVariableLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="process_definition_key" jdbcType="VARCHAR" property="processDefinitionKey"/>
        <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
        <result column="task_key" jdbcType="VARCHAR" property="taskKey"/>
        <result column="variable_data" jdbcType="LONGVARCHAR" property="variableData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime"/>
    </resultMap>
</mapper>
