<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.supie.common.report.dao.ReportVisualizationMapper">
    <resultMap id="BaseResultMap" type="com.supie.common.report.model.ReportVisualization">
        <id column="visual_id" jdbcType="BIGINT" property="visualId"/>
        <result column="visual_name" jdbcType="VARCHAR" property="visualName"/>
        <result column="config_json" jdbcType="LONGVARCHAR" property="configJson"/>
        <result column="cover_img" jdbcType="LONGVARCHAR" property="coverImg"/>
        <result column="code_page" jdbcType="VARCHAR" property="codePage"/>
        <result column="publish_status" jdbcType="INTEGER" property="publishStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.supie.common.report.dao.ReportVisualizationMapper.inputFilterRef"/>
        AND zz_report_visualization.deleted_flag = ${@com.supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="reportVisualizationFilter != null">
            <if test="reportVisualizationFilter.visualName != null and reportVisualizationFilter.visualName != ''">
                <bind name = "safeReportVisualizationVisualName" value = "'%' + reportVisualizationFilter.visualName + '%'" />
                AND zz_report_visualization.visual_name LIKE #{safeReportVisualizationVisualName}
            </if>
            <if test="reportVisualizationFilter.publishStatus != null">
                AND zz_report_visualization.publish_status = #{reportVisualizationFilter.publishStatus}
            </if>
            <if test="reportVisualizationFilter.createUserId != null">
                AND zz_report_visualization.create_user_id = #{reportVisualizationFilter.createUserId}
            </if>
        </if>
    </sql>

    <select id="getReportVisualizationList" resultMap="BaseResultMap"
            parameterType="com.supie.common.report.model.ReportVisualization">
        SELECT visual_id, visual_name, cover_img, code_page, publish_status FROM zz_report_visualization
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
