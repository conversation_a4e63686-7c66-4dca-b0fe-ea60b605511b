package com.supie.common.fileConvert.ppt2pdf;

import com.aspose.slides.Presentation;

import java.io.*;

import com.aspose.slides.SaveFormat;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * <p>
 * PPT 转 PDF 工具类
 * </p>
 *
 */
@Slf4j
public class Ppt2PdfUtil {
    /**
     * 将 PPT 转换为 PDF
     * @param inputFilePath
     * @param outputFilePath
     */
    public static void convertPptToPdf(String inputFilePath, String outputFilePath) {
        // 加载 PPT 文件
        Presentation presentation = null;
        try {
            presentation = new Presentation(inputFilePath);
            // 设置输出 PDF 文件的路径
            File outputFile = new File(outputFilePath);

            // 检查输出文件的父目录是否存在，不存在则创建
            if (!outputFile.getParentFile().exists()) {
                outputFile.getParentFile().mkdirs();
            }

            // 将 PPT 转换为 PDF 并保存
            try (FileOutputStream out = new FileOutputStream(outputFile)) {
                presentation.save(out, SaveFormat.Pdf);
            } catch (IOException e) {
                System.err.println("Error saving PDF file: " + e.getMessage());
            }
        } catch (Exception e) {
            System.err.println("Error loading PPT file: " + e.getMessage());
        } finally {
            // 手动关闭 Presentation 对象
            if (presentation != null) {
                try {
                    presentation.dispose();  // 使用 dispose 方法释放资源
                } catch (Exception e) {
                    System.err.println("Error disposing Presentation: " + e.getMessage());
                }
            }
        }
    }
}


































