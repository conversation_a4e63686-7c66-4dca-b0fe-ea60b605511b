package supie.common.core.util;

import groovy.lang.GroovyObject;
import org.springframework.stereotype.Component;

/**
 * Groovy脚本的执行器。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Component
public class GroovyExecutor {

    public String exec(String script, String method, Object[] args) {
        GroovyObject go = GroovyLoader.loadScript(script);
        return (String) go.invokeMethod(method, args);
    }
}
