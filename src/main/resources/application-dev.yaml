server:
  port: 8200
spring:
  mvc:
    static-path-pattern: "/resources/**"
  datasource:
    url: ***********************************************************************
    username: root
    password: MyUserPassword123!
    driver-class-name: com.mysql.cj.jdbc.Driver
  application:
    name: devEnvironment #开发环境

logging:
  file:
    name: review.log #在当前工程下生成一个review.log记录日志

minio:
  endpoint: http://47.123.4.109:9000
  accessKey: admin
  secretKey:  password
