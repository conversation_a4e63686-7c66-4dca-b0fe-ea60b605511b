package com.uoh.compose;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/5/9 15:40
 */
public class ComposeStatusTest {
    public static void main(String[] args) {
        ComposeStatus composeStatus = new ComposeStatus();
        composeStatus.makeDir();
       // composeStatus.getComposeServiceStatus(null,"/compose");
//        Map<String, Integer> map = new HashMap<>();
//        map.put("key1", 1);
//        map.put("key2", 2);
//        map.put("key3", 3);
//        map.put("key4", 4);
//        System.out.println(map.toString());
    }
}
