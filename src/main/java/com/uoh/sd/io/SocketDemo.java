package com.uoh.sd.io;

import java.net.InetSocketAddress;
import java.net.ProxySelector;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.WebSocket;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/3/10 16:43
 */
public class SocketDemo {
    /*
     * Socket通信流程
     * 1.服务端： 创建Socket连接-->绑定IP:端口(bind())-->监听(listen())-->接收连接(accept())
     * 2.客户端: 船舰Socket-->连接服务器(connect())-->发送数据
     * 3.数据传输: 双向通信(send()/rec())
     * 4.关闭连接： 双方调用Close释放资源
     * Socket发送方法 sendText() sendBinary()
     * Socket接收  onText() onBinary() WebSocket 通过调用监听器上的接收方法来启动接收操作
     * */
    public static void main(String[] args) {
        //1 构建WebSocket
        HttpClient httpClient = HttpClient.newHttpClient();//创建HTTP client 相当于 newBuilder().build() 创建一个服务端
        CompletableFuture<WebSocket> webSocketCompletableFuture = httpClient.newWebSocketBuilder()
                .buildAsync(URI.create("ws://websocket.example.com"), new WebSocket.Listener() {//构建一个 WebSocket 连接到给定的 URI 并与给定的 Listener 关联
                    @Override
                    public void onOpen(WebSocket webSocket) {
                        WebSocket.Listener.super.onOpen(webSocket);
                    }
                });
    }
}

   /*

    InetSocketAddress addr = new InetSocketAddress("proxy.example.com", 80);
    HttpClient client = HttpClient.newBuilder()
            .proxy(ProxySelector.of(addr))
            .build();
    CompletableFuture<WebSocket> ws = client.newWebSocketBuilder()
            .buildAsync(URI.create("ws://websocket.example.com"), new WebSocket.Listener() {

                @Override
                public void onOpen(WebSocket webSocket) {
                    WebSocket.Listener.super.onOpen(webSocket);
                }
            });



*/
