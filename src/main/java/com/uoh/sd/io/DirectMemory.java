package com.uoh.sd.io;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/3/25 15:45
 */
public class DirectMemory {
    public static void main(String[] args) throws IOException {
        // 直接内存 .allocateDirect7S  allocate非直接缓冲区10S
        long start = System.currentTimeMillis();
        // 1文件输入流
        FileInputStream fileInputStream = new FileInputStream(new File("D:\\waitsoftware\\CentOS-7-x86_64-DVD-1810.iso"));
        //2  输入流中货物通道
        FileChannel  inChannel = fileInputStream.getChannel();
        //构建文件输出流
        FileOutputStream outputStream = new FileOutputStream(new File("D:\\waitsoftware\\CentOS1.iso"));
        // 输入流中获取通道
        FileChannel outChannel = outputStream.getChannel();
        // 构建直接缓冲区
       // ByteBuffer byteBuffer = ByteBuffer.allocateDirect(5 * 1024 * 1024);
        ByteBuffer byteBuffer = ByteBuffer.allocate(5 * 1024 * 1024);
        // inChannel.read（） 将通道里面的数据写入到缓冲区
        while (inChannel.read(byteBuffer)!=-1){
            // 切换为写
            byteBuffer.flip();
            // 将·缓冲区的数据写入到通道
            outChannel.write(byteBuffer);
            // 清除缓冲区
            byteBuffer.clear();
        }
        //资源关闭
        outChannel.close();
        inChannel.close();
        fileInputStream.close();
        outputStream.close();
        long end = System.currentTimeMillis();
        System.out.println("耗时" + (end - start)/1000 + "s");

    }
}
