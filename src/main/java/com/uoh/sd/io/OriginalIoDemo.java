package com.uoh.sd.io;

import java.io.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/2/21 17:06
 */
public class OriginalIoDemo {
    public static void main(String[] args) {

        /*
        * 数据--数据输入流--缓存区--内存--缓冲区--数据输出流--数据
        *  标准IO 流式阻塞IO 模型  同步阻塞： 线程读数据时候必须等待 面向流： 按字节逐个处理
        * */
        try {
            File file = new File("D:\\record.txt");//构建文件通道
            FileInputStream fileInputStream = new FileInputStream(file);//构建文件输入流
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);//放入缓冲流提升速度
            byte[] bytes = new byte[1024];
            FileOutputStream fileOutputStream = new FileOutputStream("D:/out.txt");//构建存放文件路肩
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream);//输出缓冲流
            while (bufferedInputStream.read(bytes) != -1) {
                bufferedOutputStream.write(bytes,0,-1);
                bufferedOutputStream.flush();
           }
            fileInputStream.close();
            fileInputStream.close();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {

        }
    }
}
