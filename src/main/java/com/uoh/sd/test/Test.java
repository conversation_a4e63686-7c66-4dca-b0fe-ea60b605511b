package com.uoh.sd.test;

import com.alibaba.fastjson2.JSON;
import com.uoh.ReviewApplication;
import com.uoh.sso.ContractRoot;
import com.uoh.sso.SsoJson;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/2/16 10:07
 */
public class Test {
    public static void main(String[] args) {
        String bss3SessionId= "bb5d936f-4a74-4ad4-9ffc-446b580cdc06";
        String verifyKey="BSC@oASA";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String signTimestamp = sdf.format(new Date());
        String context = "bss3SessionId=" + bss3SessionId + "&signTimestamp=" + signTimestamp + "&verifyKey=" + verifyKey;
        String encrypt = DigestUtils.md5Hex(context);
        System.out.println("当前时间戳....signTimestamp="+signTimestamp);
        System.out.printf("加密签名.....signTimestamp="+encrypt);
        System.out.println("--------------------------");
        String url= " http://***************:31913/ssoLogin?bss3SessionId=bb5d936f-4a74-4ad4-9ffc-446b580cdc06&signTimestamp="+signTimestamp+"&signString="+encrypt;
        String url1= " http://localhost:9016/ssoLogin?bss3SessionId=bb5d936f-4a74-4ad4-9ffc-446b580cdc06&signTimestamp="+signTimestamp+"&signString="+encrypt;
        String str = "773220.5MB";
        double mb = Double.parseDouble(str.replace("MB", ""));
        System.out.println(mb);
        System.out.println("".quals("343434"));


    }
}
