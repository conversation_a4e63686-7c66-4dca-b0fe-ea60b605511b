package com.uoh.sd.thread;

public class GenericsDemo21 {
    public static void main(String args[]) {
        Info2<String> i1 = new Info2<String>();        // 声明String的泛型对象
        Info2<Object> i2 = new Info2<Object>();        // 声明Object的泛型对象
        i1.setVar("hello");
        i2.setVar(new Object());
        fun(i1);
        fun(i2);
    }

    // 只能接收String或Object类型的泛型，String类的父类只有Object类
    /*
    * super 当前类型及其父类
    * */
    public static void fun(Info2<? super String> temp) {
        System.out.print(temp + ", ");
    }
}
