package com.uoh.sd.temp;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import io.minio.*;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description 代码环境拷环境数据拷贝
 * @date 2025/4/8 17:37
 */
@Component
@Slf4j
public class CodeVolumeUtil {
    // SFTP配置
    private static final String SFTP_HOST = "************";
    private static final int SFTP_PORT = 22;
    private static final String SFTP_USER = "root";
    private static final String SFTP_PASS = "aliyun20011013!";
    private ChannelSftp sftpChannel;
     private Session session;

    public static void main(String[] args) throws Exception {
        CodeVolumeUtil processor = null;
        try {
            processor = new CodeVolumeUtil();
            processor.initialize();
            BuildFileTree buildFileTree = processor.processDirectory("/jupyter",null);
            String jsonString = JSON.toJSONString(buildFileTree);
            log.info("json 字符串...{}",jsonString);
            // 服务器拷贝上传
            //CreateFileTreeUtil createFileTreeUtil = new CreateFileTreeUtil();
            //createFileTreeUtil.targetInitialize(null,buildFileTree,"/jupyterCopy");
        } finally {
            log.info("finally ");
            assert processor != null;
            processor.close();

        }
    }

    /**
     * 连接资源关闭
     */
    public void close(){
        if(sftpChannel!=null){
            sftpChannel.disconnect();
        }
        if(session!=null){
            log.info("close.....");
            session.disconnect();
        }
        log.info("服务器文件拷贝存储结束...");
    }

    /**
     *  初始化服务器文件连接信息
     */
    private void initialize()  {
        // 初始化SFTP连接
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(SFTP_USER, SFTP_HOST, SFTP_PORT);
            session.setPassword(SFTP_PASS);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
        } catch (JSchException e) {
            log.error("服务连接失败JSchException",e);
        }
    }


    /**
     * 拷贝服务器指定文件夹下的文件信息同时构建文件结果树
     * @param path 服务器拷贝根路径
     * @param parentId 上级文件id
     * @return 文件结果树
     */
    private BuildFileTree processDirectory(String path, Long parentId) throws Exception {
        //1 初始根目录节点创建
        BuildFileTree currentNode = new BuildFileTree();
        currentNode.setFileName(new File(path).getName()).
                setId(IdUtil.getSnowflakeNextId()).
                setFilePath(path).
                setParentId(parentId)
                .setFileType("dir")
                .setFileSize(0L);
        // 2创建 子节点列表集合
        List<BuildFileTree> children = new ArrayList<>();
        List<ChannelSftp.LsEntry> entries = sftpChannel.ls(path);
        for (ChannelSftp.LsEntry entry : entries) {
            String filename = entry.getFilename();
            if (filename.equals(".") || filename.equals("..")) {
                continue;
            }
            String fullPath = path + "/" + filename;
            long fileSize = entry.getAttrs().getSize();
            boolean dir = entry.getAttrs().isDir();
            //3 进入循环说明存在孩子节点
            BuildFileTree childNode = new BuildFileTree();
            childNode.setFileName(filename)
                    .setFilePath(fullPath)
                    .setParentId(currentNode.getId())
                    .setFileType(dir ? "dir" : "file")
                    .setFileSize(fileSize)
                    .setId(IdUtil.getSnowflakeNextId());
            if (dir) {
                // 4 递归结果存储到子树
                BuildFileTree childDir = this.processDirectory(fullPath, currentNode.getId());
                children.add(childDir);
            } else {
                String ext = filename.substring(filename.lastIndexOf('.') + 1);
                boolean isImage = Pattern.matches("(?i)^(jpe?g|png|gif|bmp|webp|tiff?|svg)$", ext);
                String storePath = isImage ? "image/BusinessFile/fileJson/" + filename : "/attachment/BusinessFile/fileJson/" + filename;
                this.uploadMinio(sftpChannel.get(fullPath),storePath,fileSize);
                childNode.setStoragePath(storePath);
                children.add(childNode);
            }
        }
        currentNode.setChildren(children);
        return currentNode;
    }

    /**
     * 把目录下的文件上传存入minio
     * @param fileInPutStream 文件流
     * @param storePath 对象桶存储了路径

     */
    public  void uploadMinio(InputStream fileInPutStream,String storePath,long fileSize) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        MinioClient minioClient = MinioClient.builder()
                .endpoint("http://************:9000")
                .credentials("admin", "password")
                .build();
        String normalizedPath = storePath.startsWith("/")
                ? storePath.substring(1)
                : storePath;
        if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket("review").build())) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket("review").build());
        }
        try {
            //检查文件是否存在
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket("review")
                            .object(normalizedPath)
                            .build()
            );
        } catch (ErrorResponseException e) {
            if(e.errorResponse().code().equals("NoSuchKey")){
                log.info("上传文件不存在");
                minioClient.putObject(PutObjectArgs.builder()
                        .bucket("review")
                        // 指定对象存储桶唯一路径
                        .object(normalizedPath)
                        .stream(fileInPutStream, fileSize, -1)
                        .build()
                );
            }else {
                log.error("文件不存上传失败...");
                log.error(e.getMessage(),e);
            }
        }finally {
            try {
                fileInPutStream.close();
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
        }
    }
}

