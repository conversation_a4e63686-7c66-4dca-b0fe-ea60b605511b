package com.uoh.sd.yml;

import org.yaml.snakeyaml.Yaml;

import java.io.FileWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/3/26 20:22
 */
public class ComposeGenerator {
    public static void main(String[] args) throws Exception {
        String token = generateSecureToken(); // 生成随机TOKEN
        int jupyterPort = 8888; // 可配置端口

        DockerComposeConfig config = new DockerComposeConfig()
                .service("coder", new Service()
                        .image("quay.io/supiedt/jupyter:latest") // 替换[tag]
                        .restart("always")
                        .device("/dev/davinci0:/dev/davinci0")
                        .device("/dev/davinci_manager:/dev/davinci_manager")
                        .device("/dev/devmm_svm:/dev/devmm_svm")
                        .device("/dev/hisi_hdc:/dev/hisi_hdc")
                        .volume("/usr/local/dcmi:/usr/local/dcmi")
                        .volume("/usr/local/bin/npu-smi:/usr/local/bin/npu-smi")
                        .volume("/usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi")
                        .volume("/usr/local/Ascend/driver:/usr/local/Ascend/driver")
                        .volume("/usr/local/Ascend/firmware:/usr/local/Ascend/firmware")
                        .volume("/usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro")
                        .volume("./volumes/coder/models:/workspace/models")
                        .volume("./volumes/coder/simple:/workspace/simple")
                        .port(jupyterPort + ":9000")
                        .environment("TOKEN", token));

        generateComposeFile(config, "docker-compose2.yml");
        System.out.println("Docker compose file generated successfully!");
    }

    private static String generateSecureToken() {
        // 实际生产环境应使用更安全的生成方式
        return UUID.randomUUID().toString().replace("-", "") + Long.toHexString(System.currentTimeMillis());
    }

    private static void generateComposeFile(DockerComposeConfig config, String filename) throws Exception {
        Yaml yaml = new Yaml();
        Map<String, Object> composeData = new LinkedHashMap<>();

        Map<String, Object> services = new LinkedHashMap<>();
        config.services.forEach((name, service) -> {
            Map<String, Object> serviceMap = new LinkedHashMap<>();
            serviceMap.put("image", service.image);
            serviceMap.put("restart", service.restart);

            if (!service.devices.isEmpty()) {
                serviceMap.put("devices", service.devices);
            }

            serviceMap.put("volumes", service.volumes);
            serviceMap.put("ports", Collections.singletonList(service.ports));

            Map<String, String> envMap = new LinkedHashMap<>();
            envMap.put("TOKEN", service.environment.get("TOKEN"));
            serviceMap.put("environment", envMap);

            services.put(name, serviceMap);
        });

        composeData.put("services", services);

        try (FileWriter writer = new FileWriter(filename)) {
            yaml.dump(composeData, writer);
        }
    }

    static class DockerComposeConfig {
        Map<String, Service> services = new LinkedHashMap<>();

        DockerComposeConfig service(String name, Service service) {
            services.put(name, service);
            return this;
        }
    }

    static class Service {
        String image;
        String restart;
        List<String> devices = new ArrayList<>();
        List<String> volumes = new ArrayList<>();
        String ports;
        Map<String, String> environment = new LinkedHashMap<>();

        Service image(String image) {
            this.image = image;
            return this;
        }

        Service restart(String policy) {
            this.restart = policy;
            return this;
        }

        Service device(String device) {
            this.devices.add(device);
            return this;
        }

        Service volume(String volume) {
            this.volumes.add(volume);
            return this;
        }

        Service port(String portMapping) {
            this.ports = portMapping;
            return this;
        }

        Service environment(String key, String value) {
            this.environment.put(key, value);
            return this;
        }
    }

}
