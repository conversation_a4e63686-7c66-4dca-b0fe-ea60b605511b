package com.uoh.sd.proxy;

import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/4/8 10:42
 */
public class JupyterWebSocketHandler  extends AbstractWebSocketHandler {
    private static final String INTERNAL_WS_URL = "ws://************:2344";

    @Override
    public void afterConnectionEstablished(WebSocketSession userSession) throws Exception {
        WebSocketClient client = new StandardWebSocketClient();
        WebSocketSession internalSession = client.execute(new InternalWebSocketHandler(userSession), INTERNAL_WS_URL + userSession.getUri().getPath()).get();
    }

    private static class InternalWebSocketHandler extends AbstractWebSocketHandler {
        private final WebSocketSession userSession;

        public InternalWebSocketHandler(WebSocketSession userSession) {
            this.userSession = userSession;
        }

        @Override
        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
            userSession.sendMessage(message);
        }

        @Override
        public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
            userSession.close(status);
        }
    }
}
