package com.uoh.nettystu.client;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandler;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelPipeline;
import io.netty.util.CharsetUtil;

/**
 * <AUTHOR>
 * @Description netty 客户端 处理器 消息处理器
 * @date 2025/5/12 19:32
 */
public class NettyClientHandler extends ChannelInboundHandlerAdapter {

    /**
     *
     *
     * @param ctx ChannelHandlerContext 用于数据传输
     * @param msg 服务端传输过来的消息
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        //接收服务端发送过来的消息
        ByteBuf byteBuf = (ByteBuf) msg;
        System.out.println("收到服务端" + ctx.channel().remoteAddress() + "的消息：" + byteBuf.toString(CharsetUtil.UTF_8));

    }

    /**
     *  发送数据到服务端
     *
     * @param ctx ChannelHandlerContext 用于传输数据
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        //发送消息到服务端
        ctx.writeAndFlush(Unpooled.copiedBuffer("歪比巴卜~茉莉~Are you good~马来西亚~", CharsetUtil.UTF_8));
    }
}