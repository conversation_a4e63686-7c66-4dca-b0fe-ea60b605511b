package com.uoh.socket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

/**
 * <AUTHOR>
 * @Description 自定义socket 消息处理器
 * @date 2025/5/21 20:10
 */
@Slf4j
@Component
public class MySocketHandler extends TextWebSocketHandler {
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        log.info("mySocketHandler receive message: {}", message.getPayload());
        super.handleTextMessage(session, message);
    }
}
