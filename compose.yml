!!com.uoh.compose.DockerComposeConfig
services:
  workspace:
    devices: ['/dev/davinci0:/dev/davinci0', '/dev/davinci_manager:/dev/davinci_manager',
      '/dev/devmm_svm:/dev/devmm_svm', '/dev/hisi_hdc:/dev/hisi_hdc']
    environment: [JUPYTER_TOKEN=c12e9b10-39c0-4368-ae9b-1039c0e368f6, JUPYTER_BASE_URL=c12e9b10-39c0-4368-ae9b-1039c0e368f6+jupyter]
    image: quay.io/supiedt/workspace:ubuntu-python3.10-cann8.1.RC1.alpha001
    ports: ['8080:9009']
    restart: always
    volumes: ['/usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro', '/usr/local/dcmi:/usr/local/dcmi',
      './config.yaml:/etc/processlauncher/config.yaml:ro', './volumes/coder/models:/workspace/models:rw',
      './volumes/coder/simple:/workspace/simple:rw']
